package cn.abcyun.cis.goods.listener;

import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.model.Goods;
import cn.abcyun.cis.goods.model.GoodsExtend;

import java.util.List;

/**
 * 商品信息监听器
 *
 * <AUTHOR>
 * @since 2024/8/13 18:28
 **/
public interface GoodsInfoListener {

    /**
     * 商品停用
     */
    default void onGoodsDisable(ClinicConfig clinicConfig, Goods goods, List<GoodsExtend> updateGoodsExtend, String operatorId) {

    }

}
