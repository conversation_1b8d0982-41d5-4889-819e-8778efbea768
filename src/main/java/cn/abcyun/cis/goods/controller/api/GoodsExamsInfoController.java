package cn.abcyun.cis.goods.controller.api;


import cn.abcyun.bis.rpc.sdk.bis.model.goods.mall.MallGoodsSkuAbstractVO;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.BaseSuccessRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.exam.DeleteExamAssayDeviceReq;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.model.ExaminationDeviceModel;
import cn.abcyun.cis.goods.service.GoodsService;
import cn.abcyun.cis.goods.service.exam.ExaminationDeviceConnectApplyService;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.view.ExaminationDeviceConnectApplyView;
import cn.abcyun.cis.goods.view.TapdWorkSheetEventView;
import cn.abcyun.cis.goods.vo.frontend.exam.*;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 检查检验设备的管理的Controller
 */
@RestController
@RequestMapping("/api/v3/goods/exam")
@Api(value = "检查检验设备的管理的Controller", produces = "application/json")
public class GoodsExamsInfoController {
    @Autowired
    private GoodsService goodsService;

    @Autowired
    private ExaminationDeviceConnectApplyService examinationDeviceConnectApplyService;



    // ----- 检查设备 ------
    @GetMapping("/inspection/devices")
    @LogReqAndRsp
    @Deprecated
    @ApiOperation(value = "获取连锁内检查设备列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ExamInspectDeviceView>> getExamInspectDeviceList(@RequestParam(value = "defaultDevice", required = false) @ApiParam(value = "是否返回内置设备 null 0 全部 1 内置设备 2 非内置设备") Integer defaultDevice,
                                                                                           @RequestParam(value = "offset", required = false) @ApiParam(value = "分页参数 开始,不传表示不分页全拉") Integer offset,
                                                                                           @RequestParam(value = "limit", required = false) @ApiParam(value = "分页参数 结束,不传表示不分页全拉") Integer limit,
                                                                                           @RequestParam(value = "keyword", required = false) @ApiParam(value = "keyWord搜索参数") String keyword,
                                                                                           @RequestParam(value = "goodsType", required = false, defaultValue = "3") Integer goodsType,
                                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
                                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
                                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
                                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
                                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode) throws CisGoodsServiceException {
        GetDeviceListReq clientReq = new GetDeviceListReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);

        clientReq.setGoodsType(goodsType);
        clientReq.setGoodsSubType(GoodsConst.ExaminationSubType.INSPECTION);
        clientReq.setKeyword(keyword);
        clientReq.setDeviceType(defaultDevice);
        clientReq.setLimit(limit);
        clientReq.setOffset(offset);
        return new AbcServiceResponse<>(goodsService.getExamInspectDeviceList(clientReq));
    }

    @GetMapping("/inspection/devices/{deviceId}")
    @LogReqAndRsp
    @Deprecated
    @ApiOperation(value = "获取检查设备详情", produces = "application/json")
    public AbcServiceResponse<ExamInspectDeviceView> getExamInspectionDevice(@PathVariable("deviceId") @ApiParam(value = "设备Id") String deviceId,
                                                                             @RequestParam(value = "goodsType", required = false, defaultValue = "3") Integer goodsType,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode) throws CisGoodsServiceException {
        GetDeviceListReq clientReq = new GetDeviceListReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);

        clientReq.setDeviceId(GoodsUtils.getLongId(deviceId));
        clientReq.setGoodsType(goodsType);
        clientReq.setGoodsSubType(GoodsConst.ExaminationSubType.INSPECTION);
        return new AbcServiceResponse<>(goodsService.getExamInspectionDevice(clientReq));
    }

    @PutMapping("/inspection/devices/{deviceId}")
    @LogReqAndRsp
    @Deprecated
    @ApiOperation(value = "修改检查设备信息", produces = "application/json")
    public AbcServiceResponse<ExamInspectDeviceView> updateExamInspectionDevice(@PathVariable("deviceId") @ApiParam(value = "设备Id") String deviceId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
                                                                                @Valid @RequestBody NewOrUpdateExamInspectDeviceReq clientReq) throws CisGoodsServiceException {
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);

        clientReq.setId(deviceId);
        clientReq.setGoodsType(clientReq.getGoodsType());
        clientReq.setGoodsSubType(GoodsConst.ExaminationSubType.INSPECTION);
        clientReq.parameterCheck(true);
        return new AbcServiceResponse<>(goodsService.newOrUpdateExamInspectionDevice(clientReq));
    }

    @PutMapping("/inspection/devices/{deviceId}/on-off")
    @LogReqAndRsp
    @Deprecated
    @ApiOperation(value = "停用/启用 检查设备", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> disableExamInspectionDevice(@PathVariable("deviceId") @ApiParam(value = "设备Id") String deviceId,
                                                                        @RequestParam(value = "disable", required = true) @ApiParam(value = "停用还是启用设备 0 启用 1 停用") Integer disable,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode) throws CisGoodsServiceException {
        return new AbcServiceResponse<>(goodsService.disableExamInspectionDevice(headerChainId, headerClinicId, headerEmployeeId, headerClinicType, headerViewMode, deviceId, disable));
    }

    @PostMapping("/inspection/devices")
    @LogReqAndRsp
    @Deprecated
    @ApiOperation(value = "保存检查设备信息", produces = "application/json")
    public AbcServiceResponse<ExamInspectDeviceView> createExamInspectionDevice(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
                                                                                @Valid @RequestBody NewOrUpdateExamInspectDeviceReq clientReq) throws CisGoodsServiceException {
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);

        clientReq.setGoodsType(clientReq.getGoodsType());
        clientReq.setGoodsSubType(GoodsConst.ExaminationSubType.INSPECTION);
        clientReq.parameterCheck(false);
        return new AbcServiceResponse<>(goodsService.newOrUpdateExamInspectionDevice(clientReq));
    }


    // ----- 检查检验设备 ------
    @GetMapping("/assay/devices")
    @LogReqAndRsp
    @ApiOperation(value = "获取门店内的检查检验设备列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ExamAssayDeviceView>> getExamAssayDeviceList(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
                                                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
                                                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
                                                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
                                                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
                                                                                       @RequestParam(value = "goodsType", required = false, defaultValue = "3") Integer goodsType,
                                                                                       @RequestParam(value = "goodsSubType", required = false, defaultValue = "1") Integer goodsSubType) throws CisGoodsServiceException {
        GetDeviceListReq clientReq = new GetDeviceListReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);

        clientReq.setGoodsType(goodsType);
        clientReq.setGoodsSubType(goodsSubType);
        return new AbcServiceResponse<>(goodsService.getExamAssayDeviceList(clientReq));
    }

    @PutMapping("/assay/devices/check-link/{deviceModelId}")
    @LogReqAndRsp
    @ApiOperation(value = "检查是否能联机", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> checkLinklinkExamAssayModelDevice(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
                                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
                                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
                                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
                                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
                                                                              @PathVariable Long deviceModelId) throws CisGoodsServiceException {
        return new AbcServiceResponse<>(goodsService.checkCanLink(headerChainId, headerClinicId, deviceModelId));
    }

    @PostMapping("/assay/devices/check-link")
    @LogReqAndRsp
    @ApiOperation(value = "检查是否能联机", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> checkLinkExamDevice(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
                                                                @RequestBody ExamDeviceCheckLinkReq checkLinkReq) throws CisGoodsServiceException {
        return new AbcServiceResponse<>(goodsService.checkLinkExamDevice(headerChainId, headerClinicId, checkLinkReq));
    }

    @PutMapping("/assay/devices/link/{deviceModelId}")
    @LogReqAndRsp
    @ApiOperation(value = "绑定设备(门店新增检查检验设备)", produces = "application/json")
    public AbcServiceResponse<ExamAssayDeviceView> saveExamAssayDevice(@PathVariable("deviceModelId") @ApiParam(value = "设备型号Id") String deviceModelId,
                                                                       @RequestParam(value = "onlyCheckCanLink", required = false) @ApiParam(value = "检查是否可以联机") Integer onlyCheckCanLink,
                                                                       @RequestParam(value = "goodsType", required = false, defaultValue = "3") Integer goodsType,
                                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
                                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
                                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
                                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
                                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode) throws CisGoodsServiceException {
        InsertExamAssayDeviceReq clientReq = new InsertExamAssayDeviceReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);

        clientReq.setOnlyCheckCanLink(onlyCheckCanLink);
        clientReq.setStrDeviceModelId(deviceModelId); //连接的是设备型号 ID
        clientReq.setGoodsType(goodsType);
        //clientReq.setGoodsSubType(GoodsConst.ExaminationSubType.ASSAY);
        clientReq.parameterCheck();
        return new AbcServiceResponse<>(goodsService.saveExamAssayDevice(clientReq));
    }

    @DeleteMapping("/assay/devices/{deviceId}")
    @LogReqAndRsp
    @ApiOperation(value = "删除检查检验设备")
    public AbcServiceResponse<ExamAssayDeviceView> deleteExamAssayDeviceById(@PathVariable("deviceId") @ApiParam(value = "设备Id") Long deviceId,
                                                                             @RequestParam(value = "isDisableGoods", required = false, defaultValue = "0") Integer isDisableGoods,
                                                                             @RequestParam(value = "isDeleteGoods", required = false, defaultValue = "0") Integer isDeleteGoods,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode) {
        DeleteExamAssayDeviceReq deleteDeviceReq = new DeleteExamAssayDeviceReq();
        deleteDeviceReq.setChainId(chainId);
        deleteDeviceReq.setClinicId(clinicId);
        deleteDeviceReq.setOperatorId(operatorId);
        deleteDeviceReq.setClinicType(clinicType);
        deleteDeviceReq.setViewMode(viewMode);
        deleteDeviceReq.setIsDisableGoods(isDisableGoods);
        deleteDeviceReq.setIsDeleteGoods(isDeleteGoods);
        return new AbcServiceResponse<>(goodsService.deleteExamAssayDeviceById(deviceId, deleteDeviceReq));
    }

    @PutMapping("/assay/devices/{deviceId}")
    @LogReqAndRsp
    @ApiOperation(value = "修改检查检验设备")
    public AbcServiceResponse<OpsCommonRsp> updateExamAssayDeviceById(@PathVariable("deviceId") @ApiParam(value = "设备Id") Long deviceId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                                      @Valid @RequestBody UpdateExamAssayDeviceReq updateExamAssayDeviceReq) {
        return new AbcServiceResponse<>(goodsService.updateExamAssayDeviceById(deviceId, chainId, clinicId, operatorId, updateExamAssayDeviceReq));
    }

    @PutMapping("/assay/devices/{deviceId}/{deviceStatus}")
    @LogReqAndRsp
    @ApiOperation(value = "更新检查检验设备联机状态", produces = "application/json")
    public AbcServiceResponse<ExamAssayDeviceView> updateExamAssayDeviceStatus(@PathVariable("deviceId") @ApiParam(value = "设备Id") String deviceId,
                                                                               @PathVariable("deviceStatus") @ApiParam(value = "设备状态 10-启用 20-停用") Integer deviceStatus,
                                                                               @RequestParam(value = "onlyCheckCanLink", required = false) @ApiParam(value = "检查是否可以联机") Integer onlyCheckCanLink,
                                                                               @RequestParam(value = "goodsType", required = false, defaultValue = "3") Integer goodsType,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode) throws CisGoodsServiceException {
        UpdateExamAssayDeviceStatusReq clientReq = new UpdateExamAssayDeviceStatusReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);


        clientReq.setStrDeviceld(deviceId);
        clientReq.setDeviceStatus(deviceStatus);
        clientReq.setGoodsType(goodsType);
        //clientReq.setGoodsSubType(GoodsConst.ExaminationSubType.ASSAY);
        clientReq.setOnlyCheckCanLink(onlyCheckCanLink);
        clientReq.parameterCheck();
        return new AbcServiceResponse<>(goodsService.updateExamAssayDeviceStatus(clientReq));
    }

    /**
     * 保存或更新检验设备参数
     *
     * @param deviceId   设备id
     * @param chainId    连锁id
     * @param clinicId   诊所id
     * @param operatorId 操作符id
     * @param clinicType 临床类型
     * @param viewMode   视图模式
     * @return {@link AbcServiceResponse}<{@link ExamAssayDeviceView}>
     */
    @PutMapping("/assay/devices/parameters/{deviceId}")
    @LogReqAndRsp
    @ApiOperation(value = "保存或更新检验设备参数", produces = "application/json")
    public AbcServiceResponse<ExamAssayDeviceView> updateExamineDeviceParameters(@PathVariable("deviceId") @ApiParam(value = "设备Id") Long deviceId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                                                 @RequestBody JsonNode deviceParameters) {
        return new AbcServiceResponse<>(goodsService.updateExamineDeviceParameters(deviceId, chainId, clinicId, operatorId, deviceParameters));
    }

    @GetMapping("/assay/devices/{deviceId}")
    @ApiOperation(value = "获取检查检验设备详情", produces = "application/json")
    public AbcServiceResponse<ExamAssayDeviceView> getExamAssayDevice(@PathVariable("deviceId") @ApiParam(value = "设备Id") String deviceId,
                                                                      @RequestParam(value = "goodsType", required = false, defaultValue = "3") Integer goodsType,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode) throws CisGoodsServiceException {
        GetDeviceListReq clientReq = new GetDeviceListReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);

        clientReq.setDeviceId(GoodsUtils.getLongId(deviceId));
        clientReq.setGoodsType(goodsType);
        //clientReq.setGoodsSubType(GoodsConst.ExaminationSubType.ASSAY);
        return new AbcServiceResponse<>(goodsService.getExamAssayDevice(clientReq));
    }

    /**
     * 获取门店所有设备的关联项
     *
     * @param goodsType    货物类型
     * @param goodsSubType goods 子类型
     * @param chainId      连锁id
     * @param clinicId     诊所 ID
     * @return {@link AbcServiceResponse }<{@link AbcListPage }<{@link GoodsItem }>>
     */
    @GetMapping("/assay/devices/association-goods-list")
    public AbcServiceResponse<AbcListPage<GoodsItem>> getExamAssayDeviceAssociationGoodsList(@RequestParam(value = "goodsType", required = false, defaultValue = "3") int goodsType,
                                                                                             @RequestParam(value = "goodsSubType", required = false, defaultValue = "1") int goodsSubType,
                                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        return new AbcServiceResponse<>(goodsService.getExamAssayDeviceAssociationGoodsList(chainId, clinicId, goodsType, goodsSubType));
    }

    // ----- 检验设备型号 ------
    @GetMapping("/assay/device-models")
    @LogReqAndRsp
    @ApiOperation(value = "获取检查检验设备型号列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ExamAssayDeviceView>> getExamAssayDeviceModelList(@RequestParam("offset") @ApiParam(value = "分页参数 开始") Integer offset,
                                                                                            @RequestParam("limit") @ApiParam(value = "分页参数 结束") Integer limit,
                                                                                            @RequestParam(value = "keyword", required = false) String keyword,
                                                                                            @RequestParam(value = "goodsType", required = false, defaultValue = "3") Integer goodsType,
                                                                                            @RequestParam(value = "goodsSubType", required = false, defaultValue = "1") Integer goodsSubType,
                                                                                            @RequestParam(value = "goodsExtendSpec", required = false) String goodsExtendSpec,
                                                                                            @RequestParam(value = "cloudSupplierFlag", required = false) Integer cloudSupplierFlag,
                                                                                            @RequestParam(value = "deviceType", required = false) @ApiParam(value = "设备类型 检验: [1临床检验, 2生化检验, 3免疫检验, 4微生物检验, 5pcr检验, 6未知分类] 检查: [0未知, 1CT, 2DR, 3CR, 4普通透视, 5心电图, 6骨密度, 7试光类, 8彩超, 9MRI, 10胃肠镜, 11B超, 12脑电图, 13其他]") Integer deviceType,
                                                                                            @RequestParam(value = "usageType", required = false) @ApiParam(value = "设备用途类型（检查设备） 1 血液分析 2 生化分析 3尿液分析 4免疫分析 5 微生物分析) null 表是返回所有包括未知设备") Integer usageType,
                                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
                                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId) throws CisGoodsServiceException {
        return new AbcServiceResponse<>(goodsService.getExamDeviceModelList(goodsType, goodsSubType, goodsExtendSpec, deviceType, usageType, cloudSupplierFlag, ExaminationDeviceModel.OK, null, keyword, offset != null ? offset : 0, limit != null ? limit : 20));
    }

    @GetMapping("/assay/device-models/{deviceModelId}")
    @LogReqAndRsp
    @ApiOperation(value = "获取检查检验设备型号详情", produces = "application/json")
    public AbcServiceResponse<ExamAssayDeviceView> getExamDeviceAssayModel(
            @PathVariable("deviceModelId") @ApiParam(value = "设备型号Id") String deviceModelId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @RequestParam(value = "combineType", required = false) @ApiParam(value = "可以过滤是否只返回单个项目还是内置项目") Integer combineType,
            @RequestParam(value = "goodsType", required = false, defaultValue = "3") Integer goodsType,
            @RequestParam(value = "onlyStandardGoods", required = false) @ApiParam(value = "只返回模版goods列表") Integer onlyStandardGoods) throws CisGoodsServiceException {
        GetDeviceListReq clientReq = new GetDeviceListReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);

        clientReq.setDeviceModelId(GoodsUtils.getLongId(deviceModelId));
        clientReq.setGoodsType(goodsType);
        //clientReq.setGoodsSubType(GoodsConst.ExaminationSubType.ASSAY);
        clientReq.setCombineType(combineType);
        clientReq.setOnlyStandardGoods(onlyStandardGoods);
        return new AbcServiceResponse<>(goodsService.getExamDeviceAssayModel(clientReq));
    }

    @GetMapping("/assay/my-device-models")
    @ApiOperation(value = "获取门店内的检查检验设备型号列表(连接过的设备型号)", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ExamAssayDeviceView>> getExamAssayMyDeviceModelList(
//            @RequestParam(value = "offset", required = false) @ApiParam(value = "分页参数 开始,不传表示不分页全拉") Integer offset,
//            @RequestParam(value = "limit", required = false) @ApiParam(value = "分页参数 结束,不传表示不分页全拉") Integer limit,
            @RequestParam(value = "goodsType", required = false, defaultValue = "3") Integer goodsType,
            @RequestParam(value = "goodsSubType", required = false, defaultValue = "1") Integer goodsSubType,
            @RequestParam(value = "goodsExtendSpec", required = false) String goodsExtendSpec,
            @RequestParam(value = "withDeleted", required = false, defaultValue = "1") int withDeleted,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode) throws CisGoodsServiceException {
        GetDeviceListReq clientReq = new GetDeviceListReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);

        clientReq.setGoodsType(goodsType);
        clientReq.setGoodsSubType(goodsSubType);
        clientReq.setGoodsExtendSpec(goodsExtendSpec);
        clientReq.setWithDeleted(withDeleted);
        return new AbcServiceResponse<>(goodsService.getExamAssayMyDeviceModelList(clientReq));
    }

    @GetMapping("/mall/device-models/recommend")
    @LogReqAndRsp
    @ApiOperation(value = "获取商城推荐的检查检验设备型号列表", produces = "application/json")
    public AbcListPage<MallGoodsSkuAbstractVO> getMallRecommendDeviceModels(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                            @RequestParam(value = "categoryName") String categoryName) {
        return goodsService.getMallRecommendDeviceModels(clinicId, categoryName);
    }

    /**
     * 查询所有数据
     *
     * @return 数据列表
     */
    @GetMapping("/sample-pipe")
    @LogReqAndRsp
    @ApiOperation(value = "查询所有数据")
    public AbcServiceResponse<AbcListPage<ExaminationSamplePipeView>> getExaminationSamplePipes(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                @RequestParam(value = "withDeleted", required = false, defaultValue = "0") @ApiParam(value = "是否包含已删除数据 0-不包含(默认) 1-包含") Integer withDeleted,
                                                                                                ExaminationSamplePipeView examinationSamplePipeReq) {

        return new AbcServiceResponse<>(goodsService.getExaminationSamplePipes(chainId, clinicId, withDeleted, examinationSamplePipeReq));
    }

    /**
     * 新增数据
     *
     * @param examinationSamplePipeReq 请求参数
     * @return 新增结果
     */
    @PostMapping("/sample-pipe")
    @LogReqAndRsp
    @ApiOperation(value = "新增数据")
    public AbcServiceResponse<ExaminationSamplePipeView> saveExaminationSamplePipe(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                   @Valid @RequestBody ExaminationSamplePipeView examinationSamplePipeReq) {


        return new AbcServiceResponse<>(goodsService.saveExaminationSamplePipe(chainId, clinicId, employeeId, examinationSamplePipeReq));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除结果
     */
    @DeleteMapping("/sample-pipe/{id}")
    @LogReqAndRsp
    @ApiOperation(value = "删除数据")
    public AbcServiceResponse<ExaminationSamplePipeView> deleteExaminationSamplePipeById(@PathVariable String id,
                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {


        return new AbcServiceResponse<>(goodsService.deleteExaminationSamplePipeById(id, chainId, clinicId, employeeId));
    }

    /**
     * 修改数据
     *
     * @param id                       主键
     * @param examinationSamplePipeReq 请求参数
     * @return 修改结果
     */
    @PutMapping("/sample-pipe/{id}")
    @LogReqAndRsp
    @ApiOperation(value = "修改数据")
    public AbcServiceResponse<ExaminationSamplePipeView> updateExaminationSamplePipeById(@PathVariable String id,
                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                         @Valid @RequestBody ExaminationSamplePipeView examinationSamplePipeReq) {


        return new AbcServiceResponse<>(goodsService.updateExaminationSamplePipeById(id, chainId, clinicId, employeeId, examinationSamplePipeReq));
    }

    /**
     * 查看详情
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/sample-pipe/{id}")
    @LogReqAndRsp
    @ApiOperation(value = "查看详情")
    public AbcServiceResponse<ExaminationSamplePipeView> getExaminationSamplePipeById(@PathVariable String id,
                                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {

        return new AbcServiceResponse<>(goodsService.getExaminationSamplePipeById(id, chainId, clinicId));
    }

    /**
     * 查询所有数据
     *
     * @return 数据列表
     */
    @GetMapping("/devices/apply")
    @LogReqAndRsp
    @ApiOperation(value = "获取检验设备联机申请列表")
    public AbcServiceResponse<AbcListPage<ExaminationDeviceConnectApplyView>> getExaminationDeviceConnectApplys(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {

        return new AbcServiceResponse<>(examinationDeviceConnectApplyService.getExaminationDeviceConnectApplys(chainId, clinicId));
    }

    /**
     * 新增数据
     *
     * @param examinationDeviceConnectApplyReq 请求参数
     * @return 新增结果
     */
    @PostMapping("/devices/apply")
    @LogReqAndRsp
    @ApiOperation(value = "保存检验设备联机申请")
    public AbcServiceResponse<ExaminationDeviceConnectApplyView> saveExaminationDeviceConnectApply(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                                   @Valid @RequestBody ExaminationDeviceConnectApplyView examinationDeviceConnectApplyReq) {

        return new AbcServiceResponse<>(examinationDeviceConnectApplyService.saveExaminationDeviceConnectApply(chainId, clinicId, employeeId, examinationDeviceConnectApplyReq));
    }

    /**
     * 修改数据
     *
     * @param id                               主键
     * @param examinationDeviceConnectApplyReq 请求参数
     * @return 修改结果
     */
    @PutMapping("/devices/apply/{id}")
    @LogReqAndRsp
    @ApiOperation(value = "修改检验设备联机申请")
    public AbcServiceResponse<ExaminationDeviceConnectApplyView> updateExaminationDeviceConnectApplyById(@PathVariable Long id,
                                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                                         @Valid @RequestBody ExaminationDeviceConnectApplyView examinationDeviceConnectApplyReq) {

        return new AbcServiceResponse<>(examinationDeviceConnectApplyService.updateExaminationDeviceConnectApplyById(id, chainId, clinicId, employeeId, examinationDeviceConnectApplyReq));
    }

    /**
     * 查看详情
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/devices/apply/{id}")
    @LogReqAndRsp
    @ApiOperation(value = "获取检验设备申请详情")
    public AbcServiceResponse<ExaminationDeviceConnectApplyView> getExaminationDeviceConnectApplyById(@PathVariable Long id,
                                                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {

        return new AbcServiceResponse<>(examinationDeviceConnectApplyService.getExaminationDeviceConnectApplyById(id, chainId, clinicId));
    }

    /**
     * 处理检查设备连接申请回调
     *
     * @param tapdWorkSheetEventView 工作表事件视图
     * @return {@link AbcServiceResponse}<{@link String}>
     */
    @PostMapping("/devices/apply/callback")
    @LogReqAndRsp
    public AbcServiceResponse<String> handleExaminationDeviceConnectApplyCallback(@RequestBody TapdWorkSheetEventView tapdWorkSheetEventView) {

        examinationDeviceConnectApplyService.handleExaminationDeviceConnectApplyCallback(tapdWorkSheetEventView);
        return new AbcServiceResponse<>("success");
    }

    @DeleteMapping("/devices/room/{deviceRoomId}")
    @LogReqAndRsp
    public AbcServiceResponse<BaseSuccessRsp> deleteExamDeviceRoomById(@PathVariable String deviceRoomId,
                                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId) {
        return new AbcServiceResponse<>(goodsService.deleteExamDeviceRoomById(deviceRoomId, chainId, clinicId, operatorId));
    }

    @PutMapping("/sample-pipe/{id}/disable")
    @LogReqAndRsp
    @ApiOperation(value = "采样组停用启用")
    public AbcServiceResponse<OpsCommonRsp> disableExaminationSample(@PathVariable String id,
                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer viewMode,
                                                                     @RequestBody ClientExamDisableReq clientReq) {

        clientReq.setChainId(chainId);
        clientReq.setClinicId(clinicId);
        clientReq.setEmployeeId(employeeId);
        clientReq.setClinicType(clinicType);
        clientReq.setViewMode(viewMode);
        clientReq.setId(id);
        clientReq.checkParam();
        return new AbcServiceResponse<>(goodsService.disableExaminationSample(clientReq));
    }

    @GetMapping("/sample-pipe/simple")
    @LogReqAndRsp
    @ApiOperation(value = "查询采样组列表-用于检验列表筛选")
    public AbcServiceResponse<List<ExaminationSampleSimpleView>> getExaminationSampleSample(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {

        return new AbcServiceResponse<>(goodsService.getExaminationSampleSample(chainId, clinicId));
    }
}
