package cn.abcyun.cis.goods.controller.api;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.QueryGoodsByIdsReq;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.model.b2b.GoodsPurchaseOrder;
import cn.abcyun.cis.goods.service.GoodsService;
import cn.abcyun.cis.goods.utils.GoodsPrivUtils;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.vo.frontend.GoodsListPage;
import cn.abcyun.cis.goods.vo.frontend.RecommendView;
import cn.abcyun.cis.goods.vo.frontend.SimplePurchaseOrderRsp;
import cn.abcyun.cis.goods.vo.frontend.order.GetGoodsPurchaseOrderReq;
import cn.abcyun.cis.goods.vo.frontend.purchase.*;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 采购单/要货单
 */
@RestController
@RequestMapping("/api/v3/goods/purchase/orders")
@Api(value = "采购 ", description = "药品入库,迁移/api/goods/stocks/in/orders接口", produces = "application/json")
public class GoodsPurchaseOrderController {
    @Autowired
    private GoodsService goodsService;

    /**
     * 采购单/要货单列表
     */
    @GetMapping("")
    @LogReqAndRsp
    @ApiOperation("采购单/要货单列表")
    public AbcServiceResponse<GetClaimOrPurchaseOrderRsp> getPurchaseOrderList(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
                                                                               GetClaimOrPurchaseOrderReq req) throws CisGoodsServiceException {
        req.setChainId(chainId);
        req.setHeaderClinicId(clinicId);
        req.setHeaderEmployeeId(employeeId);
        if (CollectionUtils.isEmpty(req.getStatusList())) {
            req.setNeedDraft(YesOrNo.YES);
        } else {
            req.setNeedDraft(YesOrNo.NO);
        }
        /**
         * 子店自能看自己的
         * */
        if (GoodsPrivUtils.isSingleMode(clinicType, viewMode) || GoodsPrivUtils.isSubClinic(clinicType, viewMode)) {
            req.setQueryClinicId(clinicId);
        }
        if (req.getCreateBeginDate() != null) {
            req.setCreateBeginTime(DateUtils.getStartTime(req.getCreateBeginDate()).toInstant());
        }
        if (req.getCreateEndDate() != null) {
            req.setCreateEndTime(DateUtils.getEndTime(req.getCreateEndDate()).toInstant());
        }
        req.setIsPharmacy(GoodsPrivUtils.isAbcPharmacy(hisType) ? YesOrNo.YES : YesOrNo.NO);

        GetClaimOrPurchaseOrderRsp rsp = goodsService.getClaimOrPurchaseOrderList(req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * TODO 导出采购单/要货单（待实现）
     */
    @GetMapping("/export")
    @ApiOperation("导出采购单/要货单")
    public void exportPurchaseOrderList(
            HttpServletResponse httpServletResponse,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
            GetClaimOrPurchaseOrderReq req) throws CisGoodsServiceException {
        req.setChainId(chainId);
        req.setHeaderClinicId(clinicId);
        req.setHeaderEmployeeId(employeeId);
        if (CollectionUtils.isEmpty(req.getStatusList())) {
            req.setNeedDraft(YesOrNo.YES);
        } else {
            req.setNeedDraft(YesOrNo.NO);
        }
        /*
         * 子店自能看自己的
         * */
        if (GoodsPrivUtils.isSingleMode(clinicType, viewMode) || GoodsPrivUtils.isSubClinic(clinicType, viewMode)) {
            req.setQueryClinicId(clinicId);
        }
        if (req.getCreateBeginDate() != null) {
            req.setCreateBeginTime(DateUtils.getStartTime(req.getCreateBeginDate()).toInstant());
        }
        if (req.getCreateEndDate() != null) {
            req.setCreateEndTime(DateUtils.getEndTime(req.getCreateEndDate()).toInstant());
        }
        req.setIsPharmacy(GoodsPrivUtils.isAbcPharmacy(hisType) ? YesOrNo.YES : YesOrNo.NO);
        req.setHttpServletResponse(httpServletResponse);

        goodsService.exportPurchaseClaimOrderList(req);
    }

    @GetMapping("/receive")
    @LogReqAndRsp
    @ApiOperation("查询支持收货的采购单")
    public AbcServiceResponse<GetClaimOrPurchaseOrderRsp> getPurchaseOrderListOfReceive(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
                                                                                        GetClaimOrPurchaseOrderReq req) throws CisGoodsServiceException {
        req.setChainId(chainId);
        req.setHeaderClinicId(clinicId);
        req.setHeaderEmployeeId(employeeId);
        req.setNeedDraft(YesOrNo.NO);

        req.setHeaderHisType(hisType);
        req.setHeaderClinicType(clinicType);
        req.setHeaderViewMode(viewMode);

        /*
         * 药店:
         *  连锁子店: 查询根据请求参数中的queryClinicId值来决定,当queryClinicId空时查询传入门店的支持入库的采购单列表
         *  单店 + 连锁总部: 查询所有门店的支持入库的采购单列表
         * 其他产品线:
         *  查当前门店的支持入库的采购单列表
         */
        if (!GoodsPrivUtils.isAbcPharmacy(hisType)) {
            req.setQueryClinicId(clinicId);
        }
        if (req.getCreateBeginDate() != null) {
            req.setCreateBeginTime(DateUtils.getStartTime(req.getCreateBeginDate()).toInstant());
        }
        if (req.getCreateEndDate() != null) {
            req.setCreateEndTime(DateUtils.getEndTime(req.getCreateEndDate()).toInstant());
        }
        req.setIsPharmacy(GoodsPrivUtils.isAbcPharmacy(hisType) ? YesOrNo.YES : YesOrNo.NO);
        req.setQueryRelatePurchaseOrder(YesOrNo.YES);

        GetClaimOrPurchaseOrderRsp rsp = goodsService.getPurchaseOrderListOfReceive(req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 采购单/要货单详情
     */
    @GetMapping("/{id}")
    @LogReqAndRsp
    @ApiOperation("采购单/要货单详情")
    public AbcServiceResponse<GoodsPurchaseOrderRsp> getPurchaseOrder(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                      @PathVariable("id") Long goodsPurchaseOrderId) {
        GoodsPurchaseOrderRsp rsp = goodsService.getPurchaseOrder(chainId, goodsPurchaseOrderId);
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/recommend")
    @LogReqAndRsp
    @ApiOperation(value = "推荐采购量", produces = "application/json")
    public AbcServiceResponse<GoodsListPage<RecommendView>> recommendPurchaseGoods(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @RequestParam(value = "clinicId", required = false) @ApiParam(value = "指定要查询的诊所ID,如果不指定，则用请求登录的诊所") String queryClinicId,
            @Valid @RequestBody QueryGoodsByIdsReq clientReq) throws CisGoodsServiceException {
        if (CollectionUtils.isEmpty(clientReq.getGoodsIds())) {
            return new AbcServiceResponse<>(new GoodsListPage<RecommendView>());
        }
        if (!StringUtils.isEmpty(queryClinicId)) {
            clientReq.setClinicId(queryClinicId);
        } else {
            clientReq.setClinicId(headerClinicId);
        }
        return new AbcServiceResponse<>(goodsService.recommendPurchaseGoods(clientReq, headerChainId));
    }

    /***
     * 创建采购单
     * */
    @PostMapping("")
    @LogReqAndRsp
    @ApiOperation(value = "创建采购单", produces = "application/json")
    public AbcServiceResponse<SimplePurchaseOrderRsp> createOfflinePurchaseOrder(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int headerHisType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @Valid @RequestBody CreatePurchaseOrderReq clientReq) throws CisGoodsServiceException {
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setHeaderViewMode(headerViewMode);
        if (clientReq.getOrderType() != GoodsPurchaseOrder.OrderType.PURCHASE_ORDER) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "请创建采购单");
        }
        return new AbcServiceResponse<>(goodsService.createPurchaseOrder(clientReq));
    }


    /**
     * 修改线下集采购单
     * 草稿，还没提交
     */
    @PutMapping("/{orderId}")
    @LogReqAndRsp
    @ApiOperation(value = "保存草稿过后的修改  修改线下集采购单/要货单", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> updatePurchaseOrder(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int headerHisType,
            @PathVariable("orderId") String orderId,
            @Valid @RequestBody CreatePurchaseOrderReq clientReq) throws CisGoodsServiceException {
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setOrderId(GoodsUtils.getLongId(orderId));
        return new AbcServiceResponse<>(goodsService.updatePurchaseOrder(clientReq));
    }

    @DeleteMapping("/{orderId}")
    @LogReqAndRsp
    @ApiOperation(value = "删除草稿单", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> deleteDraftPurchaseOrder(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int headerHisType,
            @PathVariable("orderId") String orderId
    ) throws CisGoodsServiceException {
        CreatePurchaseOrderReq clientReq = new CreatePurchaseOrderReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setOrderId(GoodsUtils.getLongId(orderId));
        return new AbcServiceResponse<>(goodsService.deleteDraftPurchaseOrder(clientReq));
    }

    /**
     * 确认线下集采 采购单
     * 诊所管家的审批流前端会调用这个
     * 药店管家 是走的gsp的审批回调
     */
    @PutMapping("/{orderId}/confirm")
    @LogReqAndRsp
    @ApiOperation(value = "审核通过", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> confirmPurchaseOrder(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @PathVariable("orderId") String orderId,
            @Valid @RequestBody ReviewStockPurchaseOrderReq clientReq) throws CisGoodsServiceException {
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setOrderId(GoodsUtils.getLongId(orderId));
        clientReq.setPass(GoodsUtils.SwitchFlag.OFF);
        clientReq.setCommandType(ReviewStockPurchaseOrderReq.COMMAND_TYPE_CONFIRM);
        clientReq.parameterCheck();
        return new AbcServiceResponse<>(goodsService.reviewAndModifyPurchaseOrder(clientReq));
    }


    /***
     * 老的诊所管家的审核
     * 药店产品把审核去掉了
     * */
    @PutMapping("/{orderId}/reject")
    @LogReqAndRsp
    @ApiOperation(value = "审核拒绝采购单", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> rejectPurchaseOrder(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @PathVariable("orderId") String orderId,
            @Valid @RequestBody ReviewStockPurchaseOrderReq clientReq) throws CisGoodsServiceException {
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setOrderId(GoodsUtils.getLongId(orderId));
        clientReq.setCommandType(ReviewStockPurchaseOrderReq.COMMAND_TYPE_REVIEW);
        clientReq.parameterCheck();
        return new AbcServiceResponse<>(goodsService.reviewOrderOnly(clientReq));
    }

    /**
     * 老的诊所管家的审核
     * 药店产品把审核去掉了
     */
    @PutMapping("/{orderId}/revoke")
    @LogReqAndRsp
    @ApiOperation(value = "撤回采购单", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> revokePurchaseOrder(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @PathVariable("orderId") String orderId
    ) throws CisGoodsServiceException {
        ReviewStockPurchaseOrderReq clientReq = new ReviewStockPurchaseOrderReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setOrderId(GoodsUtils.getLongId(orderId));
        clientReq.setPass(GoodsUtils.SwitchFlag.OFF);
        clientReq.setCommandType(ReviewStockPurchaseOrderReq.COMMAND_TYPE_REVOKE);
        clientReq.parameterCheck();
        return new AbcServiceResponse<>(goodsService.reviewOrderOnly(clientReq));
    }


    @PostMapping("/pre-create")
    @LogReqAndRsp
    @ApiOperation(value = "发送供应商之前试算（未保存）", produces = "application/json")
    public AbcServiceResponse<List<GoodsPurchaseOrderRsp>> preCreatePurchaseOrder(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int headerHisType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @RequestBody CreatePurchaseOrderReq clientReq
    ) throws CisGoodsServiceException {
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setHeaderViewMode(headerViewMode);
        return new AbcServiceResponse<>(goodsService.preCreatePurchaseOrder(clientReq));
    }

    @PostMapping("/send-to-supplier")
    @LogReqAndRsp
    @ApiOperation(value = "采购单发送至供应商", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> sendPurchaseOrderToSupplier(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int headerHisType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @RequestBody CreatePurchaseOrderReq clientReq
    ) throws CisGoodsServiceException {
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setHeaderViewMode(headerViewMode);
        if (clientReq.getOrderType() != GoodsPurchaseOrder.OrderType.PURCHASE_ORDER) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "请创建采购单");
        }
        return new AbcServiceResponse<>(goodsService.sendPurchaseOrderToSupplier(clientReq));
    }

    @PostMapping("/check-valid-to-supplier-goods")
    @LogReqAndRsp
    @ApiOperation(value = "checkValidToSupplierGoods", produces = "application/json")
    public AbcServiceResponse<List<CheckValidSupplierGoodRsp>> checkValidToSupplierGoods(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int headerHisType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @RequestBody CheckValidSupplierGoodsReq clientReq
    ) throws CisGoodsServiceException {
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setHeaderViewMode(headerViewMode);
        return new AbcServiceResponse<>(goodsService.checkValidToSupplierGoods(clientReq));
    }

    /**
     * 查询商品未结束的采购单或要货单
     */
    @GetMapping("/by-goods-id")
    public AbcServiceResponse<AbcListPage<ClaimOrPurchaseOrderGoodsListView>> listClaimOrPurchaseOrderGoods(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int headerHisType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @RequestParam("goodsId") String goodsId) {
        List<ClaimOrPurchaseOrderGoodsListView> claimOrPurchaseOrderGoodsListViews = goodsService.listClaimOrPurchaseOrderByGoods(headerChainId, headerClinicId, headerClinicType, headerHisType, headerViewMode, goodsId);
        AbcListPage<ClaimOrPurchaseOrderGoodsListView> listPage = new AbcListPage<>();
        listPage.setRows(claimOrPurchaseOrderGoodsListViews);
        return new AbcServiceResponse<>(listPage);
    }

    /**
     * 采购单/要货单详情
     */
    @GetMapping("/{id}/export")
    @LogReqAndRsp
    @ApiOperation("导出采购单")
    public void exportGoodsPurchaseOrder(
            HttpServletResponse httpServletResponse,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @PathVariable("id") Long orderId) {
        GetGoodsPurchaseOrderReq clientReq = new GetGoodsPurchaseOrderReq();
        clientReq.setHeaderChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setHeaderEmployeeId(employeeId);
        clientReq.setOrderId(orderId);
        clientReq.setHttpServletResponse(httpServletResponse);
        goodsService.exportGoodsPurchaseOrder(clientReq);
    }
}
