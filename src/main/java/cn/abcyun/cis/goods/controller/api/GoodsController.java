package cn.abcyun.cis.goods.controller.api;


import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsPharmacyOpenRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.GetImplNationalGoodsWarningCountRsp;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.core.annotation.ApiEncrypt;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.cis.goods.consts.Constant;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.service.GoodsService;
import cn.abcyun.cis.goods.service.GoodsV2Service;
import cn.abcyun.cis.goods.service.rpc.CisClinicService;
import cn.abcyun.cis.goods.service.update.GoodExtendShebaoService;
import cn.abcyun.cis.goods.utils.GoodsPrivUtils;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.vo.backend.RpcTodoCountRsp;
import cn.abcyun.cis.goods.vo.frontend.ClinicPharmacyDistributionRsp;
import cn.abcyun.cis.goods.vo.frontend.batches.QueryGoodsDistributionListReq;
import cn.abcyun.cis.goods.vo.frontend.batches.QueryGoodsDistributionListRsp;
import cn.abcyun.cis.goods.vo.frontend.export.GetGoodsAsyncExportInfoReq;
import cn.abcyun.cis.goods.vo.frontend.export.GetGoodsAsyncExportInfoRsp;
import cn.abcyun.cis.goods.vo.frontend.goodslist.*;
import cn.abcyun.cis.goods.vo.frontend.pharmacy.ToClientGoodsPharmacyView;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

/*sync-match-status*
 * 库存多人协同盘点
 * 命名约定：
 * Task 盘点任务，是对整个盘点过程的描述
 * Job Task里面具体药品的盘点工作
 */
@RestController
@RequestMapping("/api/v3/goods")
@Api(value = "商品列表和其他接口", description = "库存物资列表/药房", produces = "application/json")
public class GoodsController {
    @Autowired
    private GoodsService goodsService;
    @Autowired
    private CisClinicService cisClinicService;
    @Autowired
    private GoodsV2Service goodsV2Service;

    /**
     * Goods分页列表查询
     *
     * @return 结果
     * @throws CisGoodsServiceException 异常
     */
    @GetMapping("/goods-list/non-stock-goods")
    @LogReqAndRsp
    @ApiOperation(value = "非库存药品的列表拉取", produces = "application/json")
    @ApiEncrypt
    public AbcServiceResponse<GetNonStockGoodsListRsp> listNonStockPagesV2(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer headerHisType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_LOGIN_WAY,required = false) String loginWay,
            GetNonStockGoodsListReq clientReq
    ) throws CisGoodsServiceException {
        clientReq.setChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setClinicType(clinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setHeaderEmployeeId(employeeId);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setLoginWay(loginWay);
        /**
         * 非库存商品没有总部和全部门店 这个业务逻辑
         * */
        clientReq.initQueryClinicId(clientReq.getClinicId());
        GetNonStockGoodsListRsp examinationGoodsRsp = goodsService.pageListNonStockGoods(clientReq);

        return new AbcServiceResponse<>(examinationGoodsRsp);
    }

    @GetMapping("/goods-list/non-stock-goods/export")
    @ApiOperation(value = "非库存药品的列表拉取 导出", produces = "application/json")
    public void exporlistNonStockPages(
            HttpServletResponse httpServletResponse,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer headerHisType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_LOGIN_WAY,required = false) String loginWay,
            GetNonStockGoodsListReq clientReq
    ) throws CisGoodsServiceException {

        clientReq.setChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setClinicType(clinicType);
        clientReq.setHeaderEmployeeId(employeeId);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setLoginWay(loginWay);
        clientReq.setHttpServletResponse(httpServletResponse);
        /**
         * 非库存商品没有总部和全部门店的业务逻辑
         * */
        clientReq.initQueryClinicId(clientReq.getClinicId());
        goodsService.exportPageListNonStockGoods(clientReq);

        return;
    }

    @PostMapping("/goods-list/non-stock-goods/batch-set")
    @LogReqAndRsp
    @ApiOperation(value = "项目批量设置", produces = "application/json")
    public AbcServiceResponse<BatchModifyGoodsRsp> batchModifyProjects(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer headerHisType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_LOGIN_WAY, required = false) String loginWay,
            @RequestBody GetNonStockGoodsListReq clientReq
    ) throws CisGoodsServiceException {
        clientReq.setChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setClinicType(clinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setEmployeeId(employeeId);
        clientReq.setHeaderEmployeeId(employeeId);
        clientReq.setLoginWay(loginWay);
        clientReq.initQueryClinicId(clientReq.getClinicId());
        clientReq.initOtherCustomTypeId();
        clientReq.parameterCheck();
        return new AbcServiceResponse<>(goodsService.batchModifyProjects(clientReq));
    }

    @GetMapping("/goods-list/non-stock-goods/batch-set/progress")
    @LogReqAndRsp
    @ApiOperation(value = "项目批量设置进度查询", produces = "application/json")
    public AbcServiceResponse<BatchModifyGoodsRsp> batchModifyProjectsProgress(
            @RequestParam("taskId") String taskId
    ) throws CisGoodsServiceException {
        return new AbcServiceResponse<>(goodsService.batchModifyProjectsProgress(taskId));
    }
    @PostMapping("/goods-list/stock-goods/for-print-price")
    @ApiOperation(value = "打印价签Goods列表[有,请求参数太多，换成post接口]", produces = "application/json")
    @ApiEncrypt
    @LogReqAndRsp
    public AbcServiceResponse<GetStockGoodsListRsp> listStockPagesV2PostForPrintPrice(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer headerHisType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_LOGIN_WAY,required = false) String loginWay,

            @RequestBody  GetStockGoodsListReq clientReq

    ) throws CisGoodsServiceException {
        //Http头
        clientReq.setChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setClinicType(clinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setEmployeeId(employeeId);
        clientReq.setLoginWay(loginWay);
        clientReq.initQueryClinicId(clientReq.getClinicId());
        clientReq.initOtherCustomTypeId();
        clientReq.parameterCheck();


        clientReq.initPharmacyExtendOtherType();
        GetStockGoodsListRsp stockGoodsListRsp = goodsService.pageListStockGoods(clientReq);

        return new AbcServiceResponse<>(stockGoodsListRsp);
    }

    @PostMapping("/goods-list/stock-goods/for-modify-price")
    @ApiOperation(value = "调价的过滤Goods列表Goods列表[有,请求参数太多，换成post接口]", produces = "application/json")
    @ApiEncrypt
    @LogReqAndRsp
    public AbcServiceResponse<GetStockGoodsListRsp> listStockPagesV2Post(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer headerHisType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_LOGIN_WAY,required = false) String loginWay,

            @RequestBody  GetStockGoodsListReq clientReq

    ) throws CisGoodsServiceException {
        //Http头
        clientReq.setChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setClinicType(clinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setEmployeeId(employeeId);
        clientReq.setLoginWay(loginWay);
        clientReq.initQueryClinicId(clientReq.getClinicId());
        clientReq.initOtherCustomTypeId();
        clientReq.parameterCheck();

        /**
         * 药店定价 产品要求只出指定类型的列表
         * */
        clientReq.setQueryFromPrice(YesOrNo.YES);
        if(GoodsPrivUtils.isAbcPharmacy(headerHisType)){
            if(GoodsPrivUtils.isSingleMode(clinicType,headerViewMode)){
                clientReq.setQueryPriceGoodsClinicType(1);
            } else if(GoodsPrivUtils.isSubClinic(clinicType,headerViewMode)){
                ClinicConfig clinicConfig = cisClinicService.getClinicConfig(clientReq.getHeaderClinicId());
                if (clinicConfig.enableClinicSelfPrice(clientReq.getHeaderClinicId())) {
                    clientReq.setQueryPriceGoodsClinicType(2);
                } else {
                    clientReq.setQueryPriceGoodsClinicType(3);
                }
            } else if(GoodsPrivUtils.isHeadClinic(clinicType,headerViewMode)){
                if(StringUtils.isEmpty(clientReq.getQueryClinicId()) || GoodsUtils.compareStrEqual(chainId,clientReq.getQueryClinicId())) {
                    clientReq.setQueryPriceGoodsClinicType(1);
                } else{
                    ClinicConfig clinicConfig = cisClinicService.getClinicConfig(clientReq.getQueryClinicId());
                    if (clinicConfig.enableClinicSelfPrice(clinicConfig.getClinicId())) {
                        clientReq.setQueryPriceGoodsClinicType(2);
                    } else {
                        clientReq.setQueryPriceGoodsClinicType(3);
                    }
                }
            }
        }
        clientReq.initPharmacyExtendOtherType();
        GetStockGoodsListRsp stockGoodsListRsp = goodsService.pageListStockGoods(clientReq);

        return new AbcServiceResponse<>(stockGoodsListRsp);
    }
    @PostMapping("/goods-list/stock-goods/batch-modify-goods")
    @LogReqAndRsp
    @ApiOperation(value = "批量改药品", produces = "application/json")
    public AbcServiceResponse<BatchModifyGoodsRsp> batchModifyGoods(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer headerHisType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_LOGIN_WAY,required = false) String loginWay,
            @RequestBody  GetStockGoodsListReq clientReq

    ) throws CisGoodsServiceException {
        //Http头
        clientReq.setChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setClinicType(clinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setEmployeeId(employeeId);
        clientReq.setHeaderEmployeeId(employeeId);
        clientReq.setLoginWay(loginWay);
        clientReq.initQueryClinicId(clientReq.getClinicId());
        clientReq.initOtherCustomTypeId();
        clientReq.parameterCheck();
        boolean noCheck = clientReq.getModifyField() != null && GoodsUtils.checkFlagOn(clientReq.getModifyField().intValue(), Constant.BatchModifyFlag.NO_CODE);

        if(! noCheck && GoodsPrivUtils.isSubClinic(clinicType,headerViewMode)){
            if (!Objects.equals(headerHisType, Organ.HisType.CIS_HIS_TYPE_PHARMACY)) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "门店不能批量修改药品资料");
            }
        }
        /**
         * 药店定价 产品要求只出指定类型的列表
         * */
        return new AbcServiceResponse<>(goodsService.batchModifyGoods(clientReq));
    }

    @GetMapping("/goods-list/stock-goods/batch-modify-goods/progress")
    @LogReqAndRsp
    @ApiOperation(value = "批量改药品进度查询", produces = "application/json")
    public AbcServiceResponse<BatchModifyGoodsRsp> batchModifyGoodsProgress(
            @RequestParam(value = "taskId") String taskId
    ) throws CisGoodsServiceException {
        BatchModifyGoodsRsp rsp = goodsService.batchModifyGoodsProgress(taskId);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/goods-list/stock-goods")
    @LogReqAndRsp
    @ApiOperation(value = "库存药品的列表拉取[有/]", produces = "application/json")
    @ApiEncrypt
    public AbcServiceResponse<GetStockGoodsListRsp> listStockPagesV2(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer headerHisType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_LOGIN_WAY,required = false) String loginWay,
            GetStockGoodsListReq clientReq
    ) throws CisGoodsServiceException {
        //Http头
        clientReq.setChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setClinicType(clinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setEmployeeId(employeeId);
        clientReq.initQueryClinicId(clientReq.getClinicId());
        clientReq.initOtherCustomTypeId();
        clientReq.setLoginWay(loginWay);


        clientReq.parameterCheck();
        clientReq.initPharmacyExtendOtherType();
        GetStockGoodsListRsp stockGoodsListRsp = goodsService.pageListStockGoods(clientReq);

        return new AbcServiceResponse<>(stockGoodsListRsp);
    }

    @GetMapping("/goods-list/stock-goods/export")
    @ApiOperation(value = "库存药品的列表导出", produces = "application/json")
    public void exportListStockPagesV2(
            HttpServletResponse httpServletResponse,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer viewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_LOGIN_WAY,required = false) String loginWay,
            GetStockGoodsListReq clientReq
    )
            throws CisGoodsServiceException {
        //Http头
        clientReq.setChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setEmployeeId(employeeId);
        clientReq.setHeaderEmployeeId(employeeId);
        clientReq.setLoginWay(loginWay);
        clientReq.setClinicType(clinicType);
        clientReq.setHeaderViewMode(viewMode);
        clientReq.initQueryClinicId(clientReq.getClinicId());
        clientReq.initOtherCustomTypeId();
        clientReq.parameterCheck();
        clientReq.setHttpServletResponse(httpServletResponse);
        clientReq.initPharmacyExtendOtherType();
        goodsService.exportPageListStockGoods(clientReq,false);
    }


    /**
     * 1.门店库存列表
     * 2.门店库存列表导出
     * 3.门店库存列表导出进度查询
     * 4.门店库存列表导出进度查询
     * x
     * */
    @GetMapping("/pharmacy")
    @LogReqAndRsp
    @ApiOperation(value = "门店药房信息", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ToClientGoodsPharmacyView>> findPharmacy(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType) throws CisGoodsServiceException {
        return new AbcServiceResponse<>(goodsService.findPharmacy(chainId, clinicId));
    }

    @GetMapping("/pharmacy/find-clinics-by-pharmacy-type")
    @LogReqAndRsp
    @ApiOperation(value = "查药房类型对应的门店列表", produces = "application/json")
    public AbcServiceResponse<ClinicPharmacyDistributionRsp> findPharmacyDistribution(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestParam(value = "pharmacyType", required = false,defaultValue = "0")
            @ApiParam(value = "可以过滤只要某种药房类型的门店分布取值类Pharamcy里面的type值(0本地实体类型 1 空中药房 2 虚拟药房,10合作药房),不传为所有类型都返回") Integer pharmacyType) throws CisGoodsServiceException {
        return new AbcServiceResponse<>(goodsService.findPharmacyClinics(chainId, clinicId, clinicType, pharmacyType));
    }



    @GetMapping("/virtual-pharmacy/config")
    @LogReqAndRsp
    @ApiOperation(value = "检查是否开启了虚拟药房", produces = "application/json")
    AbcServiceResponse<GoodsPharmacyOpenRsp> checkPharmacyOpen(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        return new AbcServiceResponse<>(goodsService.checkPharmacyOpen(chainId, clinicId));
    }

    /**
     * 首页portal todocount
     */
    @GetMapping(value = "/todo/count")
    @LogReqAndRsp
    @ApiOperation(value = "库存模块拉取诊所相关的代办数量count", notes = "部分数量已经做了redis缓存不是每次都从db计算", produces = "application/json")
    public AbcServiceResponse<RpcTodoCountRsp> getGoodsTodoCount(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestParam(value = "clinicId", required = false) String queryClinicId,
            @RequestParam(value = "pharmacyNo", required = false) Integer pharmacyNo

    ) throws CisGoodsServiceException {
        RpcTodoCountRsp rsp = goodsService.getGoodsTodoCount(clinicId,queryClinicId, employeeId,pharmacyNo);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping(value = "/shebao/sync-match-status")
    @LogReqAndRsp
    @Deprecated
    @ApiOperation(value = "强制同步诊所对码状态", notes = "", produces = "application/json")
    public AbcServiceResponse<GoodExtendShebaoService.SyncSheBaoMatchStatusStat> syncSheBaoMatchStatus(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestParam(value = "clinicId", required = false) String queryClinicId

    ) throws CisGoodsServiceException {
        boolean needForceFlushGoodsStat = true; //通过url刷数据要把社保等级写入redis
        if (!StringUtils.isEmpty(queryClinicId)) {
            ClinicConfig clinicConfig = cisClinicService.getClinicConfig(queryClinicId);
            if (clinicConfig != null) {
                return new AbcServiceResponse<>(goodsService.syncSheBaoMatchStatus(clinicConfig.getChainId(), clinicConfig.getClinicId(), needForceFlushGoodsStat));
            }
        }
        return new AbcServiceResponse<>(goodsService.syncSheBaoMatchStatus(chainId, clinicId, needForceFlushGoodsStat));
    }
    @GetMapping(value = "/clean-chain-redis-cache")
    @LogReqAndRsp
    @ApiOperation(value = "清理连锁下的Redis", notes = "", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> cleanJenkinsChainRedisCache(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestParam(value = "chainId", required = false) String queryClinicId

    ) throws CisGoodsServiceException {
        if (!StringUtils.isEmpty(queryClinicId)) {
            return new AbcServiceResponse<>(goodsService.cleanJenkinsChainRedisCache(queryClinicId));
        }
        return new AbcServiceResponse<>(goodsService.cleanJenkinsChainRedisCache(chainId));
    }


    @GetMapping(value = "/shebao/national-code-status-summary")
    @LogReqAndRsp
    @ApiOperation(value = "Portal页贯标统计接口", notes = "", produces = "application/json")
    public AbcServiceResponse<GetImplNationalGoodsWarningCountRsp> getRpcImplNationalCountSummary(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId
    ) throws CisGoodsServiceException {
        return new AbcServiceResponse<>(goodsService.getRpcImplNationalCountSummary(chainId, clinicId));
    }
    @GetMapping(value = "/shebao/impl-national-status-summary")
    @LogReqAndRsp
    @ApiOperation(value = "贯标统计接口", notes = "", produces = "application/json")
    public AbcServiceResponse<GetImplNationCodeStatusRsp> getImplNationalStatusSummary(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE, required = false) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE, required = false) Integer viewMode,
            @RequestParam(value = "clinicId", required = false) @ApiParam(value = "拉取药品子店的库存价格信息等，不传就是主店的药品资料信息") String queryClinicId,
            @RequestParam(value = "stockGoods", required = false) @ApiParam(value = "库存商品 1 库存商品 0 非库存商品") Integer stockGoods,
            @RequestParam(value = "onlyStock", required = false) @ApiParam(value = "(对齐库存列表过滤非0库存参数)是否只过滤有库存的，只对库存商品有效，非库存无意义") Integer onlyStock,
            @RequestParam(value = "pharmacyNo", required = false) @ApiParam(value = "虚拟药房号，不传默认就是0 本地药房") Integer pharmacyNo

    ) throws CisGoodsServiceException {
        return new AbcServiceResponse<>(goodsService.getImplNationalStatusSummary(chainId, clinicId, clinicType, viewMode != null ? viewMode : 0, queryClinicId, stockGoods, onlyStock, pharmacyNo));
    }

    /**
     * 拉取模版门店里面指定类型的列表
     *
     * @param offset 起始下标
     * @param limit  分页条数
     * @return 结果
     * @throws CisGoodsServiceException 异常
     */
    @GetMapping("/system/goods")
    @LogReqAndRsp
    @ApiOperation(value = "拉取模版门店里面指定类型的列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<GoodsItem>> listInnerSystemGoodsList(
            @RequestParam("offset") @ApiParam(value = "分页参数 开始") Integer offset,
            @RequestParam("limit") @ApiParam(value = "分页参数 结束") Integer limit,
            @RequestParam("type") @ApiParam(value = "goods类型") Integer goodsType,
            @RequestParam(value = "subType", required = false, defaultValue = "1") @ApiParam(value = "goods子类型") Integer goodsSubType,
            @RequestParam(value = "extendSpec", required = false, defaultValue = "20") @ApiParam(value = "检查检验子类型 0 普通检查 10 Ris检查 20眼科检查") String extendSpec,
            @RequestParam(value = "supplementType", required = false) @ApiParam(value = "补充类型 检查检验-examinationMethod(1临床检验2生化检验3免疫检验4微生物检验,5pcr检验)") Integer supplementType,
            @RequestParam(value = "deviceModelId", required = false) @ApiParam(value = "Lis需求新加，设备型号Id,用于过滤出这个设备型号的内置goods列表") String[] deviceModelId,
            @RequestParam(value = "combineType", required = false) @ApiParam(value = "是否组合类型") Integer combineType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId
    ) throws CisGoodsServiceException {
        return new AbcServiceResponse<>(goodsService.listInnerSystemGoodsList(headerClinicId, goodsType, goodsSubType, extendSpec, supplementType, combineType, Objects.isNull(deviceModelId) ? null : Arrays.stream(deviceModelId).filter(org.apache.commons.lang3.StringUtils::isNotBlank).distinct().collect(Collectors.toList()), offset, limit));
    }
    @GetMapping(value = "/stock-goods/goods-stat/sync")
    @LogReqAndRsp
    @ApiOperation(value = "强制同步GoodsStat 按连锁同步", notes = "", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> syncGoodsStat(
            @RequestParam(value = "chainId", required = false) String chainId

    ) throws CisGoodsServiceException {
        return new AbcServiceResponse<>(goodsService.syncGoodsStat(chainId));
    }

    @GetMapping("/goods-list/archive/stock-goods")
    @LogReqAndRsp
    @ApiOperation(value = "档案列表拉取[有/]", produces = "application/json")
    @ApiEncrypt
    public AbcServiceResponse<GetStockGoodsListRsp> listArchiveStockPagesV2(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer headerHisType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_LOGIN_WAY,required = false) String loginWay,

            GetStockGoodsListReq clientReq
    )
            throws CisGoodsServiceException {

        //Http头
        clientReq.setChainId(chainId);
        //clientReq.setClinicId(clinicId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setClinicType(clinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setEmployeeId(employeeId);
        clientReq.initQueryClinicId(clientReq.getClinicId());
        clientReq.setLoginWay(loginWay);
        clientReq.parameterCheck();
        clientReq.initPharmacyExtendOtherType();
        GetStockGoodsListRsp stockGoodsListRsp = goodsService.pageListArchiveStockGoods(clientReq);

        return new AbcServiceResponse<>(stockGoodsListRsp);
    }

    @GetMapping("/goods-list/archive/stock-goods/export")
    @LogReqAndRsp
    @ApiOperation(value = "档案列表导出", produces = "application/json")
    public void exportArchiveStockPagesV2(
            HttpServletResponse httpServletResponse,
            @RequestParam(value = "offset", required = false) @ApiParam(value = "分页参数 开始") Integer offset,
            @RequestParam(value = "limit", required = false) @ApiParam(value = "分页参数 结束") Integer limit,
            @RequestParam(value = "goodsId", required = false) @ApiParam(value = "如果搜索指定药品为指定药品的goodsId") String goodsId,
            @RequestParam(value = "customTypeId", required = false) @ApiParam(value = "(用户多选的二级分类Id)二级分类的Id，如果要查多个，可以同时写多个customTypeId参数,类型里面优先级最高") String customTypeId,
            @RequestParam(value = "typeId", required = false) @ApiParam(value = "(用户多选的系统分类Id)系统分类Id，如果要查多个，可以同时写多个typeId参数,类型里面优先级最高(typeId 和customTypeId非空过后 将忽略 type subType)") String typeId,
            @RequestParam(value = "type", required = false) @ApiParam(value = "类型，如果要查多个，可以同时写多个type参数") String type,
            @RequestParam(value = "subType", required = false) @ApiParam(value = "类型，如果要查多个，可以同时写多个type参数") String subType,
            @RequestParam(value = "isSpu", required = false) @ApiParam(value = "是否是spu") Integer isSpu,
            @RequestParam(value = "clinicId", required = false) @ApiParam(value = "如果需要指定查询某个子门店的,在这里指定") String queryClinicId,
            @RequestParam(value = "keyword", required = false) @ApiParam(value = "搜索关键字") String keyword,

            @RequestParam(value = "onlyStock", required = false) @ApiParam(value = "返回库存大于0的药品物资") Integer onlyStock,

            @RequestParam(value = "sbOverPrice", required = false) @ApiParam(value = "[限价状态]为1返回已经超限价") Integer sbOverPrice,
            @RequestParam(value = "sbNotOverPrice", required = false) @ApiParam(value = "[限价状态]为1返回未超限价") Integer sbNotOverPrice,
            @RequestParam(value = "sbGoingOverPrice", required = false) @ApiParam(value = "[限价状态]为1返回即将超限价的") Integer sbGoingOverPrice,

            @RequestParam(value = "sbExpired", required = false) @ApiParam(value = "[失效状态]为1失效的") Integer sbExpired,
            @RequestParam(value = "sbNotExpired", required = false) @ApiParam(value = "[失效状态]为1未效的") Integer sbNotExpired,
            @RequestParam(value = "sbGoingExpired", required = false) @ApiParam(value = "[失效状态]为1即将失效") Integer sbGoingExpired,

            @RequestParam(value = "sbNationalMatched", required = false) @ApiParam(value = "[对码状态][国家]过滤对码") Integer sbNationalMatched,
            @RequestParam(value = "sbNationalNotMatched", required = false) @ApiParam(value = "[对码状态][国家]过滤未对码") Integer sbNationalNotMatched,
            @RequestParam(value = "sbNationalNotPermit", required = false) @ApiParam(value = "[对码状态][国家]过滤禁止销售的") Integer sbNationalNotPermit,
            @RequestParam(value = "medicalFeeGrade", required = false) @ApiParam(value = "[医保等级] 1 甲 2 乙 3 丙 100 无等级 ") String medicalFeeGrade,

            @RequestParam(value = "shebaoPayMode", required = false) @ApiParam(value = "null 不过滤  Goods的社保支付类型  0 正常支付（对码 10 社保支付 11 现金支付） 1 自费支付 2 不容许医保支付") String shebaoPayMode,
            @RequestParam(value = "orderBy", required = false) @ApiParam(value = "排序列") String orderBy,
            @RequestParam(value = "orderType", required = false) @ApiParam(value = "排序列") String orderType,
            @RequestParam(value = "pharmacyType", required = false,defaultValue = "0") @ApiParam(value = "药房类型 0 本地 1 空中 2 虚拟 如果不传为默认0") Integer pharmacyType,
            @RequestParam(value = "pharmacyNo", required = false) @ApiParam(value = "药房序号，不传为pharmacyType的汇总") Integer pharmacyNo,
            @RequestParam(value = "isDisplayMatchCode", required = false)
            @ApiParam(value = "前端是否要展示医保对码这一列，如果前端不展示，后台将不计算社保对码状态社保汇总信息等，又节约一大把时间(为兼容后台默认为1)")
            Integer isDisplayMatchCode,
            @RequestParam(value = "disable", required = false) @ApiParam(value = "是否停用 null 条件不生效不生效 0 费停用 1 停用") Integer disable,
            @RequestParam(value = "spuGoodsId", required = false) @ApiParam(value = "如果是spu，需要传入spu的goodsId") String spuGoodsId,
            @RequestParam(value = "spuGoodsCondition", required = false) @ApiParam(value = "spu筛选条件") String spuGoodsCondition,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer headerHisType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_LOGIN_WAY,required = false) String loginWay,
            GetStockGoodsListReq clientReq
    )
            throws CisGoodsServiceException {

        //Http头
        clientReq.setChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setClinicType(clinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setEmployeeId(employeeId);
        clientReq.setHeaderEmployeeId(employeeId);
        clientReq.setLoginWay(loginWay);
        clientReq.initQueryClinicId(clientReq.getClinicId());
        clientReq.parameterCheck();
        clientReq.setHttpServletResponse(httpServletResponse);
        clientReq.initPharmacyExtendOtherType();
        goodsService.exportPageListArchiveStockGoods(clientReq);

    }

    @PostMapping("/goods-list/stock-goods/sub-detail/export")
    @ApiOperation(value = "库存药品的列表导出-总部导出门店明细", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<GetGoodsAsyncExportInfoRsp> exportListPagesStockSubDetail(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer viewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_LOGIN_WAY,required = false) String loginWay,
            @RequestBody GetStockGoodsListReq clientReq
    )
            throws CisGoodsServiceException {

        clientReq.setChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        //clientReq.setClinicId(clinicId);
        clientReq.setEmployeeId(employeeId);
        clientReq.setHeaderEmployeeId(employeeId);
        clientReq.setLoginWay(loginWay);
        clientReq.setClinicType(clinicType);
        clientReq.setHeaderViewMode(viewMode);
        clientReq.setQueryClinicId(clientReq.getClinicId());
//
//        //药品类型
//        clientReq.setOriginalCustomTypeId(clientReq.getOriginalCustomTypeId());
//        clientReq.setOriginalTypeId(clientReq.getOriginalTypeId());
//
//        clientReq.setOriginalType(clientReq.getOriginalType());
//        clientReq.setOriginalSubType(clientReq.getOriginalSubType());
//
//        //药房号
//        clientReq.setPharmacyType(GoodsUtils.getDefaultPharmacyTypeIfNoSpecific(clientReq.getPharmacyType()));
//
//        if (clientReq.getIsDisplayMatchCode() != null) {
//            clientReq.setIsDisplayMatchCode(clientReq.getIsDisplayMatchCode());
//        }
//
//        clientReq.setOriginalShebaoPayMode(clientReq.getOriginalShebaoPayMode());
//
//        clientReq.setMedicalFeeGrade(GoodsUtils.parseType(clientReq.getOriginalMedicalFeeGrade()));

        return new AbcServiceResponse<>(goodsService.exportPageListStockGoodsSubDetail(clientReq));
    }

    @GetMapping("/async/export/info")
    @LogReqAndRsp
    @ApiOperation(value = "异步导出进度", produces = "application/json")
    public AbcServiceResponse<GetGoodsAsyncExportInfoRsp> getGoodsAsyncExportInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer viewMode,
                                                                                  @RequestParam(value = "id") String id
    ) {
        GetGoodsAsyncExportInfoReq clientReq = new GetGoodsAsyncExportInfoReq();
        clientReq.setChainId(chainId);
        clientReq.setClinicId(clinicId);
        clientReq.setEmployeeId(employeeId);
        clientReq.setClinicType(clinicType);
        clientReq.setViewMode(viewMode);
        clientReq.setId(GoodsUtils.getLongId(id));
        return new AbcServiceResponse<>(goodsService.getGoodsAsyncExportInfo(clientReq));
    }

    @GetMapping("/async/export/download")
    @LogReqAndRsp
    @ApiOperation(value = "异步导出下载", produces = "application/json")
    public void downloadGoodsAsyncExport(HttpServletResponse httpServletResponse,
                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) Integer viewMode,
                                         @RequestParam(value = "id") String id) {
        GetGoodsAsyncExportInfoReq clientReq = new GetGoodsAsyncExportInfoReq();
        clientReq.setChainId(chainId);
        clientReq.setClinicId(clinicId);
        clientReq.setEmployeeId(employeeId);
        clientReq.setClinicType(clinicType);
        clientReq.setViewMode(viewMode);
        clientReq.setId(GoodsUtils.getLongId(id));
        clientReq.setHttpServletResponse(httpServletResponse);

        goodsService.downloadGoodsAsyncExport(clientReq);
    }

    @GetMapping("/goods-list/chain-stock-goods")
    @LogReqAndRsp
    @ApiOperation(value = "总部档案列表", produces = "application/json")
    public AbcServiceResponse<GetGoodsStockArchiveListRsp> getChainStockGoodsList(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            GetGoodsStockArchiveListReq clientReq
    ) {
        clientReq.setHeaderChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.initOtherCustomTypeId();
        clientReq.init();
        return new AbcServiceResponse<>(goodsV2Service.getChainStockGoodsList(clientReq));
    }

    @PostMapping(value = "/goods-list/stock-warn-distribution")
    @ApiOperation(value = "查询goods预警门店分布")
    @LogReqAndRsp
    public AbcServiceResponse<QueryGoodsDistributionListRsp> queryGoodsDistributionList(
            @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestBody QueryGoodsDistributionListReq clientReq
    ) {
        clientReq.setHeaderChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.checkParam();
        return new AbcServiceResponse<>(goodsV2Service.queryGoodsDistributionList(clientReq));
    }

    @GetMapping(value = "/goods-list/purchase-plan-goods")
    @LogReqAndRsp
    @ApiOperation(value = "采购计划goods列表")
    public AbcServiceResponse<GetGoodsPurchasePlanListRsp> getPurchasePlanGoodsList(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
            GetGoodsPurchasePlanListReq clientReq
    ) {
        clientReq.setHeaderChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setHeaderHisType(hisType);
        clientReq.initOtherCustomTypeId();
        clientReq.init();
        return new AbcServiceResponse<>(goodsV2Service.getPurchasePlanGoodsList(clientReq));
    }

    @PostMapping(value = "/goods-list/goods-sale")
    @LogReqAndRsp
    @ApiOperation(value = "销售总量")
    public AbcServiceResponse<GetGoodsSaleCountRsp> getGoodsSaleCount(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestBody GetGoodsSaleCountReq clientReq

    ) {
        clientReq.setHeaderChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.checkParam();
        return new AbcServiceResponse<>(goodsV2Service.getGoodsSaleCount(clientReq));
    }

}
