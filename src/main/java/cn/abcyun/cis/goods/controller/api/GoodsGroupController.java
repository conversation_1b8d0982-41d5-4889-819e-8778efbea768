package cn.abcyun.cis.goods.controller.api;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.service.GoodsV2Service;
import cn.abcyun.cis.goods.vo.frontend.goodsgroup.ClientGoodsGroupReq;
import cn.abcyun.cis.goods.vo.frontend.goodsgroup.GoodsGroupListView;
import cn.abcyun.cis.goods.vo.frontend.goodsgroup.GoodsGroupView;
import cn.abcyun.cis.goods.vo.frontend.goodslist.GetNonStockGoodsListReq;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;

/**
 * 商品组
 *
 * <AUTHOR>
 * @since 2025/4/24 16:11
 **/
@RestController
@RequestMapping("/api/v3/goods/goods-group")
@Api(value = "商品组管理", produces = "application/json")
public class GoodsGroupController {

    @Autowired
    private GoodsV2Service goodsV2Service;

    /**
     * 查询商品组列表
     */
    @GetMapping("")
    @LogReqAndRsp
    @ApiOperation(value = "查询商品组列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<GoodsGroupListView>> queryGoodsGroup(@RequestParam(value = "goodsId", required = false) String goodsId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
                                                                               @RequestParam(value = "offset", required = false, defaultValue = "0") int offset,
                                                                               @RequestParam(value = "limit", required = false, defaultValue = "5") int limit) {
        GetNonStockGoodsListReq getNonStockGoodsListReq = new GetNonStockGoodsListReq();
        getNonStockGoodsListReq.setChainId(chainId);
        getNonStockGoodsListReq.setClinicId(clinicId);
        getNonStockGoodsListReq.setHeaderClinicId(clinicId);
        getNonStockGoodsListReq.setHeaderEmployeeId(employeeId);
        getNonStockGoodsListReq.setHeaderViewMode(viewMode);
        getNonStockGoodsListReq.setClinicType(clinicType);
        getNonStockGoodsListReq.setHeaderHisType(hisType);
        getNonStockGoodsListReq.setComposeGoodsId(goodsId);
        getNonStockGoodsListReq.setTypeId(Collections.singletonList(GoodsConst.GoodsTypeId.GOODS_GROUP_TYPEID));
        getNonStockGoodsListReq.setOffset(offset);
        getNonStockGoodsListReq.setLimit(limit);
        AbcListPage<GoodsGroupListView> listPage = goodsV2Service.queryGoodsGroup(getNonStockGoodsListReq);
        return new AbcServiceResponse<>(listPage);
    }

    /**
     * 查询商品组详情
     */
    @GetMapping("/{goodsGroupId}")
    @LogReqAndRsp
    @ApiOperation(value = "查询商品组详情", produces = "application/json")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "商品组ID", name = "goodsGroupId", required = true, paramType = "path", dataType = "string", dataTypeClass = String.class),
            @ApiImplicitParam(value = "是返回组内价格最低商品", name = "withMinPriceGoods", required = false, paramType = "query", dataType = "int", dataTypeClass = int.class, defaultValue = "0")
    })
    public AbcServiceResponse<GoodsGroupView> queryGoodsGroupDetail(@PathVariable(value = "goodsGroupId") String goodsGroupId,
                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                    @RequestParam(value = "withMinPriceGoods", required = false, defaultValue = "0") int withMinPriceGoods) {
        GoodsGroupView goodsGroupView = goodsV2Service.queryGoodsGroupDetail(chainId, clinicId, employeeId, goodsGroupId, withMinPriceGoods);
        return new AbcServiceResponse<>(goodsGroupView);
    }

    /**
     * 创建商品组
     */
    @PostMapping("")
    @LogReqAndRsp
    @ApiOperation(value = "创建商品组", produces = "application/json")
    public AbcServiceResponse<GoodsGroupView> createGoodsGroup(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                               @Valid @RequestBody @ApiParam(value = "创建商品组请求body") ClientGoodsGroupReq req
    ) throws CisGoodsServiceException {
        return new AbcServiceResponse<>(goodsV2Service.createGoodsGroup(chainId, clinicId, employeeId, req));
    }

    /**
     * 修改商品组
     */
    @PutMapping("/{goodsGroupId}")
    @ApiOperation(value = "修改商品组", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<GoodsGroupView> updateGoodsGroup(@PathVariable("goodsGroupId") String goodsGroupId,
                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                               @Valid @RequestBody @ApiParam(value = "修改商品组请求body") ClientGoodsGroupReq req
    ) throws CisGoodsServiceException {
        GoodsGroupView rsp = goodsV2Service.updateGoodsGroup(goodsGroupId, chainId, clinicId, employeeId, req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 删除商品组
     */
    @DeleteMapping("/{goodsGroupId}")
    @LogReqAndRsp
    @ApiOperation(value = "删除商品组", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> deleteGoodsGroup(@PathVariable("goodsGroupId") String goodsGroupId,
                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) throws CisGoodsServiceException {
        goodsV2Service.deleteGoodsGroup(goodsGroupId, chainId, clinicId, employeeId, viewMode, clinicType);
        return new AbcServiceResponse<>(new OpsCommonRsp(OpsCommonRsp.SUCC, "删除成功"));
    }
}
