package cn.abcyun.cis.goods.controller.api;


import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.service.GoodsService;
import cn.abcyun.cis.goods.service.rpc.CisClinicService;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.utils.ServiceUtils;
import cn.abcyun.cis.goods.vo.frontend.batches.*;
import cn.abcyun.cis.goods.vo.frontend.goodsinfo.GetGoodsDistributionReq;
import cn.abcyun.cis.goods.vo.frontend.goodslist.GetGoodsDistributionRsp;
import cn.abcyun.cis.goods.vo.frontend.goodslist.GoodsGoodsMaxCostPriceRsp;
import cn.abcyun.cis.goods.vo.frontend.goodslist.GoodsInfoPurchaseInfoRsp;
import cn.abcyun.cis.goods.vo.frontend.goodslist.GoodsStockDetailRsp;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import reactor.util.function.Tuple3;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Collections;
import java.util.Objects;
import java.util.Set;

/**
 * 获取Goods 的库存 相关的controller
 * API 路径绝大部分都是从 老的goods服务迁移过来所以并不一定都有同样的打头路径
 */
@RestController
@RequestMapping("/api/v3/goods")
@Api(value = "库存批次信息相关", description = "库存批次信息相关", produces = "application/json")
public class GoodsBatchController {
    @Autowired
    private GoodsService goodsService;
    @Autowired
    private CisClinicService cisClinicService;

    /**
     * 获取 库存信息批次信息
     * 批次信息-用于盘点单盘点的时候拉取批次信息
     */
    @PostMapping("/stocks/batches")
    @LogReqAndRsp
    @ApiOperation(value = "批次信息-用于盘点单盘点的时候拉取批次信息", notes = "从nodejs服务的接口迁移过来/api/v2/goods/stocks/batches", produces = "application/json")
    public AbcServiceResponse<GetGoodsBatchesForStockCheckRsp> getStockBatchesForStockCheck(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @Valid @RequestBody GetGoodsBatchesForStockCheckReq req) throws CisGoodsServiceException {
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setEmployeeId(employeeId);
        return new AbcServiceResponse<>(goodsService.getStockBatchesForStockCheck(req));
    }


    /**
     * 获取 拉取有效期预警的商品
     * 使用场景有预警的批次智能出库
     */
    @GetMapping("/stocks/expired-warn-goods")
    @LogReqAndRsp
    @ApiOperation(value = "拉取有效期预警的商品", notes = "暂时不支持分页，因为这里有goods和批次两个维度，分页分到哪个维度上？同时端上有全选按钮也需要全拉回去", produces = "application/json")
    public AbcServiceResponse<GetExpiredWarnGoodsStockBatchesForStockOutRsp> getExpiredWarnGoodsForStockOut(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            GetExpiredWarnGoodsBatchesForStockOutReq clientReq
    ) throws CisGoodsServiceException {
        clientReq.setHeaderChainId(chainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setClinicType(clinicType);
        clientReq.setEmployeeId(employeeId);
        if (StringUtils.isEmpty(clientReq.getClinicId())) {
            clientReq.setClinicId(headerClinicId);
        }
        return new AbcServiceResponse<>(goodsService.getExpiredWarnGoodsForStockOut(clientReq));
    }

    /**
     * 获取 库存信息批次信息
     * 查一批药品的在指定药房里面的批次信息
     */
    @PostMapping("/stocks/batches/in-pharmacy")
    @LogReqAndRsp
    @ApiOperation(value = "查一批药品的在指定药房里面的批次信息", notes = "暂时不支持分页，因为这里有goods和批次两个维度，分页分到哪个维度上？同时端上有全选按钮也需要全拉回去", produces = "application/json")
    public AbcServiceResponse<GetExpiredWarnGoodsStockBatchesForStockOutRsp> getGoodsStockBatches(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @Valid @RequestBody GetGoodsListStockBatches clientReq
    ) throws CisGoodsServiceException {
        clientReq.setChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setClinicType(clinicType);
        clientReq.setEmployeeId(employeeId);
        return new AbcServiceResponse<>(goodsService.getGoodsStockBatches(clientReq));
    }


    /**
     * 获取 查询单个Goods的批次信息
     */
    @GetMapping("/stocks/batches")
    @LogReqAndRsp
    @ApiOperation(value = "通过参数 拉取批次信息 批次信息-用于药品资料处拉取库存批次信息", notes = "从nodejs服务的接口迁移过来/api/v2/goods/stocks/batches", produces = "application/json")
    public AbcServiceResponse<GetGoodsBatchesForGoodsInfoRsp> getStockBatchesInGoodsInfo(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
            @RequestParam("offset") @ApiParam(value = "分页参数") Integer offset,
            @RequestParam("limit") @ApiParam(value = "分页参数") Integer limit,
            @RequestParam(value = "clinicId", required = false) @ApiParam(value = "查询诊所的ID如果不传为http头里面诊所ID") String queryClinicId,
            @RequestParam(value = "goodsId", required = false) @ApiParam(value = "查询药品GoodsId") String goodsId,
            @RequestParam(value = "batchId", required = false) @ApiParam(value = "批次id") String batchId,
            @RequestParam(value = "spuGoodsId", required = false) @ApiParam(value = "指定要查的goodsId") String spuGoodsId,
            @RequestParam(value = "recentStockWithNonBatchNo", required = false) @ApiParam(value = "只返回近一个batchNo为非空的批次") Integer recentStockWithNonBatchNo,
            @RequestParam(value = "pharmacyType", required = false, defaultValue = "0") @ApiParam(value = "药房类型，没传为本地药房类型 0") Integer pharmacyType,
            @RequestParam(value = "pharmacyNo", required = false) @ApiParam(value = "药房序号,没传默认为药房类型的汇总") Integer pharmacyNo,
            @RequestParam(value = "batchNo", required = false) @ApiParam(value = "生产批号过滤 精确匹配") String batchNo,
            @RequestParam(value = "expiredWarnFirst", required = false) @ApiParam(value = "库存告警的排前面") Integer expiredWarnFirst,
            @RequestParam(value = "supplierId", required = false) @ApiParam(value = "供应商过滤") String supplierId,
            @RequestParam(value = "batchViewMode", required = false) @ApiParam(value = "批次视图展示方式 null,0:按goods汇总批次视图的方式 --产品设计的第一个版本，产品下掉，但是后台代码还保留;10 goods按门店维度的批次视图的方式;20 goods 按门店 药房维度的批次方式") Integer batchViewMode,
            @RequestParam(value = "hasStockFirst", required = false) @ApiParam(value = "有库存的排前面") Integer hasStockFirst,
            @RequestParam(value = "expiredWarn", required = false) @ApiParam(value = "库存告警 对应 expiredWarnCount") Integer expiredWarn,
            @RequestParam(value = "costPriceWarn", required = false) @ApiParam(value = "利润率告警 对应 costPriceWarnCount") Integer costPriceWarn,
            @RequestParam(value = "spuGoodsCondition", required = false) @ApiParam(value = "spu筛选条件") String spuGoodsCondition,
            @RequestParam(value = "scene", required = false) @ApiParam(value = "查询批次场景 " +
                    "1:药品养护、清斗、装斗选择批次时, " +
                    "2:清理库存," +
                    "3 药店查询总部供应商场景(可能查总部批次也可能查erp总部批次)") Integer queryScene,
            @RequestParam(value = "onlyStockInStock", required = false) @ApiParam(value = "只查入库批次") Integer onlyStockInStock,
            @RequestParam(value = "orderBy", required = false) @ApiParam(value = "排序字段") String orderBy,
            @RequestParam(value = "orderType", required = false) @ApiParam(value = "排序方式，asc生效 desc降序") String orderType
    ) throws CisGoodsServiceException {
        GetGoodsBatchesForGoodsInfoReq clientReq = new GetGoodsBatchesForGoodsInfoReq();
        clientReq.setChainId(chainId);
        clientReq.setClinicId(clinicId);
        clientReq.setViewMode(viewMode);
        clientReq.setOffset(offset);
        clientReq.setLimit(limit);
        if (StringUtils.hasText(queryClinicId)) {
            clientReq.setQueryClinicId(queryClinicId);
        } else if (!ClinicConfig.isHeadClinic(clinicType, viewMode)) {
            // 如果不是连锁总部，那么默认查询当前诊所，不知道这个接口连锁总部有没有在用，所以还是只能判断一下
            if (queryScene == null || queryScene != 2) {
                // 药店子店删除档案清理库存，需查出所有门店库存，不指定clinicId
                clientReq.setQueryClinicId(clinicId);
            }
        }
        clientReq.setGoodsId(goodsId);
        clientReq.setPharmacyType(pharmacyType);
        clientReq.setPharmacyNo(pharmacyNo);
        clientReq.setClinicType(clinicType);
        clientReq.setBatchNo(batchNo);
        clientReq.setBatchViewMode(batchViewMode);
        clientReq.setHasStockFirst(hasStockFirst);
        clientReq.setExpiredWarnFirst(expiredWarnFirst);
        clientReq.setSupplierId(supplierId);
        if (!StringUtils.isEmpty(spuGoodsId)) {
            clientReq.setSpuGoodsId(GoodsUtils.getLongId(spuGoodsId));
        }
        if (!StringUtils.isEmpty(batchId)) {
            clientReq.setBatchId(GoodsUtils.getLongId(batchId));
        }
        clientReq.setExpiredWarn(expiredWarn);
        clientReq.setCostPriceWarn(costPriceWarn);
        clientReq.setSpuGoodsCondition(spuGoodsCondition);
        clientReq.setRecentStockWithNonBatchNo(recentStockWithNonBatchNo);
        clientReq.setOrderBy(orderBy);
        clientReq.setOrderType(orderType);
        clientReq.setScene(queryScene);
        clientReq.setOnlyStockInStock(onlyStockInStock);
        if (!GoodsUtils.isAbcPharmacy(hisType) && Objects.equals(queryScene, 1)) {
            // 如果不是药店，并且只查有库存的批次，那么同时还需要返回近三个月有库存数量变动的批次
            clientReq.setLastSellBeginDate(DateUtils.toInstant(DateUtils.beginOfDay(LocalDate.now().plusMonths(-3))));
        }
        Tuple3<GetGoodsBatchesForGoodsInfoRsp, ClinicConfig, Set<Long>> returnTuple = goodsService.getStockBatchesInGoodsInfo(clientReq);
        if (!CollectionUtils.isEmpty(returnTuple.getT3())) {
            goodsService.reflushGoodsStock(returnTuple.getT2(), returnTuple.getT3());
        }
        return new AbcServiceResponse<>(returnTuple.getT1());
    }

    @GetMapping("/{goodsId}/stocks/batches/{batchId}")
    @LogReqAndRsp
    @ApiOperation(value = "查询某一个批次的详细信息", notes = "一个批次可以指定属于哪个药房等", produces = "application/json")
    public AbcServiceResponse<GetGoodsBatchesForGoodsInfoRsp> getSingleStockBatchesInfo(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
            @PathVariable("goodsId") @ApiParam(value = "要禁用的药品GoodsId") String goodsId,
            @PathVariable("batchId") @ApiParam(value = "要禁用的药品GoodsId") Long batchId,
            @RequestParam(value = "clinicId", required = false) @ApiParam(value = "查询诊所的ID如果不传为http头里面诊所ID") String queryClinicId,
            @RequestParam(value = "pharmacyType", required = false, defaultValue = "0") @ApiParam(value = "药房类型 0 本地 1 代煎代配 不传默认为0") Integer pharmacyType,
            @RequestParam(value = "pharmacyNo", required = false) @ApiParam(value = "药房序号,没传默认为pharmacyType的汇总") Integer pharmacyNo
    ) throws CisGoodsServiceException {
        GetGoodsBatchInfoReq clientReq = new GetGoodsBatchInfoReq();
        clientReq.setChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setViewMode(viewMode);
        clientReq.setOffset(null);
        clientReq.setLimit(null);
        clientReq.setClinicId(GoodsUtils.fixTheRightQueryClinicId(clinicId, queryClinicId, clinicType, viewMode));
        clientReq.setGoodsId(goodsId);
        clientReq.setBatchId(batchId);
        clientReq.setPharmacyNo(pharmacyType);
        clientReq.setPharmacyNo(pharmacyNo);
        clientReq.setClinicType(clinicType);
        return new AbcServiceResponse<>(goodsService.getSingleStockBatchesInfo(clientReq));
    }

    /**
     * 拉取某一个goods的批次信息
     * 这里只是拉取，会有各种排序
     */
    @GetMapping("/{goodsId}/stocks/batches")
    @LogReqAndRsp
    @ApiOperation(value = "[查询goods的批次信息]批次信息-用于药品资料处拉取库存批次信息", notes = "从nodejs服务的接口迁移过来/api/v2/goods/stocks/batches", produces = "application/json")
    public AbcServiceResponse<GetGoodsBatchesForGoodsInfoRsp> getSingleStockBatchesInfo(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
            @RequestParam(value = "spuGoodsId", required = false) @ApiParam(value = "指定要查SpuGOodsId的批次信息，传后台强制为1") String spuGoodsId,
            @PathVariable("goodsId") @ApiParam(value = "要禁用的药品GoodsId") String goodsId,
            @RequestParam(value = "all", required = false) @ApiParam(value = "是否全拉 ，把0库存的也拉出来") Integer all,
            @RequestParam(value = "recentStockWithNonBatchNo", required = false) @ApiParam(value = "只返回近一个batchNo为非空的批次") Integer recentStockWithNonBatchNo,
            @RequestParam(value = "expiredWarnFirst", required = false) @ApiParam(value = "库存告警的排前面") Integer expiredWarnFirst,
            @RequestParam(value = "supplierId", required = false) @ApiParam(value = "供应商过滤") String supplierId,
            @RequestParam(value = "batchViewMode", required = false) @ApiParam(value = "批次视图展示方式 null,0:按goods汇总批次视图的方式 --产品设计的第一个版本，产品下掉，但是后台代码还保留;10 goods按门店维度的批次视图的方式;20 goods 按门店 药房维度的批次方式") Integer batchViewMode,
            @RequestParam(value = "hasStockFirst", required = false) @ApiParam(value = "有库存的排前面") Integer hasStockFirst,
            @RequestParam(value = "pharmacyType", required = false, defaultValue = "0") @ApiParam(value = "药房类型 0 本地 1 代煎代配 不传默认为0") Integer pharmacyType,
            @RequestParam(value = "clinicId", required = false) @ApiParam(value = "【多库房调整】查询诊所的ID,不传为真个连锁的批次") String queryClinicId,
            @RequestParam(value = "pharmacyNo", required = false) @ApiParam(value = "[多库房调整]药房序号,没传默认为pharmacyType的汇总") Integer pharmacyNo,
            @RequestParam(value = "batchId", required = false) @ApiParam(value = "批次id") String batchId

    ) throws CisGoodsServiceException {
        GetGoodsBatchesForGoodsInfoReq clientReq = new GetGoodsBatchesForGoodsInfoReq();
        clientReq.setChainId(chainId);
        clientReq.setClinicId(clinicId);
        clientReq.setViewMode(viewMode);
        clientReq.setOffset(null);
        clientReq.setLimit(null);
        clientReq.setQueryClinicId(queryClinicId);
        if (StringUtils.isEmpty(queryClinicId)) {
            clientReq.setQueryClinicId(clinicId);
        }
        clientReq.setGoodsId(goodsId);
        clientReq.setPharmacyNo(pharmacyType);
        clientReq.setPharmacyNo(pharmacyNo);
        clientReq.setClinicType(clinicType);
        clientReq.setBatchViewMode(batchViewMode);
        clientReq.setSupplierId(supplierId);
        clientReq.setHasStockFirst(hasStockFirst);
        clientReq.setExpiredWarnFirst(expiredWarnFirst);
        clientReq.setRecentStockWithNonBatchNo(recentStockWithNonBatchNo);
        if (all == null || all == 0) {
            clientReq.setSingleGoodsAllStock(10);//只返回有库存的批次
        } else {
            clientReq.setSingleGoodsAllStock(20);//返回0批次，并把0库存的放到最后 0 批次的只返回最近三月的
        }
        if (!StringUtils.isEmpty(batchId)) {
            clientReq.setBatchId(GoodsUtils.getLongId(batchId));
            clientReq.setSingleGoodsAllStock(0);
        }
        Tuple3<GetGoodsBatchesForGoodsInfoRsp, ClinicConfig, Set<Long>> returnTuple = goodsService.getStockBatchesInGoodsInfo(clientReq);
        if (!CollectionUtils.isEmpty(returnTuple.getT3())) {
            goodsService.reflushGoodsStock(returnTuple.getT2(), returnTuple.getT3());
        }
        return new AbcServiceResponse<>(returnTuple.getT1());
    }

    /**
     * 拉取某一个goods的批次信息，把有库存返回出去
     * 这个接口会模拟发药扣库，可以输入批量goods，每个Goods带一个库存量是要准备扣的库存量
     * 返回：
     * 1.一个goods的所有批次有库存的，包括无可用库存批次
     * 2.按扣库顺讯，把库存扣完的数量也返回出去
     * 3.如果扣库的数量大于库存总量，返回错误
     * 4.用户请求什么单位，就按什么单位返回
     */
    @PostMapping("/stocks/batches/pretend-cut")
    @LogReqAndRsp
    @ApiOperation(value = "[查询goods的批次信息]批次信息-用于药品资料处拉取库存批次信息", notes = "从nodejs服务的接口迁移过来/api/v2/goods/stocks/batches", produces = "application/json")
    public AbcServiceResponse<GoodsBatchesPretendCutRsp> pretendCutGoodsStockBatches(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
            @RequestBody GoodsBatchesPretendCutReq clientReq

    ) throws CisGoodsServiceException {
        clientReq.setChainId(chainId);
        clientReq.setClinicId(clinicId);
        clientReq.setViewMode(viewMode);
        return new AbcServiceResponse<>(goodsService.pretendCutGoodsStockBatches(clientReq));
    }

    @GetMapping("/{goodsId}/stocks/io")
    @LogReqAndRsp
    @ApiOperation(value = "批次信息-用于药品资料处拉取库存批次信息", notes = "从nodejs服务的接口迁移过来/api/v2/goods/stocks/batches", produces = "application/json")
    public AbcServiceResponse<AbcListPage<BatchInfo>> getSingleGoodsStockAction(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
            @PathVariable("goodsId") @ApiParam(value = "要禁用的药品GoodsId") String goodsId,
            @RequestParam(value = "actionType", required = false) @ApiParam(value = "进销存类型 0 入库 10 出库 20 调拨 30 盘点 40 领用") Integer actionType,
            @RequestParam(value = "orderId", required = false) @ApiParam(value = "进销存单据Id") String orderId,
            @RequestParam(value = "orderItemId", required = false) @ApiParam(value = "进销存单据详情Id") String orderDetailId,
            @RequestParam(value = "fromOrganId", required = false) @ApiParam(value = "单据来源clinicId") String fromOrganId

    ) throws CisGoodsServiceException {
        if (!StringUtils.isEmpty(fromOrganId)) {
            clinicId = fromOrganId;
        }
        return new AbcServiceResponse<>(goodsService.getSingleGoodsStockAction(chainId, clinicId, goodsId, actionType, orderId, orderDetailId));
    }

    /**
     * 导出 查询单个Goods的批次信息
     */
    @GetMapping("/stocks/batches/export")
    @ApiOperation(value = "批次信息-用于药品资料处拉取库存批次信息-导出", notes = "接口迁移")
    public void exportStockBatchesInGoodsInfo(
            HttpServletResponse httpServletResponse,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
            @RequestParam(value = "offset", required = false) @ApiParam(value = "分页参数") Integer offset,
            @RequestParam(value = "limit", required = false) @ApiParam(value = "分页参数") Integer limit,
            @RequestParam(value = "clinicId", required = false) @ApiParam(value = "查询诊所的ID如果不传为http头里面诊所ID") String queryClinicId,
            @RequestParam(value = "goodsId", required = false) @ApiParam(value = "查询药品GoodsId") String goodsId,
            @RequestParam(value = "spuGoodsId", required = false) @ApiParam(value = "指定要查的goodsId") String spuGoodsId,
            @RequestParam(value = "pharmacyType", required = false, defaultValue = "0") @ApiParam(value = "药房类型 0 本地 1 代煎代配 不传默认为0") Integer pharmacyType,
            @RequestParam(value = "pharmacyNo", required = false) @ApiParam(value = "药房序号,没传默认为pharmacyType的汇总") Integer pharmacyNo,
            @RequestParam(value = "batchNo", required = false) @ApiParam(value = "生产批号过滤") String batchNo,
            @RequestParam(value = "batchId", required = false) @ApiParam(value = "要禁用的药品GoodsId") String batchId,
            @RequestParam(value = "expiredWarnFirst", required = false) @ApiParam(value = "库存告警的排前面") Integer expiredWarnFirst,
            @RequestParam(value = "supplierId", required = false) @ApiParam(value = "供应商过滤") String supplierId,
            @RequestParam(value = "batchViewMode", required = false) @ApiParam(value = "批次视图展示方式 null,0:按goods汇总批次视图的方式 --产品设计的第一个版本，产品下掉，但是后台代码还保留;10 goods按门店维度的批次视图的方式;20 goods 按门店 药房维度的批次方式") Integer batchViewMode,
            @RequestParam(value = "hasStockFirst", required = false) @ApiParam(value = "有库存的排前面") Integer hasStockFirst,
            @RequestParam(value = "spuGoodsCondition", required = false) @ApiParam(value = "spu筛选条件") String spuGoodsCondition
    ) throws CisGoodsServiceException, IOException {
        GetGoodsBatchesForGoodsInfoReq clientReq = new GetGoodsBatchesForGoodsInfoReq();
        clientReq.setChainId(chainId);
        clientReq.setClinicId(clinicId);
        clientReq.setOffset(offset);
        clientReq.setLimit(limit);
        clientReq.setQueryClinicId(queryClinicId);
        clientReq.setGoodsId(goodsId);
        clientReq.setPharmacyNo(pharmacyNo);
        clientReq.setPharmacyType(pharmacyType);
        clientReq.setBatchNo(batchNo);
        clientReq.setClinicType(clinicType);
        clientReq.setViewMode(viewMode);
        clientReq.setExpiredWarnFirst(expiredWarnFirst);
        clientReq.setHttpServletResponse(httpServletResponse);
        clientReq.setHasStockFirst(hasStockFirst);
        clientReq.setBatchViewMode(batchViewMode);
        clientReq.setSupplierId(supplierId);
        if (!StringUtils.isEmpty(spuGoodsId)) {
            clientReq.setSpuGoodsId(GoodsUtils.getLongId(spuGoodsId));
        }
        if (!StringUtils.isEmpty(batchId)) {
            clientReq.setBatchId(GoodsUtils.getLongId(batchId));
        }

        clientReq.setSpuGoodsCondition(spuGoodsCondition);
        goodsService.exportStockBatchesInGoodsInfo(clientReq);
    }

    /**
     * 拉取药品在门店的分布信息
     *
     * @param orderBy   按什么字段来排序[asc,desc]
     * @param orderType 排序类型[packagePrice,lastPackageCostPrice,profitRat,currentCount,recentAvgSell,turnoverDays]
     * @param disable   过滤disable的
     * @param limit     分页-分页没有实现
     * @param offset
     */
    @GetMapping("/{goodsId}/distribution")
    @LogReqAndRsp
    @ApiOperation(value = "查询某个药品的门店分布", produces = "application/json")
    public AbcServiceResponse<GetGoodsDistributionRsp> getGoodsDistributionList(
            @PathVariable("goodsId") @ApiParam(value = "指定要查的goodsId") String goodsId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE, required = false) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestParam(value = "clinicId", required = false) @ApiParam(value = "指定要查的诊所ID,不指定为全连锁子店") String queryClinicId,
            @RequestParam(value = "queryType", required = false) @ApiParam(value = "查询类型 1 所有有库存的药房") Integer queryType,
            @RequestParam(value = "orderBy", required = false) @ApiParam(value = "排序类型,取值:asc,desc") String orderBy,
            @RequestParam(value = "orderType", required = false) @ApiParam(value = "排序字段,排序类型[packagePrice,lastPackageCostPrice,profitRat,currentCount,recentAvgSell,lastMonthSellCount,turnoverDays]") String orderType,
            @RequestParam(value = "disable", required = false) @ApiParam(value = "是否查禁用(包括禁用升级)的药品,0 启用 1禁用 null 都查") Integer disable,
            @RequestParam(value = "pharmacyType", required = false, defaultValue = "0") @ApiParam(value = "药房号 没指定默认为本地0号药房") Integer pharmacyType,
            @RequestParam(value = "pharmacyNo", required = false) @ApiParam(value = "药房号 没指定默认为本地0号药房") Integer pharmacyNo,
            @RequestParam(value = "stockWarn", required = false) @ApiParam(value = "库存告警 对应 stockWarnCount") Integer stockWarn,
            @RequestParam(value = "profitWarn", required = false) @ApiParam(value = "利润率告警 对应 profitRatWarnCount") Integer profitWarn,
            @RequestParam(value = "unsalableWarn", required = false) @ApiParam(value = "滞销告警 对应 unsalableWarnCount") Integer unsalableWarn,
            @RequestParam(value = "offset", required = false) @ApiParam(value = "分页参数 可选，不指定为全查") Integer offset,
            @RequestParam(value = "limit", required = false) @ApiParam(value = "分页参数 可选，不指定为全查") Integer limit,
            @RequestParam(value = "throwExceptionIfDel", required = false) @ApiParam(value = "是否抛出异常") Integer throwExceptionIfDel

    ) throws CisGoodsServiceException {
        String specificClinicId = !StringUtils.isEmpty(queryClinicId) ? queryClinicId : GoodsUtils.WHOLE_CHAIN_ID;
        GetGoodsDistributionReq req = new GetGoodsDistributionReq();
        req.setGoodsId(goodsId);
        req.setChainId(chainId);
        req.setClinicId(specificClinicId);
        req.setOrderBy(orderBy);
        req.setOrderType(orderType);
        req.setQueryType(queryType);
        req.setDisable(disable);
        req.setStockWarn(stockWarn);
        req.setProfitWarn(profitWarn);
        req.setUnsalableWarn(unsalableWarn);
        req.setPharmacyType(pharmacyType);
        req.setPharmacyNo(pharmacyNo);
        req.setOffset(offset);
        req.setLimit(limit);
        req.setThrowExceptionIfDel(throwExceptionIfDel);

        return new AbcServiceResponse<GetGoodsDistributionRsp>( goodsService.getGoodsDistributionList(req));
    }


    /**
     * 查询某个药品在某个子店的购买告警信息
     */
    @GetMapping("/{goodsId}/purchase-warn-info")
    @LogReqAndRsp
    public AbcServiceResponse<GoodsInfoPurchaseInfoRsp> getGoodsPurchaseInfo(
            @PathVariable("goodsId") String goodsId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestParam(value = "clinicId", required = true) String queryClinicId,
            @RequestParam(value = "pharmacyNo", required = false) @ApiParam(value = "-1表示汇总,不传这里表示0号药房（老逻辑）") Integer pharmacyNo
    ) throws CisGoodsServiceException {
        return new AbcServiceResponse<>(goodsService.getGoodsPurchaseInfo(goodsId, chainId, queryClinicId, GoodsUtils.getDefaultLocalPharmacyNoIfNoSpecific(pharmacyNo)));
    }


    /**
     * 查询某个药品库存详细信息
     * 库存列表hover上去展示的库存量
     */
    @GetMapping("/{goodsId}/stocks/detail")
    @LogReqAndRsp
    @ApiOperation(value = "查询某个药品详细锁库库存信息 切成了Redis获取", produces = "application/json")
    public AbcServiceResponse<GoodsStockDetailRsp> getGoodsStockDetail(
            @PathVariable("goodsId") String goodsId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
            @RequestParam(value = "clinicId", required = false) @ApiParam(value = "指定要查的诊所ID,不指定为登录门店") String queryClinicId,
            @RequestParam(value = "pharmacyType", required = false, defaultValue = "0") @ApiParam(value = "不指定为本地药房") Integer pharmacyType,
            @RequestParam(value = "pharmacyNo", required = false) @ApiParam(value = "不指定pharmacyType的汇总") Integer pharmacyNo
    ) throws CisGoodsServiceException {
        return new AbcServiceResponse<>(goodsService.getGoodsStockDetail(goodsId,
                chainId,
                clinicId,
                queryClinicId,
                pharmacyType,
                pharmacyNo));
    }

    /**
     * 用户手动启用禁用批次
     */
    @PutMapping("/{goodsId}/stocks/enable")
    @LogReqAndRsp
    @ApiOperation(value = "用户手动启用禁用批次", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> enableGoodsStockList(
            @PathVariable("goodsId") String goodsId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @Valid @RequestBody EnableGoodsStocksReq req
    ) throws CisGoodsServiceException {
        req.setHeaderChainId(headerChainId);
        req.setHeaderClinicId(headerClinicId);
        req.setHeaderEmployeeId(headerEmployeeId);
        req.setHeaderClinicType(headerClinicType);
        req.setHeaderViewMode(headerViewMode);
        req.setGoodsId(goodsId);
        return new AbcServiceResponse<>(goodsService.enableGoodsStockList(req));
    }

    /**
     * 查看某个Goods在门店的最大成本价
     */
    @GetMapping("/{goodsId}/stocks/maxcost")
    @LogReqAndRsp
    @ApiOperation(value = "查看某个Goods在门店的最大成本价", produces = "application/json")
    public AbcServiceResponse<GoodsGoodsMaxCostPriceRsp> getGoodsMaxCostPrice(
            @PathVariable("goodsId") String goodsId,
            @RequestParam(value = "pharmacyNo", required = false) @ApiParam(value = "药房号的最大成本价，如果不传为本地药房") Integer pharmacyNo,
            @RequestParam(value = "clinicId", required = false) @ApiParam(value = "指定要查的诊所ID,不指定为登录门店") String queryClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode

    ) throws CisGoodsServiceException {
        if (!StringUtils.isEmpty(queryClinicId)) {
            ClinicConfig clinicConfig = ServiceUtils.getClinicConfig(cisClinicService, queryClinicId);
            if (clinicConfig != null && !GoodsUtils.compareStrEqual(clinicConfig.getChainId(), chainId)) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_FORMAT_ERROR);
            }
        } else {
            queryClinicId = clinicId;
        }
        return new AbcServiceResponse<>(goodsService.getGoodsMaxCostPrice(goodsId, chainId, queryClinicId, GoodsUtils.getDefaultLocalPharmacyNoIfNoSpecific(pharmacyNo)));
    }

    @GetMapping("/{goodsId}/pharmacy/stocks/batches")
    @LogReqAndRsp
    @ApiOperation(value = "批次信息", produces = "application/json")
    public AbcServiceResponse<PharmacyGoodsBatchesForGoodsInfoRsp> getPharmacyGoodsStockBatches(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @PathVariable(value = "goodsId") @ApiParam(value = "查询药品GoodsId") String goodsId,
            @RequestParam(value = "onlyStock", required = false) @ApiParam(value = "只查有库存的") Integer onlyStock,
            @RequestParam(value = "orderType", required = false) @ApiParam(value = "排序字段") String orderType,
            @RequestParam(value = "orderBy", required = false) @ApiParam(value = "排序方式:asc,desc") String orderBy,
            @RequestParam(value = "offset", required = false) @ApiParam(value = "分页参数") Integer offset,
            @RequestParam(value = "limit", required = false) @ApiParam(value = "分页参数") Integer limit
    ) {
        PharmacyGoodsStockBatchesReq clientReq = new PharmacyGoodsStockBatchesReq();
        clientReq.setChainId(chainId);
        clientReq.setClinicId(clinicId);
        clientReq.setGoodsId(goodsId);
        clientReq.setGoodsIdList(Collections.singletonList(goodsId));
        clientReq.setOrderType(orderType);
        clientReq.setOrderBy(orderBy);
        clientReq.setOffset(offset);
        clientReq.setLimit(limit);
        clientReq.setOnlyStock(onlyStock);
        return new AbcServiceResponse<>(goodsService.getPharmacyGoodsStockBatches(clientReq));
    }

    @PostMapping("/pharmacy/stocks/batches")
    @LogReqAndRsp
    @ApiOperation(value = "(药房药品养护记录拉批次)通过goodsId批量获取批次信息", produces = "application/json")
    public AbcServiceResponse<AbcListPage<PharmacyGoodsBatchesForGoodsInfoRsp>> batchPharmacyGoodsStockBatches(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestBody PharmacyGoodsStockBatchesReq clientReq
    ) {
        clientReq.setChainId(chainId);
        clientReq.setClinicId(clinicId);
        clientReq.setOnlyStock(YesOrNo.YES);
        clientReq.setNotFilterTime(YesOrNo.YES);
        clientReq.checkParam();
        return new AbcServiceResponse<>(goodsService.batchPharmacyGoodsStockBatches(clientReq));
    }


    @ApiOperation(value = "拉取锁库列表", produces = "application/json")
    @GetMapping("/stocks/lockings")
    public AbcServiceResponse<GoodsLockingListRsp> getGoodsLockingList(
            @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
            QueryGoodsLockingListReq clientReq
    ) {
        clientReq.setHeaderChainId(chainId);
        clientReq.setHeaderClinicId(clinicId);
        clientReq.setHeaderEmployeeId(employeeId);
        clientReq.setHeaderClinicType(clinicType);
        clientReq.setHeaderViewMode(viewMode);
        return new AbcServiceResponse<>(goodsService.getGoodsLockingList(clientReq));
    }
}
