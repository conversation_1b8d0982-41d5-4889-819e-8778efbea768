package cn.abcyun.cis.goods.controller.api;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.service.GoodsService;
import cn.abcyun.cis.goods.service.GoodsV2Service;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.vo.frontend.purchase.*;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;

/**
 * 采购计划
 * 1。可以把多个采购单加入到一个采购计划
 * 2。一个采购计划 完成后就不能被再加入采购计划
 */
@RestController
@RequestMapping("/api/v3/goods/purchase/plans")
@Api(value = "采购计划", description = "", produces = "application/json")
public class GoodsPurchasePlanController {
    @Autowired
    private GoodsService goodsService;
    @Autowired
    private GoodsV2Service goodsV2Service;


    @GetMapping("")
    @LogReqAndRsp
    @ApiOperation(value = "拉取所有的采购计划列表", produces = "application/json")
    public AbcServiceResponse<GetPurchasePlanListRsp> getPurchasePlanList(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @RequestParam(value = "begDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam(value = "日期过滤三元组过滤的开始日期:YYYY-MM-DD(后台会按YYYY-MM-DD 00:00:00开始)") Date begDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam(value = "日期过滤三元组过滤的结束日期:YYYY-MM-DD(后台会按YYYY-MM-DD 23:59:59结束)") Date endDate,
            @RequestParam(value = "isComplete", required = false) @ApiParam(value = "isComplete") Integer isComplete,
            @RequestParam(value = "completeBeginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam(value = "完成开始日期:YYYY-MM-DD(后台会按YYYY-MM-DD 00:00:00开始)") Date completeBeginDate,
            @RequestParam(value = "completeEndDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam(value = "完成结束日期:YYYY-MM-DD(后台会按YYYY-MM-DD 23:59:59结束)") Date completeEndDate,
            @RequestParam(value = "operatorId", required = false) String operatorId,
            @RequestParam(value = "offset", required = false) @ApiParam(value = "分页参数 开始") Integer offset,
            @RequestParam(value = "limit", required = false) @ApiParam(value = "分页参数 结束") Integer limit
    ) throws CisGoodsServiceException {
        GetPurchasePlanListReq clientReq = new GetPurchasePlanListReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setBegDate(begDate);
        clientReq.setEndDate(endDate);
        clientReq.setIsComplete(isComplete);
        clientReq.setOffset(offset);
        clientReq.setLimit(limit);
        clientReq.setOperatorId(operatorId);
        clientReq.setCompleteBeginDate(completeBeginDate);
        clientReq.setCompleteEndDate(completeEndDate);
        clientReq.parameterCheck();
        return new AbcServiceResponse<>(goodsService.getPurchasePlanList(clientReq));
    }

    @GetMapping("/simple")
    @LogReqAndRsp
    @ApiOperation(value = "出只有id和name的未完成的采购计划，拉采购列表用于把采购单加入采购计划", produces = "application/json")
    public AbcServiceResponse<GetPurchasePlanListRsp> getPurchasePlanListSimple(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @RequestParam(value = "begDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam(value = "日期过滤三元组过滤的开始日期:YYYY-MM-DD(后台会按YYYY-MM-DD 00:00:00开始)") Date begDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd")
            @ApiParam(value = "日期过滤三元组过滤的结束日期:YYYY-MM-DD(后台会按YYYY-MM-DD 23:59:59结束)") Date endDate,
            @RequestParam(value = "isComplete", required = false) @ApiParam(value = "isComplete") Integer isComplete,
            @RequestParam(value = "offset", required = false) @ApiParam(value = "分页参数 开始") Integer offset,
            @RequestParam(value = "limit", required = false) @ApiParam(value = "分页参数 结束") Integer limit
    ) throws CisGoodsServiceException {
        GetPurchasePlanListReq clientReq = new GetPurchasePlanListReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setBegDate(begDate);
        clientReq.setEndDate(endDate);
        clientReq.setIsComplete(isComplete);
        clientReq.setOffset(offset);
        clientReq.setLimit(limit);
        clientReq.parameterCheck();
        return new AbcServiceResponse<>(goodsService.getPurchasePlanListSimple(clientReq));
    }

    @GetMapping("/name")
    @LogReqAndRsp
    @ApiOperation(value = "获取要新建采购计划的名字", produces = "application/json")
    public AbcServiceResponse<CreatePurchasePlanReq> getCreatePurchasePlanName(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode
    ) throws CisGoodsServiceException {
        CreatePurchasePlanReq clientReq = new CreatePurchasePlanReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        return new AbcServiceResponse<>(goodsService.getCreatePurchasePlanName(clientReq));
    }


    /**
     *创建采购计划
     * */
    @PostMapping("")
    @LogReqAndRsp
    @ApiOperation(value = "创建采购计划", produces = "application/json")
    public AbcServiceResponse<GoodsPurchasePlanView> createPurchasePlan(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int headerHisType,
            @Valid @RequestBody CreatePurchasePlanReq clientReq
    ) throws CisGoodsServiceException {
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.parameterCheck();
        return new AbcServiceResponse<>(goodsService.createPurchasePlan(clientReq));
    }

    /**
     *重命名采购计划
     * */
    @PutMapping("/{planId}/rename")
    @LogReqAndRsp
    @ApiOperation(value = "重命名采购计划", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> renamePurchasePlan(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @PathVariable("planId") String planId,
            @Valid @RequestBody CreatePurchasePlanReq clientReq
    ) throws CisGoodsServiceException {
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setId(planId);
        clientReq.parameterCheck();
        return new AbcServiceResponse<>(goodsService.renamePurchasePlan(clientReq));
    }

    /**
     *完成采购计划，采购计划完成后不能再加入采购单
     * */
    @PutMapping("/{planId}/complete")
    @LogReqAndRsp
    @ApiOperation(value = "完成采购计划，采购计划完成后不能再加入采购单", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> completePurchasePlan(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @PathVariable("planId") String planId
    ) throws CisGoodsServiceException {
        CreatePurchasePlanReq clientReq = new CreatePurchasePlanReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setId(planId);
        return new AbcServiceResponse<>(goodsService.completePurchasePlan(clientReq));
    }

    /**
     *把采购单加入到采购计划
     * */
    @PutMapping("/{planId}/add")
    @LogReqAndRsp
    @ApiOperation(value = "把采购单加入到采购计划", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> addPurchaseOrderToPurchasePlan(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @PathVariable("planId") String planId,
            @Valid @RequestBody AddPurchaseOrderToPlanReq clientReq
    ) throws CisGoodsServiceException {
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setPurchasePlanId(planId);
        clientReq.parameterCheck();
        return new AbcServiceResponse<>(goodsService.addPurchaseOrderToPurchasePlan(clientReq));
    }


    @GetMapping("/{planId}")
    @LogReqAndRsp
    @ApiOperation(value = "获取某个采购计划详情", produces = "application/json")
    public AbcServiceResponse<GoodsPurchasePlanView> getPurchasePlan(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int headerHisType,
            @PathVariable("planId") String planId
    ) throws CisGoodsServiceException {
        GetPurchasePlanReq clientReq = new GetPurchasePlanReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setId(planId);
        clientReq.setWithDetail((int) GoodsUtils.SwitchFlag.ON);
        return new AbcServiceResponse<>(goodsService.getPurchasePlan(clientReq));
    }


    /**
     * 采购计划 导出汇总清单，按goods纬度进行汇总的导出
     * */
    @GetMapping("/{planId}/export")
    @ApiOperation(value = "采购计划 导出汇总清单", produces = "application/json")
    public void  exportPurchasePlan(
            HttpServletResponse httpServletResponse,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int headerHisType,
            @PathVariable("planId") String planId
    ) throws CisGoodsServiceException {
        GetPurchasePlanReq clientReq = new GetPurchasePlanReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setHttpServletResponse(httpServletResponse);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setId(planId);
        goodsService.exportPurchasePlan(clientReq);
    }

    /**
     * 采购计划导出，导出成压缩包，里面包含了加入各个采购单的导出excel（可以入库导入）
     */
    @GetMapping("/{planId}/export/zip")
    @ApiOperation(value = "采购计划导出，导出成压缩包，里面包含了加入各个采购单的导出excel", produces = "application/json")
    public void exportPurchasePlanZip(
            HttpServletResponse httpServletResponse,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @PathVariable("planId") String planId
    ) throws CisGoodsServiceException {
        GetPurchasePlanReq clientReq = new GetPurchasePlanReq();
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setHttpServletResponse(httpServletResponse);
        clientReq.setId(planId);
        goodsService.exportPurchasePlanZip(clientReq);
    }

    @DeleteMapping("/{planId}")
    @LogReqAndRsp
    @ApiOperation(value = "删除采购计划")
    public AbcServiceResponse<OpsCommonRsp> deletePurchasePlan(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @PathVariable(value = "planId") String planId
    ) {
        return new AbcServiceResponse<>(goodsV2Service.deletePurchasePlan(headerChainId, headerClinicId, headerEmployeeId, planId));
    }

    @PutMapping("/{planId}")
    @LogReqAndRsp
    @ApiOperation(value = "修改采购计划")
    public AbcServiceResponse<GoodsPurchasePlanView> updateGoodsPurchasePlan(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String headerChainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String headerEmployeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int headerClinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int headerViewMode,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int headerHisType,
            @PathVariable("planId") String planId,
            @RequestBody CreatePurchasePlanReq clientReq
    ) {
        clientReq.setHeaderChainId(headerChainId);
        clientReq.setHeaderClinicId(headerClinicId);
        clientReq.setHeaderEmployeeId(headerEmployeeId);
        clientReq.setHeaderClinicType(headerClinicType);
        clientReq.setHeaderViewMode(headerViewMode);
        clientReq.setHeaderHisType(headerHisType);
        clientReq.setId(planId);
        return new AbcServiceResponse<>(goodsV2Service.updateGoodsPurchasePlan(clientReq));
    }
}
