package cn.abcyun.cis.goods.utils;

import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.Instant;

public class UserFillUtils {
    public static final String DEFUALT_ID ="00000000000000000000000000000000";
    public static void fillCreatedBy(Object obj, String operatorId) {
        if (obj == null) {
            return;
        }
        BeanUtils.copyProperties(new FillCreatedByModel(operatorId, Instant.now()), obj);
    }



    public static void fillLastModifiedBy(Object obj, String operatorId) {
        if (obj == null) {
            return;
        }
        BeanUtils.copyProperties(new FillLastModifiedByModel(operatorId, Instant.now()), obj);
    }



    public static void fillLastModifiedUserId(Object obj, String operatorId) {
        if (obj == null) {
            return;
        }
        BeanUtils.copyProperties(new FillLastModifiedUserIdModel(operatorId, Instant.now()), obj);
    }

    public static void fillCreatedUserId(Object obj, String operatorId) {
        if(obj  == null)
            return;
        BeanUtils.copyProperties(new FillCreatedUserId(operatorId, Instant.now()), obj);
    }

    @Data
    public static class FillLastModifiedUserIdModel {
        private String lastModifiedUserId;
        private Instant lastModifiedDate;


        FillLastModifiedUserIdModel(String lastModifiedUserId, Instant lastModifiedDate) {
            this.lastModifiedUserId = lastModifiedUserId;
            this.lastModifiedDate = lastModifiedDate;
        }
    }
    @Data
    public static class FillLastModifiedByModel {
        private String lastModifiedBy;
        private Instant lastModified;



        FillLastModifiedByModel(String lastModifiedBy, Instant lastModified) {
            this.lastModifiedBy = lastModifiedBy;
            this.lastModified = lastModified;
        }

    }
    @Data
    public static class FillCreatedByModel {
        private String createdBy;
        private Instant created;
        private String lastModifiedBy;
        private Instant lastModified;


        FillCreatedByModel(String createdBy, Instant created) {
            this.created = created;
            this.lastModified = this.created;
            this.createdBy = createdBy;
            this.lastModifiedBy = this.createdBy;
        }
    }

    /**
     * 兼容举哥逻辑，goods的表举哥都建的是这两个字段名
     * */
    @Data
    public static class FillCreatedUserId {
        private String  createdUserId;
        private Instant createdDate;
        private String lastModifiedUserId;
        private Instant lastModifiedDate;



        FillCreatedUserId(String createdBy, Instant created) {
            this.createdDate = created;
            this.lastModifiedDate = this.createdDate;
            this.createdUserId = createdBy;
            this.lastModifiedUserId = this.createdUserId;
        }

    }
}
