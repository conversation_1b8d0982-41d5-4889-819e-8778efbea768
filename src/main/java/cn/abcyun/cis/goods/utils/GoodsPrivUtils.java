package cn.abcyun.cis.goods.utils;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.commons.model.CisClinicType;

/***
 * 单店升级连锁架构后,都不要使用 clincType  viewMode来直接判断是否是单店
 * 使用如下工具函数
 * */
public class GoodsPrivUtils {


    /**
     * 连锁门店
     *  1.连锁视图 ----  连锁门店
     *  2.单店视图 ---   门店
     * */
    public static boolean isHeadClinic(int clinicType,int viewMode) {
        //这一段代码是老逻辑兼容,等单店升级连锁升完，就用不不会有诊所走进来了
//        if (clinicType == Organ.NodeType.INDEPENDENT_CLINIC) {
//            return true;
//        }

        if (clinicType == Organ.NodeType.CHAIN_HEAD_CLINIC) {
            return true;
        }
        return viewMode == Organ.ViewMode.SINGLE;
    }
    /**
     * 普通门店
     *  1.连锁视图 ----  门店
     *  2.单店视图 ---   无
     * */
    public static  boolean isSubClinic(int clinicType,int viewMode) {
        //这一段代码是老逻辑兼容,等单店升级连锁升完，就用不不会有诊所走进来了
        if (clinicType == Organ.NodeType.INDEPENDENT_CLINIC) {
            return false;
        }

        if (clinicType == Organ.NodeType.CHAIN_BRANCH_CLINIC) {
            return viewMode == Organ.ViewMode.NORMAL;
        }
        return false;
    }
    /**
     * 单店视图
     * */
    public static  boolean isSingleMode(int clinicType,int viewMode) {
        return viewMode == Organ.ViewMode.SINGLE;
    }


    public static boolean supportEye(int clinicSupportBusiness) {
        return (clinicSupportBusiness & CisJWTUtils.CisBusiness.CIS_BUSINESS_EYE) != 0;
    }

    /**
     * 是否是药店
     * */
    public  static  boolean isAbcPharmacy(int hisType){
        return hisType == Organ.HisType.CIS_HIS_TYPE_PHARMACY;
    }
    public  static  boolean isHospital(int hisType){
        return hisType == Organ.HisType.CIS_HIS_TYPE_HOSPITAL;
    }
}
