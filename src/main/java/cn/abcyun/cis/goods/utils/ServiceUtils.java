/*
  处理查询goods列表的服务
 */
package cn.abcyun.cis.goods.utils;

import cn.abcyun.bis.rpc.sdk.bis.model.goods.mall.MallGoods;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.RpcGetShebaoMatchedCodesReq;
import cn.abcyun.bis.rpc.sdk.cis.model.suggest.SuggestMallHisGoods;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.model.ChainAirPharmacy;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.goods.amqp.RocketMqProducer;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisUtils;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.domain.GoodsStatManager;
import cn.abcyun.cis.goods.dto.purchase.UpdateRelatedOrderStatusCount;
import cn.abcyun.cis.goods.entity.GoodsStockDeliveryOrder;
import cn.abcyun.cis.goods.entity.GoodsStockReceiveOrder;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.mapper.GoodsPurchaseOrderMapper;
import cn.abcyun.cis.goods.model.GoodsSysType;
import cn.abcyun.cis.goods.model.*;
import cn.abcyun.cis.goods.model.b2b.GoodsPurchaseOrder;
import cn.abcyun.cis.goods.model.b2b.GoodsRelationHisToMall;
import cn.abcyun.cis.goods.model.b2b.MallOrderToInOrder;
import cn.abcyun.cis.goods.repository.*;
import cn.abcyun.cis.goods.service.GoodsStatService;
import cn.abcyun.cis.goods.service.query.GoodsSysTypeService;
import cn.abcyun.cis.goods.service.rpc.BisGoodsService;
import cn.abcyun.cis.goods.service.rpc.CisClinicService;
import cn.abcyun.cis.goods.service.rpc.CisShebaoService;
import cn.abcyun.cis.goods.stock.goods.GoodsOpsBase;
import cn.abcyun.cis.goods.vo.frontend.pharmacy.GoodsPharmacyView;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.abcyun.cis.goods.exception.CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER;


public class ServiceUtils {
    private static final Logger sLogger = LoggerFactory.getLogger(ServiceUtils.class);
    public static DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd")
            .withZone(ZoneId.systemDefault());
    public static final int MAX_QUERY_COUNT = 100;

    /**
     * 参数检查
     */
    public static void queryGoodsByIdsReqCheck(QueryGoodsByIdsReq req) throws CisGoodsServiceException {
        if (StringUtils.isEmpty(req.getClinicId())) {
            //clinicId 为空
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER);
        }
        if (CollectionUtils.isEmpty(req.getGoodsIds())) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER);
        }
        /*限定100条*/
//        if (req.getGoodsIds().size() > MAX_QUERY_COUNT) {
//            throw new CisGoodsServiceException(CisGoodsServiceError.QUERY_GOODS_LIST_SIZE_TOO_LARGE);
//        }
    }

    /**
     * 子店是否是否可能有子店定价
     * <p>
     * <p>
     * 以前 这个函数返回的逻辑是子店是否有 子店定价权 以前有子店定价权和是否有子店定价是一个意义
     * lis需求：https://www.xiaopiu.com/web/byId?type=project&id=61e684a32ac929e58be9fa87
     * 子店定价权已经变更到 {@link subClinicPricePriv} 这个函数上了
     * 为了保证改动这小，这个函数返回的含义变成  true  可能有子店定价 因为总部可以给子店定价了，总店会绕过以前 这个开关的含义(连锁子店都会返回true了)
     * <p>
     * true  可能有子店定价 具体有不有子店价格 还要去goodsPrice表查
     * false 一定无子店定价
     */
    public static boolean maybeSetSubPrice(ClinicConfig clinicConfig, String clinicId) {
        if (clinicConfig == null || StringUtils.isEmpty(clinicId)) {
            return false;
        }
        if(clinicConfig.isAbcPharmacy()){
            return true;//药店会员多价格体系
        }
        //总店和单店不需要子店定价
        if (clinicConfig.isHeadClinic()) {
            if (clinicConfig.isSingleMode()) {
                return false;
            }
            if (clinicConfig.getChainId() != null && clinicId.compareTo(clinicConfig.getChainId()) == 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * 子店是否有子店定价权
     * true 有子店定价权
     * false  无子店定价权
     *
     * @Note 定价权和子店定价是没关系的
     */
    public static Boolean subClinicHasPricePriv(ClinicConfig clinicConfig, String clinicId) {
        Integer priv = subClinicHasPricePrivFlag(clinicConfig, clinicId);
        if (priv != null && priv == GoodsPrice.SubPricePrivType.PRIV) {
            return true;
        }
        return false;
    }

    /**
     * 子店是否有子店定价权
     * null  无意义
     * 0 无定价权
     * 1 有定价权
     *
     * @Note 定价权和子店定价是没关系的
     */
    public static Integer subClinicHasPricePrivFlag(ClinicConfig clinicConfig, String clinicId) {
        if (clinicConfig == null || StringUtils.isEmpty(clinicId)) {
            return null;
        }
        //总店和单店不需要子店定价
        if (clinicConfig.isHeadClinic()) {
            if (clinicConfig.isSingleMode()) {
                return null;
            }
            if (clinicConfig.getChainId() != null && clinicId.compareTo(clinicConfig.getChainId()) == 0) {
                return null;
            }
        }

        //总开关
        if (clinicConfig.getSubSetPrice() == null || clinicConfig.getSubSetPrice() == 0) {
            return GoodsPrice.SubPricePrivType.NO_PRIV;
        }

        //所有子店都设置了
        if (clinicConfig.getSubSetPriceAllClinics() != null && clinicConfig.getSubSetPriceAllClinics() == 1) {
            return GoodsPrice.SubPricePrivType.PRIV;
        }

        //判断是否在设置的子店里面
        if (!CollectionUtils.isEmpty(clinicConfig.getClinicIdToSubSetPriceClinics())) {
            ClinicConfig.SubSetPriceClinic foundClinic = clinicConfig.getClinicIdToSubSetPriceClinics().get(clinicId);
            return foundClinic != null ? GoodsPrice.SubPricePrivType.PRIV : GoodsPrice.SubPricePrivType.NO_PRIV;
        }

        return GoodsPrice.SubPricePrivType.NO_PRIV;
    }


    /**
     * 不会返回null
     * 否则抛异常
     */
    public static ClinicConfig getClinicConfig(CisClinicService cisClinicService, String clinicId) {
        return cisClinicService.getClinicConfig(clinicId); //返回
    }

    /**
     * 从property服务里面获取诊所是否开启自主设置空中药房价格
     * 0 未开启
     */
    public static int getAllowClinicSetting(PropertyService propertyService, String chainId) {
        ChainAirPharmacy chainAirPharmacy = propertyService.getPropertyValueByKey(PropertyKey.CHAINBASIC_AIRPHARMACY, chainId, ChainAirPharmacy.class);
        if (chainAirPharmacy != null) {
            return chainAirPharmacy.getSupportClinicSetting();
        }
        return GoodsUtils.SwitchFlag.OFF;//
    }

    /**
     * 获取诊所的自主定价规则
     * <p>
     * 通his的自主定价规则一样
     */
    public static List<GoodsBisPrice> loadClinicSettings(ClinicConfig clinicConfig, PropertyService propertyService, GoodsBisPriceRepository goodsBisPriceRepository) {
        List<GoodsBisPrice> goodsBisPriceList;
        if (clinicConfig.isHeadClinic() && !clinicConfig.isSingleMode()) {
            goodsBisPriceList =  goodsBisPriceRepository.findAllByChainIdAndClinicIdAndIsDeleted(
                    clinicConfig.getChainId(),
                    clinicConfig.getChainId(), //连锁的配置
                    GoodsUtils.SwitchFlag.OFF);
        } else if (clinicConfig.isSubClinic()) {
            int allowClinicSetting;
            allowClinicSetting = getAllowClinicSetting(propertyService, clinicConfig.getChainId());
            if (allowClinicSetting == GoodsUtils.SwitchFlag.ON) { //有自主定价
                List<GoodsBisPrice> clinicGoodsBisPriceList = goodsBisPriceRepository.findAllByChainIdAndClinicIdAndIsDeleted(
                        clinicConfig.getChainId(),
                        clinicConfig.getClinicId(), //门店的配置
                        GoodsUtils.SwitchFlag.OFF);
                goodsBisPriceList = goodsBisPriceRepository.findAllByChainIdAndClinicIdAndIsDeleted(
                        clinicConfig.getChainId(),
                        clinicConfig.getChainId(), //连锁的配置
                        GoodsUtils.SwitchFlag.OFF);
                //如果子店没有配置用总部配置
                goodsBisPriceList.forEach(mainGoodsType -> {
                    GoodsBisPrice subGoodsType = clinicGoodsBisPriceList.stream().filter(clinicGoodsType -> clinicGoodsType.getTypeId() == mainGoodsType.getTypeId()).findFirst().orElse(null);
                    if (subGoodsType != null && subGoodsType.getPercent() != null) {
                        mainGoodsType.setPercent(subGoodsType.getPercent());
                        mainGoodsType.setProfitsPercent(subGoodsType.getProfitsPercent());
                        mainGoodsType.setClinicId(clinicConfig.getClinicId());
                    }
                });
            } else { //无自主定价
                goodsBisPriceList = goodsBisPriceRepository.findAllByChainIdAndClinicIdAndIsDeleted(
                        clinicConfig.getChainId(),
                        clinicConfig.getChainId(),//连锁的配置
                        GoodsUtils.SwitchFlag.OFF);
            }
        } else {//单店或总部门店
            goodsBisPriceList = goodsBisPriceRepository.findAllByChainIdAndClinicIdAndIsDeleted(
                    clinicConfig.getChainId(),
                    clinicConfig.getClinicId(),
                    GoodsUtils.SwitchFlag.OFF);
        }

        return goodsBisPriceList.stream().filter(goodsBisPrice ->
                        goodsBisPrice.getTypeId() == GoodsConst.GoodsTypeId.MEDICINE_CHINESE_PIECES_TYPEID
                                || goodsBisPrice.getTypeId() == GoodsConst.GoodsTypeId.MEDICINE_CHINESE_GRANULE_TYPEID)
                .collect(Collectors.toList());
    }

    /**
     * 设置商品门店自主定价
     * 【重要这里必须返回实时准确的价格数据，charge在收费】子店门店自主定价
     */
    public static void prepareClinicSelfPrice(List<String> stockGoodsIdList,
                                              String clinicId,
                                              Map<String, Goods> mapGoodsAll,
                                              GoodsPriceRepository goodsPriceRepository,
                                              ClinicConfig clinicConfig) {
        //为空不需要加载子店定价
        if (StringUtils.isEmpty(clinicId) || mapGoodsAll.size() == 0) {
            return;
        }
        Iterator<Map.Entry<String, Goods>> it = mapGoodsAll.entrySet().iterator();
        //遍历所有goods，设置门店自主定价
        while (it.hasNext()) {
            Map.Entry<String, Goods> entry = it.next();
            Goods goods = entry.getValue();
            if (goods == null) {
                continue;
            }
            goods.setClinicConfig(clinicConfig);
        }
        LoadGoodsUtils.loadAndAssembleSimpleDbGoodsPriceList(goodsPriceRepository, clinicConfig, mapGoodsAll, stockGoodsIdList);

    }

    /**
     * 处理goodsExtend
     * 这里是列表查询，如果用jpa自动关联加载，会发出很多sql，这里用一条sql把所有goodsExtend捞出来，设置进去
     */
    public static void prepareGoodsExtend(List<String> uniqReqGoodsIdList,
                                          String chainId,
                                          String clinicId,
                                          Map<String, Goods> mapGoodsAll,
                                          GoodsExtendRepository goodsExtendRepository) {

        //主店子店的goodsExtend都load进来
        List<String> organList = new ArrayList<>();
        organList.add(chainId);
        if (!StringUtils.isEmpty(clinicId) && !clinicId.equals(chainId)) {
            organList.add(clinicId);
        }
        //uniqReqGoodsIdList 按两百个分组去拉取
        List<List<String>> goodsIdPartionList = Lists.partition(uniqReqGoodsIdList, 100);
        List<GoodsExtend> goodsExtendList = new ArrayList<>();
        for (List<String> perQueryIdList : goodsIdPartionList) {
            GoodsStatManager.call("Ext_B_7");
            goodsExtendList.addAll(goodsExtendRepository.findAllByGoodsIdInAndOrganIdIn(perQueryIdList, organList));
        }
        if (CollectionUtils.isEmpty(goodsExtendList)) {
            return;
        }

        goodsExtendList.forEach(goodsExtend -> {
            Goods goods = mapGoodsAll.get(goodsExtend.getGoodsId());
            if (goods.getOrganId().equals(goodsExtend.getOrganId())) {
                goods.setGoodsExtend(goodsExtend);
            } else {
                goods.setChildGoodsExtend(goodsExtend);
            }
        });
    }

    public static void prepareGoodsSpuSpec(List<Goods> goodsList, Map<Long, GoodsSpuSpec> specIdToSpuSpec) {
        if (CollectionUtils.isEmpty(goodsList) || CollectionUtils.isEmpty(specIdToSpuSpec)) {
            return;
        }
        goodsList.forEach(goods -> goods.setGoodsSpuSpec(specIdToSpuSpec.get(goods.getExtendSpecId())));
    }

    /**
     * 二级分类 从redis加载,加载不出来问题也不大
     */
    public static void prepareGoodsCustomType(String chainId, List<Goods> goodsList, GoodsRedisUtils goodsRedisUtils) {
        List<Long> customTypeIdList = goodsList.stream().map(Goods::getCustomTypeId).filter(id -> id != null && id != 0).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customTypeIdList)) {
            return;
        }
        Map<Long, List<Goods>> mapGoodsByCustomId = goodsList.stream().filter(it -> it.getCustomTypeId() != null).collect(Collectors.groupingBy(Goods::getCustomTypeId));
        Map<Long, GoodsCustomTypeView> customTypeIdToName = goodsRedisUtils.getCustomType(chainId, customTypeIdList);
        if (customTypeIdToName.size() < 0) {
            return;
        }
        customTypeIdToName.forEach((customTypeId, customType) -> {
            List<Goods> goodsListIn = mapGoodsByCustomId.get(customTypeId);
            if (CollectionUtils.isEmpty(goodsListIn))
                return;
            for (Goods goods : goodsListIn) {
                goods.setCustomTypeName(customType.getName());
                goods.setProfitCategoryTypeName(goodsRedisUtils.getProfitCategoryTypeName(goods.getOrganId(), goods.getProfitCategoryType()));
                //义齿加工
                goods.setManufacturerId(customType.getManufactureId());
            }
        });
    }

    //诊所shortID
    public static String getClinicShortId(String clinicId, CisClinicService cisClinicService) throws CisGoodsServiceException {
        cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ organ = cisClinicService.getOrganByClinicId(clinicId);
        if (organ == null || StringUtils.isEmpty(organ.getShortId())) {
            sLogger.info("getClinic short id failed.");
            throw new CisGoodsServiceException(CIS_GOODS_ERROR_PARAMETER);
        }
        return organ.getShortId();
    }

    public static String getClinicShortIdNoExp(String clinicId, CisClinicService cisClinicService) {
        Organ organ = cisClinicService.getOrganByClinicId(clinicId);
        if (organ == null || StringUtils.isEmpty(organ.getShortId())) {
            sLogger.info("getClinic short id failed.");
            return "";
        }
        return organ.getShortId();
    }

    /**
     * 获取诊所的id
     */
    public static String getClinicIdNoExp(String shortClinicId, CisClinicService cisClinicService) {
        Organ organ = cisClinicService.getOrganByShortId(shortClinicId);
        if (organ == null || StringUtils.isEmpty(organ.getId())) {
            sLogger.info("getClinic  id failed.");
            return "";
        }
        return organ.getId();
    }

    public static String getEmployeeShortIdNoExp(String employeeId, CisClinicService cisClinicService, String chainId) {
        Employee employee = cisClinicService.getEmployeeById(employeeId, chainId);
        if (employee == null || StringUtils.isEmpty(employee.getShortId())) {
            sLogger.info("getClinic short id failed.");
            return "";
        }
        return employee.getShortId();
    }

    /**
     * 需要把父类型的typeId转成子类型的typeId
     */
    public static List<Integer> getFilterJsonTypesList(List<Integer> typeIds, GoodsSysTypeService goodsSysTypeService) {

        if (CollectionUtils.isEmpty(typeIds)) {
            return typeIds;
        }
        List<Integer> leafTypeIds = new ArrayList<>();
        List<GoodsSysType> filterTypesList = goodsSysTypeService.getHisGoodsSysTypesList();
        if (!CollectionUtils.isEmpty(filterTypesList)) {
            for (GoodsSysType goodsSysType : filterTypesList) {
                for (Integer typeId : typeIds) {
                    if (typeId == null) {
                        continue;
                    }
                    Long longTypeId = typeId.longValue();
                    if (goodsSysType.getId().compareTo(longTypeId) == 0 && goodsSysType.getIsLeaf() == 1) {
                        leafTypeIds.add(typeId);
                    }

                    if (goodsSysType.getParentId().compareTo(longTypeId) == 0 && goodsSysType.getIsLeaf() == 1) {
                        leafTypeIds.add(goodsSysType.getId().intValue());
                    }
                }
            }
            return leafTypeIds;
        } else {
            return typeIds;
        }

    }

    /**
     * 获取映射关系的值
     * userSetTransRatio 用户设置的换算值
     * rel 老的稳定状态的映射关系
     * goods hisGoods 用于计算自动换算关系
     * mallGoods mallGoods 用于计算自动换算关系
     * relationHisToMall 计算自动换算关系
     * null 表示没有换算关系
     */
    public static BigDecimal getTransRatio(BigDecimal userSetTransRatio, GoodsRelationHisToMall rel, Goods goods, SuggestMallHisGoods mallGoods, String skuGoodsId, RelationHisToMall relationHisToMall) {
        /**
         * 用户设置了换算单位，用用户设置的
         * */
        if (userSetTransRatio != null && userSetTransRatio.compareTo(BigDecimal.ZERO) > 0) {
            return userSetTransRatio;
        } else {
            /**
             * 负责使用稳定的推荐关系
             * */

            if (rel != null
                    && rel.getMallSkuGoodsId().compareTo(GoodsUtils.getLongId(skuGoodsId)) == 0  //映射关系 搜索的返回，应该用sku里面的
                    && rel.getTransRatio() != null) { //换算关系
                return rel.totalRatio();
            } else {
                /**
                 * 把自动推荐的值带出去
                 * */
                int suggestRatio = relationHisToMall.guessMallToHisRatio(goods, mallGoods);
                if (suggestRatio > 0) {
                    return BigDecimal.valueOf(suggestRatio);
                }
            }
        }
        return null;
    }

    /**
     * 获得最终的换算关系
     * userSetTransRatio ---  用户设置了换算值，这是第一优先级使用
     * rel --- 有映射关系（rel里面为换算关系，里面的transRatio为映射关系）
     * goods&mallGoods --- 用来算推荐关系
     */
    public static BigDecimal getTransRatio(BigDecimal userSetTransRatio,
                                           GoodsRelationHisToMall rel,
                                           Goods goods,
                                           MallGoods mallGoods,
                                           RelationHisToMall relationHisToMall) {
        /**
         * 用户设置了换算单位，用用户设置的
         * */
        if (userSetTransRatio != null && userSetTransRatio.compareTo(BigDecimal.ZERO) > 0) {
            return userSetTransRatio;
        } else {
            /**
             * 负责使用稳定的推荐关系
             *
             * */
            if (rel != null
                    && mallGoods != null
                    && rel.getMallSkuGoodsId().compareTo(GoodsUtils.getLongId(mallGoods.getId())) == 0  //映射关系
                    && rel.getTransRatio() != null) { //换算关系
                return rel.getTransRatio();
            } else {
                /**
                 * 把自动推荐的值带出去
                 * */
                int suggestRatio = relationHisToMall.guessMallToHisRatio(goods, mallGoods);
                if (suggestRatio > 0) {
                    return BigDecimal.valueOf(suggestRatio);
                }
            }
        }
        return null;
    }

    /**
     * 获取下单的his的商品信息
     */
    public static Map<String, Goods> getHisGoodsMap(List<String> goodIdList, String chainId, GoodsRepository goodsRepository) throws CisGoodsServiceException {

        List<Goods> goodsList = goodsRepository.findAllByIdInAndOrganIdAndStatusLessThan(goodIdList, chainId, GoodsConst.GoodsStatus.DELETE);
        if (CollectionUtils.isEmpty(goodsList)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_NOT_FOUND);
        }
        return goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (a, b) -> a));
    }

    /**
     * ignoreDownGoods 是否把下架的商城商品也返回出去
     * 在添加购物车环节，下架的商城商品也需要拉出来
     * false 表示要检查下架商品，如果检查到了下架商品，将抛出异常
     */
    public static Map<Long, MallGoods> getMapMallGoods(List<Long> skuGoodsIdList, BisGoodsService bisGoodsService, String shortClinicId, boolean ignoreDownGoods) {
        List<MallGoods> mallSkuGoodsList = null;
        if (!CollectionUtils.isEmpty(skuGoodsIdList)) {
            mallSkuGoodsList = bisGoodsService.getMallGoodsList(skuGoodsIdList, shortClinicId);
        }
        if (!ignoreDownGoods && !CollectionUtils.isEmpty(mallSkuGoodsList)) {
            boolean mallSkuGoodsDown = mallSkuGoodsList.stream().anyMatch(it -> it.getStatus() != 1);
            if (mallSkuGoodsDown) {
                sLogger.info("addOrModShoppingPlanList 商城商品已经下架 = {}", JsonUtils.dump(mallSkuGoodsList));
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_MALL_MALL_GOODS_IS_DOWN);
            }
        }
        Map<Long, MallGoods> mapMallGoods;
        if (!CollectionUtils.isEmpty(mallSkuGoodsList)) {
            mapMallGoods = mallSkuGoodsList.stream().collect(Collectors.toMap(it -> Long.parseLong(it.getId()), Function.identity(), (a, b) -> a));
        } else {
            mapMallGoods = new HashMap<>();
        }
        return mapMallGoods;
    }

    /**
     * true 一样的
     * false 不一样
     */
    private static boolean strSame(String s1, String s2) {
        if (s1 == null && s2 == null) {
            return true;
        }
        if (s1 == null || s2 == null) {
            return false;
        }
        if (s1.compareTo(s2) == 0) {
            return true;
        }
        return false;
    }

    /**
     * 比较两个MallGoods
     * true 有变化
     */
    public static boolean checkMallGoodsChanged(MallGoods left, MallGoods right) {
        if (left == null || right == null) {
            return true;
        }
        if (left.getStatus() != right.getStatus())
            return true;
        if (!strSame(left.getSpecification(), right.getSpecification()))
            return true;
        if (!strSame(left.getDosageForm(), right.getDosageForm()))
            return true;

        return false;
    }

    /**
     * 计算商城订单的入库入库状态
     * 1.一个商城订单 可以有多个入库单
     * 2.一个入库单有直接入库完成，审核 ，撤回，拒绝，通过等状态
     * 3.商城订单入库状态  入库状态，0：不可入库，1：可入库，2：部分入库，3：已入库，4：审核中
     * 映射关系： 0 不可入库
     * 1 可入库，所有的订单条目都没如果库 hisInOrderId == null && hisInOrderItemId == null
     * 2 部分入库 有部分订单条目 的  hisInOrderId == null && hisInOrderItemId == null
     * 3 已入库 所有的商品条目  hisInOrderId != null && hisInOrderItemId != null
     */
    public static short computeMallOrderInOrderStatus(List<MallOrderToInOrder> mallOrderItemList) {
        if (CollectionUtils.isEmpty(mallOrderItemList)) {
            return MallOrderToInOrder.InOrderStatus.INSTOCK_ENABLE;
        }
        long totalCount = mallOrderItemList.stream().filter(it -> {
            if (it.getHisInOrderId() != null) //入过库的
                return true;

            //未入库的看下订单量，大于0的
            if (it.getAfterSalePurchaseCount() != null) {
                return it.getAfterSalePurchaseCount().compareTo(BigDecimal.ZERO) > 0;
            } else if (it.getMallDealPurchaseCount() != null) {
                return it.getMallDealPurchaseCount().compareTo(BigDecimal.ZERO) > 0;
            }
            return false;
        }).count();
        long inOrderCount = mallOrderItemList.stream().filter(it -> it.getHisInOrderId() != null).count();
        if (inOrderCount == 0 && totalCount > 0) {
            return MallOrderToInOrder.InOrderStatus.INSTOCK_ENABLE;
        }
        if (inOrderCount < totalCount) {
            return MallOrderToInOrder.InOrderStatus.INSTOCK_PART;
        }
        if (inOrderCount == totalCount) {
            return MallOrderToInOrder.InOrderStatus.INSTOCK_FINISH;
        }
        return MallOrderToInOrder.InOrderStatus.INSTOCK_ENABLE;
    }


    /**
     * 把社保信息拉取回来填充到到对应的goods对象上
     */
    public static void fillSheBaoInfo(List<Goods> flatGoodsList, String clinicId, String chainId, CisShebaoService cisShebaoService) {
        List<RpcGetShebaoMatchedCodesReq.GetSmartMatchedReqItem> queryShebaoGoodsList = new ArrayList<>();
        for (Goods goods : flatGoodsList) {
            RpcGetShebaoMatchedCodesReq.GetSmartMatchedReqItem queryShebaoGoods = new RpcGetShebaoMatchedCodesReq.GetSmartMatchedReqItem();
            queryShebaoGoods.setGoodsId(goods.getId());
            queryShebaoGoods.setGoodsType((int) goods.getType());
            queryShebaoGoods.setName(goods.getName());
            queryShebaoGoods.setSubType(goods.getSubType());
            queryShebaoGoods.setMedicineCadn(goods.getMedicineCadn());
            queryShebaoGoods.setMedicineNmpn(goods.getMedicineNmpn());
            queryShebaoGoods.setMedicineDosageNum(goods.getMedicineDosageNum());
            queryShebaoGoods.setMedicineDosageUnit(goods.getMedicineDosageUnit());
            queryShebaoGoods.setMedicineDosageForm(goods.getMedicineDosageForm());
            queryShebaoGoods.setPieceNum(BigDecimal.valueOf(goods.getPieceNum()));
            queryShebaoGoods.setPieceUnit(goods.getPieceUnit());
            queryShebaoGoods.setPackageUnit(goods.getPackageUnit());
            queryShebaoGoods.setCMSpec(goods.getCMSpec());
            queryShebaoGoods.setExtendSpec(goods.getExtendSpec());
            queryShebaoGoods.setManufacturer(goods.getManufacturer());
            queryShebaoGoods.setManufacturerFull(goods.getManufacturerFull());
            queryShebaoGoodsList.add(queryShebaoGoods);
        }
        Map<String, GoodsSheBaoMatchedCodesInfo> mapGoodsIdToSheBaoInfo = cisShebaoService.getMatchedSheBaoCodesMap(chainId, clinicId, queryShebaoGoodsList, false);
        if (mapGoodsIdToSheBaoInfo == null || mapGoodsIdToSheBaoInfo.size() == 0) {
            sLogger.info("getMatchedSheBaoCodesMap return null");
            return;
        }
        for (Goods goods : flatGoodsList) {
            if (!mapGoodsIdToSheBaoInfo.containsKey(goods.getId())) {
                continue;
            }
            goods.setGoodsSheBaoMatchedCodesInfo(mapGoodsIdToSheBaoInfo.get(goods.getId()));
        }
    }

    /**
     * 加载所有的goodsStat 包括全部门店
     */
    public static Map<String, GoodsStat> loadGoodsStat(
            ClinicConfig clinicConfig,
            GoodsStatRepository goodsStockCacheRepository,
            List<String> goodsIdList,
            int pharmacyType,
            List<Integer> pharmacyNos) {
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return new HashMap<>();
        }
        String chainId = clinicConfig.getChainId();
        String clinicId = clinicConfig.getClinicId();
        List<Integer> pharmacyNoList = new ArrayList<>();
        pharmacyNoList.addAll(pharmacyNos);
        List<String> queryClinicIdList = new ArrayList<>();
        queryClinicIdList.add(clinicId);
        pharmacyNoList.add(GoodsUtils.SUMMERY_PHARMACY_NO);
        queryClinicIdList.add(GoodsUtils.WHOLE_CHAIN_ID);

        List<GoodsStat> goodsStatList = goodsStockCacheRepository.findAllByGoodsIdInAndChainIdAndClinicIdInAndPharmacyTypeAndPharmacyNoIn(
                goodsIdList,
                chainId,
                queryClinicIdList,
                pharmacyType,
                pharmacyNoList);

        if (CollectionUtils.isEmpty(goodsStatList)) {
            sLogger.info("设置goodsStat库存信息，但是貌似还没入过库，还无goodsStat信息 chainId={},clinicId={},goodsIdList={}", clinicConfig.getChainId(), clinicConfig.getClinicId(), JsonUtils.dump(goodsIdList));
            return new HashMap<>();
        }
        List<Long> spuGoodsIdList = goodsStatList.stream().map(GoodsStat::getSpuGoodsId).filter(it -> it.compareTo(0L) != 0).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(spuGoodsIdList)) {
            goodsStatList.addAll(goodsStockCacheRepository.findAllBySpuGoodsIdInAndGoodsIdAndChainIdAndClinicIdInAndPharmacyTypeAndPharmacyNoIn(
                    spuGoodsIdList,
                    GoodsUtils.EMPTY_GOODS_ID,
                    chainId,
                    queryClinicIdList,
                    pharmacyType,
                    pharmacyNoList));
        }

        //以goodsId为key组织goodsStat为map，方便使用
        return goodsStatList.stream().collect(Collectors.toMap(GoodsStat::getUniqKey, Function.identity(), (a, b) -> a));

    }

    public static Map<String, GoodsStat> loadGoodsStat(ClinicConfig clinicConfig,
                                                       GoodsStatRepository goodsStockCacheRepository,
                                                       List<String> goodsIdList,
                                                       int pharmacyType,
                                                       int pharmacyNo) {
        return loadGoodsStat(clinicConfig, goodsStockCacheRepository, goodsIdList, pharmacyType, Arrays.asList(pharmacyNo));
    }

    /**
     * 加载调拨影响到的GoodsStat
     *
     * @param outClinicConfig           调出门店配置
     * @param outPharmacyView           调出门店的药房
     * @param inClinicConfig            调入门店配置
     * @param inPharmacyView            调入门店药房
     * @param goodsIdList               goods列表
     * @param goodsStockCacheRepository
     */
    public static Map<String, GoodsStat> loadStockTransGoodsStat(
            ClinicConfig outClinicConfig,
            GoodsPharmacyView outPharmacyView,
            ClinicConfig inClinicConfig,
            GoodsPharmacyView inPharmacyView,
            List<String> goodsIdList,
            GoodsStatRepository goodsStockCacheRepository
    ) {
        List<Integer> outPharmacyNoList = new ArrayList<>();
        outPharmacyNoList.add(outPharmacyView.getNo());
        if (outClinicConfig.isClinicPharmacyTypeGoodsStatSummery(outPharmacyView.getType())) {
            outPharmacyNoList.add(GoodsUtils.SUMMERY_PHARMACY_NO);
        }
        List<GoodsStat> outGoodsStatList = goodsStockCacheRepository.findAllByGoodsIdInAndChainIdAndClinicIdInAndPharmacyTypeAndPharmacyNoIn(
                goodsIdList,
                outClinicConfig.getChainId(),
                Arrays.asList(outClinicConfig.getClinicId()),
                outPharmacyView.getType(),
                outPharmacyNoList);
        List<Integer> inPharmacyNoList = new ArrayList<>();
        inPharmacyNoList.add(inPharmacyView.getNo());
        /**
         * 如果是本门店 统一类型药房的调拨  不用加载汇总goodsStat了，前面已经加载了
         * */
        if (!GoodsUtils.compareStrEqual(outClinicConfig.getClinicId(), inClinicConfig.getClinicId())
                || outPharmacyView.getType() != inPharmacyView.getType()) {
            if (inClinicConfig.isClinicPharmacyTypeGoodsStatSummery(inPharmacyView.getType())) {
                inPharmacyNoList.add(GoodsUtils.SUMMERY_PHARMACY_NO);
            }
        }
        List<GoodsStat> inGoodsStatList = goodsStockCacheRepository.findAllByGoodsIdInAndChainIdAndClinicIdInAndPharmacyTypeAndPharmacyNoIn(
                goodsIdList,
                inClinicConfig.getChainId(),
                Arrays.asList(inClinicConfig.getClinicId()),
                inPharmacyView.getType(),
                inPharmacyNoList);


        /***
         * 能调拨肯定有两个药房 ，肯定需要汇总，主要看下是不是单店
         * */
        List<GoodsStat> chainGoodsStatList = new ArrayList<>();

        if (!outClinicConfig.isSingleMode()) {
            chainGoodsStatList.addAll(goodsStockCacheRepository.findAllByGoodsIdInAndChainIdAndClinicIdAndPharmacyNo(
                    goodsIdList,
                    outClinicConfig.getChainId(),
                    GoodsUtils.WHOLE_CHAIN_ID,
                    GoodsUtils.SUMMERY_PHARMACY_NO));
        }

        HashMap<String, GoodsStat> goodsStatKeyToGoodsStat = new HashMap<>();
        // 通过spuGoodsId加载
        List<Long> spuGoodsIdList = outGoodsStatList.stream().map(GoodsStat::getSpuGoodsId).filter(it -> it.compareTo(0L) != 0).distinct().collect(Collectors.toList());
        spuGoodsIdList.addAll(inGoodsStatList.stream().map(GoodsStat::getSpuGoodsId).filter(it -> it.compareTo(0L) != 0).distinct().collect(Collectors.toList()));
        spuGoodsIdList = spuGoodsIdList.stream().distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(spuGoodsIdList)) {
            List<String> clinicIdList = Arrays.asList(outClinicConfig.getClinicId(), inClinicConfig.getClinicId());
            List<Integer> pharmacyNoList = Stream.concat(outPharmacyNoList.stream(), inPharmacyNoList.stream()).distinct().collect(Collectors.toList());
            new ArrayList<>(outPharmacyNoList);
            List<Integer> pharmacyTypeList = Stream.of(outPharmacyView.getType(), inPharmacyView.getType()).distinct().collect(Collectors.toList());
            goodsStatKeyToGoodsStat.putAll(goodsStockCacheRepository.findAllBySpuGoodsIdInAndGoodsIdAndChainIdAndClinicIdInAndPharmacyTypeInAndPharmacyNoIn(spuGoodsIdList, GoodsUtils.EMPTY_GOODS_ID,
                    outClinicConfig.getChainId(), clinicIdList, pharmacyTypeList, pharmacyNoList).stream().collect(Collectors.toMap(GoodsStat::getUniqKey, Function.identity(), (a, b) -> a)));
        }

        //以goodsId为key组织goodsStat为map，方便使用
        goodsStatKeyToGoodsStat.putAll(outGoodsStatList.stream().collect(Collectors.toMap(GoodsStat::getUniqKey, Function.identity(), (a, b) -> a)));
        goodsStatKeyToGoodsStat.putAll(inGoodsStatList.stream().collect(Collectors.toMap(GoodsStat::getUniqKey, Function.identity(), (a, b) -> a)));
        goodsStatKeyToGoodsStat.putAll(chainGoodsStatList.stream().collect(Collectors.toMap(GoodsStat::getUniqKey, Function.identity(), (a, b) -> a)));

        return goodsStatKeyToGoodsStat;
    }

    public static void fixMinMaxSalePrice(GoodsStat clinicPharmacyStat, GoodsStat summeryStat) {
        if (clinicPharmacyStat == null ||  summeryStat == null) {
            return;
        }
        //如果 clinicPharmacyStat.getMinPackageCostPrice() 比  summeryStat.getMinPackageCostPrice()小
        if(clinicPharmacyStat.getMinPackageCostPrice() != null){
            if (summeryStat.getMinPackageCostPrice() == null || clinicPharmacyStat.getMinPackageCostPrice().compareTo(summeryStat.getMinPackageCostPrice()) < 0) {
                summeryStat.setMinPackageCostPrice(clinicPharmacyStat.getMinPackageCostPrice());
            }
        }
        //如果 clinicPharmacyStat.getMaxPackageCostPrice() 比  summeryStat.getMaxPackageCostPrice()大
        if(clinicPharmacyStat.getMaxPackageCostPrice() != null){
            if (summeryStat.getMaxPackageCostPrice() == null || clinicPharmacyStat.getMaxPackageCostPrice().compareTo(summeryStat.getMaxPackageCostPrice()) > 0) {
                summeryStat.setMaxPackageCostPrice(clinicPharmacyStat.getMaxPackageCostPrice());
            }
        }

        if (GoodsUtils.checkFlagOn(clinicPharmacyStat.getPriceType(), GoodsConst.PriceType.PKG_PRICE_MAKEUP)) {
            summeryStat.setMinPackagePrice(GoodsStockUtils.priceUpPercent2(summeryStat.getMinPackageCostPrice(), summeryStat.getPriceMakeupPercent(), summeryStat.getTypeId()));
            summeryStat.setMaxPackagePrice(GoodsStockUtils.priceUpPercent2(summeryStat.getMaxPackageCostPrice(), summeryStat.getPriceMakeupPercent(), summeryStat.getTypeId()));
        } else {
            if (clinicPharmacyStat.getPackagePrice() != null) {
                if (summeryStat.getMinPackagePrice() == null || clinicPharmacyStat.getPackagePrice().compareTo(summeryStat.getMinPackagePrice()) < 0) {
                    summeryStat.setMinPackagePrice(clinicPharmacyStat.getPackagePrice());
                }
                if (summeryStat.getMaxPackagePrice() == null || clinicPharmacyStat.getPackagePrice().compareTo(summeryStat.getMaxPackagePrice()) > 0) {
                    summeryStat.setMaxPackagePrice(clinicPharmacyStat.getPackagePrice());
                }
            }
        }
    }


    public static void addNewGoodsStockToRefreshGoodsStatImpl(ClinicConfig clinicConfig,
                                                              GoodsStock newGoodsStock,
                                                              GoodsSupplierView goodsSupplier,
                                                              GoodsStat goodsStat) {
        /**
         * 设置最近成本价
         * */
        goodsStat.setLastPackageCostPrice(newGoodsStock.getPackageCostPrice());
        /**
         * 入库是入库条目id，调拨入库是调拨条目id
         * */
        if (newGoodsStock.getStockInId() != null) {
            goodsStat.setLastStockInId(newGoodsStock.getStockInId().intValue());
        }
        //进价加成
        if(!GoodsUtils.isEyeGoods(goodsStat.getGoodsType()) && newGoodsStock.getPackageCostPrice() != null){
            //如果最近进价 比最小价格小
            if (goodsStat.getMinPackageCostPrice() == null || newGoodsStock.getPackageCostPrice().compareTo(goodsStat.getMinPackageCostPrice()) < 0) {
                goodsStat.setMinPackageCostPrice(newGoodsStock.getPackageCostPrice());
                if(GoodsUtils.checkFlagOn(goodsStat.getPriceType(),GoodsConst.PriceType.PKG_PRICE_MAKEUP)){
                    goodsStat.setMinPackagePrice(GoodsStockUtils.priceUpPercent2(goodsStat.getMinPackageCostPrice(), goodsStat.getPriceMakeupPercent(), goodsStat.getTypeId()));
                }
            }
            //如果最近进价 比最大价格大
            if (goodsStat.getMaxPackageCostPrice() == null || newGoodsStock.getPackageCostPrice().compareTo(goodsStat.getMaxPackageCostPrice()) > 0) {
                goodsStat.setMaxPackageCostPrice(newGoodsStock.getPackageCostPrice());
                if(GoodsUtils.checkFlagOn(goodsStat.getPriceType(),GoodsConst.PriceType.PKG_PRICE_MAKEUP)){
                    goodsStat.setMaxPackagePrice(GoodsStockUtils.priceUpPercent2(goodsStat.getMaxPackageCostPrice(),goodsStat.getPriceMakeupPercent(), goodsStat.getTypeId()));
                }
            }
            if (GoodsUtils.checkFlagOn(goodsStat.getPriceType(), GoodsConst.PriceType.PKG_PRICE_MAKEUP)
                    && MathUtils.wrapBigDecimalCompare(goodsStat.getPackagePrice(), BigDecimal.ZERO) == 0) {
                // https://www.tapd.cn/tapd_fe/43780818/bug/detail/1143780818001087592
                // 进价加成库存用完重新入库stat刷下价格
                goodsStat.setPackagePrice(GoodsStockUtils.priceUpPercent2(goodsStat.getMaxPackageCostPrice(),
                        goodsStat.getPriceMakeupPercent(), goodsStat.getTypeId()));
                goodsStat.setPiecePrice(GoodsStockUtils.priceUpPercent2PiecePrice(goodsStat.getPackagePrice(),
                        goodsStat.getPieceNum(), goodsStat.getTypeId()));
            }
        }
        /**
         *30日销量空值策略错误，已有过入库批次，此时应展示0，而不是空
         * */
        if(goodsStat.getLastMonthSellCount() == null){
            goodsStat.setLastMonthSellCount(BigDecimal.ZERO);
        }

        /**
         * 初始化最近供应商的名字
         * */
        if (goodsSupplier != null) {
            goodsStat.setLastStockInOrderSupplier(goodsSupplier.getName());
        } else {
            goodsStat.setLastStockInOrderSupplier(SupplierUtils.defaultSupplierName(clinicConfig, newGoodsStock.getFrom(), newGoodsStock.getSupplierId()));
        }
        // 效期
        goodsStat.setMinExpiryDate(newGoodsStock.getExpiryDate());
        goodsStat.setOriginalExpiryDate(newGoodsStock.getOriginalExpiryDate());
    }

    /***
     * 工具函数 goodsStock扣库存后 通过增量的方式更新goodsStat
     * @param goodsStatUniqKeyToGoodsStat 输入输出 初始是已经存在的goodsStat 过程中新生成的goodsstat会插入
     * @param changeGoodsStatList 需要写入db的goodsStat的集合
     * @param clinicConfig 门店配置
     * @param goodsPharmacy 药房配置
     * @param newStockCreated true有新的goodsStock产生
     * @param supplierUtils 在有新的goodsStock产生的时候，传入新批次的供应商
     *
     *
     * @return 非空表示有新的goodsStat产生
     *
     * */
    public static GoodsStat updateGoodsStatByIncrement(Map<String, GoodsStat> goodsStatUniqKeyToGoodsStat,
                                                       RocketMqProducer rocketMqProducer,
                                                       CisClinicService cisClinicService,
                                                       List<GoodsStat> changeGoodsStatList,
                                                       Set<String> changeZeroToPositiveGoodsStatList,
                                                       Set<Long> setAddYetIdList,
                                                       GoodsOpsBase goodsOps,
                                                       ClinicConfig clinicConfig,
                                                       GoodsPharmacyView goodsPharmacy,
                                                       boolean newStockCreated,
                                                       SupplierUtils supplierUtils,
                                                       GoodsStatService goodsStatService,
                                                       GoodsRedisUtils goodsRedisUtils,
                                                       List<GoodsStat> needSendStatMessageList) {

        /*
         * 药房的汇总
         * */
        boolean isCreated = false;
        String pharmacySkuGoodsStatKey = GoodsStat.uniqKey(
                goodsOps.getSpuGoodsId(),
                goodsOps.getGoodsId(),
                clinicConfig.getClinicId(),
                goodsPharmacy.getType(),
                goodsPharmacy.getNo());
        GoodsStat pharmacyGoodsStat = goodsStatUniqKeyToGoodsStat.get(pharmacySkuGoodsStatKey);
        GoodsStat newCreatedStat = null;
        if (pharmacyGoodsStat == null) {
            pharmacyGoodsStat = goodsStatService.createGoodsStatOnGoodsStockOps(goodsOps.getGoodsRedisCache(), clinicConfig.getClinicId(), clinicConfig, goodsPharmacy.getType(), goodsPharmacy.getNo());
            isCreated = true;
            if (pharmacyGoodsStat != null) {
                goodsStatUniqKeyToGoodsStat.put(pharmacySkuGoodsStatKey, pharmacyGoodsStat);
                if (needSendStatMessageList != null) {
                    newCreatedStat = pharmacyGoodsStat;
                }
            }
        }
        //删除药，在退药
        if(pharmacyGoodsStat == null){
            if (needSendStatMessageList != null && newCreatedStat != null) {
                needSendStatMessageList.add(newCreatedStat);
            }
            return null;
        }
        pharmacyGoodsStat.setMinPackageCostPrice(goodsOps.getMinPackageCostPrice());
        pharmacyGoodsStat.setMaxPackageCostPrice(goodsOps.getMaxPackageCostPrice());
        pharmacyGoodsStat.setCurrentSellPackageCostPrice(goodsOps.getCurrentSellPackageCostPrice());
        if(GoodsUtils.checkFlagOn(pharmacyGoodsStat.getPriceType(),GoodsConst.PriceType.PKG_PRICE_MAKEUP)){
            pharmacyGoodsStat.setMinPackagePrice(GoodsStockUtils.priceUpPercent2(pharmacyGoodsStat.getMinPackageCostPrice(),pharmacyGoodsStat.getPriceMakeupPercent(), pharmacyGoodsStat.getTypeId()));
            pharmacyGoodsStat.setMaxPackagePrice(GoodsStockUtils.priceUpPercent2(pharmacyGoodsStat.getMaxPackageCostPrice(),pharmacyGoodsStat.getPriceMakeupPercent(), pharmacyGoodsStat.getTypeId()));
        }
        /*
         * SPU汇总
         * */
        GoodsStat pharmacySpuGoodsStat = null;
        if (pharmacyGoodsStat.getGoodsType() == GoodsConst.GoodsType.EYE) {
            String pharmacySpuGoodsStatKey = GoodsStat.uniqKey(
                    goodsOps.getSpuGoodsId(),
                    GoodsUtils.EMPTY_GOODS_ID,
                    clinicConfig.getClinicId(),
                    goodsPharmacy.getType(),
                    goodsPharmacy.getNo());
            pharmacySpuGoodsStat = goodsStatUniqKeyToGoodsStat.get(pharmacySpuGoodsStatKey);
            if (pharmacySpuGoodsStat == null) {
                pharmacySpuGoodsStat = goodsStatService.createGoodsStatOnGoodsStockOps(goodsOps.getGoodsRedisCache(), clinicConfig.getClinicId(), clinicConfig, goodsPharmacy.getType(), goodsPharmacy.getNo());
                if (pharmacySpuGoodsStat != null) {
                    pharmacySpuGoodsStat.setGoodsId(GoodsUtils.EMPTY_GOODS_ID);
                    goodsStatUniqKeyToGoodsStat.put(pharmacySpuGoodsStatKey, pharmacySpuGoodsStat);
                }
            }
            fixMinMaxSalePrice(pharmacyGoodsStat, pharmacySpuGoodsStat);
        }

        /*
         * 门店本地多药房汇总
         * */
        GoodsStat clinicGoodsStat = null;
        if (clinicConfig.isClinicPharmacyTypeGoodsStatSummery(goodsPharmacy.getType())) {
            String pharmacyTypeGoodsStatKey = GoodsStat.uniqKey(
                    goodsOps.getSpuGoodsId(),
                    goodsOps.getGoodsId(),
                    clinicConfig.getClinicId(),
                    goodsPharmacy.getType(),
                    GoodsUtils.SUMMERY_PHARMACY_NO);
            clinicGoodsStat = goodsStatUniqKeyToGoodsStat.get(pharmacyTypeGoodsStatKey);
            if (clinicGoodsStat == null) {
                clinicGoodsStat = goodsStatService.createGoodsStatOnGoodsStockOps(goodsOps.getGoodsRedisCache(), clinicConfig.getClinicId(), clinicConfig, goodsPharmacy.getType(), GoodsUtils.SUMMERY_PHARMACY_NO);
                if (clinicGoodsStat != null) {
                    goodsStatUniqKeyToGoodsStat.put(pharmacyTypeGoodsStatKey, clinicGoodsStat);
                    //只发一个，优先发汇总药房
                    if (needSendStatMessageList != null) {
                        newCreatedStat = clinicGoodsStat;
                    }
                }
            }
        }
        if(clinicGoodsStat != null){
            clinicGoodsStat.setCurrentSellPackageCostPrice(goodsOps.getCurrentSellPackageCostPrice());
        }

        if (needSendStatMessageList != null && newCreatedStat != null) {
            needSendStatMessageList.add(newCreatedStat);
        }
        GoodsStat clinicSpuGoodsStat = null;
        if (pharmacyGoodsStat.getGoodsType() == GoodsConst.GoodsType.EYE && clinicConfig.isClinicPharmacyTypeGoodsStatSummery(goodsPharmacy.getType())) {
            String pharmacyTypeSpuGoodsStatKey = GoodsStat.uniqKey(
                    goodsOps.getSpuGoodsId(),
                    GoodsUtils.EMPTY_GOODS_ID,
                    clinicConfig.getClinicId(),
                    goodsPharmacy.getType(),
                    GoodsUtils.SUMMERY_PHARMACY_NO);
            clinicSpuGoodsStat = goodsStatUniqKeyToGoodsStat.get(pharmacyTypeSpuGoodsStatKey);
            if (clinicSpuGoodsStat == null) {
                clinicSpuGoodsStat = goodsStatService.createGoodsStatOnGoodsStockOps(goodsOps.getGoodsRedisCache(), clinicConfig.getClinicId(), clinicConfig, goodsPharmacy.getType(), GoodsUtils.SUMMERY_PHARMACY_NO);
                if (clinicSpuGoodsStat != null) {
                    clinicSpuGoodsStat.setGoodsId(GoodsUtils.EMPTY_GOODS_ID);
                    goodsStatUniqKeyToGoodsStat.put(pharmacyTypeSpuGoodsStatKey, clinicSpuGoodsStat);
                }
            }
            fixMinMaxSalePrice(pharmacyGoodsStat, clinicSpuGoodsStat);
            //return isCreated ? pharmacyGoodsStat : null;
        }

        /*
         * 连锁总部的汇总
         * 总部的goodsStat不应该在这里建出来
         * */
        GoodsStat chainGoodsStat = null;
        if (clinicConfig.isChainPharmacyTypeGoodsStatSummery(goodsPharmacy.getType())) {
            String chainSkuGoodsStatKey = GoodsStat.uniqKey(
                    goodsOps.getSpuGoodsId(),
                    goodsOps.getGoodsId(),
                    GoodsUtils.WHOLE_CHAIN_ID,
                    goodsPharmacy.getType(),
                    GoodsUtils.SUMMERY_PHARMACY_NO);
            chainGoodsStat = goodsStatUniqKeyToGoodsStat.get(chainSkuGoodsStatKey);
            fixMinMaxSalePrice(pharmacyGoodsStat, chainGoodsStat);
        }
        GoodsStat chainSpuGoodsStat = null;
        if (pharmacyGoodsStat.getGoodsType() == GoodsConst.GoodsType.EYE && clinicConfig.isChainPharmacyTypeGoodsStatSummery(goodsPharmacy.getType())) {
            String chainSpuGoodsStatKey = GoodsStat.uniqKey(
                    goodsOps.getSpuGoodsId(),
                    GoodsUtils.EMPTY_GOODS_ID,
                    GoodsUtils.WHOLE_CHAIN_ID,
                    goodsPharmacy.getType(),
                    GoodsUtils.SUMMERY_PHARMACY_NO);
            chainSpuGoodsStat = goodsStatUniqKeyToGoodsStat.get(chainSpuGoodsStatKey);
            fixMinMaxSalePrice(pharmacyGoodsStat, chainSpuGoodsStat);
        }

        /*
         * 因为新加的这个GoodsStock 导致GoodsStat相关字段的变化
         *  如果是新加的goodsStock需要刷goodsstat相关字段
         * */
        if (newStockCreated && !CollectionUtils.isEmpty(goodsOps.getChangeGoodsStock())) {
            for (GoodsStock goodsStock : goodsOps.getChangeGoodsStock()) {
                GoodsSupplierView supplierView = supplierUtils.getSupplierCachedById(clinicConfig,clinicConfig.getChainId(), goodsStock.getSupplierId());
                if (pharmacyGoodsStat != null) {
                    addNewGoodsStockToRefreshGoodsStatImpl(clinicConfig,goodsOps.getChangeGoodsStock().get(goodsOps.getChangeGoodsStock().size() - 1), supplierView, pharmacyGoodsStat);
                    pharmacyGoodsStat.setCurrentBatchId(goodsStock.getBatchId());
                }
                if (pharmacySpuGoodsStat != null) {
                    addNewGoodsStockToRefreshGoodsStatImpl(clinicConfig,goodsOps.getChangeGoodsStock().get(goodsOps.getChangeGoodsStock().size() - 1), supplierView, pharmacySpuGoodsStat);
                }
                if (clinicGoodsStat != null) {
                    addNewGoodsStockToRefreshGoodsStatImpl(clinicConfig,goodsOps.getChangeGoodsStock().get(goodsOps.getChangeGoodsStock().size() - 1), supplierView, clinicGoodsStat);
                    clinicGoodsStat.setCurrentBatchId(goodsStock.getBatchId());
                }
                if (clinicSpuGoodsStat != null) {
                    addNewGoodsStockToRefreshGoodsStatImpl(clinicConfig,goodsOps.getChangeGoodsStock().get(goodsOps.getChangeGoodsStock().size() - 1), supplierView, clinicSpuGoodsStat);
                }
                if (chainGoodsStat != null) {
                    addNewGoodsStockToRefreshGoodsStatImpl(clinicConfig,goodsOps.getChangeGoodsStock().get(goodsOps.getChangeGoodsStock().size() - 1), supplierView, chainGoodsStat);
                    chainGoodsStat.setCurrentBatchId(goodsStock.getBatchId());
                }
                if (chainSpuGoodsStat != null) {
                    addNewGoodsStockToRefreshGoodsStatImpl(clinicConfig,goodsOps.getChangeGoodsStock().get(goodsOps.getChangeGoodsStock().size() - 1), supplierView, chainSpuGoodsStat);
                }
            }
        }
        /*
         * DB还没落地 先写入Redis
         * */
        // Before 0 -> 非0
        if (MathUtils.wrapBigDecimalCompare(pharmacyGoodsStat.getPieceCount(), BigDecimal.ZERO) == 0
                && MathUtils.wrapBigDecimalCompare(pharmacyGoodsStat.getPackageCount(), BigDecimal.ZERO) == 0
                && !StringUtils.isEmpty(pharmacyGoodsStat.getGoodsId())
                && MathUtils.wrapBigDecimalCompare(goodsOps.getAddPieceCountSuccess(), BigDecimal.ZERO) != 0
                && changeZeroToPositiveGoodsStatList != null
        ) {
            changeZeroToPositiveGoodsStatList.add(pharmacyGoodsStat.getGoodsId());
        }
        goodsOps.updateClinicGoodsStat(clinicConfig, pharmacyGoodsStat, pharmacySpuGoodsStat, clinicGoodsStat, clinicSpuGoodsStat, chainGoodsStat, chainSpuGoodsStat); //先更新GoodsStat
        // After 非0 -> 0
        if (MathUtils.wrapBigDecimalCompare(pharmacyGoodsStat.getPieceCount(), BigDecimal.ZERO) == 0
                && MathUtils.wrapBigDecimalCompare(pharmacyGoodsStat.getPackageCount(), BigDecimal.ZERO) == 0
                && !StringUtils.isEmpty(pharmacyGoodsStat.getGoodsId())
                && MathUtils.wrapBigDecimalCompare(goodsOps.getAddPieceCountSuccess(), BigDecimal.ZERO) != 0
                && changeZeroToPositiveGoodsStatList != null
        ) {
            changeZeroToPositiveGoodsStatList.add(pharmacyGoodsStat.getGoodsId());
        }
        List<GoodsStat> thisTimeChangeGoodsStatList = new ArrayList<>();
        if (rocketMqProducer != null) { // 写redis
            if (pharmacyGoodsStat.getEsNeedSyncFlag() == GoodsUtils.SwitchFlag.ON) {
                GoodsStat finalPharmacyGoodsStat = pharmacyGoodsStat;
                GoodsUtils.runAfterTransaction(() -> rocketMqProducer.sendGoodsStatUpdateMessage(cisClinicService, clinicConfig, finalPharmacyGoodsStat.getGoodsId(), goodsRedisUtils));
            }
            if (pharmacyGoodsStat.getId() == null || !setAddYetIdList.contains(pharmacyGoodsStat.getId())) {
                thisTimeChangeGoodsStatList.add(pharmacyGoodsStat);
            }
        }
        if (pharmacySpuGoodsStat != null) { // 写redis
            if (pharmacySpuGoodsStat.getId() == null || !setAddYetIdList.contains(pharmacySpuGoodsStat.getId())) {
                thisTimeChangeGoodsStatList.add(pharmacySpuGoodsStat);
            }
        }
        if (clinicGoodsStat != null) { // 写redis
            if (clinicGoodsStat.getId() == null || !setAddYetIdList.contains(clinicGoodsStat.getId())) {
                thisTimeChangeGoodsStatList.add(clinicGoodsStat);
            }
        }
        if (clinicSpuGoodsStat != null) { // 写redis
            if (clinicSpuGoodsStat.getId() == null || !setAddYetIdList.contains(clinicSpuGoodsStat.getId())) {
                thisTimeChangeGoodsStatList.add(clinicSpuGoodsStat);
            }
        }
        if (chainGoodsStat != null) { // 写redis
            if (chainGoodsStat.getId() == null || !setAddYetIdList.contains(chainGoodsStat.getId())) {
                thisTimeChangeGoodsStatList.add(chainGoodsStat);
            }
        }
        if (chainSpuGoodsStat != null) { // 写redis
            if (chainGoodsStat.getId() == null || !setAddYetIdList.contains(chainSpuGoodsStat.getId())) {
                thisTimeChangeGoodsStatList.add(chainSpuGoodsStat);
            }
        }
        /*
         * 每个goods取清理一次，避免太大
         * */
        if (thisTimeChangeGoodsStatList.size() > 0) {
            goodsRedisUtils.writeGoodsStatToRedis(clinicConfig.getChainId(), thisTimeChangeGoodsStatList);
            changeGoodsStatList.addAll(thisTimeChangeGoodsStatList);
        }
        return isCreated ? pharmacyGoodsStat : null;
    }

    public static GoodsPurchaseOrder anotherAsynTransToReflushPurchaseOrderImpl(GoodsPurchaseOrderRepository goodsPurchaseOrderRepository,
                                                                                GoodsPurchaseOrderMapper goodsPurchaseOrderMapper,
                                                                                String chainId,
                                                                                Long purchaseOrderId,
                                                                                String operatorId,
                                                                                CisClinicService clinicService) {
        if (purchaseOrderId == null) {
            return null;
        }


        GoodsPurchaseOrder purchaseOrder = goodsPurchaseOrderRepository.findByChainIdAndIdAndOrderType(chainId, purchaseOrderId, GoodsPurchaseOrder.OrderType.PURCHASE_ORDER);
        if (Objects.isNull(purchaseOrder)) {
            return null;
        }
        /**
         * 商城采购单不要刷
         * */
        if (purchaseOrder.getOrderType() == GoodsPurchaseOrder.OrderType.ABC_PURCHASE_ORDER) {
            return purchaseOrder;
        }
        List<UpdateRelatedOrderStatusCount> countList = goodsPurchaseOrderMapper.countWaitingDeliveryOrderCount(purchaseOrder.getChainId(), purchaseOrder.getId());

        List<UpdateRelatedOrderStatusCount> countListStock = goodsPurchaseOrderMapper.countWaitingInOrderByPurchaseOrderIdCount(purchaseOrder.getChainId(), purchaseOrder.getId());
        // 总入库成本
        purchaseOrder.setInOrderTotalCost(countListStock.stream().filter(it -> it.getStatus() == GoodsStockInOrder.GoodsStockInOrderStatus.FINISHED).map(it -> it.getTotalAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));

        /**
         * 有多个配货单，只要又一个配货单是 待发货
         * 那么整个要货单就是待发货状态
         * */
        if (countList.stream().anyMatch(it -> it.getStatus() == GoodsStockDeliveryOrder.DeliveryStatus.WAITING_RECEIVE)) {
            purchaseOrder.setStatus(GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.MALL_WAITING_SEND_GOODS);
            UserFillUtils.fillLastModifiedBy(purchaseOrder, operatorId);
            goodsPurchaseOrderRepository.save(purchaseOrder);
            return purchaseOrder;
        }
        /**
         * 有多个配货单，没有 待发货
         * 有一个单子的状态是 待收货 那么这个单子就是
         * */
        //if (countList.stream().anyMatch(it -> it.getStatus() == GoodsStockDeliveryOrder.DeliveryStatus.WAIT_RECEIVE_GOODS)) {
        //    purchaseOrder.setStatus(GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.MALL_WAITING_RECV_GOODS);
        //    UserFillUtils.fillLastModifiedBy(purchaseOrder, operatorId);
        //    goodsPurchaseOrderRepository.save(purchaseOrder);
        //    return purchaseOrder;
        //}


        countList = goodsPurchaseOrderMapper.countWaitingReceiveOrderCount(purchaseOrder.getChainId(), purchaseOrder.getId());

        if (countList.stream().anyMatch(it -> it.getStatus() == GoodsStockReceiveOrder.ReceiveStatus.WAIT_RECEIVE)) {
            purchaseOrder.setStatus(GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.MALL_WAITING_RECV_GOODS);
            UserFillUtils.fillLastModifiedBy(purchaseOrder, operatorId);
            goodsPurchaseOrderRepository.save(purchaseOrder);
            return purchaseOrder;
        }
        int todoCount = goodsPurchaseOrderMapper.countWaitingInspectOrderByPurchaseOrderIdCount(purchaseOrder.getChainId(), purchaseOrder.getId());
        if (todoCount > 0) {
            purchaseOrder.setStatus(GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.WAITING_ACCEPT_GOODS);
            UserFillUtils.fillLastModifiedBy(purchaseOrder, operatorId);
            goodsPurchaseOrderRepository.save(purchaseOrder);
            return purchaseOrder;
        }
        if (countListStock.stream().anyMatch(it -> it.getStatus() < GoodsStockInOrder.GoodsStockInOrderStatus.FINISHED)) {
            purchaseOrder.setStatus(GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.WAITING_STOCK_IN);
            UserFillUtils.fillLastModifiedBy(purchaseOrder, operatorId);
            goodsPurchaseOrderRepository.save(purchaseOrder);
            return purchaseOrder;
        }
        ClinicConfig clinicConfig = clinicService.getClinicConfig(purchaseOrder.getApplicantOrganId());
        if (clinicConfig.isAbcPharmacy()) {
            purchaseOrder.setStatus(GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.STOCK_IN);
        } else {
            if (purchaseOrder.getOrderType() == GoodsPurchaseOrder.OrderType.PURCHASE_ORDER) {
                purchaseOrder.setStatus(GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.AUDIT_PASS);
            } else {
                //TODO ... ABC商城的不要刷
            }
        }
        UserFillUtils.fillLastModifiedBy(purchaseOrder, operatorId);
        goodsPurchaseOrderRepository.save(purchaseOrder);
        if (purchaseOrder.isOrderStable() && purchaseOrder.getOrderType() != GoodsPurchaseOrder.OrderType.CLAIM_ORDER) {
            goodsPurchaseOrderMapper.cleanGoodsStatPurchaseOrderCountByOrder(purchaseOrder.getChainId(), purchaseOrder.getApplicantOrganId(), purchaseOrder.getId());
            if (clinicService != null && !clinicConfig.isSingleMode()) {
                goodsPurchaseOrderMapper.cleanGoodsStatPurchaseOrderCountByOrder(purchaseOrder.getChainId(), GoodsUtils.WHOLE_CHAIN_ID, purchaseOrder.getId());
            }
        }
        /**
         * 走到这里
         * 代表 这个配送单的状态变更，不会导致要货单的状态变更
         * 要货单保持之前状态
         * */
        return purchaseOrder;


    }

    public static void anotherAysnClaimOrderStatusUpdateImpl(GoodsPurchaseOrderRepository goodsPurchaseOrderRepository,
                                                             GoodsPurchaseOrderMapper goodsPurchaseOrderMapper,
                                                             String chainId,
                                                             Long claimOrderId,
                                                             String operatorId,
                                                             CisClinicService clinicService) {
        if (claimOrderId == null) {
            return;
        }
        GoodsPurchaseOrder claimOrder = goodsPurchaseOrderRepository.findByChainIdAndIdAndOrderType(chainId, claimOrderId, GoodsPurchaseOrder.OrderType.CLAIM_ORDER);
        if (claimOrder == null) {
            return;
        }
        if (claimOrder.getStatus() == GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.AUDIT_PASS) {
            return;
        }
        sLogger.info("anotherAysnClaimOrderStatusUpdateImpl update, claimOrder={}.", JsonUtils.dump(claimOrder));
        claimOrder.setStatus(GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.AUDIT_PASS);
        UserFillUtils.fillLastModifiedBy(claimOrder, operatorId);
        goodsPurchaseOrderRepository.save(claimOrder);
        //List<UpdateRelatedOrderStatusCount> claimOrderByPurchaseOrderIdCount = goodsPurchaseOrderMapper.countWaitingClaimOrderByPurchaseOrderIdCount(chainId, claimOrderId);
        //if (claimOrderByPurchaseOrderIdCount.stream().anyMatch(it -> it.getStatus() == GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.MALL_WAITING_RECV_GOODS)) {
        //    claimOrder.setStatus(GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.MALL_WAITING_RECV_GOODS);
        //    UserFillUtils.fillLastModifiedBy(claimOrder, operatorId);
        //    goodsPurchaseOrderRepository.save(claimOrder);
        //    return;
        //}
        //if (claimOrderByPurchaseOrderIdCount.stream().anyMatch(it -> it.getStatus() == GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.WAITING_ACCEPT_GOODS)) {
        //    claimOrder.setStatus(GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.WAITING_ACCEPT_GOODS);
        //    UserFillUtils.fillLastModifiedBy(claimOrder, operatorId);
        //    goodsPurchaseOrderRepository.save(claimOrder);
        //    return;
        //}
        //if (claimOrderByPurchaseOrderIdCount.stream().anyMatch(it -> it.getStatus() == GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.WAITING_STOCK_IN)) {
        //    claimOrder.setStatus(GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.WAITING_STOCK_IN);
        //    UserFillUtils.fillLastModifiedBy(claimOrder, operatorId);
        //    goodsPurchaseOrderRepository.save(claimOrder);
        //    return;
        //}
        //ClinicConfig clinicConfig = clinicService.getClinicConfig(claimOrder.getApplicantOrganId());
        //if (clinicConfig.isAbcPharmacy()) {
        //    claimOrder.setStatus(GoodsPurchaseOrder.PurchaseAndClaimOrderStatus.STOCK_IN);
        //    UserFillUtils.fillLastModifiedBy(claimOrder, operatorId);
        //    goodsPurchaseOrderRepository.save(claimOrder);
        //}
    }

}
