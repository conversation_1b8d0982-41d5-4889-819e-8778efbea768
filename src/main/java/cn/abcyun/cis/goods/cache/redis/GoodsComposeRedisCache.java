package cn.abcyun.cis.goods.cache.redis;

import cn.abcyun.cis.goods.model.GoodsComposeExtendInfo;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 存放到Redis里面的套餐组合项目
 * 1.三合一需求后，费用项的构成读redis较多读db的量太大了
 * 2.redis数据结构组织
 * GoodsInfoRedisCache是一个goods的RedisCache
 * 里面会追加一个成员变量list ，存放这个Goods的所有套餐子项
 * 或者费用子项 --产品设计上每个子店费用项会是不一样的
 * <p>
 * 每个门店的子店自主定价 也直接全部存到这个goods的Goods里面
 * (套餐自主定价的不会太多)
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoodsComposeRedisCache {

    @JsonIgnore
    private String chainId;

    /**
     * 套餐相关
     * 2025.06 套餐，子项对重复项目的支持，费用项里面可以加入重复goods了，只是价格不一样
     * v2_goods_compose_opt id
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;
    /**
     * 诊所id，总部为空，子店有值(子店单独管理对码)
     * 2023.07 三合一 composeType = 10  每个子店可以单独对码
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonProperty("a")
    private String clinicId;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonProperty("b")
    private String parentGoodsId; // '商品id',
    /**
     * goodsId和goodsParentId是主键
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonProperty("c")
    private String goodsId; // '商品id',


    /**
     * 类型0 套餐/检查检验 10费用项
     */
    @JsonInclude(JsonInclude.Include.NON_NULL) // null 0  == 0
    @JsonProperty("d")
    private int composeType;
    @JsonProperty("e")
    private int composeSort = GoodsUtils.SortFlag.FIRST_ORDER;//套餐项目里面排序字段，升序
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("f")
    private BigDecimal composePackageCount;//套餐内包含大单位数量',
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("g")
    private BigDecimal composePieceCount;// '套餐内包含小单数据量',
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("h")
    private BigDecimal composePackagePrice;//'套餐内大单位定价',
    /**
     * 手术等级
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonProperty("i")
    private String surgeryGradeCode;
    /**
     * 手术切口愈合等级代码
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonProperty("j")
    private String surgeryIncisionHealingCode;
    /**
     * 手术操作身体部位代码
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonProperty("k")
    private String surgerySiteCode;
    /**
     * 操作编码
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonProperty("l")
    private String operationCode;
    /**
     * 扩展字段
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("m")
    private GoodsComposeExtendInfo extendInfo;
    /**
     * 套餐子项有自主定价，用子店的自主定价
     * <p>
     * 套餐子项如果有自主定价，理论上套餐母项目，应该也有一个GoodsPirce
     * <p>
     * 子项价格的累加不会实时算
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public BigDecimal getComposePackagePrice() {
        if (goodsComposePriceRedisCache != null) {
            return goodsComposePriceRedisCache.getComposePackagePrice();
        }
        return this.composePackagePrice;
    }

    @JsonIgnore
    public BigDecimal getChainComposePackagePrice() {
        return composePackagePrice;
    }

    @JsonIgnore
    public BigDecimal getChainComposePiecePrice() {
        return composePiecePrice;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("n")
    private BigDecimal composePiecePrice;//'套餐内小单位定价', 套餐按拆零价格售卖的小包价格

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public BigDecimal getComposePiecePrice() {
        if (goodsComposePriceRedisCache != null) {
            return goodsComposePriceRedisCache.getComposePiecePrice();
        }
        return this.composePiecePrice;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("o")
    private BigDecimal composePrice; //这个物资在套餐内的的总价

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public BigDecimal getComposePrice() {
        if (goodsComposePriceRedisCache != null) {
            return goodsComposePriceRedisCache.getComposePrice();
        }
        return this.composePrice;

    }

    @JsonInclude(JsonInclude.Include.NON_NULL) //null 0 == 0
    @JsonProperty("p")
    private Short composeUseDismounting; //这个套餐怎么卖，通拆零销售的字段一样
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("q")
    private BigDecimal composeFractionPrice; // 套餐单项零头金额，按比例计算单价后再反算总价与按比例算的总价的零头金额


    /**
     * 套餐子项的自主定价
     */
    @JsonIgnore
    private GoodsComposePriceRedisCache goodsComposePriceRedisCache;
    /**
     * 套餐子项的Goods，
     * 套餐子项的Goods需要额外加载
     */
    @JsonIgnore
    private GoodsRedisCache goodsRedisCache;

    @JsonIgnore
    public void initSubClinicPrice(GoodsComposePriceRedisCache goodsComposePriceRedisCache) {
        this.goodsComposePriceRedisCache = goodsComposePriceRedisCache;
    }

    @JsonIgnore
    public void initSubGoods(GoodsRedisCache goodsRedisCache) {
        this.goodsRedisCache = goodsRedisCache;
    }
}
