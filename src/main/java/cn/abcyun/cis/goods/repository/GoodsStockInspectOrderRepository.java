package cn.abcyun.cis.goods.repository;

import cn.abcyun.cis.goods.entity.GoodsStockInspectOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

/**
 * 验货单(GoodsStockInspectOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-11 18:07:26
 */
public interface GoodsStockInspectOrderRepository extends JpaRepository<GoodsStockInspectOrder, Long> {
    @Query(nativeQuery = true, value = "select " +
            "  max(order_no) " +
            " from  v2_goods_stock_inspect_order v2po " +
            " where  v2po.chain_id =:clinicId " +
            "   and v2po.order_no like :prefix% ")
    List<Object> maxOrderNo(@Param("clinicId") String clinicId, @Param("prefix") String prefix);
    List<GoodsStockInspectOrder> findByChainIdAndOrderNoOrderByIdAsc(String chainId, String orderNo);

    GoodsStockInspectOrder findByChainIdAndIdAndIsDeleted(String chainId, Long id, int isDeleted);
    Optional<GoodsStockInspectOrder> findByChainIdAndReceiveOrderId(String chainId, Long receiveOrderId);

    List<GoodsStockInspectOrder> findAllByChainIdAndToClinicIdAndSupplierIdAndIsDeleted(String chainId, String toClinicId, String supplierId, int isDeleted);
    List<GoodsStockInspectOrder> findAllByChainIdAndToClinicIdAndInspectByAndIsDeleted(String chainId, String toClinicId, String inspectBy, int isDeleted);
}

