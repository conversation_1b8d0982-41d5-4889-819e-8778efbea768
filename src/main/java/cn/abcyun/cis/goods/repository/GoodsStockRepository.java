package cn.abcyun.cis.goods.repository;

import cn.abcyun.cis.goods.model.GoodsStock;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Collection;
import java.util.List;

/**
 * 进销存 GoodsStock id解释
 * id           ---> stockId 每条记录有自己唯一的id,通过这个id区分调拨多次 不一样的goodsStock
 * 数据库自增长
 * batch_id     ---> 批次Id  入库后唯一 要通过clinicId+起来保证门店之间的，同一batchId的批次不会窜
 * 等于 第一次入库GoodsStock的Id
 * batch_no     ---> 批次号 用户手动输入的批次号 入库单上带入的，可以修改，修改后所有调拨的GoodsStock都会跟着改
 * 可以为空，如果用户不填就是空
 */
public interface GoodsStockRepository extends JpaRepository<GoodsStock, Long> {


    /**
     * 按StockId 查批次列表的库存信息
     * 用途：goodsStockLog查回来的进销存log的来反查GoodsStock
     *
     * @param stockIdList GoodsStock的StockId
     */
    List<GoodsStock> findAllByIdIn(Collection<Long> stockIdList);


    /**
     * 按批次Id来查，某个门店内的 批次列表的库存信息
     *
     * @param clinicId    门店ID
     * @param batchIdList GoodsStock的批次Id
     * @return 每一个批次存在多余一条的情况，议价调拨成本价会不一样
     * DebugNo : 10
     */
    List<GoodsStock> findAllByBatchIdInAndChainId(Collection<Long> batchIdList, String chainId);
    List<GoodsStock> findAllByBatchIdInAndClinicId(Collection<Long> batchIdList, String clinicId);

    /**
     * DebugNo : 9
     **/
    @Query("select u from GoodsStock u where u.clinicId = :clinicId and  u.batchId in :batchIdList  and ( u.pieceCount != :pieceCount or u.packageCount != :packageCount )")
    List<GoodsStock> queryGoodsStockByClinicIdAndBatchIdList(@Param("batchIdList") List<Long> batchIdList, @Param("clinicId") String clinicId, @Param("pieceCount") BigDecimal pieceCount, @Param("packageCount") BigDecimal packageCount);

    /**
     * 按批次Id来查，全连锁下 批次列表的库存信息
     *
     * @param chainId     连锁ID
     * @param batchIdList GoodsStock的批次Id
     * @return 1.调用者还要区分门店，2.每一个批次存在多余一条的情况，议价调拨成本价会不一样
     * @note 应用场景：拉取出库列表，调拨列表，盘点列表，单据里面有按批次出库调拨盘点。这个接口拉出所有批次
     * * DebugNo : 8
     */
    List<GoodsStock> findAllByBatchIdIn(Collection<Long> batchIdList);

    List<GoodsStock> findAllByChainIdAndClinicId(String chainId, String clinicId);

    /**
     * 按入库单条目ID或出库单条目Id ，把对应goodsStock加载出来
     */
    List<GoodsStock> findAllByStockInIdInAndChainIdAndClinicIdAndFrom(List<Long> stockInIdList, String chainId, String clinicId, int from);

    /**
     * 把入库单的所有相关的goodsStock找出来 包括所有的调拨出去的goodsStock 加载出来
     * DebugNo : 7
     */
    List<GoodsStock> findAllByOriginStockInIdInAndChainId(List<Long> stockInIdList, String chainId);

    /**
     * DebugNo : 6
     */
    List<GoodsStock> findAllByChainIdAndGoodsId(String chainId, String goodsId);

    /**
     * 一下几个API 查成本
     */
    List<GoodsStock> findAllByChainIdAndGoodsIdAndPharmacyType(String chainId, String goodsId, int pharmacyType);

    List<GoodsStock> findAllByChainIdAndClinicIdAndGoodsIdAndPharmacyType(String chainId, String ClinicId, String goodsId, int pharmacyType);

    List<GoodsStock> findAllByChainIdAndGoodsIdAndPharmacyNo(String chainId, String goodsId, int pharmacyNo);

    List<GoodsStock> findAllByChainIdAndClinicIdAndGoodsIdAndPharmacyNo(String chainId, String ClinicId, String goodsId, int pharmacyNo);

    List<GoodsStock> findAllByChainIdAndClinicIdAndGoodsIdInAndStatusIn(String chainId, String ClinicId, List<String> goodsIdList, List<Integer> status);

    List<GoodsStock> findAllByChainIdAndGoodsIdInAndStatusIn(String chainId, List<String> goodsId, List<Integer> status);
    /**
     * 查询goodslist的库存信息
     * 过滤条件：获取库存大于0的stock
     * status == 0 可用，等有20状态的需求的时候再用in
     */
    /***
     * [StockManager][门店][指定药房][库存大于0]
     * DebugNo : 1
     * */
    @Query("select u from GoodsStock u where u.chainId = :chainId and  u.goodsId in :goodsIdList  and u.clinicId = :clinicId and (u.pieceCount != :pieceCount or u.packageCount != :packageCount) and u.pharmacyType = :pharmacyType")
    List<GoodsStock> queryStockInClinicAndNonEmpty(@Param("chainId") String chainId,
                                                   @Param("goodsIdList") List<String> goodsIdList,
                                                   @Param("clinicId") String clinicId,
                                                   @Param("pieceCount") BigDecimal pieceCount,
                                                   @Param("packageCount") BigDecimal packageCount,
                                                   @Param("pharmacyType") int pharmacyType);

    /**
     * DebugNo : 2
     */
    @Query("select u from GoodsStock u where u.chainId = :chainId and  u.goodsId in :goodsIdList  and u.clinicId != :clinicId and (u.pieceCount != :pieceCount or u.packageCount != :packageCount) and u.pharmacyType = :pharmacyType")
    List<GoodsStock> queryStockNotTheClinicAndNonEmpty(@Param("chainId") String chainId,
                                                       @Param("goodsIdList") List<String> goodsIdList,
                                                       @Param("clinicId") String clinicId,
                                                       @Param("pieceCount") BigDecimal pieceCount,
                                                       @Param("packageCount") BigDecimal packageCount,
                                                       @Param("pharmacyType") int pharmacyType);

    /***
     * [StockManager][所有门店][指定药房][有库存的]
     * DebugNo : 3
     * */
    @Deprecated
    @Query("select u from GoodsStock u where  u.chainId = :chainId and u.goodsId in :goodsIdList  and (u.pieceCount != :pieceCount or u.packageCount != :packageCount) and u.pharmacyType = :pharmacyType")
    List<GoodsStock> queryStockInChainAndNonEmptyStock(
            @Param("chainId") String chainId,
            @Param("goodsIdList") List<String> goodsIdList,
            @Param("pieceCount") BigDecimal pieceCount,
            @Param("packageCount") BigDecimal packageCount,
            @Param("pharmacyType") int pharmacyType
    );

    /**
     * 本次先发锁库，锁库漏加加载问题不大，不影响进销存
     * Redis拉到内存里面，直接用GoodsId，不要带chainId，避免药诊互通传错chainId，加载到redis里面导致后面批次都没有的情况发生
     */
    List<GoodsStock> findAllByGoodsIdInAndInnerHasStockAndPharmacyType(List<String> goodsIdList, int innerHasStock, int pharmacyType);

    List<GoodsStock> findAllByGoodsIdInAndInnerHasStock(List<String> goodsIdList, int innerHasStock);
    List<GoodsStock> findAllByClinicIdAndGoodsIdInAndInnerHasStockAndPharmacyType(String clinicId, List<String> goodsIdList, int innerHasStock, int pharmacyType);
    List<GoodsStock> findAllByClinicIdAndGoodsIdIn(String clinicId, List<String> goodsIdList);

    /**
     * 删除的时候
     * DebugNo : 4
     */
//    @Query("select u from GoodsStock u where u.chainId = :chainId and  u.goodsId in :goodsIdList and ( u.pieceCount != 0 or u.packageCount != 0 )")
//    List<GoodsStock> queryHasGoodsStockListByGoodsIdList(@Param("chainId") String chainId, @Param("goodsIdList") List<String> goodsIdList);

    /**
     * DebugNo : 5
     */
    List<GoodsStock> findAllByChainIdAndIdIn(String chainId, List<Long> ids);

    @Modifying(clearAutomatically = true)
    @Transactional(rollbackFor = Exception.class)
    @Query(value = "update v2_goods_stock set maintenance_time = :maintenanceTime where chain_id = :chainId and " +
            "organ_id = :clinicId and batch_id in (:batchIdList)", nativeQuery = true)
    int updateGoodsStockBatchMaintenanceTime(@Param("chainId") String chainId, @Param("clinicId") String clinicId,
                                             @Param("batchIdList") List<Long> batchIdList,
                                             @Param("maintenanceTime") Instant maintenanceTime);

    List<GoodsStock> findAllByIdInAndChainId(List<Long> stockIdList, String chainId);

    List<GoodsStock> findByChainIdAndClinicIdAndOriginStockInIdIn(String chainId, String clinicId, List<Long> originalStockInIdList);

    List<GoodsStock> findAllByChainIdAndClinicIdAndBatchIdInAndPharmacyNo(String chainId, String clinicId,
                                                                          List<Long> batchIdList, int pharmacyNo);
}
