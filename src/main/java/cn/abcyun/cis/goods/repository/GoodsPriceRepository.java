package cn.abcyun.cis.goods.repository;

import cn.abcyun.cis.goods.model.GoodsPrice;
import cn.abcyun.cis.goods.model.GoodsPriceKey;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * goods在分店店设置的价格
 */
public interface GoodsPriceRepository extends JpaRepository<GoodsPrice, GoodsPriceKey> {


    /**
     * 获取一批goodsid在某个子店诊所的价格
     */

    List<GoodsPrice> findAllByGoodsIdInAndOrganId(List<String> goodsIds, String clinicId);

    /**
     * 加载子店的price
     */
    Optional<GoodsPrice> findByGoodsIdAndOrganId(String goodsId, String clinicId);

    List<GoodsPrice> findAllByGoodsId(String goodsId);

    Page<GoodsPrice> findAllByOrganId(String clinicId, Pageable pageable);
    Page<GoodsPrice> findAllByOrganIdAndPriceType(String clinicId, int priceType ,Pageable pageable);

    /**
     * 1.药店产品搞了个定价方式出来，这个定价方式下的自主定价完全不受开关控制
     * 2.产品
     */
    @Query("select u from GoodsPrice u where  u.organId =:organId and u.isDeleted = 0 and (u.individualPricingType is null or u.individualPricingType  = 0)   ")
    List<GoodsPrice> queryAllByOrganIdAndIndividualPricingType(@Param("organId") String organId);

    @Query("select u from GoodsPrice u where  u.organId =:organId and u.isDeleted = 0 and (u.individualPricingType is null or u.individualPricingType  = 0)  and u.priceType = 3 ")
    List<GoodsPrice> queryAllByOrganIdAndIndividualPricingTypeAndPriceType(@Param("organId") String organId);

    List<GoodsPrice> findAllByChainIdAndGoodsId(String chainId, String goodsId);

    List<GoodsPrice> findAllByChainIdAndGoodsIdIn(String chainId, Collection<String> goodsIds);

    @Modifying
    @Query("update GoodsPrice u set u.isDeleted = 1, u.lastModifiedDate = now(), u.lastModifiedUserId = :operatorId where u.goodsId in :goodsIdList")
    int deleteAllByGoodsIdIn(@Param("goodsIdList") List<String> goodsIdList, @Param("operatorId") String operatorId);

    default void delete(GoodsPrice goodsPrice, String operatorId) {
        if (goodsPrice == null) {
            return;
        }
        goodsPrice.setIsDeleted(1);
        goodsPrice.setLastModifiedDate(Instant.now());
        goodsPrice.setLastModifiedUserId(operatorId);
        save(goodsPrice);
    }

    default void deleteAll(List<GoodsPrice> goodsPrices, String operatorId) {
        if (CollectionUtils.isEmpty(goodsPrices)) {
            return;
        }
        goodsPrices.forEach(goodsPrice -> {
            goodsPrice.setIsDeleted(1);
            goodsPrice.setLastModifiedDate(Instant.now());
            goodsPrice.setLastModifiedUserId(operatorId);
        });
        saveAll(goodsPrices);
    }

    List<GoodsPrice> findAllByChainIdAndGoodsIdAndOrganIdIn(String chainId, String goodsId, List<String> organIds);

    List<GoodsPrice> findAllByChainIdAndGoodsIdInAndOrganIdIn(String chainId, List<String> goodsIds, List<String> organIds);

    List<GoodsPrice> findAllByOrganIdAndPharmacyTypeAndPharmacyNo(String clinicId, Integer pharmacyType, Integer pharmacyNo);
    List<GoodsPrice> findAllByOrganIdAndPharmacyTypeAndPharmacyNoIn(String clinicId, Integer pharmacyType, List<Integer> pharmacyNo);

    List<GoodsPrice> findAllByOrganIdAndGoodsIdAndPharmacyType(String clinicId, String goodsId, Integer pharmacyType);

    List<GoodsPrice> findAllByOrganIdInAndGoodsIdAndPharmacyType(List<String> clinicIds, String goodsId, Integer pharmacyType);
    List<GoodsPrice> findAllByOrganIdInAndGoodsIdInAndPharmacyType(List<String> clinicIds, List<String> goodsIds, Integer pharmacyType);
    List<GoodsPrice> findAllByOrganIdAndGoodsIdInAndPharmacyType(String clinicId, List<String> goodsIds, Integer pharmacyType);
    List<GoodsPrice> findAllByOrganIdAndGoodsIdInAndPharmacyTypeAndPharmacyNo(String clinicId, List<String> goodsIds, Integer pharmacyType, Integer pharmacyNo);


    List<GoodsPrice> findAllByOrganIdAndPharmacyNoAndGoodsIdInAndTargetTypeAndIsDeleted(String clinicId, int pharmacyNo, Collection<String> goodsIds, int targetType, int isDeleted);
}
