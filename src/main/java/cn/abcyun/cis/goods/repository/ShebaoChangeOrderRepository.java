package cn.abcyun.cis.goods.repository;

import cn.abcyun.cis.goods.model.ShebaoChangeOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 社保变更单据
 */
@Repository
public interface ShebaoChangeOrderRepository extends JpaRepository<ShebaoChangeOrder, Long> {

    Optional<ShebaoChangeOrder> findByChainIdAndIdAndIsDeleted(String chainId, Long id, int isDeleted);
    List<ShebaoChangeOrder> findByChainIdAndClinicIdAndOrderNoAndTypeAndIsDeleted(String chainId, String clinicId, String orderNo, int type , int isDeleted);

}

