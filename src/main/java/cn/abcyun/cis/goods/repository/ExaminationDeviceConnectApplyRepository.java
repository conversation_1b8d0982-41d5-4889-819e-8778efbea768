package cn.abcyun.cis.goods.repository;

import cn.abcyun.cis.goods.entity.ExaminationDeviceConnectApply;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 检验设备联机申请(v2_goods_examination_device_connect_apply)表数据库访问层
 *
 * <AUTHOR>
 * @date 2022-11-29 17:12:03
 */
@Repository
public interface ExaminationDeviceConnectApplyRepository extends JpaRepository<ExaminationDeviceConnectApply, Long> {

    List<ExaminationDeviceConnectApply> findAllByClinicIdAndStatusIn(String clinicId, List<Integer> statues);

}

