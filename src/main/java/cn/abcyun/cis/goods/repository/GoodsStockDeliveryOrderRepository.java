package cn.abcyun.cis.goods.repository;

import cn.abcyun.cis.goods.entity.GoodsStockDeliveryOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 配送单(GoodsStockDeliveryOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-11 18:07:27
 */
public interface GoodsStockDeliveryOrderRepository extends JpaRepository<GoodsStockDeliveryOrder, Long> {
    @Query(nativeQuery = true, value = "select " +
            "  max(order_no) " +
            " from  v2_goods_stock_delivery_order v2po " +
            " where  v2po.chain_id =:clinicId " +
            "   and v2po.order_no like :prefix% ")
    List<Object> maxOrderNo(@Param("clinicId") String clinicId, @Param("prefix") String prefix);

    List<GoodsStockDeliveryOrder> findByChainIdAndOrderNoOrderByIdAsc(String chainId, String orderNo);

    GoodsStockDeliveryOrder findByChainIdAndId(String chainId, Long id);
    List<GoodsStockDeliveryOrder> findAllByIdIn(List<Long> id);
}

