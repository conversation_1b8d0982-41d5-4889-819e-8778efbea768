package cn.abcyun.cis.goods.repository;

import cn.abcyun.cis.goods.entity.GoodsOrderNo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * 单号仓储层
 *
 * <AUTHOR>
 * @since 2024-02-24 16:48:22
 **/
public interface GoodsOrderNoRepository extends JpaRepository<GoodsOrderNo, Long> {

    @Query(nativeQuery = true, value = "select max(order_no), count(1) from v2_goods_order_no " +
            "where chain_id = :chainId and order_type = :orderType " +
            "and order_no > :beginOrderNo and order_no <= :endOrderNo ")
    List<Object> maxOrderNo(@Param("chainId") String chainId, @Param("orderType") int orderType,
                            @Param("beginOrderNo") long beginOrderNo, @Param("endOrderNo") long endOrderNo);

    List<GoodsOrderNo> findAllByChainIdAndOrderTypeAndOrderNoOrderByRelateIdAsc(String chainId, int orderType, Long orderNo);
}
