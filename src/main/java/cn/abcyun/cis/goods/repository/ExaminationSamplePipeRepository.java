package cn.abcyun.cis.goods.repository;

import cn.abcyun.cis.goods.model.ExaminationSamplePipe;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 检验样本容器(v2_goods_examination_sample_pipe)表数据库访问层
 *
 * <AUTHOR>
 * @date 2022-09-21 18:32:55
 */
@Repository
public interface ExaminationSamplePipeRepository extends JpaRepository<ExaminationSamplePipe, String> {

    List<ExaminationSamplePipe> findAllByChainIdAndIdInAndIsDeleted(String chainId, List<String> ids, int isDeleted);

    List<ExaminationSamplePipe> findAllByChainIdAndIsDeleted(String chainId,  int isDeleted);
    List<ExaminationSamplePipe> findAllByChainIdAndIsDeletedOrderByCreatedDesc(String chainId,  int isDeleted);
    List<ExaminationSamplePipe> findAllByChainIdOrderByCreatedDesc(String chainId);

    Optional<ExaminationSamplePipe> findByChainIdAndCodeAndIsDeleted(String chainId,String code, int isDeleted);

    Optional<ExaminationSamplePipe> findByChainIdAndIdAndIsDeleted(String chainId,String id, int isDeleted);
}

