package cn.abcyun.cis.goods.vo.backend;

import lombok.Data;

/**
 * 有变更
 * */
@Data
public class GoodsNotifyScGoodsModifyReq {
    //社保关注字段
    private String id; //goodsID
    private Short type;// '类型: 1,药品; 2,物资; 3,检查; 4,治疗; 5,挂号(保)',
    private String shebaoCode;
    private String standardCode;
    private String clinicId ;
    private String chainId;

    private int    needDeleteRelation = 0;
    /**
     * 定义三个常量来区三种hisgoods类型的变更
     * */
    public static final int GOODS_RELATION_NOT_CHANGED = 0;
    public static final int GOODS_RELATION_SPECIFIC_CHANGED = 1;
    public static final int GOODS_RELATION_DISABLE_CHANGED = 2;
}
