package cn.abcyun.cis.goods.vo.frontend.goodsinfo.price;

import cn.abcyun.cis.goods.model.GoodsPrice;
import cn.abcyun.cis.goods.model.GoodsSnapV3;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 单个goods的修详情
 */
@Data
public class ModifyPriceOrderItemView {

    /**
     * 前端js的整数的精度只有16位,这里必须给前端string，
     */
    private String id;
    @ApiModelProperty("折扣价会有多个，这个是调价组ID")
    private String itemId ;

    @ApiModelProperty("状态 0 等待调价生效 1 调价完成 2 调价失败 3 因为这个药品在调价中，产品要求这种药品也能加入调价单，但是不调价")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer status;

    private Long orderId;

    @ApiModelProperty("快照 改过价格后的")
    private GoodsSnapV3 goods;


    @ApiModelProperty("改价时快照成本价")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal packageCostPrice;
    @ApiModelProperty("改价时快照成本价")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal avgPackageCostPrice;

    @ApiModelProperty("最近供应商名字 改价时的快照")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String lastSupplierName;

    @ApiModelProperty("改前利润率")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal beforeProfitRat;
    @ApiModelProperty("改后利润率")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal profitRat;

    @ApiModelProperty("改前价格")
    private BigDecimal beforePackagePrice;
    @ApiModelProperty("改前拆零价格")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal beforePiecePrice;

    @ApiModelProperty("改 基准数据 (基于进价/社保限价 调时记录的是最近入库成本价/社保限价/药品平均成本)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal modifyBasePackagePrice;
    @ApiModelProperty("改 拆零 基准数据 (基于进价/社保限价 调时记录的是最近入库成本价/社保限价/药品平均成本)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal modifyBasePiecePrice;

    @ApiModelProperty("改 比例(基于modifyBasePackagePrice的比例，不是基于改前价格beforePackagePrice的比例)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal packageUpPercent;
    @ApiModelProperty("改 拆零 比例(基于modifyBasePiecePrice的比例，不是基于改前价格beforePiecePrice的比例)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal pieceUpPercent;

    @ApiModelProperty("改 动作类型   (单个goods还可以手动改，这里的opType和order表上opType可以不一样) 这个Goods 修改价格的类型 0 手动设置售价,1 按原进价进行比例调价，2按最近进价进行比例调价")
    private int packageOpType;
    @ApiModelProperty("改 拆零 动作类型  (单个goods还可以手动改，这里的opType和order表上opType可以不一样) 这个Goods 修改价格的类型 0 手动设置售价,1 按原进价进行比例调价，2按最近进价进行比例调价")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer pieceOpType;

    @ApiModelProperty("改后价格--可能是用户设置，也可能是计算的 前端只管拿来展示")
    private BigDecimal afterPackagePrice;
    @ApiModelProperty("改后拆零价格--可能是用户设置，也可能是计算的 前端只管拿来展示")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal afterPiecePrice;

    @ApiModelProperty("改前定价类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer beforePriceType;
    @ApiModelProperty("改后前定价类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer afterPriceType;

    @ApiModelProperty("改前加成比例")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal beforeMakeUpPercent;
    @ApiModelProperty("改后加成比例")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal afterMakeUpPercent;
    @ApiModelProperty("生效时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Instant effected;

    /**
     * 0 普通价格(price_type,piece_price,package_price,price_makeup_percent),1会员折扣价
     *
     * @see GoodsPrice.PriceTargetType
     */
    private int targetType;

    /**
     * 会员卡类型ID
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String memberTypeId;

    /**
     * 会员类型名称
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String memberTypeName;

    /**
     * 折扣类型 0 折扣， 1 特价 , 10 无单独会员价
     *
     * @see GoodsPrice.DiscountType
     */
    private Integer beforeDiscountType;

    /**
     * 修改前-会员折扣价-折扣率
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal beforeDiscountValue;

    /**
     * 折扣类型 0 折扣， 1 特价 , 10 无单独会员价
     *
     * @see GoodsPrice.DiscountType
     */
    private Integer afterDiscountType;

    /**
     * 会员折扣价-折扣率
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal afterDiscountValue;
}
