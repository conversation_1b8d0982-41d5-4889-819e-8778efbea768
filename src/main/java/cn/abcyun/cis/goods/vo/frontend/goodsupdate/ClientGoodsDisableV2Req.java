package cn.abcyun.cis.goods.vo.frontend.goodsupdate;

import cn.abcyun.cis.commons.amqp.message.goods.GoodsBroadMessage;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;


/**
 * 药品禁用
 * */
@Data
public class ClientGoodsDisableV2Req {
    private static final Logger sLogger = LoggerFactory.getLogger(ClientGoodsDisableV2Req.class);
    /**
     * http头
     * */
    @JsonIgnore
    private String chainId;
    @JsonIgnore
    private String clinicId;
    @JsonIgnore
    private String employeeId;
    @JsonIgnore
    private int clinicType;

    @JsonIgnore
    private String goodsId;

    @ApiModelProperty(value = "传true：没抛异常就直接进行操作，直接修改。\n" +
            "\t\t有组成套餐，抛异常 false/不传直接修改  传false/不传：直接修改")
    private Boolean throwExpIfComposed = false; //是否只做检查,不做实际的停用


    @ApiModelProperty(value = "禁用升级状态 0 正常状态 10 禁止入库和采购，销售完禁用 20 禁止入库采购和销售")
    private Integer v2DisableStatus = 0;

//    /**
//     * 特别指定要禁用的诊所
//     * 如果为空，为整个连锁禁用
//     * 禁用的逻辑和之前的逻辑保持一样，只是加入了子店停用自己了
//     * */
//    @JsonIgnore
//    @Setter(AccessLevel.NONE)
//    private String disableClinicId;

    /**
     * disable状态的变更，也是goods信息的变更，goods服务需要通知其他关联的服务good状态的变化
     * goods 端上传上来用于广播的消息。实现的原因：
     * 1.老的goodsnodejs修改药品资料就是用端上传上来的goods信息进行消息广播的
     * 2.GoodsMessage字段太多，相关服务太多，无法快速梳理出来，还是端上来传
     * */
    @ApiModelProperty(value = "修改为了发广播通知其他服务，终端传上来的消息体")
    private GoodsBroadMessage goods;

    /**
     * 请求参数检查
     */
    public void checkParameter() throws CisGoodsServiceException {

        if (StringUtils.isEmpty(goodsId) ) {
            sLogger.info("ClientGoodsDisableReq paramCheck 参数为空 goodsId={},chainId={}", goodsId, chainId);
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "请求参数错误:要指定停用商品ID");
        }
//        /**
//         * 子店能停用自己的
//         * */
//        if (clinicType == CisClinicType.CHAIN_BRANCH_CLINIC
//                && !StringUtils.isEmpty(disableClinicId)
//                && disableClinicId.compareTo(clinicId) != 0
//        ) {
//            sLogger.info("ClientGoodsDisableReq paramCheck 子店不能停用启用药品. clinicId={},clinicType={}", clinicId, clinicType);
//            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "请求参数错误:子店只能停启用本店药品");
//        }
//
//        /**
//         * 子店只停用自己 goods会加载自己子店的配置
//         * disableClinicId 主店，单店这个为空 会加载主店配置
//         * */
//        if (clinicType == CisClinicType.CHAIN_BRANCH_CLINIC && StringUtils.isEmpty(disableClinicId)) {
//            disableClinicId = clinicId;
//        }

    }
}
