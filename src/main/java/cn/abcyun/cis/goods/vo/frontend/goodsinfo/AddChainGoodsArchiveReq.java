package cn.abcyun.cis.goods.vo.frontend.goodsinfo;

import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-24 19:55:45
 */
@Data
public class AddChainGoodsArchiveReq {

    @JsonIgnore
    private String chainId;
    @JsonIgnore
    private String clinicId;
    @JsonIgnore
    private String employeeId;

    private List<String> goodsIdList;

    public void checkParam() {
        if (CollectionUtils.isEmpty(goodsIdList)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "goodsIdList不能为空");
        }
        if (goodsIdList.size() > 200) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "一次最多只能添加200个药品");
        }
    }
}
