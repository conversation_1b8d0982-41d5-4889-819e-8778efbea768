package cn.abcyun.cis.goods.vo.frontend.purchase;

import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-04 23:12:10
 */
@Data
public class AddClaimOrderToPurchasePlanReq {
    @JsonIgnore
    private String headerChainId;
    @JsonIgnore
    private String headerClinicId;
    @JsonIgnore
    private String headerEmployeeId;
    @JsonIgnore
    private int headerClinicType;
    @JsonIgnore
    private int headerViewMode;

    @JsonIgnore
    private Long claimOrderId;

    @ApiModelProperty(value = "采购计划id")
    private String planId;

    public void paramCheck() {
        if (StringUtils.isEmpty(planId)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "未指定采购计划");
        }
    }
}
