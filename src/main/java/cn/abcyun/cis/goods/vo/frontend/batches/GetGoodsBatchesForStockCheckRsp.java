package cn.abcyun.cis.goods.vo.frontend.batches;

import cn.abcyun.common.model.AbcListPage;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * 客户端盘点任务协议
 * 列表全量拉取，这里也是全量
 */
@Data
public class GetGoodsBatchesForStockCheckRsp {


    private List<BatchesGoodsStockItemForStockCheck> list = new ArrayList<>(); //这是药品的盘点列表
}
