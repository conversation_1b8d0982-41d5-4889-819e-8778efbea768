package cn.abcyun.cis.goods.vo.frontend;

import cn.abcyun.cis.goods.service.b2b.B2BGoodsPurchaseCarService;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class B2BShoppingPlanPreparePurchaseRsp {


    private String hisPurchaseOrderId; //HIS系统下单订单号

    private B2BGoodsPurchaseCarService.UnqualifiedPurchaseNumber errorPurchaseItems;
    private Integer buyGoodsCount;
    private int code;
    private String message="";
}
