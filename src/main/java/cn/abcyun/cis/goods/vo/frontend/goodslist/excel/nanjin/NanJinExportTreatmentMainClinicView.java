package cn.abcyun.cis.goods.vo.frontend.goodslist.excel.nanjin;

import cn.abcyun.cis.goods.vo.frontend.goodslist.excel.IExportNonStockListGoodsView;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 治疗理疗导出
 *
 * @2021-08
 */

@Data
public class NanJinExportTreatmentMainClinicView implements IExportNonStockListGoodsView {
    public static final int COLUMN_NUM = 15;
    @ExcelProperty(value="收费项目名")
    @ColumnWidth(15)
    private String  name;

    @ExcelProperty(value="项目编码")
    @ColumnWidth(15)
    private String  shortId;
    @ExcelProperty(value="执行划扣")
    @ColumnWidth(15)
    private String  needExecute;

    @ExcelProperty(value="类型")
    @ColumnWidth(15)
    private String  typeName;

    @ExcelProperty(value="单位")
    @ColumnWidth(20)
    private String  displaySpec;

    @ExcelProperty(value="成本价")
    @ColumnWidth(20)
    private BigDecimal  packageCostPrice;

    @ExcelProperty(value="销售价")
    @ColumnWidth(20)
    private BigDecimal  packagePrice;


    @ExcelProperty(value="状态")
    @ColumnWidth(20)
    private String  statusName;




    @ExcelProperty(value="医保码(国家)")
    @ColumnWidth(20)
    private String  shebaoNationalCode;
    /**
     * 南京地区特有
     * */
    @ExcelProperty(value="中心码")
    @ColumnWidth(20)
    private String  nanjingCentralCode;
    @ExcelProperty(value="医保类别(国家)")
    @ColumnWidth(20)
    private String  medicalFeeGradeNational;
    @ExcelProperty(value="医保限价(国家)")
    @ColumnWidth(20)
    private BigDecimal  shebaoNationalCurrentPriceLimited;
    @ExcelProperty(value="医保支付(国家)")
    @ColumnWidth(20)
    private String  shebaoNationalPayModeName;
    @ExcelProperty(value="儿童加收(国家)")
    @ColumnWidth(20)
    private String  shebaoNationalChildExtra;
    @ExcelProperty(value="过期时间(国家)")
    @ColumnWidth(20)
    private String  shebaoNationalCurrentExpiredTime;
    @ExcelProperty(value="限制说明(国家)")
    @ColumnWidth(20)
    private String  shebaoNationalUsage;


    @Override
    public void setDeviceName(String s) {

    }

    @Override
    public void setCombineType(String s) {

    }

    @Override
    public void setSampleType(String s) {

    }

    @Override
    public void setDeviceTypeName(String s) {

    }

    @Override
    public void setSamplingReq(String s) {

    }

    @Override
    public void setSamplingGroup(String s) {

    }

    @Override
    public void setInspectionInstitution(String s) {

    }

    @Override
    public void setGenderRestriction(String s) {

    }

    @Override
    public void setExecuteDepartment(String s) {

    }

    @Override
    public void setExternalSales(String s) {

    }
}
