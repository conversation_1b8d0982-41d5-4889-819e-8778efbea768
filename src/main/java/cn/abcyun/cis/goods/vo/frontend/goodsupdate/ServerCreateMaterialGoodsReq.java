package cn.abcyun.cis.goods.vo.frontend.goodsupdate;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.model.Goods;
import cn.abcyun.cis.goods.utils.GoodsStockUtils;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.vo.frontend.goodsinfo.GoodsSellerImportCheckDataValidRsp;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;


/**
 * 附加品 创建用到的字段
 * 放到一个类，不要继承，搞复杂了。
 * 本类是实际从客户端请求接收了的,用于药品物资修改的字段
 *
 * <AUTHOR> 2021.02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ServerCreateMaterialGoodsReq extends ServerCreateGoodsAbstractReq {
    // 供26个字段
    /*-----ID和类型 Part------*/
    /**
     * goodsId
     */
    protected String id;
    /**
     * 药品编码
     */
    protected String shortId;
    /**
     * 条码
     */
    private String barCode = "";

    public String getBarCode() {
        return barCode != null ? barCode : "";
    }

    @Override
    public List<TraceableCodeNoInfo> getTraceableCodeNoInfoList() {
        return null;
    }

    @Override
    public BigDecimal getInTaxRat() {
        return null;
    }

    @Override
    public BigDecimal getOutTaxRat() {
        return null;
    }


    /**
     * 药品类型 三元组 （type,subType)
     * typeId是药品类型三元组的ID
     * 已经被fix过，可以使用
     */
    private short type;
    private short subType;

    @JsonIgnore
    private String cMSpec;

    private int typeId;

    /**
     * 用户自定义分类ID/二级分类
     */
    private Long customTypeId;




    /*-----名字 Part------*/
    /**
     * 药品商品名
     */
    private String name = "";

    public String getName() {
        return name != null ? name : "";
    }

    private String medicineNmpn;
    /**
     * 药品厂家的长名-》短名自己转
     */
    private String manufacturerFull = "";

    public String getManufacturerFull() {
        return manufacturerFull != null ? manufacturerFull : "";
    }

    private BigDecimal packagePrice;

    private BigDecimal piecePrice;
    /*-----包装价格 Part------*/
    private int pieceNum;

    private String pieceUnit;

    private String packageUnit;


    /**
     * 是否拆0 0 ，1
     */
    private Short dismounting;
    /**
     * 附加品特有的规格
     */
    private String materialSpec = "";

    public String getMaterialSpec() {
        return materialSpec != null ? materialSpec : "";
    }

    /**
     * 附加品有是否对外销售
     */
    private Short isSell = YesOrNo.NO;

    /*-----医保社保 不是所有的药品类型有对码所以这里 只在子类里面定义 Part------*/

    private String cityCode;

    private String provinceCode;

    private String nationalCode;

    private String cityCodeId;

    private String provinceCodeId;

    private String nationalCodeId;

    /**
     * piecenum有修改，是否强制修改pieceNum还是抛出异常提示
     */
    private Boolean forceChangePieceNum;

    /**
     * 规格有效性检查
     */
    public void specificCheck() {
        if (pieceNum != GoodsConst.PIECE_ONE && packageUnit.compareTo(pieceUnit) == 0) {
            if (jenkinsGoodsImportItem != null) {
                jenkinsGoodsImportItem.addCreateErrorReason(GoodsSellerImportCheckDataValidRsp.ERROR_MED_PIECENUM, "pieceNum", "格式错误，制剂数量不为1时，制剂单位和包装单位不能相同");
            } else {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_FORMAT_ERROR,
                        GoodsUtils.goodsFullName(type, "", name) + "格式错误，制剂数量不为1时，制剂单位和包装单位不能相同");
            }
        }

        if (pieceNum == GoodsConst.PIECE_ONE
                && dismounting == GoodsConst.DismountingStatus.DISMOUNTING) {
            if (jenkinsGoodsImportItem != null) {
                jenkinsGoodsImportItem.addCreateErrorReason(GoodsSellerImportCheckDataValidRsp.ERROR_MED_PIECENUM, "pieceNum", "格式错误，剂量为1时，商品不允许拆零");
            } else {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_FORMAT_ERROR,
                        GoodsUtils.goodsFullName(type, "", name) + "格式错误，剂量为1时，商品不允许拆零");
            }

        }
    }

    /**
     * 参数检查，不用注解，方便文案的提示
     */
    public void parameterCheck(ClinicConfig clinicConfig) {

        super.parameterCheck(clinicConfig);

        if (StringUtils.isEmpty(packageUnit) || StringUtils.isEmpty(pieceUnit)) {
            if (jenkinsGoodsImportItem != null) {
                jenkinsGoodsImportItem.addCreateErrorReason(GoodsSellerImportCheckDataValidRsp.ERROR_MED_PACKAGEUNIT, "packageUnit", "请指定包装单位");
            } else {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER,
                        GoodsUtils.goodsFullName(type, "", name) + "请指定包装单位");
            }
        }

        if (dismounting != GoodsConst.DismountingStatus.PACKAGE
                && dismounting != GoodsConst.DismountingStatus.DISMOUNTING) {
            if (jenkinsGoodsImportItem != null) {
                jenkinsGoodsImportItem.addCreateErrorReason(GoodsSellerImportCheckDataValidRsp.ERROR_MED_DISMOUNTING, "dismounting", "请指定正确的包装类型");
            } else {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER,
                        GoodsUtils.goodsFullName(type, "", name) + "请指定正确的包装类型");
            }
        }

        if (pieceNum <= 0) {
            if (jenkinsGoodsImportItem != null) {
                jenkinsGoodsImportItem.addCreateErrorReason(GoodsSellerImportCheckDataValidRsp.ERROR_MED_PIECENUM, "pieceNum", "请指定有效的制剂数量,不能小于等于0");
            } else {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER,
                        GoodsUtils.goodsFullName(type, "", name) + "请指定有效的制剂数量,不能小于等于0");
            }
        }
        if (!GoodsUtils.checkFlagOn(getPriceType(), GoodsConst.PriceType.PKG_PRICE_MAKEUP)) {

            if (packagePrice != null) { //2位小数
                packagePrice = packagePrice.setScale(Goods.CLIENT_CHINESE_PRICE_PRECISION, RoundingMode.UP);
            }


            if (dismounting == GoodsConst.DismountingStatus.DISMOUNTING) {
                if (piecePrice != null) { //2位小数
                    piecePrice = piecePrice.setScale(Goods.CLIENT_CHINESE_PRICE_PRECISION, RoundingMode.UP);
                }
            } else {
                /**
                 * 医院医生开药都是按小单位出的，不管拆不拆零
                 * */
                if (packagePrice != null) {
                    piecePrice = GoodsStockUtils.divide(packagePrice, BigDecimal.valueOf(pieceNum), Goods.CLIENT_CHINESE_PRICE_PRECISION, RoundingMode.UP);
                }
            }

        }
        if (pieceNum == 0) {
            pieceNum = GoodsConst.PIECE_ONE;
        }


        /**
         * 如果客户端没传的值，整成默认值
         * 写null和写空值到db还是不一样的
         * */
        if (name == null) {
            name = "";
        }
        if (manufacturerFull == null) {
            manufacturerFull = "";
        }
        if (materialSpec == null) {
            materialSpec = "";
        }

    }

    @Override
    public String getMedicineCadn() {
        return null;
    }

    @Override
    public void setMedicineCadn(String cadn) {

    }

    /***
     *  无效
     */
    @Override
    public BigDecimal getPackageCostPrice() {
        return null;
    }

    @Override
    public void setPackageCostPrice(BigDecimal packageCostPrice) {

    }

    @Override
    public int getDefaultInOutTax() {
        return 0;
    }



    @Override
    public Integer getAntibiotic() {
        return null;
    }

    @Override
    public BigDecimal getDddOfAntibiotic() {
        return null;
    }

    @Override
    public String getUnitOfAntibiotic() {
        return null;
    }

    @Override
    public int getPriceType() {
        return GoodsConst.PriceType.PRICE;
    }

    @Override
    public BigDecimal getPriceMakeupPercent() {
        return null;
    }


    @Override
    public Long getProfitCategoryType() {
        return null;
    }

    @Override
    public Integer getOtcType() {
        return null;
    }

    @Override
    public Integer getDosageFormType() {
        return null;
    }

    @Override
    public BigDecimal getShelfLife() {
        return null;
    }

    @Override
    public Integer getBaseMedicineType() {
        return null;
    }

    @Override
    public Integer getMaintainType() {
        return null;
    }

    @Override
    public Integer getStorageType() {
        return null;
    }

    @Override
    public String getMha() {
        return null;
    }

    @Override
    public Integer getDangerIngredient() {
        return null;
    }

    @Override
    public String getPharmacologicId() {
        return null;
    }

    /***
     * 进销税优化 库存特有
     *https://www.xiaopiu.com/web/byId?type=project&id=605be8da19033c66f00f4532
     * 1.新建和修改goods  在 {@link ClientGoodsCreateReq#getUpdateInOutTaxRate()} 非空的情况下，后台将不认前端传的 传的 请求里面的{@link ClientGoodsCreateReq#getInTaxRat()} 和{@link ClientGoodsCreateReq#getOutTaxRat()}  字段
     * 2. effectedType == 0 月初生效，ScGoods服务会立即修改 goods税率 ，并异步实时的把 >= {@link ClientGoodsCreateReq.OptUpdateInOutTaxRate#getEffectedTime()}  }的 所有包含这个goods或这种类型的 入库单 出库单 调拨单 盘点单 结算单 全按修改税率重刷单据的不含税金额
     * 3. effectedType == 1 记录这条税率规则，不修改goods或类型税率。每天晚上定时任务，会检查但前日期是否 >= {@link ClientGoodsCreateReq.OptUpdateInOutTaxRate#getEffectedTime()}} 并把响应goods刷成这条配置的税率即可。
     * */
    private ClientGoodsCreateReq.OptUpdateInOutTaxRate updateInOutTaxRate;
    /**
     * 1.中药备注
     * 2.检验goods模版门店 的备注
     * 3.库存档案增加产地、备注
     * https://www.tapd.cn/22044681/prong/stories/view/1122044681001052404
     */
    private String remark = "";//  '',

    public String getRemark() {
        return remark != null ? remark : "";
    }

    /**
     * 库存档案增加产地、备注
     * https://www.tapd.cn/22044681/prong/stories/view/1122044681001052404
     * 长度 10
     */
    private String origin = "";

    public String getOrigin() {
        return origin != null ? origin : "";
    }

    /**
     * 批注文号开始效期
     * 这个数据从goods表移动到extendData里面：
     * 数据：（神奇定制）上海1400万goods，只有3000个goods不到设置了 批注文号开始效期
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String medicineNmpnStartExpiryDate;
    /**
     * 批注文号结束效期
     * 数据：（神奇定制）上海1400万goods，只有3000个goods不到设置了 批注文号开始效期
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String medicineNmpnEndExpiryDate;

    @ApiModelProperty("存储（存逗号分隔字符串）前端改动大")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String  storage;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<BusinessScope> businessScopeList;
}
