package cn.abcyun.cis.goods.vo.frontend;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class B2BWarningStocksReq {

    //分页
    private Integer offset ;
    private Integer limit ;
    private Integer turnoverDays; //周转天数
    @JsonIgnoreProperties
    private int headerClinicType;
    @JsonIgnore
    private int headerViewMode;
    @JsonIgnoreProperties
    private Integer page;//JPA的分页参数按0开始
    @JsonIgnoreProperties
    private List<HisJsonType> jsonTypes;
    private String jsonTypeString;

    //Api接口通过header设置进来，覆盖不会用请求里面的参数
    @JsonIgnore
    private String clinicId;
    @JsonIgnore
    private String chainId;
    @JsonIgnore
    private String employeeId;
    @JsonIgnoreProperties
    private List<Integer> typeIdList;
    private List<Long> customTypeIdList;
    private List<Long> otherCustomTypeTypeIdList;

    /**
     * 1:库存预警 2:日均销量大于0  3:效期预警 4: 毛利预警
     */
    @JsonIgnore
    private Integer goodsStatQueryType;

    @JsonIgnore
    private Integer queryType;


    @JsonProperty("typeId")
    @ApiModelProperty(value = "(用户多选的系统分类Id)系统分类Id，如果要查多个，可以同时写多个typeId参数,类型里面优先级最高(typeId 和customTypeId非空过后 将忽略 type subType)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Integer> typeId;
    @JsonProperty("customTypeId")
    @ApiModelProperty(value = "(用户多选的二级分类Id)二级分类的Id，如果要查多个，可以同时写多个customTypeId参数,类型里面优先级最高")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Long> customTypeId;

    @ApiModelProperty(value = "按药品标签查药品，如果要查多个tagId")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Long> tagId;
    @ApiModelProperty(value = "有限价")
    private Integer sbLimitPrice;
    @ApiModelProperty(value = "有挂网限价")
    private Integer sbListingPrice;

    @ApiModelProperty(value = "利润类型毛利类型 ")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> profitCategoryTypeList;
    @JsonIgnore
    private Long otherProfitCategoryType;

    @ApiModelProperty(value = "可售库存数量")
    private BigDecimal stockCount;
    @ApiModelProperty(value = "最低毛利率")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal minProfitRat;
    @ApiModelProperty(value = "最高毛利率")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal maxProfitRat;

    @JsonIgnore
    private int pharmacyNo;
    @JsonIgnore
    private Integer queryTagCount;

    @ApiModelProperty(value = "库存预警")
    private Integer stockWarn;
    @JsonIgnore
    private Integer fromRpc;

    public boolean hasQueryType() {
        if (goodsStatQueryType == null) {
            return false;
        }
        if (Arrays.stream(GoodsConst.GoodsStatQueryTypeEnum.values()).noneMatch(t -> t.getValue() == goodsStatQueryType)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "queryType错误");
        }
        return true;
    }
    /**
     * 查询类型，这里分一下，走不通的jpa实现更高的效率
     * */
    public int checkQueryType() throws CisGoodsServiceException {
        if (turnoverDays == null && CollectionUtils.isEmpty(typeIdList) && CollectionUtils.isEmpty(customTypeIdList)) {
            return QUERY_TYPE.TYPE_DEFAULT;
        } else {
            if (turnoverDays == null || turnoverDays <= 0) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER.getCode(),"请输入有效的周转天数");
            }
            if (CollectionUtils.isEmpty(typeIdList) &&  CollectionUtils.isEmpty(customTypeIdList)) {
                return QUERY_TYPE.TYPE_ONLY_TURNOVERDAYS;
            } else {
                return QUERY_TYPE.TYPE_ONLY_TYPEIDS_AND_TURNOVERDAYS;
            }
        }

    }

    public void parameterCheck() throws CisGoodsServiceException {
        if (StringUtils.isEmpty(clinicId) || StringUtils.isEmpty(chainId)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER);
        }
        if (limit == null ) { //未分页
            limit = 1000; //表示不分页，端上没支持分页
        }
        if (offset == null || offset < 0) {
            offset = 0;
        }
        //if (!CollectionUtils.isEmpty(customTypeIdList)) {
        //    otherCustomTypeTypeIdList = customTypeIdList.stream().filter(it -> it.compareTo(0L) < 0).map(it -> Math.abs(it)).collect(Collectors.toList());
        //    customTypeIdList = customTypeIdList.stream().filter(it -> it.compareTo(0L) > 0).collect(Collectors.toList());
        //}
    }

    /**
     * 按his类型的条件过滤
     * 实现方式:把type,subtype类型转成typeId
     * TODO：这里按type subyType cmspec来过滤类型觉得不太合理，主要是兼容端上老逻辑
     */
//    public List<Integer> getFilterJsonTypesList(GoodsSysTypeRepository goodsSysTypeRepository) {
//        if (StringUtils.isEmpty(jsonTypeString)) {
//            return null;
//        }
//
//        try {
//            JSONArray json = JSONArray.fromObject(decode(jsonTypeString));
//            jsonTypes = JSONArray.toList(json, HisJsonType.class);
//            if (CollectionUtils.isEmpty(jsonTypes)) {
//                return null;
//            }
//            List<GoodsSysType> filterTypesList = ServiceUtils.getHisGoodsSysTypesByJsonTypes(goodsSysTypeRepository, jsonTypes);
//            if (!CollectionUtils.isEmpty(filterTypesList)) {
//                return filterTypesList.stream().map(GoodsSysType::getId).distinct().collect(Collectors.toList());
//            }
//
//            return null;
//        } catch (Exception exp) {
//            return null;
//        }
//
//    }

    public static final class QUERY_TYPE {
        public static final int TYPE_DEFAULT = 0; //默认的按库存预警来搜索
        public static final int TYPE_ONLY_TURNOVERDAYS = 1; //只有周转天数的过滤，没选择类型，分开，sql是单表，会很快
        public static final int TYPE_ONLY_TYPEIDS_AND_TURNOVERDAYS = 2; //周转天数必然会有，+类型

    }

    public void init() {
        typeIdList = typeId;
        if (!CollectionUtils.isEmpty(customTypeId)) {
            otherCustomTypeTypeIdList = customTypeId.stream().filter(it -> it.compareTo(0L) < 0).map(it -> Math.abs(it)).collect(Collectors.toList());
            customTypeIdList = customTypeId.stream().filter(it -> it.compareTo(0L) > 0).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(profitCategoryTypeList)) {
            otherProfitCategoryType = profitCategoryTypeList.stream().filter(it -> it < 0).findFirst().orElse(null);
            profitCategoryTypeList = profitCategoryTypeList.stream().filter(it -> it >= 0).collect(Collectors.toList());
        }
    }

}
