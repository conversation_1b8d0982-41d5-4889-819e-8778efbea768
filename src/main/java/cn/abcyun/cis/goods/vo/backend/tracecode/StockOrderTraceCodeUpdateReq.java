package cn.abcyun.cis.goods.vo.backend.tracecode;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 库存单据追溯码更新请求
 *
 * <AUTHOR>
 * @since 29/07/25 下午7:52
 **/
@Data
public class StockOrderTraceCodeUpdateReq {

    /**
     * 连锁ID
     */
    private String chainId;

    /**
     * 诊所ID
     */
    private String clinicId;

    /**
     * 单据ID
     */
    private String orderId;

    /**
     * 操作人ID
     */
    private String employeeId;

    /**
     * 场景
     *
     * @see cn.abcyun.cis.goods.service.GoodsTraceCodeService.UpdateTraceCodeScene
     */
    private int scene;

    /**
     * 药房类型
     *
     * @see cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst.PharmacyType
     */
    private int pharmacyType;

    /**
     * 药房号
     */
    private int pharmacyNo;

    /**
     * 发药退药列表
     */
    private List<StockOrderTraceCodeUpdateReq.GoodsUseTraceCodeItem> list;

    @Data
    public static class GoodsUseTraceCodeItem {

        /**
         * 映射ID
         */
        private String keyId;

        /**
         * 追溯码号
         */
        private String no;

        /**
         * 变更小单位数量
         */
        private BigDecimal changePieceCount;

        /**
         * 变更大单位数量
         */
        private BigDecimal changePackageCount;

        /**
         * 就诊单ID
         */
        private String orderDetailId;

        /**
         * 商品ID
         */
        private String goodsId;

    }


}
