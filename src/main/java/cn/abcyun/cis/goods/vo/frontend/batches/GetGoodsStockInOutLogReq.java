package cn.abcyun.cis.goods.vo.frontend.batches;

import cn.abcyun.cis.commons.model.CisClinicType;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import lombok.Data;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.time.Instant;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Data
public class GetGoodsStockInOutLogReq {
    private String chainId;
    private String clinicId;
    private int clinicType;

    private Integer offset;
    private Integer limit;
    private String queryClinicId;
    private String goodsId;
    private String action;
    private Long   stockId;
    private List<String> actionList ;
    private Integer pharmacyNo;
    /**
     * 老逻辑
     *  1.在goodsId为非空的情况下 withTotalCount ==  1 表示的是取 goods的进销存的 变更总量的和
     *  2.看老代码入库取的是入库单上当前的入库值，不是进销存记录上记录的值
     *  确认：
     *  1.withTotalCount = 1 是否在传
     *  2.计算这个汇总值好像没什么意义
     *
     * */
    private Integer withTotalCount;
    private Integer type;
    private Integer subType;
    private String cMSpec;

    /**
     * 是否需要查询入库单是否被修改过，只在拉当个api的时候有用
     * */
    private int inOrderModifyFlag  = 0;
    private int scene =0;//0单个药品的进销存清单   1 诊所进销存 2 诊所进销存导出

    private HttpServletResponse httpServletResponse;

    //@ApiParam(value = "日期过滤三元组过滤的开始日期:YYYY-MM-DD(后台会按YYYY-MM-DD 00:00:00开始)")
    private Date begDate;

    public Instant getBeginInstant() {
        return begDate != null ? DateUtils.getStartTime(begDate).toInstant() : null;
    }

    //@ApiParam(value = "日期过滤三元组过滤的结束日期:YYYY-MM-DD(后台会按YYYY-MM-DD 23:59:5结束)")
    private Date endDate;

    public Instant getEndInstant() {
        return endDate != null ? DateUtils.getEndTime(endDate).toInstant() : null;
    }



    public void parameterCheck() {

        if (clinicType != CisClinicType.CHAIN_HEAD_CLINIC && StringUtils.isEmpty(queryClinicId)) {
            queryClinicId = clinicId;
        }
        if (pharmacyNo == null) {
            pharmacyNo = GoodsUtils.PHARMACY_NO_0;
        }

    }
}
