package cn.abcyun.cis.goods.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 收货单导出Excel视图
 */
@Data
public class GoodsDeliveryOrderItemExcelView {

    @ExcelProperty("商品编码")
    private String goodsCode;

    @ExcelProperty("商品名称")
    private String goodsName;

    @ExcelProperty("售价")
    private BigDecimal sellingPrice;

    @ExcelProperty("采购量")
    private BigDecimal purchaseCount;

    @ExcelProperty("收货数量")
    private BigDecimal receiveCount;

    @ExcelProperty("单位")
    private String unit;

    @ExcelProperty("进价")
    private BigDecimal costPrice;

    @ExcelProperty("金额")
    private BigDecimal amount;

    @ExcelProperty("生产批号")
    private String batchNo;

    @ExcelProperty("生产日期")
    private String productionDate;

    @ExcelProperty("有效日期")
    private String expiryDate;

    @ExcelProperty("追溯码")
    private String traceCode;
}
