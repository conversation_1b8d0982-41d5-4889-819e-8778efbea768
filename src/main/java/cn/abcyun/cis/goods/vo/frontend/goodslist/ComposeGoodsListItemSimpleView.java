package cn.abcyun.cis.goods.vo.frontend.goodslist;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.core.handler.ApiEncryptSerializer;
import cn.abcyun.cis.goods.model.Goods;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.vo.frontend.GoodsSimpleView;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 这个类是拉取诊疗项目列表返回的goodsView
 * 重构
 * @2021-01
 * */

@EqualsAndHashCode(callSuper = true)
@Data
public class ComposeGoodsListItemSimpleView extends GoodsSimpleView {
    /**
     * 用前面的七个字段生成可读的displaySpec
     * */
    @ApiModelProperty(value = "后台已经组装好的可直接显示的规格")
    @JsonSerialize(using = ApiEncryptSerializer.class)
    private String displaySpec;
    /**
     * 拼接的字符串名字
     * */
    @ApiModelProperty(value = "后台已经组装好的可直接显示的名字")
    @JsonSerialize(using = ApiEncryptSerializer.class)
    private String displayName;
    /**
     * 如果组合商品用的大单位，这个字段指示在大单位的数量
     * */
    @ApiModelProperty(value = "套餐的大包装数量")
    private BigDecimal composePackageCount = BigDecimal.ZERO;//套餐内包含大单位数量',
    /**
     * 如果组合商品用的小单位，这个字段指示在小单位的数量
     * */
    @ApiModelProperty(value = "套餐的小包装数量")
    private BigDecimal composePieceCount = BigDecimal.ZERO;// '套餐内包含小单数据量',
    /**
     * 如果组合商品用的大单位，这个字段指示在大单位的单价
     * */
//    @JsonIgnore
//    private BigDecimal composePackagePrice = BigDecimal.ZERO;//'套餐内大单位定价',
    /**
     * 套餐内的整体价格
     * */
//    @JsonIgnore
//    private BigDecimal composePrice = BigDecimal.ZERO; //这个物资在套餐内的的总价
    /**
     * 如果组合商品用的小单位，这个字段指示在小单位的单价
     * */
//    @JsonIgnore
//    private BigDecimal composePiecePrice = BigDecimal.ZERO;//'套餐内小单位定价', 套餐按拆零价格售卖的小包价格
    /**
     * 排序
     * */
    @ApiModelProperty(value = "套餐的排序")
    private int composeSort = GoodsUtils.SortFlag.FIRST_ORDER;//套餐项目里面排序字段，升序
    /**
     * 指示是大单位还是小单位
     * */
    @ApiModelProperty(value = "套餐里面的商品的拆零状态 0 不拆  1 拆零")
    private short composeUseDismounting = GoodsConst.DismountingStatus.PACKAGE; //这个套餐怎么卖，通拆零销售的字段一样

    @ApiModelProperty(value = "国家医保对码视图")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private GoodsShebaoView shebaoNationalView;
}
