package cn.abcyun.cis.goods.vo.frontend.order;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.cis.goods.vo.frontend.stockin.GoodsStockRowErrorDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Author: 何平
 * @Description: 配货单分组
 * @Since: 2024/1/18 11:39
 **/

@Data
@ApiModel(value = "DeliveryOrderItemGroupReq", description = "配货单分组请求")
public class DeliveryOrderItemGroupReq {

    @ApiModelProperty(value = "快照goods")
    private GoodsItem goods;
    @ApiModelProperty(value = "快照goods")
    private String goodsId;

    /**
     * Items
     */
    @ApiModelProperty("Items")
    private List<DeliveryOrderItemReq> list;

    public void parameterCheck(GoodsStockRowErrorDetail errorDetail, int rowIndex) {
        if (StringUtils.isEmpty(goodsId)) {
            errorDetail.addErrMsg(rowIndex, "无效的药品ID");
        }

        if (CollectionUtils.isEmpty(list)) {
            errorDetail.addErrMsg(rowIndex, "配货单列表不能为空");
        }
        /***
         * 参数检查
         * */
        for (int index = 0; index < list.size(); index++) {
            errorDetail.putGoodsStockRowErrorDetailItem(rowIndex, "第" + (rowIndex + 1) + "行", null);
            list.get(index).parameterCheck(errorDetail, rowIndex + 1);
        }
    }
}
