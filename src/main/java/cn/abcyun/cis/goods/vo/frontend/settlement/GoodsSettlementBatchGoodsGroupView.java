package cn.abcyun.cis.goods.vo.frontend.settlement;

import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.goods.model.Goods;
import cn.abcyun.cis.goods.model.GoodsSettlementBatch;
import cn.abcyun.cis.goods.model.GoodsSettlementOrder;
import cn.abcyun.cis.goods.model.GoodsStock;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 按批次结算，根据goodsId分组
 */
@Data
@Accessors(chain = true)
public class GoodsSettlementBatchGoodsGroupView {
    @ApiModelProperty("连锁id")
    private String chainId;
    @ApiModelProperty("商品id")
    private String goodsId;
    @ApiModelProperty("商品编码")
    private String shortId;
    @ApiModelProperty("商品展示名称")
    private String displayName;
    @ApiModelProperty("商品展示规格")
    private String displaySpec;
    @ApiModelProperty("厂家简称")
    private String manufacturer;
    @ApiModelProperty("厂家全称")
    private String manufacturerFull;
    @ApiModelProperty("小单位")
    private String pieceUnit;
    @ApiModelProperty("大单位")
    private String packageUnit;
    @ApiModelProperty("结算的批次信息")
    private List<GoodsSettlementBatchView> batches;

    public static GoodsSettlementBatchGoodsGroupView fromGroupBatches(Goods goods,
                                                                      List<GoodsSettlementBatch> goodsBatches,
                                                                      Map<Long, Long> batchIdToOtherSettlementOrderSupplierSellerId,
                                                                      List<GoodsStock> goodsStocks,
                                                                      Map<Long, Map<Integer, BigDecimal>> batchIdToSettlementStatusCountMap) {
        GoodsSettlementBatchGoodsGroupView view = new GoodsSettlementBatchGoodsGroupView();
        view.setChainId(goods.getOrganId());
        view.setGoodsId(goods.getId());
        view.setShortId(goods.getShortId());
        view.setDisplayName(GoodsUtils.goodsFullName(goods));
        view.setDisplaySpec(GoodsUtils.displaySpec(goods));
        view.setManufacturer(goods.getManufacturer());
        view.setManufacturerFull(goods.getManufacturerFull());
        view.setPieceUnit(goods.getPieceUnit());
        view.setPackageUnit(goods.getPackageUnit());
        Map<Long, List<GoodsStock>> batchIdToGoodsStocks = ListUtils.groupByKey(goodsStocks, GoodsStock::getBatchId);
        view.setBatches(
                goodsBatches
                        .stream()
                        .map(goodsBatch -> {
                            Long refBatchId = goodsBatch.getRefBatchId();
                            Map<Integer, BigDecimal> settlementStatusToCount = batchIdToSettlementStatusCountMap.getOrDefault(refBatchId, new HashMap<>(1));
                            return GoodsSettlementBatchView.fromSettlementBatch(goodsBatch, batchIdToGoodsStocks.get(refBatchId), settlementStatusToCount.get(GoodsSettlementOrder.Status.AUDIT_PASS),settlementStatusToCount.get(GoodsSettlementOrder.Status.AUDITING), batchIdToOtherSettlementOrderSupplierSellerId.get(refBatchId));
                        })
                        .collect(Collectors.toList())
        );
        return view;
    }
}
