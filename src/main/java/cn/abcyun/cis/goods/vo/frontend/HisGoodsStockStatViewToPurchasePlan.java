package cn.abcyun.cis.goods.vo.frontend;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class HisGoodsStockStatViewToPurchasePlan  extends  HisGoodsStockStatBaseView{


    private BigDecimal recommendCount; //推荐采购库存

    //        private BigDecimal avgSell; //销量
    private Integer purchaseCycleDays;//计算周转库存的天数
    private BigDecimal stockCapacityMaxLimit;//最大采购库存

    @ApiModelProperty(value = "限价")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal priceLimit;
    @ApiModelProperty(value = "挂网价")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal listingPrice;

}
