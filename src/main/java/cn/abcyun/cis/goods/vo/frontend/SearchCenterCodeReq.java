package cn.abcyun.cis.goods.vo.frontend;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-25 17:52:31
 */
@Data
public class SearchCenterCodeReq {
    @JsonIgnore
    private String chainId;
    @JsonIgnore
    private String clinicId;

    @ApiModelProperty(value = "关键字")
    private String keyword;
    @ApiModelProperty(value = "药监中心编码")
    private String centerCode;
    @ApiModelProperty(value = "名称")
    private String cadn;
    @ApiModelProperty(value = "批准文号")
    private String approvedCode;
    @ApiModelProperty(value = "生产厂家")
    private String manufacture;
    @ApiModelProperty(value = "本位码")
    private String adminStandardCode;

    @ApiModelProperty(value = "编码类型 0=河北药监中心编码 1=云南卫健平台编码")
    private Integer centerCodeType;
    @ApiModelProperty(value = "0=西药 1=中药 centerCodeType=1有效")
    private Integer queryType;

    /**
     * 药品类型编码
     */
    private List<String> medicineCategoryCodeList = new ArrayList<>();

    private Integer offset;
    private Integer limit;

    public boolean isAllEmpty() {
        return StringUtils.isEmpty(keyword)
                && StringUtils.isEmpty(centerCode)
                && StringUtils.isEmpty(cadn)
                && StringUtils.isEmpty(approvedCode)
                && StringUtils.isEmpty(manufacture)
                && StringUtils.isEmpty(adminStandardCode);
    }
}
