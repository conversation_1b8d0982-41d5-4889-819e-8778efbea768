package cn.abcyun.cis.goods.vo.frontend.todo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 入库单上的待办数量
 * 1.入库单数量
 * 2.退货出库单数量
 */
@Data
public class GoodsStockInOrderCountRedisCache {
    private String clinicId;
    private Integer pharmacyNo;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer inOrderReviewTodoCount;//入库单待审核数量
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer inOrderConfirmInTodoCount;//入库单待审核数量
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer returnOutOrderReviewTodoCount;//入库单待审核数量
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer returnOutOrderConfirmOutTodoCount;//入库单待审核数量
}
