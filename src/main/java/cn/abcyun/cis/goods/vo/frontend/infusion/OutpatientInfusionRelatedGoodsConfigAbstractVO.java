package cn.abcyun.cis.goods.vo.frontend.infusion;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/11/10 5:21 下午
 */
@Data
public class OutpatientInfusionRelatedGoodsConfigAbstractVO {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    private int usageType;
    private int goodsCount;
    /**
     * 分组里面的被勾选上的用法列表
     */
    private List<String> usages;
    private List<String> relateGoodsName;
}
