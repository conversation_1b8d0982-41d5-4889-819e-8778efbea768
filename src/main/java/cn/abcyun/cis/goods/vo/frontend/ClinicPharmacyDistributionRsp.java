package cn.abcyun.cis.goods.vo.frontend;

import cn.abcyun.cis.goods.vo.frontend.pharmacy.GoodsPharmacyView;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.OrganView;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 按药房类型返回的，诊所列表
 * */
@Data
public class ClinicPharmacyDistributionRsp {
    /**
     * 诊所药房列表
     * */
    private List<ClinicPharmacyView> list;

    @Data
    public static class ClinicPharmacyView {
        //药房类型
        private int pharmacyType;
        /**
         * 备注：
         * goodsStat里面 目前type和No是一一对应的，当前goodsStat是按no来聚合的。但是给前端的协议还是type
         * */
        @ApiModelProperty("这个药房类型下的所有门店列表 list里面是1个元素，库存物资列表这里因为都是一一一对应才用pharmacyType来映射no")
        private List<PharmacyView> organList;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class PharmacyView extends OrganView {
        @ApiModelProperty("这个门店下可能开了多个这样的药房")
        private List<GoodsPharmacyView> pharmacyList;
    }
}
