package cn.abcyun.cis.goods.vo.frontend.goodsinfo;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.utils.GoodsPrivUtils;
import cn.abcyun.cis.goods.vo.frontend.goodslist.DiagnosisAndTreatGoodsView;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 拉取批量改价列表请求
 */
@Data
public class CreateOrUpdateProcessGoodsReq {
    /**
     * http头
     **/
    @JsonIgnore
    private String headerChainId;
    @JsonIgnore
    private String headerClinicId;
    @JsonIgnore
    private String headerEmployeeId;
    @JsonIgnore
    private int headerClinicType;
    @JsonIgnore
    private int headerViewMode;

    private String clinicId;
    /**
     * 修改模式 0 普通的批量修改
     * 1 导入修改 导入修改会把这个供应商下的所有项目 和 二级分类全删了
     */
    private int mode;
    /**
     * 义齿加工的类型
     */
    private int typeId;
    /**
     * 厂家ID
     */
    @JsonProperty("supplierId")
    @NotBlank(message = "supplierId不能为空")
    private String manufactureId;


    /**
     * 产品逻辑 修改是批量提交修改
     * */
    @Valid
    @NotEmpty(message = "项目列表不能为空")
    private List<CreateOrUpdateProcessGoodsItem> list = new ArrayList<>();

    /**
     * {@link DiagnosisAndTreatGoodsView}
     * */
    @Data
    public static class CreateOrUpdateProcessGoodsItem {
        @ApiModelProperty("goodsId 加工项目Id")
        private String id;
        @ApiModelProperty("goodsId 加工项目名字")
        @ExcelProperty(value = "项目名", index = 1)
        @NotBlank(message = "项目名称不能为空")
        @Length(max = 30, message = "项目名称不能超出30个字符")
        private String name;
        @ApiModelProperty("启用停用状态")
        private int disable;
        @ApiModelProperty("分类ID")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long customTypeId;
        @ApiModelProperty("分类名字，在导入的时候用用")
        @ExcelProperty(value = "分类", index = 0)
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @NotBlank(message = "分类名称不能为空")
        @Length(max = 20, message = "分类名称不能超出20个字符")
        private String customTypeName;

        @ApiModelProperty("0 新增 1 修改 2 删除 一定要前端告诉明确删才删，否则不删 因为可能有于网络原因 前端没有拉到原来的项目列表，这个时候新加一条 不要把老的误删了")
        private int opType;

        @ApiModelProperty("售价")
        @ExcelProperty(value = "加工单价", index = 3)
        @DecimalMin(value = "0", message = "加工单价不能为负数")
        @Digits(integer = 8, fraction = 2, message = "加工单价小数点前8位后2位")
        private BigDecimal packagePrice;
        @ApiModelProperty("成本加，留着避免产品上使用")
        private BigDecimal packageCostPrice;
        @ApiModelProperty("单位")
        @ExcelProperty(value = "单位", index = 2)
        @NotBlank(message = "单位不能为空")
        @Length(max = 2, message = "单位不能超出2个字符")
        private String packageUnit;
        @ApiModelProperty("义齿加工备注")
        @ExcelProperty(value = "备注", index = 4)
        @Length(max = 500, message = "备注不能超出500个字符")
        private String remark;

        @JsonIgnore
        private Integer sort;

        /**
         * excel解析错误消息
         */
        @JsonIgnore
        private List<String> excelParseErrorMessages;

        public void setName(String name) {
            this.name = StringUtils.trimWhitespace(name);
        }

        public void setCustomTypeName(String customTypeName) {
            this.customTypeName = StringUtils.trimWhitespace(customTypeName);
        }

        public void setPackageUnit(String packageUnit) {
            this.packageUnit = StringUtils.trimWhitespace(packageUnit);
        }
    }


    /**
     * 业务参数检查
     * */
    public void parameterCheck() {
        if (!GoodsPrivUtils.isHeadClinic(headerClinicType, headerViewMode)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "无权限修改加工项目");
        }

        if(typeId != GoodsConst.GoodsTypeId.DENTURE_PROCESSING){
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "只能操作义齿加工项目");
        }

        if (StringUtils.isEmpty(manufactureId)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "加工项目必须指定加工厂家");
        }

        if(mode != 0 && mode != 1){
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "无效操作类型");
        }

        if(CollectionUtils.isEmpty(list)){
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "加工项目列表为空");
        }
        /**
         * 把EXCEL导入 构造成全部新建
         * */
        if (mode == 1) {
            for (CreateOrUpdateProcessGoodsReq.CreateOrUpdateProcessGoodsItem createOrUpdateProcessGoodsItem : list) {
                createOrUpdateProcessGoodsItem.setId(null);
                createOrUpdateProcessGoodsItem.setOpType(0);
                createOrUpdateProcessGoodsItem.setCustomTypeId(null);
            }
        }

        /**
         * 按用的请求进行排序
         * */
        int sortIndex = 0;
        for (CreateOrUpdateProcessGoodsItem createOrUpdateProcessGoodsItem : list) {
            if(createOrUpdateProcessGoodsItem.opType != 2){
                createOrUpdateProcessGoodsItem.setSort( sortIndex ++);
            }
            //只是保护 有一行异常数据就返回
            if (createOrUpdateProcessGoodsItem.opType != 0 && StringUtils.isEmpty(createOrUpdateProcessGoodsItem.getId())) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "修改或删除加工项目，确实项目ID");
            }
        }

        Map<String,Long> nameToCount = list.stream().filter(it->it.getOpType() != 2).map(CreateOrUpdateProcessGoodsItem::getName).collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        nameToCount.forEach((name ,count)->{
            if (count > 1) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "加工项目:"+name+" 重复");
            }
        });
    }
}
