package cn.abcyun.cis.goods.vo.frontend.supplier;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.cis.commons.util.ClinicUtils;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.model.GoodsSupplier;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Instant;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-16 17:22:18
 */
@Data
public class UpdateGoodsSupplierCopyReq {
    private String id = "";
    /**
     * 连锁id或单店id
     * 要确认下，这里到底是不是可以写入clinicId。(看nodejs代码记录都是插入到chainId下的)
     */
    private String chainId;
    /**
     * 药店GSP一定要门店ID，即使是单店，所以要这个字段存一下
     * */
    private String headerClinicId;
    private String clinicId;
    private int clinicType;
    private int hisType;
    private int viewMode;
    private String employeeId;
    /**
     * 可以修改的内容
     */
    @ApiModelProperty(value = "【修改/新增】供应商的名字，不能为空，长度最大20")
    private String name;
    @ApiModelProperty(value = "【修改/新增】供应商的启用停用装填  0-未启用，1-启用")
    private int status;
    @JsonProperty("license")
    @ApiModelProperty(value = "【修改/新增】供应商许可证号 可空，最大20")
    private String licenseId;
    @ApiModelProperty(value = "【修改/新增】联系人 可空，最大20")
    private String contact;
    @ApiModelProperty(value = "【修改/新增】联系电话 可空，最大20")
    private String mobile;
    @ApiModelProperty(value = "【修改/新增】备注 可空，最大50")
    private String mark;
    @ApiModelProperty(value = "【新增】(修改也需要传用来判重)每种药房有自己独立的供应商,只第一次新增有用，新增后不能再修改。供应商还是增加到对应的连锁下面不是子店")
    private int pharmacyType;//
    /**
     * 供应商扩展信息
     * 代煎代配药房一定有
     */
    @ApiModelProperty(value = "【修改/新增】代煎代配药房 供应商")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private GoodsSupplierExtendInfo extendInfo;
    /**
     * 内置供应商标记
     */
    @ApiModelProperty("内置供应商标记")
    @JsonIgnore
    private Integer innerFlag;

    @ApiModelProperty(value = "供应商类型 0 /null 为库存供应商  10 加工供应商 20 检验机构")
    private Integer type;

    @ApiModelProperty(value = "义齿加工供应商，可以指定 创建后在列表里面的顺，")
    private Integer sort;
    /**
     * 企业类型
     */
    @ApiModelProperty(value = "企业类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer companyType;
    /**
     * 企业编码
     */
    @ApiModelProperty(value = "企业编码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String companyCode;

    /**
     * 统一社会信用代码（
     */
    @ApiModelProperty(value = "统一社会信用代码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String unifiedSocialCreditIdentifier;
    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String legalRepresentative;

    /**
     * 法定代表人
     */
    @ApiModelProperty("法定代表人ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String legalRepresentativeIdCard;
    /**
     * 法定代表人
     */
    @ApiModelProperty("法定代表人Mail")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String legalRepresentativeMail;
    /**
     * 企业负责人
     */
    @ApiModelProperty(value = "企业负责人")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String personInChargeOfEnterprise;

    /**
     * 质量负责人
     */
    @ApiModelProperty(value = "质量负责人")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String qualityDirecter;
    /**
     * 经营范围
     */
    @ApiModelProperty(value = "经营范围")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private GoodsSupplierView.BussinessScope businessScope;
    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String registeredAddress;

    /**
     * 仓库地址
     */
    @ApiModelProperty(value = "仓库地址")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String warehouseAddress;

    /**
     * 开户行
     */
    @ApiModelProperty(value = "开户行")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String openingBank;
    /**
     * 开户名
     */
    @ApiModelProperty(value = "开户名")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String accountName;

    /**
     * 银行帐号
     */
    @ApiModelProperty(value = "银行帐号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String bankAccount;

    /**
     * 企业委托人
     */
    @ApiModelProperty(value = "企业委托人")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String principal;

    /**
     * 企业委托人 身份证号
     */
    @ApiModelProperty(value = "企业委托人 身份证号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String principalIdCard;
    /**
     * 企业委托人 电话
     */
    @ApiModelProperty(value = "企业委托人 电话")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String principalTelephone;

    /**
     * 委托开始时间
     */
    @ApiModelProperty(value = "委托开始时间")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String delegationBeginDate;
    /**
     * 委托结束时间
     */
    @ApiModelProperty(value = "委托结束时间")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String delegationEndDate;
    @ApiModelProperty(value = "有效期至 YYYY-MM_DD  ")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String validateEndDate;

    /**
     * 委托范围
     */
    @ApiModelProperty(value = "委托范围")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String delegationScope;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty("发起Gsp审批流 新建 立即发起GSP首营审批;修改如果传1 表示已经发起首营审批,调用方要传正确的gsp状态和id ")
    private Integer gspReviewStarted;
    /**
     * 申请人（如果不指定，会把operatorId当作申请人）
     */
    @ApiModelProperty("申请人可以不是发起人,申请人（如果不指定，会把operatorId当作申请人）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String submitterId;

    @ApiModelProperty("Goods服务相关的Gsp审批相关的业务字段（审批服务不理解的都丢着里面）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private GoodsGsp gsp;
    /**
     * 申请日期（如果不指定，会使用当前表单提交时间）
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty("申请日期（如果不指定，会使用当前表单提交时间）")
    private Instant submitTime;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty("详细注册地址")
    private GoodsSupplierAddress registeredAddressDetail;

    /**
     * 是否委托配送 0:否 1:是
     */
    @ApiModelProperty("是否委托配送 0:否 1:是")
    private int isEntrustDelivery;
    private Integer gspModifyStatus;

    @ApiModelProperty(value = "集采委托配送方式 1=委配 2=自配")
    private Integer entrustDeliveryType;
}
