package cn.abcyun.cis.goods.vo.frontend.goodsupdate;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.goods.conf.SuggestConfiguration;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.model.GoodsSysType;
import cn.abcyun.cis.goods.service.query.GoodsSysTypeService;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.vo.frontend.goodsinfo.GoodsSellerImportCheckDataValidRsp;
import cn.abcyun.cis.goods.vo.frontend.goodsinfo.spu.*;
import cn.abcyun.cis.goods.vo.frontend.jenkins.JenkinsGoodsImportItem;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 新建修改药品请求 工具类，用于生成请求对象
 * 背景：药品的新建和删除就一个接口，所有字段都在一个接口里面，而每一个具体类型的药品
 * 也就用了其中部分字段。
 * 为了兼容以前逻辑，一个接口的逻辑保留
 * 本类根据客户端请求，生成出各种类型药品的请求
 * clientGoodsCreateReq -> ServierGoodsCreateReq
 * <p>
 * 不用BeanUtils copy整个对象，方便看出每类商品实际用了哪些参数
 */
public class GoodsCreateReqFactory {

    public static final String DEFAULT_EXAM_PKG_UNIT = "次";

    public static ServerCreateGoodsAbstractReq buildServerRequest(String chainId, ClientGoodsCreateReq clientReq, SuggestConfiguration suggestConfiguration) {
        if (clientReq == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER);
        }
        if (clientReq.getType() == null || clientReq.getSubType() == null || clientReq.getTypeId() == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "创建/修改药品资料模板数据异常,请你手动创建/修改药品资料");
        }

        switch (clientReq.getType().intValue()) {
            case GoodsConst.GoodsType.MEDICINE: { //药品
                switch (clientReq.getSubType().intValue()) {
                    case GoodsConst.GoodsMedicineSubType.CHINESE_MEDICINE: { //中药
                        return createServerChineseMedicineReq(clientReq);
                    }
                    case GoodsConst.GoodsMedicineSubType.WESTERN_MEDICINE: //西药
                    case GoodsConst.GoodsMedicineSubType.CHINESE_PATENT_MEDICINE: {//中成药
                        return createServerWestMedicineReq(clientReq);
                    }
                    default: {
                        throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "无效的药品类型子类型");
                    }
                }
            }
            case GoodsConst.GoodsType.MATERIAL: { //物资
                if (clientReq.getSubType().intValue() == GoodsConst.GoodsMaterialSubType.MEDICINE_MATERIAL) {
                    return createServerMedicalMaterialGoodsReq(clientReq);
                } else if (clientReq.getSubType().intValue() == GoodsConst.GoodsMaterialSubType.DISINFECTION_PRODUCT) {
                    return createServerMedicalDisInfectGoodsReq(clientReq);
                } else {
                    return createServerMaterialGoodsReq(clientReq);
                }
            }
            case GoodsConst.GoodsType.EXAMINATION:
            case GoodsConst.GoodsType.PHYSICAL_EXAMINATION: {//检查检验
                return createServerExaminationGoodsReq(clientReq);
            }
            case GoodsConst.GoodsType.TREATMENT: {//治疗理疗
                return createServerTreatmentGoodsReq(clientReq);
            }
            case GoodsConst.GoodsType.ADDITION: { //商品
                return createServerAdditionalGoodsReq(clientReq);
            }
            case GoodsConst.GoodsType.COMPOSE: { //普通套餐
                return createServerComposeGoodsReq(clientReq);
            }
            case GoodsConst.GoodsType.PHYSICAL_EXAMINATION_COMPOSE: {//体检套餐
                if (suggestConfiguration.isPhysicalExaminationStandardChain(chainId)) {
                    // 体检模版门店套餐走普通套餐的逻辑
                    return createServerComposeGoodsReq(clientReq);
                }
                return createServerPhysicalExaminationComposeGoodsReq(clientReq);
            }
            case GoodsConst.GoodsType.GOODS_GROUP: {// 商品组
                return createServerGoodsGroupReq(clientReq);
            }
            case GoodsConst.GoodsType.OTHER_GOODS: {//治疗理疗里面的其他移动出来变成一级分类
                return createServerOtherGoodsReq(clientReq);
            }
            case GoodsConst.GoodsType.PROCESSING: {//加工类型
                return createServerProcessGoodsReq(clientReq);
            }
            case GoodsConst.GoodsType.NURSE: {//护理
                return createServerNurseGoodsReq(clientReq);
            }
            case GoodsConst.GoodsType.CONSULTATION:
                // 会诊
                return createServerConsultationGoodsReq(clientReq);
            case GoodsConst.GoodsType.SURGERY:
                // 手术
                return createServerSurgeryGoodsReq(clientReq);
            default: {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "无效的药品类型");
            }
        }
    }

    private static ServerCreateGoodsAbstractReq createServerGoodsGroupReq(ClientGoodsCreateReq clientReq) {
        ServerCreateGoodsGroupGoodsReq serverReq = new ServerCreateGoodsGroupGoodsReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(GoodsConst.GoodsType.GOODS_GROUP);
        serverReq.setSubType(GoodsConst.GoodsGroupSubType.GROUP);
        serverReq.setTypeId(GoodsConst.GoodsTypeId.GOODS_GROUP_TYPEID);
        serverReq.setName(clientReq.getName());
        serverReq.setDismounting(GoodsConst.DismountingStatus.PACKAGE);
        serverReq.setPiecePrice(BigDecimal.ZERO);
        serverReq.setPackagePrice(BigDecimal.ZERO);
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);
        serverReq.setIsSell(GoodsConst.SellStatus.NO_SELL);
        serverReq.setCombineType(GoodsConst.GoodsCombine.COMBINE);
        serverReq.setGoodsGroupComposeList(genComposeGoodsChildrenList(clientReq.getChildren()));

        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(GoodsConst.FeeTypeId.FEE_TYPE_ID_GOODS_GROUP);
        serverReq.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_GOODS_GROUP);

        return serverReq;
    }

    /**
     * jenkins导入任务的情请求
     */
    public static ServerCreateGoodsAbstractReq jenkinsBuildServerRequest(JenkinsGoodsImportItem clientReq) {
        if (clientReq == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER);
        }
        if (clientReq.getType() == null || clientReq.getSubType() == null || clientReq.getTypeId() == null) {
            clientReq.addCreateErrorReason(GoodsSellerImportCheckDataValidRsp.ERROR_INVALID_GOODS_TYPE, "type", "未指定要导入的药品资料类型");
            return null;
        }

        switch (clientReq.getType().intValue()) {
            case GoodsConst.GoodsType.MEDICINE: { //药品
                switch (clientReq.getSubType().intValue()) {
                    case GoodsConst.GoodsMedicineSubType.CHINESE_MEDICINE: { //中药
                        return jenkinsCreateServerChineseMedicineReq(clientReq);
                    }
                    case GoodsConst.GoodsMedicineSubType.WESTERN_MEDICINE: //西药
                    case GoodsConst.GoodsMedicineSubType.CHINESE_PATENT_MEDICINE: {//中成药
                        return jenkinsCreateServerWestMedicineReq(clientReq);
                    }
                    default: {
                        clientReq.addCreateErrorReason(GoodsSellerImportCheckDataValidRsp.ERROR_INVALID_GOODS_TYPE, "type", "无效的药品类型子类型");
                        return null;
                    }
                }
            }
            case GoodsConst.GoodsType.MATERIAL: { //物资
                if (clientReq.getSubType() == GoodsConst.GoodsMaterialSubType.MEDICINE_MATERIAL) {
                    return jenkinsCreateServerMedicalMaterialGoodsReq(clientReq);
                } else if (clientReq.getSubType() == GoodsConst.GoodsMaterialSubType.DISINFECTION_PRODUCT) {
                    return jenkinsCreateServerMedicalDisInfectGoodsReq(clientReq);
                } else {
                    return jenkinsCreateServerMaterialGoodsReq(clientReq);
                }
            }
            case GoodsConst.GoodsType.EXAMINATION:
            case GoodsConst.GoodsType.PHYSICAL_EXAMINATION: {//检查检验
                if (clientReq.getSubType() == GoodsConst.ExaminationSubType.ASSAY) {
                    return jenkinsCreateServerExaminationGoodsReq(clientReq);
                }
                clientReq.addCreateErrorReason(GoodsSellerImportCheckDataValidRsp.ERROR_INVALID_GOODS_TYPE, "type", "只支持检验项目");
                return null;
            }
            case GoodsConst.GoodsType.TREATMENT: {//治疗理疗
                return jenkinsCreateServerTreatmentGoodsReq(clientReq);
            }
            case GoodsConst.GoodsType.ADDITION: { //商品
                return jenkinsCreateServerAdditionalGoodsReq(clientReq);
            }
            case GoodsConst.GoodsType.OTHER_GOODS: {//治疗理疗里面的其他移动出来变成一级分类
                return jenkinsCreateServerOtherGoodsReq(clientReq);
            }
            case GoodsConst.GoodsType.EYE: {//眼科类型
                return jenkinsCreateServerEyeGoodsReq(clientReq);
            }

            default: {
                clientReq.addCreateErrorReason(GoodsSellerImportCheckDataValidRsp.ERROR_INVALID_GOODS_TYPE, "type", "无效的药品类型类型");
                return null;
            }
        }
    }


    private static ServerCreateGoodsAbstractReq createServerSurgeryGoodsReq(ClientGoodsCreateReq clientReq) {
        ServerCreateSurgeryGoodsReq serverReq = new ServerCreateSurgeryGoodsReq();

        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());
        serverReq.setName(clientReq.getName());
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        serverReq.setPackageCostPrice(clientReq.getPackageCostPrice());

        /**
         * 药品标签，后台做成项目都支持
         * */
        if (clientReq.getGoodsTagIdList() != null) {
            serverReq.setGoodsTagIdToSort(new HashMap<>());
            for (int i = 0; i < clientReq.getGoodsTagIdList().size(); i++) {
                serverReq.getGoodsTagIdToSort().put(clientReq.getGoodsTagIdList().get(i), i);
            }
        }

        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());
        serverReq.setFeeComposeList(genFeeComposeChildrenList(clientReq.getFeeComposeList()));
        serverReq.setChildren(genComposeGoodsChildrenList(clientReq.getChildren()));
        return serverReq;
    }

    /**
     * 中药请求
     * 注：   1.中药请求客户端默认传的是小单位，后台这里会把对应的大单位字段不成小单位的值
     * 2.中药饮片不能进行智能销售
     */
    private static ServerCreateChineseMedicineReq createServerChineseMedicineReq(ClientGoodsCreateReq clientReq) {
        ServerCreateChineseMedicineReq serverReq = new ServerCreateChineseMedicineReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setBarCode(clientReq.getBarCode());
        serverReq.setTraceableCodeNoInfoList(clientReq.getTraceableCodeNoInfoList());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());


        /*
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());
        serverReq.setMedicineCadn(clientReq.getMedicineCadn());
        serverReq.setManufacturerFull(clientReq.getManufacturerFull());

        // 2025.09 中药支持 产地 /厂家
        serverReq.setDosageFormType(clientReq.getDosageFormType());
        serverReq.setMedicineDosageNum(clientReq.getMedicineDosageNum());
        serverReq.setMedicineDosageUnit(clientReq.getMedicineDosageUnit());
        serverReq.setMedicineNmpn(clientReq.getMedicineNmpn());

        /*
         * 价格和进销税等
         * */
        serverReq.setDismounting(GoodsConst.DismountingStatus.DISMOUNTING); //这里比较特殊，中药小单位
        serverReq.setPiecePrice(clientReq.getPiecePrice());
        serverReq.setPackagePrice(serverReq.getPiecePrice()); //用小单位的价格
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);//中药piecenum 就是1
        serverReq.setEqCoefficient(clientReq.getEqCoefficient());
        serverReq.setPieceUnit(clientReq.getPieceUnit());
        serverReq.setPackageUnit(clientReq.getPackageUnit());//大单位的值就是小单位的值
        serverReq.setInTaxRat(clientReq.getInTaxRat());
        serverReq.setOutTaxRat(clientReq.getOutTaxRat());
        if (clientReq.getDefaultInOutTax() != null) {
            serverReq.setDefaultInOutTax(clientReq.getDefaultInOutTax());
        }

        //中药特有规格
        serverReq.setCMSpec(clientReq.getCMSpec());
        serverReq.setMaterialSpec(clientReq.getCMSpec());
        serverReq.setExtendSpec(clientReq.getExtendSpec());


        /**
         * 中药特有相关
         * 其实这些字段不应该放到 v2_goods表
         * */
        serverReq.setPosition(clientReq.getPosition());//中药的货架
        if (clientReq.getPharmacyNo() != null) {
            serverReq.setPharmacyNo(clientReq.getPharmacyNo());//中药的药房编号
        }
        if (clientReq.getPharmacyNo() != null) {
            serverReq.setPharmacyNo(clientReq.getPharmacyNo());//中药的药房编号
        }
        serverReq.setRemark(clientReq.getRemark());//中药的备注
        serverReq.setOrigin(clientReq.getOrigin());//复用老字段

        serverReq.setForceChangePieceNum(clientReq.getForce());

        /**
         * 社保相关
         * */
        if (clientReq.getShebao() != null) {
            serverReq.setNationalCode(clientReq.getShebao().getNationalCode());
            serverReq.setNationalCodeId(clientReq.getShebao().getNationalCodeId());
            serverReq.setMedicalFeeGrade(clientReq.getShebao().getMedicalFeeGrade());
            serverReq.setMatchMode(clientReq.getShebao().getMatchMode());
            serverReq.setCleanMatchCodeFlag(clientReq.getShebao().getCleanMatchCodeFlag());
        }
        /**
         * Goods社保支付方式
         * */
        serverReq.setShebaoPayMode(clientReq.getShebaoPayMode());


        /**
         * 销售相关 和 智能发药
         * */
        serverReq.setIsSell(GoodsConst.SellStatus.SELL);//[药品默认都是强制对外销售的]
        serverReq.setSmartDispense(clientReq.getSmartDispense());
        serverReq.setSmartDispenseMachineNo(clientReq.getSmartDispenseMachineNo());
        if (serverReq.getCMSpec().compareTo(GoodsConst.GoodsChineseMedicineSpec.CMPIECES) == 0) {
            serverReq.setSmartDispense(null);//中药饮片智能发药设置为null，原有业务逻辑
        }

        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();


        /**
         * 税率规则
         * */
        serverReq.setUpdateInOutTaxRate(clientReq.getUpdateInOutTaxRate());

        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());

        /**
         * 2023.11新家
         * */
        serverReq.setFeeCategoryId(clientReq.getFeeCategoryId());
        serverReq.setOtcType(clientReq.getOtcType());
        serverReq.setBaseMedicineType(clientReq.getBaseMedicineType());
        serverReq.setShelfLife(clientReq.getShelfLife());
        serverReq.setPriceType(clientReq.getPriceType());
        serverReq.setPriceMakeupPercent(clientReq.getPriceMakeupPercent());
        serverReq.setProfitCategoryType(clientReq.getProfitCategoryType());
        serverReq.setPharmacologicId(clientReq.getPharmacologicId());
        serverReq.setMha(clientReq.getMha());
        serverReq.setMaintainType(clientReq.getMaintainType());
        serverReq.setStorageType(clientReq.getStorageType());
        //EXtendData
        serverReq.setMedicineNmpnStartExpiryDate(clientReq.getMedicineNmpnStartExpiryDate());
        serverReq.setMedicineNmpnEndExpiryDate(clientReq.getMedicineNmpnEndExpiryDate());
        serverReq.setStorage(clientReq.getStorage());
        serverReq.setBusinessScopeList(clientReq.getBusinessScopeList());
        /**
         * 药品标签，后台做成项目都支持
         * */
        if (clientReq.getGoodsTagIdList() != null) {
            serverReq.setGoodsTagIdToSort(new HashMap<>());
            for (int i = 0; i < clientReq.getGoodsTagIdList().size(); i++) {
                serverReq.getGoodsTagIdToSort().put(clientReq.getGoodsTagIdList().get(i), i);
            }
        }
        /**
         * 多价格
         * */
        serverReq.setMultiPriceList(clientReq.getMultiPriceList());
        serverReq.setPieceUnitWeight(clientReq.getPieceUnitWeight());

        serverReq.setMedicineNmpn(clientReq.getMedicineNmpn());

        serverReq.setCenterCode(clientReq.getCenterCode());
        serverReq.setCenterCodeType(clientReq.getCenterCodeType());

        return serverReq;
    }

    /**
     * 中药请求
     * 注：   1.中药请求客户端默认传的是小单位，后台这里会把对应的大单位字段不成小单位的值
     * 2.中药饮片不能进行智能销售
     */
    private static ServerCreateChineseMedicineReq jenkinsCreateServerChineseMedicineReq(JenkinsGoodsImportItem clientReq) {
        ServerCreateChineseMedicineReq serverReq = new ServerCreateChineseMedicineReq();
        serverReq.setJenkinsGoodsImportItem(clientReq);
        /**
         * ID 相关
         * */
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setBarCode(clientReq.getBarCode());
        serverReq.setRemark(clientReq.getRemark());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId().intValue());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());


        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());
        serverReq.setMedicineCadn(clientReq.getMedicineCadn());
        serverReq.setManufacturerFull(clientReq.getManufacturerFull());
        // 2025.09 中药支持 产地 /厂家
        serverReq.setManufacturer(clientReq.getManufacturer());
        serverReq.setOrigin(clientReq.getOrigin());
        serverReq.setDosageFormType(clientReq.getDosageFormType());
        serverReq.setMedicineDosageNum(clientReq.getMedicineDosageNum());
        serverReq.setMedicineDosageUnit(clientReq.getMedicineDosageUnit());
        serverReq.setMedicineNmpn(clientReq.getMedicineNmpn());


        /**
         * 价格和进销税等
         * */
        serverReq.setDismounting(GoodsConst.DismountingStatus.DISMOUNTING); //这里比较特殊，中药小单位
        serverReq.setPiecePrice(clientReq.getPiecePrice());
        serverReq.setPackagePrice(serverReq.getPiecePrice()); //用小单位的价格
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);//中药piecenum 就是1
        serverReq.setEqCoefficient(clientReq.getEqCoefficient());
        serverReq.setPieceUnit(clientReq.getPieceUnit());
        serverReq.setPackageUnit(clientReq.getPackageUnit());//大单位的值就是小单位的值
        serverReq.setInTaxRat(clientReq.getInTaxRat());
        serverReq.setOutTaxRat(clientReq.getOutTaxRat());

        //中药特有规格
        serverReq.setCMSpec(clientReq.getCMSpec());
        serverReq.setMaterialSpec(clientReq.getCMSpec());
        serverReq.setExtendSpec(clientReq.getExtendSpec());


        /**
         * 销售相关 和 智能发药
         * */
        serverReq.setIsSell(GoodsConst.SellStatus.SELL);//[药品默认都是强制对外销售的]
        serverReq.setSmartDispense(clientReq.getSmartDispense());
        serverReq.setSmartDispenseMachineNo(clientReq.getSmartDispenseMachineNo());
        serverReq.setPosition(clientReq.getPosition());
        if (serverReq.getCMSpec().compareTo(GoodsConst.GoodsChineseMedicineSpec.CMPIECES) == 0) {
            serverReq.setSmartDispense(null);//中药饮片智能发药设置为null，原有业务逻辑
        }

        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();
        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getTypeId().longValue());
        serverReq.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);
        if (clientReq.getFeeTypeId() != 0L) {
            serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        }
        serverReq.setFeeCategoryId(clientReq.getFeeCategoryId());
        serverReq.setPharmacologicId(clientReq.getPharmacologicId());
        serverReq.setOtcType(clientReq.getOtcType());
        serverReq.setBaseMedicineType(clientReq.getBaseMedicineType());
        serverReq.setShelfLife(clientReq.getShelfLife());
        serverReq.setProfitCategoryType(clientReq.getProfitCategoryType());
        serverReq.setFirstSupplierId(clientReq.getFirstSupplierId());
        serverReq.setStorageType(clientReq.getStorageType());
        serverReq.setMaintainType(clientReq.getMaintainType());
        serverReq.setMha(clientReq.getMha());

        //EXtendData
//        serverReq.setMedicineNmpnStartExpiryDate(clientReq.getMedicineNmpnStartExpiryDate());
//        serverReq.setMedicineNmpnEndExpiryDate(clientReq.getMedicineNmpnEndExpiryDate());
//        serverReq.setStorageList(clientReq.getStorageList());
        serverReq.setBusinessScopeList(clientReq.getBusinessScopeList());
        serverReq.setStorage(clientReq.getStorage());
        if (clientReq.getPriceType() != null) {
            serverReq.setPriceType(clientReq.getPriceType());
            serverReq.setPriceMakeupPercent(clientReq.getPriceMakeupPercent());
        }
        return serverReq;
    }

    /**
     * 建西药请求
     */
    private static ServerCreateWestMedicineReq createServerWestMedicineReq(ClientGoodsCreateReq clientReq) {
        ServerCreateWestMedicineReq serverReq = new ServerCreateWestMedicineReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setBarCode(clientReq.getBarCode());
        serverReq.setTraceableCodeNoInfoList(clientReq.getTraceableCodeNoInfoList());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());
        serverReq.setMedicineCadn(clientReq.getMedicineCadn());
        serverReq.setMedicineNmpn(clientReq.getMedicineNmpn());
        serverReq.setManufacturerFull(clientReq.getManufacturerFull());


        /**
         * 价格和进销税等
         * */
        if (clientReq.getDismounting() != null) {
            serverReq.setDismounting(clientReq.getDismounting().shortValue());
        }
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        serverReq.setPiecePrice(clientReq.getPiecePrice());
        if (clientReq.getPieceNum() == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "计量不能为空");
        }
        serverReq.setPieceNum(clientReq.getPieceNum());
        serverReq.setPieceUnit(clientReq.getPieceUnit());
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setInTaxRat(clientReq.getInTaxRat());
        serverReq.setOutTaxRat(clientReq.getOutTaxRat());
        if (clientReq.getDefaultInOutTax() != null) {
            serverReq.setDefaultInOutTax(clientReq.getDefaultInOutTax());
        }


        // 西药特有的规格
        serverReq.setSpecType(clientReq.getSpecType());
        serverReq.setMedicineDosageForm(clientReq.getMedicineDosageForm());
        serverReq.setMedicineDosageNum(clientReq.getMedicineDosageNum());
        serverReq.setMedicineDosageUnit(clientReq.getMedicineDosageUnit());
        serverReq.setComponentContentNum(clientReq.getComponentContentNum());
        serverReq.setComponentContentUnit(clientReq.getComponentContentUnit());
        /**
         * 社保相关
         * */
        if (clientReq.getShebao() != null) {
            serverReq.setNationalCode(clientReq.getShebao().getNationalCode());
            serverReq.setNationalCodeId(clientReq.getShebao().getNationalCodeId());
            serverReq.setMedicalFeeGrade(clientReq.getShebao().getMedicalFeeGrade());
            serverReq.setStandardCode(clientReq.getShebao().getStandardCode());
            serverReq.setMatchMode(clientReq.getShebao().getMatchMode());
            serverReq.setCleanMatchCodeFlag(clientReq.getShebao().getCleanMatchCodeFlag());
        }

        /**
         * Goods社保支付方式
         * */
        serverReq.setShebaoPayMode(clientReq.getShebaoPayMode());

        /**
         * 销售相关 和 智能发药
         * */
        serverReq.setIsSell(GoodsConst.SellStatus.SELL);//药品类型都是要对外销售的
        serverReq.setForceChangePieceNum(clientReq.getForce());

        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        serverReq.specificCheck();

        serverReq.setRemark(clientReq.getRemark());
        serverReq.setOrigin(clientReq.getOrigin());


        /**
         * 税率规则
         * */
        serverReq.setUpdateInOutTaxRate(clientReq.getUpdateInOutTaxRate());

        /***
         * 2022.09支持柜号
         * */
        serverReq.setPosition(clientReq.getPosition());
        if (clientReq.getPharmacyNo() != null) {
            serverReq.setPharmacyNo(clientReq.getPharmacyNo());//中药的药房编号
        }

        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());

        serverReq.setFeeCategoryId(clientReq.getFeeCategoryId());
        serverReq.setAntibiotic(clientReq.getAntibiotic());
        /**
         * 2023.11新家
         * */
        serverReq.setDddOfAntibiotic(clientReq.getDddOfAntibiotic());
        serverReq.setUnitOfAntibiotic(clientReq.getUnitOfAntibiotic());
        serverReq.setDangerIngredient(clientReq.getDangerIngredient());//精麻毒放/麻黄碱
        serverReq.setOtcType(clientReq.getOtcType());//OTC
        serverReq.setBaseMedicineType(clientReq.getBaseMedicineType());//基药
        serverReq.setMha(clientReq.getMha());//上市许可持有人
        serverReq.setMaintainType(clientReq.getMaintainType());//养护条件
        serverReq.setStorageType(clientReq.getStorageType());//存储条件
        serverReq.setShelfLife(clientReq.getShelfLife());//保质期

        serverReq.setPriceType(clientReq.getPriceType());
        serverReq.setPriceMakeupPercent(clientReq.getPriceMakeupPercent());
        serverReq.setProfitCategoryType(clientReq.getProfitCategoryType());

        serverReq.setDosageFormType(clientReq.getDosageFormType());//剂型
        serverReq.setPharmacologicId(clientReq.getPharmacologicId());
        serverReq.setProfitCategoryType(clientReq.getProfitCategoryType());
        //EXtendData
        serverReq.setMedicineNmpnStartExpiryDate(clientReq.getMedicineNmpnStartExpiryDate());
        serverReq.setMedicineNmpnEndExpiryDate(clientReq.getMedicineNmpnEndExpiryDate());
        serverReq.setStorage(clientReq.getStorage());
        serverReq.setBusinessScopeList(clientReq.getBusinessScopeList());

        /**
         * 药品标签，后台做成项目都支持
         * */
        if (clientReq.getGoodsTagIdList() != null) {
            serverReq.setGoodsTagIdToSort(new HashMap<>());
            for (int i = 0; i < clientReq.getGoodsTagIdList().size(); i++) {
                serverReq.getGoodsTagIdToSort().put(clientReq.getGoodsTagIdList().get(i), i);
            }
        }
        serverReq.setCenterCode(clientReq.getCenterCode());
        serverReq.setCenterCodeType(clientReq.getCenterCodeType());
        /**
         * 多价格
         * */
        serverReq.setMultiPriceList(clientReq.getMultiPriceList());
        return serverReq;
    }

    /**
     * 建西药请求
     */
    private static ServerCreateWestMedicineReq jenkinsCreateServerWestMedicineReq(JenkinsGoodsImportItem clientReq) {
        ServerCreateWestMedicineReq serverReq = new ServerCreateWestMedicineReq();
        serverReq.setJenkinsGoodsImportItem(clientReq);
        /**
         * ID 相关
         * */
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setBarCode(clientReq.getBarCode());
        serverReq.setRemark(clientReq.getRemark());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId().intValue());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());
        serverReq.setMedicineCadn(clientReq.getMedicineCadn());
        serverReq.setMedicineNmpn(clientReq.getMedicineNmpn());
        serverReq.setManufacturerFull(clientReq.getManufacturerFull());


        /**
         * 价格和进销税等
         * */
        if (clientReq.getDismounting() != null) {
            serverReq.setDismounting(clientReq.getDismounting().shortValue());
        }
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        serverReq.setPiecePrice(clientReq.getPiecePrice());
        if (clientReq.getPieceNum() == null) {
            clientReq.addCreateErrorReason(GoodsSellerImportCheckDataValidRsp.ERROR_INVALID_GOODS_TYPE, "pieceNum", "计量不能为空");
        }
        serverReq.setPieceNum(clientReq.getPieceNum());
        serverReq.setPieceUnit(clientReq.getPieceUnit());
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setInTaxRat(clientReq.getInTaxRat());
        serverReq.setOutTaxRat(clientReq.getOutTaxRat());


        serverReq.setPosition(clientReq.getPosition());
        // 西药特有的规格
        serverReq.setSpecType(clientReq.getSpecType() != null ? clientReq.getSpecType() : GoodsConst.GoodsSpecType.WEST_DOSAGE_MDOE);
        serverReq.setMedicineDosageNum(clientReq.getMedicineDosageNum());
        serverReq.setMedicineDosageUnit(clientReq.getMedicineDosageUnit());
        serverReq.setComponentContentNum(clientReq.getComponentContentNum());
        serverReq.setComponentContentUnit(clientReq.getComponentContentUnit());
        if (clientReq.getComponentContentNum() != null && !StringUtils.isEmpty(clientReq.getComponentContentUnit())) {
            serverReq.setSpecType((int) GoodsConst.GoodsSpecType.WEST_COMPONENT_MDOE);
        }


        /**
         * 销售相关 和 智能发药
         * */
        serverReq.setIsSell(GoodsConst.SellStatus.SELL);//药品类型都是要对外销售的

        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        serverReq.specificCheck();
        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getTypeId().longValue());
        serverReq.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);
        if (clientReq.getFeeTypeId() != 0L) {
            serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        }
        serverReq.setFeeCategoryId(clientReq.getFeeCategoryId());
        serverReq.setAntibiotic(clientReq.getAntibiotic());
        serverReq.setDddOfAntibiotic(clientReq.getDddOfAntibiotic());
        serverReq.setUnitOfAntibiotic(clientReq.getUnitOfAntibiotic());
        serverReq.setDosageFormType(clientReq.getDosageFormType());
        serverReq.setPharmacologicId(clientReq.getPharmacologicId());
        serverReq.setOtcType(clientReq.getOtcType());
        serverReq.setBaseMedicineType(clientReq.getBaseMedicineType());
        serverReq.setShelfLife(clientReq.getShelfLife());
        serverReq.setMaintainType(clientReq.getMaintainType());
        serverReq.setStorageType(clientReq.getStorageType());
        serverReq.setMha(clientReq.getMha());
        serverReq.setProfitCategoryType(clientReq.getProfitCategoryType());
        serverReq.setDangerIngredient(clientReq.getDangerIngredient());
        serverReq.setFirstSupplierId(clientReq.getFirstSupplierId());
        serverReq.setDangerIngredient(clientReq.getDangerIngredient());

        //EXtendData
        serverReq.setMedicineNmpnStartExpiryDate(clientReq.getMedicineNmpnStartExpiryDate());
        serverReq.setMedicineNmpnEndExpiryDate(clientReq.getMedicineNmpnEndExpiryDate());
        serverReq.setStorage(clientReq.getStorage());
        serverReq.setBusinessScopeList(clientReq.getBusinessScopeList());
        if (clientReq.getPriceType() != null) {
            serverReq.setPriceType(clientReq.getPriceType());
            serverReq.setPriceMakeupPercent(clientReq.getPriceMakeupPercent());
        }
        return serverReq;
    }

    /**
     * 建物资请求【材料】
     * 字段通 Additional一样
     */
    private static ServerCreateMaterialGoodsReq createServerMaterialGoodsReq(ClientGoodsCreateReq clientReq) {
        ServerCreateMaterialGoodsReq serverReq = new ServerCreateMaterialGoodsReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setBarCode(clientReq.getBarCode());
        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());
        serverReq.setManufacturerFull(clientReq.getManufacturerFull());
        serverReq.setMedicineNmpn(clientReq.getMedicineNmpn());

        serverReq.setRemark(clientReq.getRemark());
        serverReq.setOrigin(clientReq.getOrigin());
        /**
         * 价格和进销税等
         * */
        if (clientReq.getDismounting() != null) {
            serverReq.setDismounting(clientReq.getDismounting().shortValue());
        }
        serverReq.setPiecePrice(clientReq.getPiecePrice());
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        if (clientReq.getPieceNum() == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "计量不能为空");
        }
        serverReq.setPieceNum(clientReq.getPieceNum());
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPieceUnit(clientReq.getPieceUnit());

        //材料特有规格
        serverReq.setMaterialSpec(clientReq.getMaterialSpec());
        serverReq.setCMSpec(clientReq.getMaterialSpec());
        /**
         * 社保相关
         * */
        if (clientReq.getShebao() != null) {
            serverReq.setNationalCode(clientReq.getShebao().getNationalCode());
            serverReq.setNationalCodeId(clientReq.getShebao().getNationalCodeId());
            serverReq.setMedicalFeeGrade(clientReq.getShebao().getMedicalFeeGrade());
        }
        /**
         * Goods社保支付方式
         * */
        serverReq.setShebaoPayMode(clientReq.getShebaoPayMode());

        /**
         * 是否对外销售
         * 不对外销售，没有价格
         * */
        if (clientReq.getIsSell() != null) {
            serverReq.setIsSell(clientReq.getIsSell().shortValue());
        }
        serverReq.setForceChangePieceNum(clientReq.getForce());


        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        serverReq.specificCheck();

        /**
         * 税率规则
         * */
        serverReq.setUpdateInOutTaxRate(clientReq.getUpdateInOutTaxRate());

        /***
         * 2022.09支持柜号
         * */
        serverReq.setPosition(clientReq.getPosition());
        if (clientReq.getPharmacyNo() != null) {
            serverReq.setPharmacyNo(clientReq.getPharmacyNo());//中药的药房编号
        }
        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());
        serverReq.setFeeCategoryId(clientReq.getFeeCategoryId());
        //EXtendData
        serverReq.setMedicineNmpnStartExpiryDate(clientReq.getMedicineNmpnStartExpiryDate());
        serverReq.setMedicineNmpnEndExpiryDate(clientReq.getMedicineNmpnEndExpiryDate());
        serverReq.setStorage(clientReq.getStorage());
        serverReq.setBusinessScopeList(clientReq.getBusinessScopeList());
        /**
         * 药品标签，后台做成项目都支持
         * */
        if (clientReq.getGoodsTagIdList() != null) {
            serverReq.setGoodsTagIdToSort(new HashMap<>());
            for (int i = 0; i < clientReq.getGoodsTagIdList().size(); i++) {
                serverReq.getGoodsTagIdToSort().put(clientReq.getGoodsTagIdList().get(i), i);
            }
        }
        /**
         * 多价格
         * */
        serverReq.setMultiPriceList(clientReq.getMultiPriceList());
        return serverReq;
    }

    private static ServerCreateMedicalDisInfectGoodsReq createServerMedicalDisInfectGoodsReq(ClientGoodsCreateReq clientReq) {
        ServerCreateMedicalDisInfectGoodsReq serverReq = new ServerCreateMedicalDisInfectGoodsReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setBarCode(clientReq.getBarCode());
        serverReq.setTraceableCodeNoInfoList(clientReq.getTraceableCodeNoInfoList());
        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());
        serverReq.setManufacturerFull(clientReq.getManufacturerFull());
        serverReq.setMedicineNmpn(clientReq.getMedicineNmpn());

        /**
         * 价格和进销税等
         * */
        if (clientReq.getDismounting() != null) {
            serverReq.setDismounting(clientReq.getDismounting().shortValue());
        }
        serverReq.setPiecePrice(clientReq.getPiecePrice());
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        if (clientReq.getPieceNum() == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "计量不能为空");
        }
        serverReq.setPieceNum(clientReq.getPieceNum());
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPieceUnit(clientReq.getPieceUnit());
        serverReq.setInTaxRat(clientReq.getInTaxRat());
        serverReq.setOutTaxRat(clientReq.getOutTaxRat());
        if (clientReq.getDefaultInOutTax() != null) {
            serverReq.setDefaultInOutTax(clientReq.getDefaultInOutTax());
        }

        //材料特有规格
        serverReq.setMaterialSpec(clientReq.getMaterialSpec());
        serverReq.setCMSpec(clientReq.getMaterialSpec());

        /**
         * 商品特有相关
         * */
        serverReq.setRemark(clientReq.getRemark());
        serverReq.setCertificateNo(clientReq.getCertificateNo());
        serverReq.setCertificateName(clientReq.getCertificateName());

        /**
         * 社保相关
         * */
        if (clientReq.getShebao() != null) {
            serverReq.setNationalCode(clientReq.getShebao().getNationalCode());
            serverReq.setNationalCodeId(clientReq.getShebao().getNationalCodeId());
            serverReq.setMedicalFeeGrade(clientReq.getShebao().getMedicalFeeGrade());
            serverReq.setMatchMode(clientReq.getShebao().getMatchMode());
            serverReq.setCleanMatchCodeFlag(clientReq.getShebao().getCleanMatchCodeFlag());
        }
        /**
         * Goods社保支付方式
         * */
        serverReq.setShebaoPayMode(clientReq.getShebaoPayMode());

        /**
         * 是否对外销售
         * 不对外销售，没有价格
         * */
        if (clientReq.getIsSell() != null) {
            serverReq.setIsSell(clientReq.getIsSell().shortValue());
        }
        serverReq.setForceChangePieceNum(clientReq.getForce());


        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        serverReq.specificCheck();

        /**
         * 税率规则
         * */
        serverReq.setUpdateInOutTaxRate(clientReq.getUpdateInOutTaxRate());

        /***
         * 2022.09支持柜号
         * */
        serverReq.setPosition(clientReq.getPosition());
        if (clientReq.getPharmacyNo() != null) {
            serverReq.setPharmacyNo(clientReq.getPharmacyNo());//中药的药房编号
        }
        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());
        serverReq.setFeeCategoryId(clientReq.getFeeCategoryId());

        serverReq.setShelfLife(clientReq.getShelfLife());
        serverReq.setPriceType(clientReq.getPriceType());
        serverReq.setPriceMakeupPercent(clientReq.getPriceMakeupPercent());
        serverReq.setProfitCategoryType(clientReq.getProfitCategoryType());
        serverReq.setProfitCategoryType(clientReq.getProfitCategoryType());
        serverReq.setMha(clientReq.getMha());
        serverReq.setShelfLife(clientReq.getShelfLife());
        serverReq.setMaintainType(clientReq.getMaintainType());
        serverReq.setStorageType(clientReq.getStorageType());
        //EXtendData
        serverReq.setMedicineNmpnStartExpiryDate(clientReq.getMedicineNmpnStartExpiryDate());
        serverReq.setMedicineNmpnEndExpiryDate(clientReq.getMedicineNmpnEndExpiryDate());
        serverReq.setStorage(clientReq.getStorage());
        serverReq.setBusinessScopeList(clientReq.getBusinessScopeList());
        /**
         * 药品标签，后台做成项目都支持
         * */
        if (clientReq.getGoodsTagIdList() != null) {
            serverReq.setGoodsTagIdToSort(new HashMap<>());
            for (int i = 0; i < clientReq.getGoodsTagIdList().size(); i++) {
                serverReq.getGoodsTagIdToSort().put(clientReq.getGoodsTagIdList().get(i), i);
            }
        }
        /**
         * 多价格
         * */
        serverReq.setMultiPriceList(clientReq.getMultiPriceList());
        return serverReq;
    }

    private static ServerCreateMedicalDisInfectGoodsReq jenkinsCreateServerMedicalDisInfectGoodsReq(JenkinsGoodsImportItem clientReq) {
        ServerCreateMedicalDisInfectGoodsReq serverReq = new ServerCreateMedicalDisInfectGoodsReq();
        serverReq.setJenkinsGoodsImportItem(clientReq);
        /**
         * ID 相关
         * */
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setBarCode(clientReq.getBarCode());
        serverReq.setRemark(clientReq.getRemark());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId().intValue());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());
        serverReq.setManufacturerFull(clientReq.getManufacturerFull());
        serverReq.setMedicineNmpn(clientReq.getMedicineNmpn());

        /**
         * 价格和进销税等
         * */
        if (clientReq.getDismounting() != null)
            serverReq.setDismounting(clientReq.getDismounting().shortValue());
        serverReq.setPiecePrice(clientReq.getPiecePrice());
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        if (clientReq.getPieceNum() == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "计量不能为空");
        }
        serverReq.setPosition(clientReq.getPosition());
        serverReq.setPieceNum(clientReq.getPieceNum());
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPieceUnit(clientReq.getPieceUnit());
        serverReq.setInTaxRat(clientReq.getInTaxRat());
        serverReq.setOutTaxRat(clientReq.getOutTaxRat());

        //材料特有规格
        serverReq.setMaterialSpec(clientReq.getMaterialSpec());
        serverReq.setCMSpec(clientReq.getMaterialSpec());

        /**
         * 商品特有相关
         * */
        serverReq.setRemark(null);
        serverReq.setCertificateNo(clientReq.getCertificateNo());
        serverReq.setCertificateName(clientReq.getCertificateName());


        /**
         * 是否对外销售
         * */
        if (clientReq.getIsSell() != null) {
            serverReq.setIsSell(clientReq.getIsSell().shortValue());
        }


        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        serverReq.specificCheck();

        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getTypeId().longValue());
        serverReq.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);
        if (clientReq.getFeeTypeId() != 0L) {
            serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        }
        serverReq.setFeeCategoryId(clientReq.getFeeCategoryId());
        serverReq.setShelfLife(clientReq.getShelfLife());
        serverReq.setMaintainType(clientReq.getMaintainType());
        serverReq.setStorageType(clientReq.getStorageType());
        serverReq.setMha(clientReq.getMha());
        serverReq.setProfitCategoryType(clientReq.getProfitCategoryType());
        serverReq.setFirstSupplierId(clientReq.getFirstSupplierId());
//        //EXtendData
        serverReq.setMedicineNmpnStartExpiryDate(clientReq.getMedicineNmpnStartExpiryDate());
        serverReq.setMedicineNmpnEndExpiryDate(clientReq.getMedicineNmpnEndExpiryDate());
        serverReq.setStorage(clientReq.getStorage());
        serverReq.setBusinessScopeList(clientReq.getBusinessScopeList());
        if (clientReq.getPriceType() != null) {
            serverReq.setPriceType(clientReq.getPriceType());
            serverReq.setPriceMakeupPercent(clientReq.getPriceMakeupPercent());
        }

        return serverReq;
    }

    //
    private static ServerCreateMedicalMaterialGoodsReq createServerMedicalMaterialGoodsReq(ClientGoodsCreateReq clientReq) {
        ServerCreateMedicalMaterialGoodsReq serverReq = new ServerCreateMedicalMaterialGoodsReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setBarCode(clientReq.getBarCode());
        serverReq.setTraceableCodeNoInfoList(clientReq.getTraceableCodeNoInfoList());
        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());
        serverReq.setManufacturerFull(clientReq.getManufacturerFull());
        serverReq.setMedicineNmpn(clientReq.getMedicineNmpn());

        /**
         * 价格和进销税等
         * */
        if (clientReq.getDismounting() != null) {
            serverReq.setDismounting(clientReq.getDismounting().shortValue());
        }
        serverReq.setPiecePrice(clientReq.getPiecePrice());
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        if (clientReq.getPieceNum() == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "计量不能为空");
        }
        serverReq.setPieceNum(clientReq.getPieceNum());
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPieceUnit(clientReq.getPieceUnit());
        serverReq.setInTaxRat(clientReq.getInTaxRat());
        serverReq.setOutTaxRat(clientReq.getOutTaxRat());
        if (clientReq.getDefaultInOutTax() != null) {
            serverReq.setDefaultInOutTax(clientReq.getDefaultInOutTax());
        }

        //材料特有规格
        serverReq.setMaterialSpec(clientReq.getMaterialSpec());
        serverReq.setCMSpec(clientReq.getMaterialSpec());

        /**
         * 商品特有相关
         * */
        serverReq.setOrigin(clientReq.getOrigin());
        serverReq.setRemark(clientReq.getRemark());
        serverReq.setCertificateNo(clientReq.getCertificateNo());
        serverReq.setCertificateName(clientReq.getCertificateName());

        /**
         * 社保相关
         * */
        if (clientReq.getShebao() != null) {
            serverReq.setNationalCode(clientReq.getShebao().getNationalCode());
            serverReq.setNationalCodeId(clientReq.getShebao().getNationalCodeId());
            serverReq.setMedicalFeeGrade(clientReq.getShebao().getMedicalFeeGrade());
            serverReq.setMatchMode(clientReq.getShebao().getMatchMode());
            serverReq.setCleanMatchCodeFlag(clientReq.getShebao().getCleanMatchCodeFlag());
        }
        /**
         * Goods社保支付方式
         * */
        serverReq.setShebaoPayMode(clientReq.getShebaoPayMode());

        /**
         * 是否对外销售
         * 不对外销售，没有价格
         * */
        if (clientReq.getIsSell() != null) {
            serverReq.setIsSell(clientReq.getIsSell().shortValue());
        }
        serverReq.setForceChangePieceNum(clientReq.getForce());


        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        serverReq.specificCheck();

        /**
         * 税率规则
         * */
        serverReq.setUpdateInOutTaxRate(clientReq.getUpdateInOutTaxRate());

        /***
         * 2022.09支持柜号
         * */
        serverReq.setPosition(clientReq.getPosition());
        if (clientReq.getPharmacyNo() != null) {
            serverReq.setPharmacyNo(clientReq.getPharmacyNo());//中药的药房编号
        }
        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());
        serverReq.setFeeCategoryId(clientReq.getFeeCategoryId());

        serverReq.setShelfLife(clientReq.getShelfLife());
        serverReq.setPriceType(clientReq.getPriceType());
        serverReq.setPriceMakeupPercent(clientReq.getPriceMakeupPercent());
        serverReq.setProfitCategoryType(clientReq.getProfitCategoryType());
        serverReq.setDeviceType(clientReq.getDeviceType());
        serverReq.setProfitCategoryType(clientReq.getProfitCategoryType());
        serverReq.setMha(clientReq.getMha());
        serverReq.setShelfLife(clientReq.getShelfLife());
        serverReq.setMaintainType(clientReq.getMaintainType());
        serverReq.setStorageType(clientReq.getStorageType());
        //EXtendData
        serverReq.setMedicineNmpnStartExpiryDate(clientReq.getMedicineNmpnStartExpiryDate());
        serverReq.setMedicineNmpnEndExpiryDate(clientReq.getMedicineNmpnEndExpiryDate());
        serverReq.setStorage(clientReq.getStorage());
        serverReq.setBusinessScopeList(clientReq.getBusinessScopeList());
        serverReq.setIsPreciousDevice(clientReq.getIsPreciousDevice());
        /**
         * 药品标签，后台做成项目都支持
         * */
        if (clientReq.getGoodsTagIdList() != null) {
            serverReq.setGoodsTagIdToSort(new HashMap<>());
            for (int i = 0; i < clientReq.getGoodsTagIdList().size(); i++) {
                serverReq.getGoodsTagIdToSort().put(clientReq.getGoodsTagIdList().get(i), i);
            }
        }
        /**
         * 多价格
         * */
        serverReq.setMultiPriceList(clientReq.getMultiPriceList());
        return serverReq;
    }

    private static ServerCreateMaterialGoodsReq jenkinsCreateServerMaterialGoodsReq(JenkinsGoodsImportItem clientReq) {
        ServerCreateMaterialGoodsReq serverReq = new ServerCreateMaterialGoodsReq();
        serverReq.setJenkinsGoodsImportItem(clientReq);
        /**
         * ID 相关
         * */
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setBarCode(clientReq.getBarCode());
        serverReq.setRemark(clientReq.getRemark());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId().intValue());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());
        serverReq.setManufacturerFull(clientReq.getManufacturerFull());
        serverReq.setMedicineNmpn(clientReq.getMedicineNmpn());

        serverReq.setRemark(null);
        serverReq.setOrigin(clientReq.getOrigin());

        /**
         * 价格和进销税等
         * */
        if (clientReq.getDismounting() != null)
            serverReq.setDismounting(clientReq.getDismounting().shortValue());
        serverReq.setPiecePrice(clientReq.getPiecePrice());
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        if (clientReq.getPieceNum() == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "计量不能为空");
        }
        serverReq.setPosition(clientReq.getPosition());
        serverReq.setPieceNum(clientReq.getPieceNum());
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPieceUnit(clientReq.getPieceUnit());

        //材料特有规格
        serverReq.setMaterialSpec(clientReq.getMaterialSpec());
        serverReq.setCMSpec(clientReq.getMaterialSpec());


        /**
         * 是否对外销售
         * */
        if (clientReq.getIsSell() != null) {
            serverReq.setIsSell(clientReq.getIsSell().shortValue());
        }


        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        serverReq.specificCheck();

        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getTypeId().longValue());
        serverReq.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);
        if (clientReq.getFeeTypeId() != 0L) {
            serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        }
        serverReq.setFeeCategoryId(clientReq.getFeeCategoryId());

        return serverReq;
    }

    private static ServerCreateMedicalMaterialGoodsReq jenkinsCreateServerMedicalMaterialGoodsReq(JenkinsGoodsImportItem clientReq) {
        ServerCreateMedicalMaterialGoodsReq serverReq = new ServerCreateMedicalMaterialGoodsReq();
        serverReq.setJenkinsGoodsImportItem(clientReq);
        /**
         * ID 相关
         * */
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setBarCode(clientReq.getBarCode());
        serverReq.setRemark(clientReq.getRemark());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId().intValue());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());
        serverReq.setManufacturerFull(clientReq.getManufacturerFull());
        serverReq.setMedicineNmpn(clientReq.getMedicineNmpn());

        /*
         * 价格和进销税等
         * */
        if (clientReq.getDismounting() != null){
            serverReq.setDismounting(clientReq.getDismounting().shortValue());
        }
        serverReq.setPiecePrice(clientReq.getPiecePrice());
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        if (clientReq.getPieceNum() == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "计量不能为空");
        }
        serverReq.setPosition(clientReq.getPosition());
        serverReq.setPieceNum(clientReq.getPieceNum());
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPieceUnit(clientReq.getPieceUnit());
        serverReq.setInTaxRat(clientReq.getInTaxRat());
        serverReq.setOutTaxRat(clientReq.getOutTaxRat());

        //材料特有规格
        serverReq.setMaterialSpec(clientReq.getMaterialSpec());
        serverReq.setCMSpec(clientReq.getMaterialSpec());

        /**
         * 商品特有相关
         * */
        serverReq.setOrigin(clientReq.getOrigin());
        serverReq.setRemark(null);
        serverReq.setCertificateNo(clientReq.getCertificateNo());
        serverReq.setCertificateName(clientReq.getCertificateName());


        /**
         * 是否对外销售
         * */
        if (clientReq.getIsSell() != null) {
            serverReq.setIsSell(clientReq.getIsSell().shortValue());
        }


        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        serverReq.specificCheck();

        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getTypeId().longValue());
        serverReq.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);
        if (clientReq.getFeeTypeId() != 0L) {
            serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        }
        serverReq.setFeeCategoryId(clientReq.getFeeCategoryId());
        serverReq.setShelfLife(clientReq.getShelfLife());
        serverReq.setMaintainType(clientReq.getMaintainType());
        serverReq.setStorageType(clientReq.getStorageType());
        serverReq.setMha(clientReq.getMha());
        serverReq.setDeviceType(clientReq.getDeviceType());
        serverReq.setProfitCategoryType(clientReq.getProfitCategoryType());
        serverReq.setFirstSupplierId(clientReq.getFirstSupplierId());
//        //EXtendData
        serverReq.setMedicineNmpnStartExpiryDate(clientReq.getMedicineNmpnStartExpiryDate());
        serverReq.setMedicineNmpnEndExpiryDate(clientReq.getMedicineNmpnEndExpiryDate());
        serverReq.setStorage(clientReq.getStorage());
        serverReq.setBusinessScopeList(clientReq.getBusinessScopeList());
        if (clientReq.getPriceType() != null) {
            serverReq.setPriceType(clientReq.getPriceType());
            serverReq.setPriceMakeupPercent(clientReq.getPriceMakeupPercent());
        }

        return serverReq;
    }

    /**
     * 建附加品请求
     */
    private static ServerCreateAdditionalGoodsReq createServerAdditionalGoodsReq(ClientGoodsCreateReq clientReq) {
        ServerCreateAdditionalGoodsReq serverReq = new ServerCreateAdditionalGoodsReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setBarCode(clientReq.getBarCode());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());
        serverReq.setManufacturerFull(clientReq.getManufacturerFull());
        serverReq.setMedicineNmpn(clientReq.getMedicineNmpn());
        serverReq.setPriceType(clientReq.getPriceType());
        serverReq.setPriceMakeupPercent(clientReq.getPriceMakeupPercent());


        /**
         * 价格和进销税等
         * */
        if (clientReq.getDismounting() != null) {
            serverReq.setDismounting(clientReq.getDismounting().shortValue());
        }
        serverReq.setPiecePrice(clientReq.getPiecePrice());
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        if (clientReq.getPieceNum() == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "计量不能为空");
        }
        serverReq.setPieceNum(clientReq.getPieceNum());
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPieceUnit(clientReq.getPieceUnit());
        serverReq.setInTaxRat(clientReq.getInTaxRat());
        serverReq.setOutTaxRat(clientReq.getOutTaxRat());
        if (clientReq.getDefaultInOutTax() != null) {
            serverReq.setDefaultInOutTax(clientReq.getDefaultInOutTax());
        }

        //商品类型特有规格
        serverReq.setMaterialSpec(clientReq.getMaterialSpec());

        /**
         * 商品特有相关
         * */
        serverReq.setOrigin(clientReq.getOrigin());
        serverReq.setRemark(clientReq.getRemark());
        serverReq.setCertificateNo(clientReq.getCertificateNo());
        serverReq.setCertificateName(clientReq.getCertificateName());

        /**
         * 社保相关
         * */
        if (clientReq.getShebao() != null) {
            serverReq.setNationalCode(clientReq.getShebao().getNationalCode());
            serverReq.setNationalCodeId(clientReq.getShebao().getNationalCodeId());
            serverReq.setMedicalFeeGrade(clientReq.getShebao().getMedicalFeeGrade());
        }
        /**
         * Goods社保支付方式
         * */
        serverReq.setShebaoPayMode(clientReq.getShebaoPayMode());

        /**
         * 是否对外销售
         * 不对外销售，没有价格
         * */
        if (clientReq.getIsSell() != null) {
            serverReq.setIsSell(clientReq.getIsSell().shortValue());
        }

        //默认是化妆品
        if (serverReq.getIsSell() == null && serverReq.getSubType() == GoodsConst.GoodsAdditionSubType.COSMETICS) {
            serverReq.setIsSell((short) YesOrNo.YES);
        }
        serverReq.setForceChangePieceNum(clientReq.getForce());

        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        serverReq.specificCheck();

        /**
         * 税率规则
         * */
        serverReq.setUpdateInOutTaxRate(clientReq.getUpdateInOutTaxRate());


        /***
         * 2022.09支持柜号
         * */
        serverReq.setPosition(clientReq.getPosition());
        if (clientReq.getPharmacyNo() != null) {
            serverReq.setPharmacyNo(clientReq.getPharmacyNo());//中药的药房编号
        }

        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());
        serverReq.setFeeCategoryId(clientReq.getFeeCategoryId());
        serverReq.setProfitCategoryType(clientReq.getProfitCategoryType());
        //EXtendData
        serverReq.setMedicineNmpnStartExpiryDate(clientReq.getMedicineNmpnStartExpiryDate());
        serverReq.setMedicineNmpnEndExpiryDate(clientReq.getMedicineNmpnEndExpiryDate());
        serverReq.setStorage(clientReq.getStorage());
        serverReq.setBusinessScopeList(clientReq.getBusinessScopeList());
        /**
         * 药品标签，后台做成项目都支持
         * */
        if (clientReq.getGoodsTagIdList() != null) {
            serverReq.setGoodsTagIdToSort(new HashMap<>());
            for (int i = 0; i < clientReq.getGoodsTagIdList().size(); i++) {
                serverReq.getGoodsTagIdToSort().put(clientReq.getGoodsTagIdList().get(i), i);
            }
        }
        /**
         * 多价格
         * */
        serverReq.setMultiPriceList(clientReq.getMultiPriceList());


        return serverReq;
    }

    private static ServerCreateAdditionalGoodsReq jenkinsCreateServerAdditionalGoodsReq(JenkinsGoodsImportItem clientReq) {
        ServerCreateAdditionalGoodsReq serverReq = new ServerCreateAdditionalGoodsReq();
        serverReq.setJenkinsGoodsImportItem(clientReq);
        /**
         * ID 相关
         * */
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setBarCode(clientReq.getBarCode());
        serverReq.setRemark(clientReq.getRemark());


        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId().intValue());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());
        serverReq.setManufacturerFull(clientReq.getManufacturerFull());
        serverReq.setMedicineNmpn(clientReq.getMedicineNmpn());
        serverReq.setPosition(clientReq.getPosition());

        /**
         * 价格和进销税等
         * */
        if (clientReq.getDismounting() != null)
            serverReq.setDismounting(clientReq.getDismounting().shortValue());
        serverReq.setPiecePrice(clientReq.getPiecePrice());
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        if (clientReq.getPieceNum() == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "计量不能为空");
        }
        serverReq.setPieceNum(clientReq.getPieceNum());
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPieceUnit(clientReq.getPieceUnit());
        serverReq.setInTaxRat(clientReq.getInTaxRat());
        serverReq.setOutTaxRat(clientReq.getOutTaxRat());

        //商品类型特有规格
        serverReq.setMaterialSpec(clientReq.getMaterialSpec());

        /**
         * 商品特有相关
         * */
        serverReq.setOrigin(clientReq.getOrigin());
        serverReq.setRemark(null);
        serverReq.setCertificateNo(clientReq.getCertificateNo());
        serverReq.setCertificateName(clientReq.getCertificateName());


        /**
         * 是否对外销售
         * */
        if (clientReq.getIsSell() != null) {
            serverReq.setIsSell(clientReq.getIsSell().shortValue());
        }

        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        serverReq.specificCheck();

        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getTypeId().intValue());
        serverReq.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);
        if (clientReq.getFeeTypeId() != 0L) {
            serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        }
        serverReq.setFeeCategoryId(clientReq.getFeeCategoryId());
        serverReq.setProfitCategoryType(clientReq.getProfitCategoryType());
        serverReq.setFirstSupplierId(clientReq.getFirstSupplierId());
        // ExtendData
        serverReq.setMedicineNmpnStartExpiryDate(clientReq.getMedicineNmpnStartExpiryDate());
        serverReq.setMedicineNmpnEndExpiryDate(clientReq.getMedicineNmpnEndExpiryDate());
        serverReq.setBusinessScopeList(clientReq.getBusinessScopeList());
        if (clientReq.getPriceType() != null) {
            serverReq.setPriceType(clientReq.getPriceType());
            serverReq.setPriceMakeupPercent(clientReq.getPriceMakeupPercent());
        }

        return serverReq;
    }

    /**
     * 检查检验请求
     */
    private static ServerCreateExaminationGoodsReq createServerExaminationGoodsReq(ClientGoodsCreateReq clientReq) {
        ServerCreateExaminationGoodsReq serverReq = new ServerCreateExaminationGoodsReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());

        /**
         * 价格和进销税等
         * */
        serverReq.setDismounting(GoodsConst.DismountingStatus.PACKAGE);
        serverReq.setPiecePrice(null);
        /**
         * 新版本Lis的售价和陈本价可以是null的，
         * null代表未设置售价，未设置售价仍旧可以销售。
         * 因为模版goods是没有价格的
         * */
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        serverReq.setPackageCostPrice(clientReq.getPackageCostPrice());
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);
        serverReq.setPieceUnit(null);
        if (StringUtils.isEmpty(clientReq.getPackageUnit())) {
            serverReq.setPackageUnit(DEFAULT_EXAM_PKG_UNIT);
        } else {
            serverReq.setPackageUnit(clientReq.getPackageUnit());
        }
        /**
         * 检查检验设备 的ID
         * 新版LIS 一定会让goods 绑定一个设备ID
         * */
        serverReq.setBizRelevantId(clientReq.getBizRelevantId());
        serverReq.setExtendInfo(clientReq.getExtendInfo());

        /**
         * 治疗理疗特有
         * */
        serverReq.setEnName(clientReq.getEnName()); //检查检验的代码
        serverReq.setBizExtensions(clientReq.getBizExtensions()); //检查检验的样本类型
        if (clientReq.getCombineType() != null) {
            serverReq.setCombineType(clientReq.getCombineType().shortValue());//检查检验的组合类型
        }

        /**
         * 这是回射给调用放的 ID ，goods不存也不理解他的含义
         * */
        serverReq.setBizRefId(clientReq.getBizRefId());

        /**
         * 设置扩展类型
         * */
        serverReq.setExtendSpec(clientReq.getExtendSpec());
        serverReq.setDeviceType(clientReq.getDeviceType());
        serverReq.setInspectionSite(clientReq.getInspectionSite());
        serverReq.setGender(clientReq.getGender());
        serverReq.setSourceGoodsId(clientReq.getSourceGoodsId());
        serverReq.setCopyFlag(clientReq.getCopyFlag());
        serverReq.setInnerFlag(clientReq.getInnerFlag());

        /**
         * 模版门店的检验goods要写备注信息
         * */
        serverReq.setRemark(clientReq.getRemark());


        /**
         * 社保相关
         * */
        if (clientReq.getShebao() != null) {
            serverReq.setNationalCode(clientReq.getShebao().getNationalCode());
            serverReq.setNationalCodeId(clientReq.getShebao().getNationalCodeId());
            serverReq.setMedicalFeeGrade(clientReq.getShebao().getMedicalFeeGrade());
        }
        /**
         * Goods社保支付方式
         * */
        serverReq.setShebaoPayMode(clientReq.getShebaoPayMode());

        /**
         * 是否对外销售
         * 新版本Lis  检查goods还是通过这个字段设置。
         * 检验Goods 是否能地外销售  有设备关联
         * */
        if (clientReq.getIsSell() != null) {
            serverReq.setIsSell(clientReq.getIsSell().shortValue());
        }


        /**
         * 子项也要绑定到这个 设备上
         * */
        switch (clientReq.getSubType()) {
            case (int) GoodsConst.ExaminationSubType.ASSAY:
                serverReq.setChildren(genExamComposeGoodsChildrenList(clientReq.getBizRelevantId(), clientReq.getChildren()));
                break;
            case (int) GoodsConst.ExaminationSubType.INSPECTION:
                switch (clientReq.getExtendSpec()) {
                    case GoodsConst.GoodsExtendSpec.EYE_INSPECT:
                        // 眼科检查的设备id不必跟随母项
                        serverReq.setChildren(genEyeInspectionComposeGoodsChildrenList(clientReq.getBizRelevantId(), clientReq.getChildren()));
                        break;
                    case GoodsConst.GoodsExtendSpec.RIS_INSPECT:
                    default:
                        break;
                }
                break;
            default:
                break;
        }
        serverReq.setReturnExistGoods(clientReq.getReturnExistGoods());
        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();


        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());
        serverReq.setFeeComposeList(genFeeComposeChildrenList(clientReq.getFeeComposeList()));
        serverReq.setAssociationComposeList(genAssociationComposeChildrenList(clientReq.getAssociationComposeList()));
        /**
         * 药品标签，后台做成项目都支持
         * */
        if (clientReq.getGoodsTagIdList() != null) {
            serverReq.setGoodsTagIdToSort(new HashMap<>());
            for (int i = 0; i < clientReq.getGoodsTagIdList().size(); i++) {
                serverReq.getGoodsTagIdToSort().put(clientReq.getGoodsTagIdList().get(i), i);
            }
        }
        /**
         * 多价格
         * */
        serverReq.setMultiPriceList(clientReq.getMultiPriceList());

        return serverReq;
    }

    private static ServerCreateExaminationGoodsReq jenkinsCreateServerExaminationGoodsReq(JenkinsGoodsImportItem clientReq) {
        ServerCreateExaminationGoodsReq serverReq = new ServerCreateExaminationGoodsReq();

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId().intValue());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());

        /**
         * 价格和进销税等
         * */
        serverReq.setDismounting(GoodsConst.DismountingStatus.PACKAGE);
        serverReq.setPiecePrice(null);
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);
        serverReq.setPieceUnit(null);
        if (StringUtils.isEmpty(clientReq.getPackageUnit())) {
            serverReq.setPackageUnit(DEFAULT_EXAM_PKG_UNIT);
        } else {
            serverReq.setPackageUnit(clientReq.getPackageUnit());
        }

        serverReq.setPackageCostPrice(clientReq.getPackageCostPrice());

        /**
         * 治疗理疗特有
         * */
        if (clientReq.getCombineType() != null) {
            serverReq.setCombineType(clientReq.getCombineType().shortValue());//检查检验的组合类型
        }

        /**
         * 子项也要绑定到这个 设备上
         * */
        if (GoodsTypeHelper.isExaminationAssay(clientReq.getType(), clientReq.getSubType())) {
            serverReq.setChildren(genExamComposeGoodsChildrenList(clientReq.getBizRelevantId(), clientReq.getChildren()));
        } else {
            serverReq.setChildren(genEyeInspectionComposeGoodsChildrenList(clientReq.getBizRelevantId(), clientReq.getChildren()));
        }

        serverReq.setExtendSpec(null);
        serverReq.setEnName(clientReq.getEnName());
        serverReq.setBizRelevantId(clientReq.getBizRelevantId());
        serverReq.setBizRefId(clientReq.getBizRefId());
        serverReq.setBizExtensions(clientReq.getBizExtensions());
        /**
         * 默认值,中药初始化的潜规则
         * */
        if (clientReq.getIsSell() != null) {
            serverReq.setIsSell(clientReq.getIsSell().shortValue());
        }
        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getTypeId().longValue());
        serverReq.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);


        return serverReq;
    }


    /**
     * 建治疗理疗请求
     */
    private static ServerCreateTreatmentGoodsReq createServerTreatmentGoodsReq(ClientGoodsCreateReq clientReq) {
        ServerCreateTreatmentGoodsReq serverReq = new ServerCreateTreatmentGoodsReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());

        /**
         * 价格和进销税等,治疗理疗有成本价
         * */
        serverReq.setDismounting(GoodsConst.DismountingStatus.PACKAGE);
        serverReq.setPiecePrice(null);
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);
        serverReq.setPieceUnit(null);
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPackageCostPrice(clientReq.getPackageCostPrice());


        /**
         * 社保相关
         * */
        if (clientReq.getShebao() != null) {
            serverReq.setNationalCode(clientReq.getShebao().getNationalCode());
            serverReq.setNationalCodeId(clientReq.getShebao().getNationalCodeId());
            serverReq.setMedicalFeeGrade(clientReq.getShebao().getMedicalFeeGrade());

        }

        /**
         * 价格和进销税等,治疗理疗有成本价
         * */
        if (clientReq.getNeedExecutive() != null) {
            serverReq.setNeedExecutive(clientReq.getNeedExecutive().shortValue());
        }
        if (clientReq.getHospitalNeedExecutive() != null) {
            serverReq.setHospitalNeedExecutive(clientReq.getHospitalNeedExecutive());
            serverReq.setNeedExecutive((short) ((serverReq.getNeedExecutive() != null ? serverReq.getNeedExecutive() : 0) | (clientReq.getHospitalNeedExecutive() << 1)));
        }
        /**
         * Goods社保支付方式
         * */
        serverReq.setShebaoPayMode(clientReq.getShebaoPayMode());

        /**
         * 默认值,中药初始化的潜规则
         * */
        serverReq.setIsSell(GoodsConst.SellStatus.SELL); //强制对外销售
        serverReq.setForceChangePieceNum(clientReq.getForce());
        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();
        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());
        serverReq.setFeeComposeList(genFeeComposeChildrenList(clientReq.getFeeComposeList()));
        serverReq.setExtendInfo(clientReq.getExtendInfo());
        /**
         * 药品标签，后台做成项目都支持
         * */
        if (clientReq.getGoodsTagIdList() != null) {
            serverReq.setGoodsTagIdToSort(new HashMap<>());
            for (int i = 0; i < clientReq.getGoodsTagIdList().size(); i++) {
                serverReq.getGoodsTagIdToSort().put(clientReq.getGoodsTagIdList().get(i), i);
            }
        }
        /**
         * 多价格
         * */
        serverReq.setMultiPriceList(clientReq.getMultiPriceList());

        return serverReq;
    }

    private static ServerCreateTreatmentGoodsReq jenkinsCreateServerTreatmentGoodsReq(JenkinsGoodsImportItem clientReq) {
        ServerCreateTreatmentGoodsReq serverReq = new ServerCreateTreatmentGoodsReq();
        serverReq.setJenkinsGoodsImportItem(clientReq);
        /**
         * ID 相关
         * */
        serverReq.setShortId(clientReq.getShortId());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId().intValue());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());

        /**
         * 价格和进销税等,治疗理疗有成本价
         * */
        serverReq.setDismounting(GoodsConst.DismountingStatus.PACKAGE);
        serverReq.setPiecePrice(null);
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);
        serverReq.setPieceUnit(null);
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPackageCostPrice(clientReq.getPackageCostPrice());


        /**
         * 默认值,中药初始化的潜规则
         * */
        serverReq.setIsSell(GoodsConst.SellStatus.SELL); //强制对外销售
        if (clientReq.getNeedExecutive() != null) {
            serverReq.setNeedExecutive(clientReq.getNeedExecutive().shortValue());
        }
        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getTypeId().longValue());
        serverReq.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);

        return serverReq;
    }


    /**
     * 治疗理疗里面的其他子类 移动出来 变成 一级大类
     * 2022.04.08
     */
    private static ServerCreateOtherGoodsReq createServerOtherGoodsReq(ClientGoodsCreateReq clientReq) {
        ServerCreateOtherGoodsReq serverReq = new ServerCreateOtherGoodsReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());

        /**
         * 价格和进销税等,治疗理疗有成本价
         * */
        serverReq.setDismounting(GoodsConst.DismountingStatus.PACKAGE);
        serverReq.setPiecePrice(null);
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);
        serverReq.setPieceUnit(null);
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPackageCostPrice(clientReq.getPackageCostPrice());


        /**
         * 社保相关
         * */
        if (clientReq.getShebao() != null) {
            serverReq.setNationalCode(clientReq.getShebao().getNationalCode());
            serverReq.setNationalCodeId(clientReq.getShebao().getNationalCodeId());
            serverReq.setMedicalFeeGrade(clientReq.getShebao().getMedicalFeeGrade());
            serverReq.setPriceLimit(clientReq.getShebao().getPriceLimit());
        }
        /**
         * Goods社保支付方式
         * */
        serverReq.setShebaoPayMode(clientReq.getShebaoPayMode());

        /**
         * 默认值,中药初始化的潜规则
         * */
        serverReq.setIsSell(GoodsConst.SellStatus.SELL); //强制对外销售
        if (clientReq.getNeedExecutive() != null) {
            serverReq.setNeedExecutive(clientReq.getNeedExecutive().shortValue());
        }
        serverReq.setForceChangePieceNum(clientReq.getForce());
        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());
        serverReq.setFeeComposeList(genFeeComposeChildrenList(clientReq.getFeeComposeList()));

        serverReq.setFeeCategoryId(clientReq.getFeeCategoryId());
        serverReq.setShebaoPayLimitPriceRule(clientReq.getShebaoPayLimitPriceRule());

        /**
         * 药品标签，后台做成项目都支持
         * */
        if (clientReq.getGoodsTagIdList() != null) {
            serverReq.setGoodsTagIdToSort(new HashMap<>());
            for (int i = 0; i < clientReq.getGoodsTagIdList().size(); i++) {
                serverReq.getGoodsTagIdToSort().put(clientReq.getGoodsTagIdList().get(i), i);
            }
        }
        /**
         * 多价格
         * */
        serverReq.setMultiPriceList(clientReq.getMultiPriceList());

        return serverReq;
    }

    /**
     * 治疗理疗里面的其他子类 移动出来   jenkins任务
     * 2022.04.08
     */
    private static ServerCreateOtherGoodsReq jenkinsCreateServerOtherGoodsReq(JenkinsGoodsImportItem clientReq) {
        ServerCreateOtherGoodsReq serverReq = new ServerCreateOtherGoodsReq();
        serverReq.setJenkinsGoodsImportItem(clientReq);
        /*
         * ID 相关
         * */
        serverReq.setShortId(clientReq.getShortId());

        /*
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId().intValue());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /*
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());

        /*
         * 价格和进销税等,治疗理疗有成本价
         * */
        serverReq.setDismounting(GoodsConst.DismountingStatus.PACKAGE);
        serverReq.setPiecePrice(clientReq.getPiecePrice());
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);
        serverReq.setPieceUnit(null);
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPackageCostPrice(clientReq.getPackageCostPrice());


        /*
         * 默认值,中药初始化的潜规则
         * */
        if(clientReq.getIsSell() != null){
            serverReq.setIsSell(clientReq.getIsSell().shortValue()); //强制对外销售
        }
        if (clientReq.getNeedExecutive() != null) {
            serverReq.setNeedExecutive(clientReq.getNeedExecutive().shortValue());
        }
        /*
         * 如果是对内销售，修正销售价格为 null
         * */
        //serverReq.fixSellAndPrice();

        /*
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getTypeId().longValue());
        serverReq.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);
        if (clientReq.getFeeTypeId() != 0L) {
            serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        }
        serverReq.setFeeCategoryId(clientReq.getFeeCategoryId());
        serverReq.setInnerFlag(clientReq.getInnerFlag());

        serverReq.setShebaoPayLimitPriceRule(clientReq.getShebaoPayLimitPriceRule());

        return serverReq;
    }

    /**
     * 建套餐请求
     */
    private static ServerCreateComposeGoodsReq createServerComposeGoodsReq(ClientGoodsCreateReq clientReq) {
        ServerCreateComposeGoodsReq serverReq = new ServerCreateComposeGoodsReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());


        /**
         * 价格和进销税等,治疗理疗有成本价
         * */
        serverReq.setDismounting(GoodsConst.DismountingStatus.PACKAGE);
        serverReq.setPiecePrice(BigDecimal.ZERO);
        serverReq.setPackagePrice(BigDecimal.ZERO);
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);
        serverReq.setPieceUnit(null);
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPackageCostPrice(clientReq.getPackageCostPrice());

        /**
         * 默认值,中药初始化的潜规则
         * */
        serverReq.setIsSell(GoodsConst.SellStatus.SELL);
        serverReq.setCombineType(GoodsConst.GoodsCombine.COMBINE);
        serverReq.setGender(clientReq.getGender());
        serverReq.setApplyPopulation(clientReq.getApplyPopulation());

        serverReq.setChildren(genComposeGoodsChildrenList(clientReq.getChildren()));
        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());
        /**
         * 药品标签，后台做成项目都支持
         * */
        if (clientReq.getGoodsTagIdList() != null) {
            serverReq.setGoodsTagIdToSort(new HashMap<>());
            for (int i = 0; i < clientReq.getGoodsTagIdList().size(); i++) {
                serverReq.getGoodsTagIdToSort().put(clientReq.getGoodsTagIdList().get(i), i);
            }
        }

        return serverReq;
    }

    private static ServerCreatePhysicalExaminationComposeGoodsReq createServerPhysicalExaminationComposeGoodsReq(ClientGoodsCreateReq clientReq) {
        ServerCreatePhysicalExaminationComposeGoodsReq serverReq = new ServerCreatePhysicalExaminationComposeGoodsReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());


        /**
         * 价格和进销税等,治疗理疗有成本价
         * */
        serverReq.setDismounting(GoodsConst.DismountingStatus.PACKAGE);
        serverReq.setPiecePrice(BigDecimal.ZERO);
        serverReq.setPackagePrice(BigDecimal.ZERO);
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);
        serverReq.setPieceUnit(null);
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPackageCostPrice(clientReq.getPackageCostPrice());

        /**
         * 默认值,中药初始化的潜规则
         * */
        serverReq.setIsSell(GoodsConst.SellStatus.SELL);
        serverReq.setCombineType(GoodsConst.GoodsCombine.COMBINE);
        serverReq.setGender(clientReq.getGender());
        serverReq.setApplyPopulation(clientReq.getApplyPopulation());

        serverReq.setChildren(genComposeGoodsChildrenList(clientReq.getChildren()));
        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());
        serverReq.setBizExtensions(JsonUtils.readValue(clientReq.getBizExtensions(), GoodsPhysicalExaminationComposeExtensions.class));
        serverReq.setReturnExistGoods(clientReq.getReturnExistGoods());
        /**
         * 药品标签，后台做成项目都支持
         * */
        if (clientReq.getGoodsTagIdList() != null) {
            serverReq.setGoodsTagIdToSort(new HashMap<>());
            for (int i = 0; i < clientReq.getGoodsTagIdList().size(); i++) {
                serverReq.getGoodsTagIdToSort().put(clientReq.getGoodsTagIdList().get(i), i);
            }
        }

        return serverReq;
    }

    /**
     * 套餐子项
     */
    public static List<ServerCreateComposeChildrenBase> genComposeGoodsChildrenList(List<ClientGoodsCreateComposeItemReq> children) {
        List<ServerCreateComposeChildrenBase> childrenList = new ArrayList<>();
        if (CollectionUtils.isEmpty(children)) {
            return childrenList;
        }
        for (ClientGoodsCreateComposeItemReq child : children) {
            ServerCreateComposeChildren childReturn = new ServerCreateComposeChildren();
            childReturn.setId(child.getId());
            childReturn.setComposeId(child.getComposeId());
            childReturn.setCreateGoodsRefId(child.getCreateGoodsRefId());
            childReturn.setName(child.getName());
            childReturn.setComposeUseDismounting(child.getComposeUseDismounting() != null ? child.getComposeUseDismounting().shortValue() : GoodsConst.DismountingStatus.PACKAGE);
            childReturn.setComposePackageCount(child.getComposePackageCount());
            childReturn.setShebaoPayMode(child.getShebaoPayMode());
            childReturn.setNationalCode(child.getNationalCode());
            childReturn.setNationalCodeId(child.getNationalCodeId());
            childReturn.setCentralCode(child.getCentralCode());

            childReturn.setComposePackagePrice(child.getComposePackagePrice());
            childReturn.setComposePieceCount(child.getComposePieceCount());
            childReturn.setComposePiecePrice(child.getComposePiecePrice());
            childReturn.setComposePrice(child.getComposePrice());
            childReturn.setComposeFractionPrice(child.getComposeFractionPrice());
            childReturn.setPackageUnit(child.getPackageUnit());// 项目判重规则加上了大包单位，一定要带上
            childReturn.setSurgeryGradeCode(child.getSurgeryGradeCode());
            childReturn.setSurgeryIncisionHealingCode(child.getSurgeryIncisionHealingCode());
            childReturn.setSurgerySiteCode(child.getSurgerySiteCode());
            childReturn.setOperationCode(child.getOperationCode());
            childReturn.setOperateType(child.getOperateType());
            childrenList.add(childReturn);
        }
        return childrenList;
    }

    /**
     * 费用子项
     */
    public static List<ServerCreateComposeChildrenBase> genFeeComposeChildrenList(List<ClientGoodsCreateComposeItemReq> children) {
        List<ServerCreateComposeChildrenBase> childrenList = new ArrayList<>();
        if (CollectionUtils.isEmpty(children)) {
            return childrenList;
        }
        for (ClientGoodsCreateComposeItemReq child : children) {
            ServerCreateFeeComposeChildren childReturn = new ServerCreateFeeComposeChildren();
            childReturn.setId(child.getId());
            childReturn.setComposeId(child.getComposeId());
            childReturn.setCreateGoodsRefId(child.getCreateGoodsRefId());
            childReturn.setName(child.getName());
            childReturn.setNationalCode(child.getNationalCode());
            childReturn.setNationalCodeId(child.getNationalCodeId());
            childReturn.setComposeUseDismounting(child.getComposeUseDismounting() != null ? child.getComposeUseDismounting().shortValue() : GoodsConst.DismountingStatus.PACKAGE);
            childReturn.setComposePackageCount(child.getComposePackageCount());
            childReturn.setShebaoPayMode(child.getShebaoPayMode());
            childReturn.setNationalCode(child.getNationalCode());
            childReturn.setNationalCodeId(child.getNationalCodeId());
            childReturn.setCentralCode(child.getCentralCode());
            childReturn.setComposePackagePrice(child.getComposePackagePrice());
            childReturn.setComposePieceCount(child.getComposePieceCount());
            childReturn.setComposePiecePrice(child.getComposePiecePrice());
            childReturn.setComposePrice(child.getComposePrice());
            childReturn.setComposeFractionPrice(child.getComposeFractionPrice());
            childReturn.setPieceUnit(child.getPieceUnit());
            childReturn.setPackageUnit(child.getPackageUnit());
            childReturn.setMedicalFeeGrade(child.getMedicalFeeGrade());
            childReturn.setPriceLimit(child.getPriceLimit());
            childReturn.setSelfPayProp(child.getSelfPayProp());
            childReturn.setShebaoPayLimitPriceRule(child.getShebaoPayLimitPriceRule());
            childReturn.setMatchMode(child.getMatchMode());
            childrenList.add(childReturn);
        }
        return childrenList;
    }

    public static List<ServerCreateComposeChildrenBase> genAssociationComposeChildrenList(List<ClientGoodsCreateComposeItemReq> associationComposeList) {
        if (CollectionUtils.isEmpty(associationComposeList)) {
            return Collections.emptyList();
        }
        return associationComposeList
                .stream()
                .map(child -> {
                    ServerCreateComposeChildren composeChild = new ServerCreateComposeChildren();
                    composeChild.setId(child.getId());
                    composeChild.setComposeId(child.getComposeId());
                    composeChild.setCreateGoodsRefId(child.getCreateGoodsRefId());
                    composeChild.setName(child.getName());
                    composeChild.setType((short) child.getType());
                    composeChild.setSubType((short) child.getSubType());
                    composeChild.setTypeId(child.getTypeId());
                    composeChild.setComposeUseDismounting(ObjectUtils.defaultIfNull(child.getComposeUseDismounting(), GoodsConst.DismountingStatus.PACKAGE).shortValue());
                    composeChild.setComposePackageCount(child.getComposePackageCount());
                    composeChild.setPackageUnit(child.getPackageUnit());
                    composeChild.setComposePackagePrice(child.getComposePackagePrice());
                    composeChild.setComposePieceCount(child.getComposePieceCount());
                    composeChild.setPieceUnit(child.getPieceUnit());
                    composeChild.setComposePiecePrice(child.getComposePiecePrice());
                    composeChild.setComposePrice(child.getComposePrice());
                    composeChild.setComposeFractionPrice(child.getComposeFractionPrice());
                    composeChild.setPieceNum(child.getPieceNum());
                    composeChild.setManufacturerFull(child.getManufacturerFull());
                    composeChild.setMaterialSpec(child.getMaterialSpec());
                    composeChild.setIsSell(child.getIsSell());
                    composeChild.setInnerFlag(child.getInnerFlag());
                    //composeChild.setMedicalFeeGrade();
                    return composeChild;
                }).collect(Collectors.toList());
    }


    /**
     * 检查检验子项的检查
     *
     * @param deviceModelId 父亲对象绑定的 设备id
     * @return 返回空对象 不会返回null
     */
    public static List<ServerCreateComposeChildrenBase> genExamComposeGoodsChildrenList(String deviceModelId, List<ClientGoodsCreateComposeItemReq> children) {
        List<ServerCreateComposeChildrenBase> childrenList = new ArrayList<>();
        if (CollectionUtils.isEmpty(children)) {
            return childrenList;
        }
        for (ClientGoodsCreateComposeItemReq child : children) {

            ServerCreateExaminationAssayChildren childReturn = new ServerCreateExaminationAssayChildren();
            childReturn.setId(child.getId());
            childReturn.setComposeId(child.getComposeId());
            childReturn.setName(child.getName());
            childReturn.setComposePackageCount(child.getComposePackageCount());
            childReturn.setShebaoPayMode(child.getShebaoPayMode());
            childReturn.setNationalCode(child.getNationalCode());
            childReturn.setNationalCodeId(child.getNationalCodeId());
            childReturn.setCentralCode(child.getCentralCode());
            childReturn.setBizRefId(child.getBizRefId());
            childReturn.setEnName(child.getEnName());
            childReturn.setBizRelevantId(child.getBizRelevantId());
            childReturn.setPackagePrice(child.getPackagePrice());
            childReturn.setPackageCostPrice(child.getPackageCostPrice());
            childReturn.setPackageUnit(child.getPackageUnit());// 项目判重规则加上了大包单位，一定要带上
            childReturn.setComposeUseDismounting(child.getComposeUseDismounting() != null ? child.getComposeUseDismounting().shortValue() : GoodsConst.DismountingStatus.PACKAGE);
            childReturn.setComposePackagePrice(child.getComposePackagePrice());
            childReturn.setComposePieceCount(child.getComposePieceCount());
            childReturn.setComposePiecePrice(child.getComposePiecePrice());
            childReturn.setComposePrice(child.getComposePrice());
            childReturn.setNationalCode(child.getNationalCode());
            childReturn.setNationalCodeId(child.getNationalCodeId());
            childReturn.setIsSell(child.getIsSell());
            childReturn.setBizExtensions(child.getBizExtensions());
            childReturn.setDeviceType(child.getDeviceType());
            childReturn.setMedicalFeeGrade(child.getMedicalFeeGrade());
            childReturn.setPriceLimit(child.getPriceLimit());
            childrenList.add(childReturn);
        }
        return childrenList;
    }

    public static List<ServerCreateComposeChildrenBase> genEyeInspectionComposeGoodsChildrenList(String deviceModelId, List<ClientGoodsCreateComposeItemReq> children) {
        List<ServerCreateComposeChildrenBase> childrenList = new ArrayList<>();
        if (CollectionUtils.isEmpty(children)) {
            return childrenList;
        }
        for (ClientGoodsCreateComposeItemReq child : children) {

            ServerCreateExaminationInspectionChildren childReturn = new ServerCreateExaminationInspectionChildren();
            childReturn.setId(child.getId());
            childReturn.setComposeId(child.getComposeId());
            childReturn.setName(child.getName());
            childReturn.setComposePackageCount(child.getComposePackageCount());
            childReturn.setShebaoPayMode(child.getShebaoPayMode());
            childReturn.setNationalCode(child.getNationalCode());
            childReturn.setNationalCodeId(child.getNationalCodeId());
            childReturn.setCentralCode(child.getCentralCode());
            childReturn.setBizRefId(child.getBizRefId());
            childReturn.setEnName(child.getEnName());
            childReturn.setBizRelevantId(child.getBizRelevantId());
            childReturn.setPackagePrice(child.getPackagePrice());
            childReturn.setPackageCostPrice(child.getPackageCostPrice());
            childReturn.setPackageUnit(child.getPackageUnit());// 项目判重规则加上了大包单位，一定要带上
            childReturn.setNationalCode(child.getNationalCode());
            childReturn.setNationalCodeId(child.getNationalCodeId());
            childReturn.setComposeUseDismounting(child.getComposeUseDismounting() != null ? child.getComposeUseDismounting().shortValue() : GoodsConst.DismountingStatus.PACKAGE);
            childReturn.setComposePackagePrice(child.getComposePackagePrice());
            childReturn.setComposePieceCount(child.getComposePieceCount());
            childReturn.setComposePiecePrice(child.getComposePiecePrice());
            childReturn.setComposePrice(child.getComposePrice());
            childReturn.setShebaoPayMode(child.getShebaoPayMode());
            childReturn.setNationalCode(child.getNationalCode());
            childReturn.setNationalCodeId(child.getNationalCodeId());
            childReturn.setCentralCode(child.getCentralCode());
            childReturn.setIsSell(child.getIsSell());
            childReturn.setBizExtensions(child.getBizExtensions());
            childReturn.setDeviceType(child.getDeviceType());
            // 眼科打印配置 0-不打印 1-打印
            childReturn.setDisableComposePrint(child.getDisableComposePrint());
            childrenList.add(childReturn);
        }
        return childrenList;
    }

    /**
     * 加工项目
     */
    private static ServerCreateProcessingGoodsReq createServerProcessGoodsReq(ClientGoodsCreateReq clientReq) {
        ServerCreateProcessingGoodsReq serverReq = new ServerCreateProcessingGoodsReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());

        /**
         * 价格和进销税等,治疗理疗有成本价
         * */
        serverReq.setDismounting(GoodsConst.DismountingStatus.PACKAGE);
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);
        serverReq.setPackageUnit(clientReq.getPackageUnit());

        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());

        return serverReq;
    }

    /**
     * 费用项目
     */
    @Deprecated
    private static ServerCreateFeeGoodsReq createServerFeeGoodsReq(ClientGoodsCreateReq clientReq) {
        ServerCreateFeeGoodsReq serverReq = new ServerCreateFeeGoodsReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());
        /**
         * Goods社保支付方式
         * */
        serverReq.setShebaoPayMode(clientReq.getShebaoPayMode());

        /**
         * 社保相关
         * */
        if (clientReq.getShebao() != null) {
            serverReq.setNationalCode(clientReq.getShebao().getNationalCode());
            serverReq.setNationalCodeId(clientReq.getShebao().getNationalCodeId());
            serverReq.setMedicalFeeGrade(clientReq.getShebao().getMedicalFeeGrade());
        }
        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());

        /**
         * 价格和进销税等,治疗理疗有成本价
         * */
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        serverReq.setPackageCostPrice(clientReq.getPackageCostPrice());
        return serverReq;
    }

    /**
     * 护理项目
     */
    private static ServerCreateNurseGoodsReq createServerNurseGoodsReq(ClientGoodsCreateReq clientReq) {
        ServerCreateNurseGoodsReq serverReq = new ServerCreateNurseGoodsReq();
        /**
         * ID 相关
         * */
        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());
        /**
         * Goods社保支付方式
         * */
        serverReq.setShebaoPayMode(clientReq.getShebaoPayMode());

        //医嘱等级写入
        serverReq.setBizRelevantId(clientReq.getBizRelevantId());
        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());

        /**
         * 价格和进销税等,治疗理疗有成本价
         * */
        if (clientReq.getNeedExecutive() != null) {
            serverReq.setNeedExecutive(clientReq.getNeedExecutive().shortValue());
        }
        if (clientReq.getHospitalNeedExecutive() != null) {
            serverReq.setHospitalNeedExecutive(clientReq.getHospitalNeedExecutive());
            serverReq.setNeedExecutive((short) ((serverReq.getNeedExecutive() != null ? serverReq.getNeedExecutive() : 0) | (clientReq.getHospitalNeedExecutive() << 1)));
        }

        serverReq.setPieceNum(GoodsConst.PIECE_ONE);
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        serverReq.setPackageCostPrice(clientReq.getPackageCostPrice());
        //bizRelevantId 0 特级护理 1 1级护理 2 2级护理 3 3级护理 10 一般护理操作
        serverReq.setBizRelevantId(clientReq.getBizRelevantId());

        /**
         * 社保相关
         * */
        if (clientReq.getShebao() != null) {
            serverReq.setNationalCode(clientReq.getShebao().getNationalCode());
            serverReq.setNationalCodeId(clientReq.getShebao().getNationalCodeId());
            serverReq.setMedicalFeeGrade(clientReq.getShebao().getMedicalFeeGrade());
        }


        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());
        serverReq.setFeeComposeList(genFeeComposeChildrenList(clientReq.getFeeComposeList()));

        return serverReq;
    }

    private static ServerCreateConsultationGoodsReq createServerConsultationGoodsReq(ClientGoodsCreateReq clientReq) {
        ServerCreateConsultationGoodsReq serverReq = new ServerCreateConsultationGoodsReq();

        serverReq.setId(clientReq.getId());
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());
        serverReq.setShebaoPayMode(clientReq.getShebaoPayMode());
        serverReq.setName(clientReq.getName());
        serverReq.setPieceNum(GoodsConst.PIECE_ONE);
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        serverReq.setPackageCostPrice(clientReq.getPackageCostPrice());
        if (clientReq.getShebao() != null) {
            serverReq.setNationalCode(clientReq.getShebao().getNationalCode());
            serverReq.setNationalCodeId(clientReq.getShebao().getNationalCodeId());
        }
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(clientReq.getFeeComposeType());
        serverReq.setFeeComposeList(genFeeComposeChildrenList(clientReq.getFeeComposeList()));
        return serverReq;
    }

    /**
     * 建眼科类型goods
     */
    private static ServerCreateEyeGoodsReq jenkinsCreateServerEyeGoodsReq(JenkinsGoodsImportItem clientReq) {
        ServerCreateEyeGoodsReq serverReq = new ServerCreateEyeGoodsReq();
        serverReq.setJenkinsGoodsImportItem(clientReq);
        /**
         * ID 相关
         * */
        serverReq.setShortId(clientReq.getShortId());
        serverReq.setBarCode(clientReq.getBarCode());
        serverReq.setRemark(clientReq.getRemark());

        /**
         * 类型和规格相关
         * */
        serverReq.setType(clientReq.getType().shortValue());
        serverReq.setSubType(clientReq.getSubType().shortValue());
        serverReq.setTypeId(clientReq.getTypeId().intValue());
        serverReq.setCustomTypeId(clientReq.getCustomTypeId());
        serverReq.setDisable(clientReq.getDisable());
        serverReq.setV2DisableStatus(clientReq.getV2DisableStatus());

        /**
         * 名字和供应商相关
         * */
        serverReq.setName(clientReq.getName());
        serverReq.setManufacturerFull(clientReq.getManufacturerFull());
        serverReq.setManufacturer(clientReq.getManufacturer());


        /**
         * 价格和进销税等
         * */
        if (clientReq.getDismounting() != null)
            serverReq.setDismounting(clientReq.getDismounting().shortValue());
        serverReq.setPiecePrice(clientReq.getPiecePrice());
        serverReq.setPackagePrice(clientReq.getPackagePrice());
        if (clientReq.getPieceNum() == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "计量不能为空");
        }
        serverReq.setPieceNum(clientReq.getPieceNum());
        serverReq.setPackageUnit(clientReq.getPackageUnit());
        serverReq.setPieceUnit(clientReq.getPieceUnit());
        serverReq.setSpuGoodsId(clientReq.getSpuGoodsId());
        serverReq.setProcessPrice(clientReq.getProcessPrice());
        serverReq.setDefaultInOutTax(clientReq.getDefaultInOutTax());
        serverReq.setInTaxRat(clientReq.getInTaxRat());
        serverReq.setOutTaxRat(clientReq.getOutTaxRat());


        /**
         * 是否对外销售
         * */
        if (clientReq.getIsSell() != null) {
            serverReq.setIsSell(clientReq.getIsSell().shortValue());
        }

        /**
         * 如果是对内销售，修正销售价格为 null
         * */
        serverReq.fixSellAndPrice();

        serverReq.setGroupSpecId(clientReq.getGroupSpecId());
        serverReq.setCustomType(clientReq.getCustomType());
        serverReq.setFirst(clientReq.getSpherical() != null ? clientReq.getSpherical() : clientReq.getFocalLength());
        serverReq.setSecond(clientReq.getLenticular());
        serverReq.setGroupName(clientReq.getGroupName());
        serverReq.setHyperopiaCombinedLuminosity(clientReq.getHyperopiaCombinedLuminosity());
        serverReq.setMyopiaCombinedLuminosity(clientReq.getMyopiaCombinedLuminosity());
        serverReq.setSpec(clientReq.getSpec());
        serverReq.setColor(clientReq.getColor());
        serverReq.setSort(clientReq.getSort());


        /**
         * 费用类型
         * */
        serverReq.setFeeTypeId(clientReq.getFeeTypeId());
        serverReq.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);
        serverReq.setMedicineNmpn(clientReq.getMedicineNmpn());

        return serverReq;
    }


    /**
     * 客户端请求 转成 后台请求
     */
    public static ServerGoodsSpuCreateReq buildCreateSpuServerRequest(ClientGoodsSpuCreateReq clientReq, GoodsSysTypeService goodsSysTypeService) {
        if (clientReq == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER);
        }
        /**
         * 参数都没传
         * */

        GoodsSysType goodsSysType = goodsSysTypeService.getHisGoodsSysType(clientReq.getType(), clientReq.getSubType(), null);
        if (goodsSysType != null && clientReq.getSpu() != null) {
            clientReq.getSpu().setTypeId(goodsSysType.getId().intValue());
        }

        switch (clientReq.getType()) {
            case GoodsConst.GoodsType.EYE: { // 眼视光
                switch (clientReq.getSubType()) {
                    case GoodsConst.GoodsEysSubType.EYE_GLASS: { // 镜片
                        return createGlassReq(clientReq);
                    }
                    case GoodsConst.GoodsEysSubType.BRACKET: // 镜架
                        return createBracketReq(clientReq);
                    case GoodsConst.GoodsEysSubType.ORTHOKERATOLOGY: // 角膜塑形镜
                        return createOrthokeratologyReq(clientReq);
                    case GoodsConst.GoodsEysSubType.SOFT_HYDROPHILIC: // 软性亲水镜
                        return createSoftHydrophilicReq(clientReq);
                    case GoodsConst.GoodsEysSubType.RIGID_OXYGEN: // 硬性透氧镜
                        return createRigidOxygenReq(clientReq);
                    case GoodsConst.GoodsEysSubType.SUN_GLASS: // 太阳镜
                        return createSunGlassReq(clientReq);
                    default: {
                        throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "无效的眼镜类型");
                    }
                }
            }
            default: {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "无效的眼镜类型");
            }
        }
    }

    private static ServerGoodsSpuCreateReq createRigidOxygenReq(ClientGoodsSpuCreateReq clientReq) {
        ServerGoodsSpuRigidOxygenCreateReq serverReq = new ServerGoodsSpuRigidOxygenCreateReq();
        fillCreateSpuBaseInfo(serverReq, clientReq);
        SpuBaseInfo spu = clientReq.getSpu();
        if (spu != null) {
            serverReq.setRegistrationNumber(spu.getRegistrationNumber());
            serverReq.setWearCycle(spu.getWearCycle());
            serverReq.setPieceUnit(spu.getPieceUnit());
            serverReq.setPieceNum(BigDecimal.ONE.intValue());
        }
        return serverReq;
    }

    private static ServerGoodsSpuCreateReq createSunGlassReq(ClientGoodsSpuCreateReq clientReq) {
        ServerGoodsSpuSunGlassCreateReq serverReq = new ServerGoodsSpuSunGlassCreateReq();
        fillCreateSpuBaseInfo(serverReq, clientReq);
        return serverReq;
    }

    private static ServerGoodsSpuCreateReq createBracketReq(ClientGoodsSpuCreateReq clientReq) {
        ServerGoodsSpuBracketCreateReq serverReq = new ServerGoodsSpuBracketCreateReq();
        fillCreateSpuBaseInfo(serverReq, clientReq);
        SpuBaseInfo spu = clientReq.getSpu();
        if (spu != null) {
            serverReq.setMaterial(spu.getMaterial());
        }
        return serverReq;
    }

    private static ServerGoodsSpuCreateReq createOrthokeratologyReq(ClientGoodsSpuCreateReq clientReq) {
        ServerGoodsSpuOrthokeratologyCreateReq serverReq = new ServerGoodsSpuOrthokeratologyCreateReq();
        fillCreateSpuBaseInfo(serverReq, clientReq);
        SpuBaseInfo spu = clientReq.getSpu();
        if (spu != null) {
            serverReq.setRegistrationNumber(spu.getRegistrationNumber());
            serverReq.setProcessManufacturer(spu.getProcessManufacturer());
        }
        return serverReq;
    }

    private static ServerGoodsSpuCreateReq createSoftHydrophilicReq(ClientGoodsSpuCreateReq clientReq) {
        ServerGoodsSpuSoftHydrophilicCreateReq serverReq = new ServerGoodsSpuSoftHydrophilicCreateReq();
        fillCreateSpuBaseInfo(serverReq, clientReq);
        SpuBaseInfo spu = clientReq.getSpu();
        if (spu != null) {
            serverReq.setPieceUnit(spu.getPieceUnit());
            serverReq.setRegistrationNumber(spu.getRegistrationNumber());
            serverReq.setWearCycle(spu.getWearCycle());
        }
        return serverReq;
    }


    /**
     * 填充基本信息
     */
    private static void fillCreateSpuBaseInfo(ServerGoodsSpuCreateReq serverReq, ClientGoodsSpuCreateReq clientReq) {
        if (clientReq.getSpuTagIdList() != null) {
            serverReq.setSpuTagIdToSort(new HashMap<>());
            for (int i = 0; i < clientReq.getSpuTagIdList().size(); i++) {
                serverReq.getSpuTagIdToSort().put(clientReq.getSpuTagIdList().get(i), i);
            }
        }
        serverReq.setHeaderChainId(clientReq.getHeaderChainId());
        serverReq.setHeaderClinicId(clientReq.getHeaderClinicId());
        serverReq.setHeaderEmployeeId(clientReq.getHeaderEmployeeId());
        serverReq.setHeaderClinicType(clientReq.getHeaderClinicType());
        serverReq.setHeaderViewMode(clientReq.getHeaderViewMode());
        serverReq.setType(clientReq.getType());
        serverReq.setSubType(clientReq.getSubType());
        serverReq.setId(clientReq.getId());
        serverReq.setJenkinsImport(clientReq.getJenkinsImport());
        if (clientReq.getSpu() != null) {
            SpuBaseInfo spu = clientReq.getSpu();
            //和前端约定好了的 spu非空就表示 spu基本信息有修改
            serverReq.setIsModifySpuInfo(true);
            serverReq.setTypeId(spu.getTypeId());
            serverReq.setCustomTypeId(spu.getCustomTypeId());
            serverReq.setName(spu.getName());
            serverReq.setPackageUnit(spu.getPackageUnit());
            serverReq.setDefaultInOutTax(spu.getDefaultInOutTax());
            serverReq.setInTaxRat(spu.getInTaxRat());
            serverReq.setOutTaxRat(spu.getOutTaxRat());

            serverReq.setManufacturerFull(spu.getManufacturerFull());
            serverReq.setBrandName(spu.getBrandName());
            serverReq.setFeeTypeId(GoodsUtils.getLongId(spu.getFeeTypeId()));
        }
        serverReq.setSpecGroup(clientReq.getSpecGroup());
        serverReq.setSkuGoodsList(clientReq.getSkuGoodsList());
    }

    /**
     * 填充镜片新建信息
     */
    private static ServerGoodsSpuCreateReq createGlassReq(ClientGoodsSpuCreateReq clientReq) {
        ServerGoodsSpuEyeGlassCreateReq serverReq = new ServerGoodsSpuEyeGlassCreateReq();
        fillCreateSpuBaseInfo(serverReq, clientReq);
        if (clientReq.getSpu() != null) {
            SpuBaseInfo spu = clientReq.getSpu();
            serverReq.setProcessManufacturer(spu.getProcessManufacturer());
            serverReq.setRefractiveIndex(spu.getRefractiveIndex());
            serverReq.setAddLightRight(spu.getAddLightRight());
            serverReq.setAddLightLeft(spu.getAddLightLeft());
        }
        return serverReq;
    }
}
