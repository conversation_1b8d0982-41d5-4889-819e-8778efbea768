package cn.abcyun.cis.goods.vo.frontend.stockcheck;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.Instant;

@Data
public class GoodsOrderReviewReq {

    //http头里面的参数
    @JsonIgnore
    private String chainId;
    @JsonIgnore
    private String employeeId;
    @JsonIgnore
    private String clinicId;
    @JsonIgnore
    private int headerViewMode;
    @JsonIgnore
    private int headerClinicType;
    //http路径里面的参数
    @JsonIgnore
    private Long orderId;


    @ApiModelProperty(value = "评论", notes = "")
    private String comment;

    @ApiModelProperty(value = "审批订单的最近修改时间", notes = "")
    private Instant lastModifiedDate;

    @ApiModelProperty(value = "审批状态 1通过 0 拒绝", notes = "[revoke不用传]")
    private Integer pass;

    @ApiModelProperty(value = "可选参数 前端用于标识请求 主要用于解决盘点请求超过25s后，第二次继续点确定按钮 再次提交产生第二个盘点单的情况", notes = "")
    private String orderClientUniqKey;

    @JsonIgnore
    private Boolean isApproval = false;

}
