package cn.abcyun.cis.goods.vo.frontend.order;

import cn.abcyun.cis.goods.vo.frontend.order.base.OrderBaseRsp;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * @Author: 验收单列表响应类
 * @Description: 验收单列表响应类
 * @Since: 2024/1/13 14:45
 **/

@Data
@ApiModel(value = "InspectOrderListRsp", description = "验收单列表响应类")
@EqualsAndHashCode(callSuper=false)
public class InspectOrderListRsp extends OrderBaseRsp {


    @ApiModelProperty
    private Long id;

    @ApiModelProperty
    private String orderNo;

    @ApiModelProperty
    @JsonIgnore
    private String supplierId;

    @ApiModelProperty("供应商")
    private String supplier;


    /**
     * 验收人
     */
    @ApiModelProperty("验收人")
    private String inspector;

    @JsonIgnore
    private String inspectBy;
    /**
     * 验收时间
     */
    @ApiModelProperty("验收时间")
    private Instant inspectTime;

    /**
     * 订单状态 {@link cn.abcyun.cis.goods.entity.GoodsStockInspectOrder.InspectStatus}
     */
    @ApiModelProperty("订单状态 0:草稿 10:代验收 20:验收合格 30：不合格 40：部分合格")
    private int status;


    /**
     * 0:总部集采 10:门店自采 20:总部配货 30:门店调拨
     */
    @ApiModelProperty("0:总部集采 10:门店自采 20:总部配货 30:门店调拨")
    private int type;


    @ApiModelProperty("来源单据")
    private String sourceOrderNo;

    @ApiModelProperty("收货数量")
    private BigDecimal receiveNumSum;

    @ApiModelProperty("验货数量")
    private BigDecimal inspectNumSum;
}
