package cn.abcyun.cis.goods.vo.frontend.stockin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 条目是否可以编辑
 *
 */
@Data
public class CanEditableView {
    public static final String  REASON_DISBALE  = "只能修改未停用药品的入库单";
    public static final String  REASON_STOP_INORDER  = "只能修改未禁用药品的入库单";
    public static final String REASON_TOOLONG ="只能修改近三个月的入库单";
    public static final String REASON_MODIFIED_YET ="入库药品成本单价已经修改过入库单";
    public static final String REASON_SETTLED ="该入库单已经与供应商结算，不可修改，请进行退货处理";
    @ApiModelProperty(value = "1 不能修改 ")
    private int readOnly;
    @ApiModelProperty(value = "不能修改的原因")
    private String reason;

}
