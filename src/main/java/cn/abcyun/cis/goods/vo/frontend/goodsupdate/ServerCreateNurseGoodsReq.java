package cn.abcyun.cis.goods.vo.frontend.goodsupdate;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.model.Goods;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.vo.frontend.goodsinfo.GoodsSellerImportCheckDataValidRsp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;


/**
 * 护理类型
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ServerCreateNurseGoodsReq extends ServerCreateGoodsAbstractReq {
    /**
     * goodsId
     */
    private String id;
    /**
     * 药品编码
     */
    private String shortId;


    /**
     * 药品类型 三元组 （type,subType,cMSpec）
     * typeId是药品类型三元组的ID
     */
    private short type;

    private short subType;

    private int typeId;

    /**
     * 用户自定义分类ID/二级分类
     */
    private Long customTypeId;

    /**
     * 药品商品名
     */
    private String name = "";

    public String getName() {
        return name != null ? name : "";
    }

    /**
     * 检查检验用的是大单位
     */
    private int pieceNum;

    private String packageUnit;
    /**
     * 价格
     */
    private BigDecimal packagePrice;//  治疗理疗也是按大包价格买的

    private BigDecimal packageCostPrice;// 成本价


    private String nationalCode;

    private String nationalCodeId;
    //医嘱等级存 医嘱等级  0 1 2 3
    private String bizRelevantId;

    private Short isSell = GoodsUtils.SwitchFlag.ON;

    private Short needExecutive;
    private Integer hospitalNeedExecutive;

    /**
     * 费用项类型
     * */
    private List<ServerCreateComposeChildrenBase> feeComposeList;

    /**
     * 参数检查，不用注解，方便文案的提示
     */
    public void parameterCheck(ClinicConfig clinicConfig) {

        if (StringUtils.isEmpty(name)) {
            if (jenkinsGoodsImportItem != null) {
                jenkinsGoodsImportItem.addCreateErrorReason(GoodsSellerImportCheckDataValidRsp.ERROR_INVALID_GOODS_TYPE,"type","请为指定的费用类型指定名称");
            } else {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER,
                        " 请为指定的费用类型指定名称");
            }

        }

        if (subType != GoodsConst.GoodsNurseSubType.NURSE ) {
            if (jenkinsGoodsImportItem != null) {
                jenkinsGoodsImportItem.addCreateErrorReason(GoodsSellerImportCheckDataValidRsp.ERROR_INVALID_GOODS_TYPE,"type","请指定的正确的护理类型");
            } else {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER,
                        " 请指定的正确的护理类型");
            }
        }

        /**
         * 【医院管家】产品确认，护理等级不允许关联项目
         * https://www.tapd.cn/22044681/bugtrace/bugs/view?bug_id=1122044681001038524&url_cache_key=from_url_bug_query_list_8991e5cf7874df5c8ae7d3ffcd153ed9
         * */

        if (packagePrice == null) { //2位小数
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER,
                    GoodsUtils.goodsFullName(type, "", name) + "没有指定护理的价格");
        }
        packagePrice = packagePrice.setScale(Goods.CLIENT_PRICE_PRECISION, RoundingMode.UP);

        if (hospitalNeedExecutive != null && GoodsUtils.nurseNursingLevelNameList.contains(name) && hospitalNeedExecutive == GoodsUtils.SwitchFlag.ON) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER,
                    GoodsUtils.goodsFullName(type, "", name) + "内置护理等级医嘱住院执行设置错误");
        }

        super.parameterCheck(clinicConfig);

    }


    /**
     * 为了基类的处理，增加的空的占位方法
     * 无此属性
     */
    @Override
    public String getManufacturerFull() {
        return null;
    }

    /**
     * 为了基类的处理，增加的空的占位方法
     * 无此属性
     */
    @Override
    public String getBarCode() {
        return null;
    }

    @Override
    public List<TraceableCodeNoInfo> getTraceableCodeNoInfoList() {
        return null;
    }

    /**
     * 为了基类的处理，增加的空的占位方法
     * 无此属性
     */
    @Override
    public BigDecimal getInTaxRat() {
        return null;
    }

    /**
     * 为了基类的处理，增加的空的占位方法
     * 无此属性
     */
    @Override
    public BigDecimal getOutTaxRat() {
        return null;
    }

    /**
     * 为了基类的处理，增加的空的占位方法
     * 无此属性
     */
    @Override
    public String getMedicineCadn() {
        return null;
    }

    /**
     * 为了基类的处理，增加的空的占位方法
     * 无此属性
     */
    @Override
    public void setMedicineCadn(String cadn) {

    }

    @Override
    public BigDecimal getPiecePrice() {
        return null;
    }

    @Override
    public String getPieceUnit() {
        return null;
    }

    @Override
    public void setPiecePrice(BigDecimal piecePrice) {

    }

    @Override
    public void setDismounting(Short dismounting) {

    }

    @Override
    public Short getDismounting() {
        return null;
    }

    @Override
    public String getCityCode() {
        return null;
    }

    @Override
    public String getProvinceCode() {
        return null;
    }

    @Override
    public String getCityCodeId() {
        return null;
    }

    @Override
    public String getProvinceCodeId() {
        return null;
    }


    @Override
    public int getDefaultInOutTax() {
        return 0;
    }

    @Override
    public Integer getAntibiotic() {
        return null;
    }

    @Override
    public BigDecimal getDddOfAntibiotic() {
        return null;
    }

    @Override
    public String getUnitOfAntibiotic() {
        return null;
    }

    @Override
    public int getPriceType() {
        return GoodsConst.PriceType.PRICE;
    }

    @Override
    public BigDecimal getPriceMakeupPercent() {
        return null;
    }

    @Override
    public Long getProfitCategoryType() {
        return null;
    }

    @Override
    public Integer getOtcType() {
        return null;
    }

    @Override
    public Integer getDosageFormType() {
        return null;
    }

    @Override
    public BigDecimal getShelfLife() {
        return null;
    }

    @Override
    public Integer getBaseMedicineType() {
        return null;
    }

    @Override
    public Integer getMaintainType() {
        return null;
    }

    @Override
    public Integer getStorageType() {
        return null;
    }

    @Override
    public String getMha() {
        return null;
    }

    @Override
    public Integer getDangerIngredient() {
        return null;
    }

    @Override
    public String getPharmacologicId() {
        return null;
    }
}
