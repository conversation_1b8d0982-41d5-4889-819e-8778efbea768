package cn.abcyun.cis.goods.vo.frontend.exam;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-22 08:46:02
 */
@Data
public class ExaminationSampleSimpleView {

    @ApiModelProperty(value = "采样组id")
    private String id;
    @ApiModelProperty(value = "采样组名称")
    private String name;
    @ApiModelProperty(value = "采样组编号")
    private String code;
    @ApiModelProperty(value = "采样组状态 0=启用 99=停用")
    private int status;
}
