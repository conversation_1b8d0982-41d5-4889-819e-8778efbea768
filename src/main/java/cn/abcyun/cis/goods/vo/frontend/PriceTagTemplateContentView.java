package cn.abcyun.cis.goods.vo.frontend;

import cn.abcyun.cis.core.handler.SignatureUrlJsonNodeSerializer;
import cn.abcyun.cis.core.handler.SignatureUrlSerializer;
import cn.abcyun.cis.goods.dto.pricetag.PriceTagTemplateContentDto;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * 价签模板内容
 */
@Data
@Accessors(chain = true)
public class PriceTagTemplateContentView {
    /**
     * 尺寸模板id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sizeTemplateId;
    /**
     * 尺寸模板-高快照
     */
    private int sizeHeightSnap;
    /**
     * 尺寸模板-宽快照
     */
    private int sizeWidthSnap;
    /**
     * 原始背景图url
     */
    @JsonSerialize(using = SignatureUrlSerializer.class)
    private String originalBackgroundImageUrl;
    /**
     * 裁剪后使用的背景图url
     */
    @JsonSerialize(using = SignatureUrlSerializer.class)
    private String clippedBackgroundImageUrl;
    /**
     * 裁剪的坐标相关信息，存jsonNode。后端不关心
     */
    private JsonNode clippedPosition;
    /**
     * 业务属性信息。包括映射的字段和字段的样式
     */
    @JsonSerialize(using = SignatureUrlJsonNodeSerializer.class)
    private JsonNode businessPropertyFields;

    public static PriceTagTemplateContentView fromDto(PriceTagTemplateContentDto entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        return new PriceTagTemplateContentView()
                .setSizeTemplateId(entity.getSizeTemplateId())
                .setSizeHeightSnap(entity.getSizeHeightSnap())
                .setSizeWidthSnap(entity.getSizeWidthSnap())
                .setOriginalBackgroundImageUrl(entity.getOriginalBackgroundImageUrl())
                .setClippedBackgroundImageUrl(entity.getClippedBackgroundImageUrl())
                .setClippedPosition(entity.getClippedPosition())
                .setBusinessPropertyFields(entity.getBusinessPropertyFields());
    }
}
