package cn.abcyun.cis.goods.vo.frontend.purchase;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @Author: 何平
 * @Description: 校验供应商商品是否有效
 * @Since: 2024/1/27 18:23
 **/
@Data
@ApiModel(value="CheckValidSupplierGoodsItemReq", description = "校验供应商商品是否有效")
public class CheckValidSupplierGoodsItemReq {
    @ApiModelProperty(value="商品id")
    @NotEmpty(message = "商品id不能为空")
    private String goodsId;

    @ApiModelProperty(value="供应商id")
    @NotEmpty(message = "供应商id不能为空")
    private String supplierId;

    @ApiModelProperty(value="shortId",hidden = true)
    @JsonIgnore
    private String shortId;

    @ApiModelProperty(value="shortId",hidden = true)
    @JsonIgnore
    private String erpSupplierId;
}
