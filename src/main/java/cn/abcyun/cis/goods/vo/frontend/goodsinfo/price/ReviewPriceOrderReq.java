package cn.abcyun.cis.goods.vo.frontend.goodsinfo.price;

import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.vo.frontend.purchase.CreatePurchaseOrderItem;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

/**
 * 审核结算单
 */
@Data
public class ReviewPriceOrderReq {

    /**
     * 【流程】总部确认
     * */
    public static final int COMMAND_TYPE_REVIEW = 0;
    /**
     * 【流程】门店确认
     * */
    public static final int COMMAND_TYPE_CONFIRM = 10;
    /**
     * 未发起流程撤回
     * */
    public static final int COMMAND_TYPE_REVOKE = 20;

    /**
     * 停止发货
     */
    public static final int COMMAND_TYPE_TERMINATE = 30;

    @JsonIgnore
    private String headerChainId;
    @JsonIgnore
    private String headerClinicId;
    @JsonIgnore
    private String headerEmployeeId;
    @JsonIgnore
    private int headerHisType;
    @JsonIgnore
    private int headerClinicType;
    @JsonIgnore
    private int headerViewMode;
    @JsonIgnore
    private Long orderId;

    /**
     * 0 总部审核 1 门店确认
     * */
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private int commandType = COMMAND_TYPE_REVIEW ;

    @ApiModelProperty("本次评论的修改只有审核有评论，确认没有评论")
    private String comment;
    @ApiModelProperty("入库单审核的值 1 通过 同意，0 拒绝 驳回。入库单确认无用")
    private int pass; // 1 通过，0 拒绝
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Instant lastModified;




    /**
     * 输入参数有效性检查
     * 为了方便实时同学排查问题，这些错误全部一次性返回
     */
    public void parameterCheck() {
        if (orderId == null || orderId == 0L) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "请指定有效的结算单号");
        }

        if (commandType == 0) {
            if (pass != GoodsUtils.SwitchFlag.ON && pass != GoodsUtils.SwitchFlag.OFF) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "审核请输入正确的审核值");
            }
        }
        return;
    }
}
