package cn.abcyun.cis.goods.vo.frontend.goodsinfo.price;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 根据套餐总价计算单项单价及总价
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-01-09 13:54:25
 */
@Data
public class CalGoodsComposePriceReq {


    @ApiModelProperty("套餐总价-用户输入价格")
    private BigDecimal composePrice;

    @ApiModelProperty("套餐原总价-原价计算出")
    private BigDecimal srcComposePrice;

    @ApiModelProperty("修改单项或套餐总价标识 1=修改单项单价 2=修改单项总价 3=修改套餐总价 4=修改单项数量")
    @Range(min = 1, max = 4)
    private int modifyIdentification;

    @ApiModelProperty(value = "套餐成本价")
    private BigDecimal composePackageCostPrice;

    @ApiModelProperty("套餐项目列表")
    private List<CalGoodsComposePriceItem> itemList;

    @ApiModelProperty(value = "类型 0=有goods 1=无goods，诊所管家用,检查检验等,需随机生成goodsId")
    private int type;

    @ApiModelProperty(value = "使用数量占比进行摊费, 诊所管家为1")
    private int useCount;

    public static final int NO_GOODS_TYPE = 1;

    public void parameterCheck() {
        if (CollectionUtils.isEmpty(itemList)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "项目列表为空");
        }
        if (composePrice != null && composePrice.compareTo(BigDecimal.ZERO) < 0) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "套餐总价设置错误");
        }
        for (CalGoodsComposePriceItem item : itemList) {
            if (TextUtils.isEmpty(item.goodsId)) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "GOODSID不能为空");
            }
            if (item.getComposeUseDismounting() == GoodsConst.DismountingStatus.PACKAGE) {
                // 大单位
                 if (item.getComposePackageCount().compareTo(BigDecimal.ZERO) <= 0) {
                     throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "数量需大于0");
                 }
                 if (item.getComposePackagePrice() == null || item.getComposePackagePrice().compareTo(BigDecimal.ZERO) < 0) {
                     throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "套餐单项单价设置错误");
                 }
            } else if (item.getComposeUseDismounting() == GoodsConst.DismountingStatus.DISMOUNTING) {
                // 小单位
                if (item.getComposePieceCount().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "数量需大于0");
                }
                if (item.getComposePiecePrice() == null || item.getComposePiecePrice().compareTo(BigDecimal.ZERO) < 0) {
                    throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "套餐单项单价设置错误");
                }
            }
            if (item.getComposePrice() == null || item.getComposePrice().compareTo(BigDecimal.ZERO) < 0) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "套餐单项总价设置错误");
            }
            if (item.getComposeFractionPrice() != null && item.getComposeFractionPrice().compareTo(BigDecimal.ZERO) < 0) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "套餐单项差价设置错误");
            }
        }
    }


    @Data
    public static class CalGoodsComposePriceItem {


        @ApiModelProperty("回射ID--唯一")
        private Long composeId;

        @ApiModelProperty("goodsId")
        private String goodsId;

        @ApiModelProperty("名称")
        private String goodsName;

        /**
         * 这个套餐怎么卖，通拆零销售的字段一样
         * 用大单位还是小单位买
         */
        @ApiModelProperty("用大单位还是小单位卖 0=大单位 1=小单位")
        private Integer composeUseDismounting = 0;

        @ApiModelProperty("大单位数量")
        private BigDecimal composePackageCount = BigDecimal.ZERO;

        @ApiModelProperty("小单位数量")
        private BigDecimal composePieceCount = BigDecimal.ZERO;

        @ApiModelProperty("大单位单价")
        private BigDecimal composePackagePrice;

        @ApiModelProperty("小单位单价")
        private BigDecimal composePiecePrice;

        @ApiModelProperty("套餐内总价")
        private BigDecimal composePrice;

        @ApiModelProperty("套餐单项零头金额，按比例计算单价后再反算的总价与按比例算的总价的零头金额")
        private BigDecimal composeFractionPrice;

        @ApiModelProperty(value = "单项成本价")
        private BigDecimal packageCostPrice;
    }
}
