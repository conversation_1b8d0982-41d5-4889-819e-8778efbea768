package cn.abcyun.cis.goods.vo.frontend.stockin;

import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 创建入库单请求
 */
@Data
public class GetStockInOrderReq {
    /**
     * Http头
     */
    @JsonIgnore
    private String headerChainId;
    @JsonIgnore
    private String headerClinicId;
    @JsonIgnore
    private String headerEmployeeId;
    @JsonIgnore
    private int headerClinicType;
    @JsonIgnore
    private int headerViewMode;
    @JsonIgnore
    private String loginWay;
    @JsonIgnore
    private HttpServletResponse httpServletResponse;

    public String getClinicId() {
        return headerClinicId;
    }

    /**
     *
     * 入库单
     * */
    private Long inOrderId;
    /***
     * */
    @ApiModelProperty(value = "拉某个批次的入库单信息")
    private Long withStockId;

    /**
     * 指定拉取某个GoodsId的入库单Item，因为存在入库单过大，指定拉取goods的情况
     * */
    private List<String> withGoodsIdList;
    // 1 上面的withGoodsId只是排前面
    private Integer  withGoodsIdOrderFirst;

    /**
     * 入库单明细id
     * 药店初始化入库退货出库用
     */
    private Long stockInId;
    /**
     * 是否返回可退数量
     * */
    @ApiModelProperty(value = "是否返回可退数量")
    private Integer withReturnLeft;
    public Integer getWithReturnLeft() {
        return withReturnLeft != null ? withReturnLeft : 0;
    }

    /**
     * ??? 为啥要带商城订单？
     * */
    private Long mallOrderId;


    /**
     * 参数检查
     * */
    public void parameterCheck() {
        if (inOrderId == null || inOrderId == 0L) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "请输入有效的入库单号");
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "是否需要合并订单")
    private Integer needMergedOrder;

    private Integer offset;
    private Integer limit;

}
