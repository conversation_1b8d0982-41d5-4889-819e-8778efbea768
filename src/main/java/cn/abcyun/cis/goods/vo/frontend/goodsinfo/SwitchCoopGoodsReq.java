package cn.abcyun.cis.goods.vo.frontend.goodsinfo;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-09 17:43
 */
@Data
public class SwitchCoopGoodsReq {

    @Valid
    @NotEmpty(message = "items不能为空")
    private List<ItemReq> items;

    @Data
    public static class ItemReq {

        @NotBlank(message = "goodsId不能为空")
        private String goodsId;

        private BigDecimal coopPackagePrice;

    }
}
