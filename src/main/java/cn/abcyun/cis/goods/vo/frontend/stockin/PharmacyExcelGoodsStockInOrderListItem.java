package cn.abcyun.cis.goods.vo.frontend.stockin;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;


/**
 * 药店入库的订单列表
 */
@Data
@FieldNameConstants
public class PharmacyExcelGoodsStockInOrderListItem {
    @ExcelProperty(value = "药品编码")
    @ColumnWidth(20)
    private String shortId;

    @ExcelProperty(value = "药品")
    @ColumnWidth(20)
    private String goodsName;

    @ExcelProperty(value = "规格")
    @ColumnWidth(10)
    private String goodsSpec;

    @ExcelProperty(value = "生产厂家")
    @ColumnWidth(30)
    private String goodsManufacture;

    @ExcelProperty(value = "类型")
    @ColumnWidth(10)
    private String goodsTypeName;

    @ExcelProperty(value = "二级分类")
    @ColumnWidth(20)
    private String goodsCustomTypeName;

    @ExcelProperty(value = "批准文号")
    @ColumnWidth(20)
    private String medicineNmpn;

    @ExcelProperty(value = "售价")
    @ColumnWidth(10)
    private BigDecimal packagePrice;


    @ExcelProperty(value = "批次")
    @ColumnWidth(10)
    private String goodsStockId;
    @ExcelProperty(value = "生产批号")
    @ColumnWidth(20)
    private String goodsBatchNo;


    @ExcelProperty(value = "效期")
    @ColumnWidth(15)
    private String expiryDate;
    @ExcelProperty(value = "生产日期")
    @ColumnWidth(15)
    private String productionDate;
    @ExcelProperty(value = "数量")
    @ColumnWidth(15)
    private BigDecimal inOrderCount;
    @ExcelProperty(value = "单位")
    @ColumnWidth(15)
    private String inOrderUnit;
    @ExcelProperty(value = "进价")
    @ColumnWidth(15)
    private BigDecimal inOrderUnitCostPrice;
    @ExcelProperty(value = "税率")
    @ColumnWidth(15)
    @ExcelIgnore
    private BigDecimal inTaxRat;
    @ExcelProperty(value = "金额")
    @ColumnWidth(20)
    private BigDecimal amountRat;
    @ExcelProperty(value = "不含税金额")
    @ColumnWidth(25)
    @ExcelIgnore
    private BigDecimal amountExcludeRat;
    /**
     * 老的数据库 int(11) unsigned 不会爆掉，和老的协议兼容
     */
    @ExcelProperty(value = "单号")
    @ColumnWidth(20)
    private String orderNo;

    @ExcelProperty(value = "状态")
    @ColumnWidth(20)
    private String statusName;
    @ExcelProperty(value = "结算状态")
    @ColumnWidth(20)
    private String settlementStatusName;

    @ExcelProperty(value = "品种")
    @ColumnWidth(10)
    private int kindCount;

    @ExcelProperty(value = "数量")
    @ColumnWidth(10)
    private BigDecimal totalInOrderCount;

    @ExcelProperty(value = "含税金额")
    @ColumnWidth(20)
    private BigDecimal totalInOrderTotalCostPrice;
    @ExcelProperty(value = "来源类型")
    @ColumnWidth(20)
    private String sourceTypeName;
    @ExcelProperty(value = "金额")
    @ColumnWidth(20)
    private BigDecimal returnTotalInOrderTotalCostPrice;

    @ExcelProperty(value = "不含税金额")
    @ColumnWidth(20)
    @ExcelIgnore
    private BigDecimal totalInOrderTotalCostPriceExcludingTax;

    @ExcelProperty(value = "供货商")
    @ColumnWidth(30)
    private String supplier;

    @ExcelProperty(value = "入库门店")
    @ColumnWidth(30)
    private String organName;
    @ExcelProperty(value = "退货门店")
    @ColumnWidth(30)
    private String returnOrganName;

    @ExcelProperty(value = "入库人")
    @ColumnWidth(20)
    private String stockInBy;
    @ExcelProperty(value = "退货人")
    @ColumnWidth(20)
    private String returnStockInBy;

    @ExcelProperty(value = "入库日期")
    @ColumnWidth(20)
    private String inDate;
    @ExcelProperty(value = "退货日期")
    @ColumnWidth(20)
    private String returnInDate;

    @ExcelProperty(value = "创建日期")
    @ColumnWidth(20)
    @ExcelIgnore
    private String created;

    @ExcelProperty(value = "来源类型")
    @ColumnWidth(20)
    private String returnSourceTypeName;

    @ExcelProperty(value = "来源单据")
    @ColumnWidth(40)
    private String sourceFromOrder;

}
