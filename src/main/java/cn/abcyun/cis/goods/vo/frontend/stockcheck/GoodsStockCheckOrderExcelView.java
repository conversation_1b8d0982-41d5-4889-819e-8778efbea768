package cn.abcyun.cis.goods.vo.frontend.stockcheck;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 盘点单列表 单条订单的订单项条目
 */
@Data
public class GoodsStockCheckOrderExcelView {

    @ExcelProperty(value="盘点单号")
    @ColumnWidth(20)
    private String orderNo;
    @ExcelProperty(value="单据类型")
    @ColumnWidth(12)
    private String orderTypeName;

    @ExcelProperty(value="状态")
    @ColumnWidth(10)
    private String statusName;

    @ExcelProperty(value="门店")
    @ColumnWidth(30)
    private String organName;

    @ExcelProperty(value = "盘点药房")
    @ColumnWidth(30)
    private String pharmacyName;

    @ExcelProperty(value="盘点日期")
    @ColumnWidth(15)
    private String createdDate;

    @ExcelProperty(value="盘点人员")
    @ColumnWidth(15)
    private String createdUserName;



    @ExcelProperty(value="品种")
    @ColumnWidth(10)
    private Integer kindCount;

    @ExcelProperty(value="盈亏数量")
    @ColumnWidth(20)
    private BigDecimal packageCountChange = BigDecimal.ZERO;

    @ExcelProperty(value="盈亏金额（进价）")
    @ColumnWidth(30)
    private BigDecimal packageCostAmount = BigDecimal.ZERO;

    @ExcelProperty(value="盈亏金额（售价）")
    @ColumnWidth(30)
    private BigDecimal packageSaleAmount = BigDecimal.ZERO;

    @ExcelProperty(value="备注")
    @ColumnWidth(30)
    private String comment = "";
}
