package cn.abcyun.cis.goods.vo.frontend.stocktrans;

import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.MD5Utils;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.model.GoodsStockTransOrder;
import cn.abcyun.cis.goods.utils.GoodsPrivUtils;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.time.Instant;
import java.util.Date;

/**
 * 调拨单订单项条目 内部请求结构
 */
@Data
public class GoodsStockTransOrderListReq {
    @ApiModelProperty(hidden = true)
    private String headerChainId;
    @ApiModelProperty(hidden = true)
    private String headerClinicId;
    @ApiModelProperty(hidden = true)
    private String headerEmployeeId;
    @ApiModelProperty(hidden = true)
    private int headerClinicType;
    @ApiModelProperty(hidden = true)
    private int headerClinicViewMode;

    @JsonIgnore
    private String queryClinicString ="";
    @JsonIgnore
    private String pharmacyQueryClinicString ="";

    @JsonIgnore
    private HttpServletResponse httpServletResponse;


    //出库单状态 0-待审核,1-待确认,2-已完成,9-已拒绝,31-撤回,不传不过状态
    private Integer status;

    private Integer orderType;

    //日期过滤三元组过滤的日期类型，前端[outDate,createdDate]不传outDate都为createdDate
    private String dateField;

    //@ApiParam(value = "日期过滤三元组过滤的开始日期:YYYY-MM-DD(后台会按YYYY-MM-DD 00:00:00开始)")
    private Date begDate;

    public Instant getBeginInstant() {
        return begDate != null ? DateUtils.getStartTime(begDate).toInstant() : null;
    }

    //@ApiParam(value = "日期过滤三元组过滤的结束日期:YYYY-MM-DD(后台会按YYYY-MM-DD 23:59:5结束)")
    private Date endDate;

    public Instant getEndInstant() {
        return endDate != null ? DateUtils.getEndTime(endDate).toInstant() : null;
    }




    //@ApiParam(value = "只有在总店查询的时候有用，指定查询某个子店的出库单")
    private String transOutClinicId;
    private String transInClinicId;

    private Integer transOutPharmacyType ;
    private Integer transOutPharmacyNo;
    private Integer transInPharmacyType ;
    private Integer transInPharmacyNo;
    private int pharmacyNoOr = 1;
    private Integer currentPharmacyNo;

    private Integer transType = GoodsStockTransOrder.TransType.TRANS_BETWEEN_CLINIC;

    //@ApiParam(value = "查某个Goods的所有出库单")
    private String withGoodsId;

    public Long getExportTimeGap() {
        if (begDate != null && endDate != null) {
            return getEndInstant().getEpochSecond() - getBeginInstant().getEpochSecond();
        } else {
            return null;
        }
    }

    //@ApiParam(value = "分页请求")
    private Integer offset;

    //@ApiParam(value = "分页请求参数")
    private Integer limit;




    public void parameterCheck() {
        /**
         * 没指定默认都从0 开始啦
         * */
        if (offset == null) {
            offset = 0;
        }
        /**
         * 通nodejs的实现逻辑稍微不一样，用null表示全部拉取
         * */
        if (limit != null && limit == 0) {
            limit = null;
        }

        /**
         * 没指定outDate 都按createdDate来过滤时间
         * */
        if (dateField == null || dateField.compareTo("inConfirmDate") != 0) {
            dateField = "createdDate";
        }



        if (begDate != null) {
            begDate = DateUtils.getStartTime(begDate);
        }

        if (endDate != null) {
            endDate = DateUtils.getEndTime(endDate);
        }
        if (transInPharmacyType != null) {
            pharmacyQueryClinicString += String.format(" AND a.trans_in_pharmacy_type = %d ", transInPharmacyType);
        }
        if (transOutPharmacyType != null) {
            pharmacyQueryClinicString += String.format(" AND a.trans_out_pharmacy_type = %d ", transOutPharmacyType);
        }
        if (transInPharmacyNo != null && transOutPharmacyNo != null) {
            pharmacyQueryClinicString += String.format(" AND  ( a.trans_in_pharmacy_no = %d ", transInPharmacyNo);
            if (pharmacyNoOr == 1) {
                pharmacyQueryClinicString += String.format(" OR ");
            } else {
                pharmacyQueryClinicString += String.format(" AND ");
            }
            pharmacyQueryClinicString += String.format("  a.trans_out_pharmacy_no = %d )", transOutPharmacyNo);
        } else if (transInPharmacyNo != null && transOutPharmacyNo == null) {
            pharmacyQueryClinicString += String.format(" AND   a.trans_in_pharmacy_no = %d ", transInPharmacyNo);
        } else if (transInPharmacyNo == null && transOutPharmacyNo != null) {
            pharmacyQueryClinicString += String.format(" AND   a.trans_out_pharmacy_no = %d ", transOutPharmacyNo);
        }

        /**
         * 总部
         * */
        if (GoodsPrivUtils.isHeadClinic(headerClinicType, headerClinicViewMode)) {
            //TODO nothing
            if (!StringUtils.isEmpty(transOutClinicId) && !StringUtils.isEmpty(transInClinicId)) {
                queryClinicString = String.format(" (a.from_organ_id = '%s' and a.to_organ_id ='%s') ",
                        transOutClinicId, transInClinicId
                );
            } else if (!StringUtils.isEmpty(transOutClinicId) && StringUtils.isEmpty(transInClinicId)) {
                queryClinicString = String.format(" (a.from_organ_id = '%s' ) ",
                        transOutClinicId
                );
            } else if (StringUtils.isEmpty(transOutClinicId) && !StringUtils.isEmpty(transInClinicId)) {
                queryClinicString = String.format(" (a.to_organ_id = '%s' ) ",
                        transInClinicId
                );
            }
        } else {
            /**
             * 调入调出门店都指定了
             * */
            if (!StringUtils.isEmpty(transOutClinicId) && !StringUtils.isEmpty(transInClinicId)) {
                /**
                 * 本门店内调拨
                 * */
                if (GoodsUtils.compareStrEqual(transInClinicId, transOutClinicId)) {
                    if (GoodsUtils.compareStrEqual(transInClinicId, headerClinicId)) {//总部
                        queryClinicString = "";
                    } else {
                        queryClinicString = String.format(" ((a.from_organ_id = '%s' and a.to_organ_id ='%s') OR (a.from_organ_id = '%s' and a.to_organ_id ='%s') )",
                                transOutClinicId, headerClinicId,
                                headerClinicId, transInClinicId
                        );
                    }
                } else { //出入库门店不一样
                    if (GoodsUtils.compareStrEqual(transOutClinicId, headerClinicId)) {
                        queryClinicString = String.format(" (a.from_organ_id = '%s' and a.to_organ_id = '%s' ) ", transOutClinicId,transInClinicId
                        );
                    } else if (GoodsUtils.compareStrEqual(transInClinicId, headerClinicId)) {
                        queryClinicString = String.format(" (a.from_organ_id = '%s' and a.to_organ_id = '%s' ) ", transOutClinicId,transInClinicId
                        );
                    } else {
                        throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "子店筛选中不能调拨方或出口方都是其他门店");
                    }
                }
            } else if (!StringUtils.isEmpty(transOutClinicId) && StringUtils.isEmpty(transInClinicId)) {
                if (!GoodsUtils.compareStrEqual(transOutClinicId, headerClinicId)) {
                    queryClinicString = String.format(" (a.from_organ_id = '%s' and a.to_organ_id ='%s') ",
                            transOutClinicId, headerClinicId
                    );
                } else {
                    queryClinicString = String.format(" (a.from_organ_id = '%s' ) ", transOutClinicId
                    );
                }
            } else if (StringUtils.isEmpty(transOutClinicId) && !StringUtils.isEmpty(transInClinicId)) {
                if (!GoodsUtils.compareStrEqual(transInClinicId, headerClinicId)) {
                    queryClinicString = String.format(" (a.from_organ_id = '%s' and a.to_organ_id ='%s') ",
                            headerClinicId, transInClinicId
                    );
                } else {
                    queryClinicString = String.format(" (a.to_organ_id = '%s' ) ", transInClinicId
                    );
                }
            } else {
                queryClinicString = String.format(" (a.from_organ_id = '%s' OR a.to_organ_id ='%s') ",
                        headerClinicId, headerClinicId
                );
            }
        }

    }

    /**
     * 是否是进入首页的请求---这个才有数据缓存的必要
     */
    public boolean isDefaultQuery() {
       return  status == null &&
               begDate == null && endDate == null  &&
               transInPharmacyType == null && transOutPharmacyType == null &&
               transInPharmacyNo == null && transOutPharmacyNo == null &&
               StringUtils.isEmpty(transInClinicId) &&
               StringUtils.isEmpty(transOutClinicId) &&
               GoodsUtils.compareStrEqual(dateField,"createdDate") &&
               StringUtils.isEmpty(withGoodsId) ;
    }
    /**
     * 上一个没导出完成，下一个不容许导入
     * */
    private String md5Key;

    public String getReqMd5Key() {
        if (md5Key != null) {
            return md5Key;
        }
        StringBuilder sb = new StringBuilder();
        sb.append("exportTransOrder"); //因为一个门店库存非库存复用同一主key
        sb.append(GoodsUtils.getInt(status)).append("_");
        sb.append(headerChainId).append("_");
        sb.append(dateField).append("_");
        sb.append(getBeginInstant()).append("_");
        sb.append(getEndInstant()).append("_");
        sb.append(transOutClinicId).append("_");
        sb.append(transInClinicId).append("_");
        sb.append(withGoodsId).append("_");
        sb.append(transOutPharmacyType).append("_");
        sb.append(transOutPharmacyNo).append("_");
        sb.append(transInPharmacyType).append("_");
        sb.append(transInPharmacyNo).append("_");
        sb.append(GoodsUtils.getInt(offset)).append("_");
        sb.append(GoodsUtils.getInt(limit)).append("_");


        md5Key = MD5Utils.sign(sb.toString());
        return md5Key;
    }


}
