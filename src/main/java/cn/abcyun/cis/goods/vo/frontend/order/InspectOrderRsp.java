package cn.abcyun.cis.goods.vo.frontend.order;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsSupplierView;
import cn.abcyun.cis.goods.vo.frontend.inspect.GoodsInspectOrderLogView;
import cn.abcyun.cis.goods.vo.frontend.order.base.OrderBaseRsp;
import cn.abcyun.cis.goods.vo.frontend.supplier.GoodsSupplierSellerView;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * @Author: 何平
 * @Description: 验收单响应
 * @Since: 2024/1/13 14:43
 **/

@ApiModel(value = "InspectOrderRsp", description = "验收单响应")
@Data
@EqualsAndHashCode(callSuper = false)
public class InspectOrderRsp extends OrderBaseRsp {

    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 验收人
     */
    @ApiModelProperty("验收人")
    private Employee inspector;

    @ApiModelProperty("")
    @JsonIgnore
    private String inspectBy;
    /**
     * 验收时间
     */
    @ApiModelProperty("验收时间")
    private Instant inspectTime;

    /**
     * 收货人
     */
    @ApiModelProperty("供应商")
    private GoodsSupplierView supplier;


    /**
     * Items
     */
    @ApiModelProperty("Items")
    private List<InspectOrderItemRsp> list;

    /**
     * 0:总部集采 10:门店自采 20:总部配货 30:门店调拨
     */
    @ApiModelProperty("0:总部集采 10:门店自采 20:总部配货 30:门店调拨")
    //@JsonIgnore
    private Integer sourceType;

    /**
     * 订单状态 {@link cn.abcyun.cis.goods.entity.GoodsStockInspectOrder.InspectStatus}
     */
    @ApiModelProperty("订单状态 0:草稿 10:代验收 20:验收合格 30：不合格 40：部分合格")
    private int status;

    @ApiModelProperty("收货数量")
    private BigDecimal receiveNumSum;

    @ApiModelProperty("验货数量")
    private BigDecimal inspectNumSum;


    @ApiModelProperty("关联收货单单号")
    private String relationReceiveOrderNo;

    @ApiModelProperty("关联收货单ID")
    private Long relationReceiveOrderId;

    @ApiModelProperty("关联订单编号")
    private String relationOrderNo;

    @ApiModelProperty("关联订单ID")
    private Long relationOrderId;

    @ApiModelProperty("关联订单类型")
    private Integer relationType;

    /**
     * 收货时间
     */
    @ApiModelProperty("收货时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Instant receiveTime;

    /**
     * 操作日志
     */
    @ApiModelProperty("操作日志")
    private List<GoodsInspectOrderLogView> logs;

    /**
     * 商城订单ID
     */
    @ApiModelProperty("商城订单ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long mallOrderId;

    @ApiModelProperty(value = "配送单id")
    private Long deliveryOrderId;

    @ApiModelProperty("供应商销售员")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private GoodsSupplierSellerView supplierSeller;

}
