package cn.abcyun.cis.goods.vo.frontend.goodslist;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class GoodsGoodsMaxCostPriceRsp {

    @ApiModelProperty(value = "最大成本价的门店Id")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String clinicId;

    @ApiModelProperty(value = "最大成本价的门店名字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String clinicName;

    @ApiModelProperty(value = "最大成本价")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal packageCostPrice;

}
