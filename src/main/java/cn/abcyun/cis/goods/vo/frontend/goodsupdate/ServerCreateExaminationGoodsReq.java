package cn.abcyun.cis.goods.vo.frontend.goodsupdate;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.model.Goods;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;


/**
 * 检查检验 创建用到的字段
 * 放到一个类，不要继承，搞复杂了。
 * 本类是实际从客户端请求接收了的,用于药品物资修改的字段
 *
 * <AUTHOR> 2021.02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ServerCreateExaminationGoodsReq extends ServerCreateGoodsAbstractReq {
    /*-----ID和类型 Part------*/
    /**
     * goodsId
     */
    private String id;
    /**
     * 药品编码
     */
    private String shortId;

    /**
     * 药品类型 三元组 （type,subType,cMSpec）
     * typeId是药品类型三元组的ID
     */
    private short type;

    private short subType;

    private int typeId;
    /**
     * 用户自定义分类ID/二级分类
     */
    private Long customTypeId;


    /*-----名字 Part------*/
    /**
     * 药品商品名
     */
    private String name = "";

    public String getName() {
        return name != null ? name : "";
    }


    /*----包装价格Part------*/
    /**
     * 套餐默认用的大单位
     */
    private String packageUnit;

    private String pieceUnit;

    /**
     * 检查检验虽然是套餐类型的，但是价格是在goods上的
     */
    private BigDecimal packagePrice = null;//

    private BigDecimal piecePrice = null;

    /**
     * 成本价
     */
    private BigDecimal packageCostPrice = null;//


    /*----套餐Part------*/



    /*----治疗理疗特有Part------*/
    /**
     * 检查检验的扩展字段
     * 里面主要放样本类型
     */
    private JsonNode bizExtensions;

    /**
     * 检查检验的代码
     */
    private String enName;

    /**
     * 检查检验特有的 模版Goods的Id
     */
    private String bizRefId;

    /**
     * 检查检验设备id
     * 检验goods  设备型号的Id
     * 检查goods  设备的Id
     */
    private String bizRelevantId;

    /**
     * 扩展信息
     */
    private GoodsExtendInfo extendInfo;
    /**
     * 2023.02 为了快速支持医院 对检查项目的兼容，不加类型的情况下快速区分不同检查类型 0 普通检查 10 RIS检查 20
     *  0 普通检查 10 RIS检查 20
     * */
    private String extendSpec;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 供应商ID
     */
    @JsonIgnore
    private Integer providerId;

    /**
     * 检查部位 0=无 1=头部 2=胸部 3=脊柱 4=腹部 5=骨盆 6=上肢 7=下肢
     */
    private Integer inspectionSite;

    /**
     * 内部标识
     */
    private int innerFlag;

    /*-----医保社保 不是所有的药品类型有对码所以这里 只在子类里面定义 Part------*/

    private String cityCode;

    private String provinceCode;

    private String nationalCode;

    private String cityCodeId;

    private String provinceCodeId;

    private String nationalCodeId;


    private Short isSell;

    private Short dismounting;

    private int pieceNum;

    /**
     * 是否使用连锁的默认税率
     * 1 使用连锁配置的自定税率
     * 0 使用自定义的税率
     * 对于前段来讲都使用  inTax outTax字段 。scGoods后台会把 诊所的进销税率的修改 修改到 1 的goods上。这样其他统计各种地方只要用goods上的这两个字段就可以了。
     */
    @Deprecated
    private int defaultInOutTax = 0;

    /**
     * 1.中药备注
     * 2.检验goods模版门店 的备注
     */
    private String remark;

    /**
     * 如果goods存在那么不抛异常直接返回存在goods
     * 检验项目RPC需要
     */
    private Integer returnExistGoods;
    /**
     * 参数检查，不用注解，方便文案的提示
     * 检查检验不支持jenkins导入
     * 检查检验导入有examchli
     */
    @Override
    public void parameterCheck(ClinicConfig clinicConfig) {

        if (StringUtils.isEmpty(name)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "检查检验项目需要指定名称");
        }

        if (subType != GoodsConst.ExaminationSubType.ASSAY && subType != GoodsConst.ExaminationSubType.INSPECTION) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, GoodsUtils.goodsFullName(type, "", name) + "不是检查检验");
        }


        if (subType == GoodsConst.ExaminationSubType.ASSAY) {
            // 多设备调整，单项才校验
            if (getCombineType() == GoodsConst.GoodsCombine.NO_COMBINE && StringUtils.isEmpty(bizRelevantId)) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "检查检验项目要绑定设备");
            }
            //检验Goods 容许 null价格，null价格表示未定价
            if (getCombineType() != GoodsConst.GoodsCombine.COMBINE && getCombineType() != GoodsConst.GoodsCombine.NO_COMBINE) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, GoodsUtils.goodsFullName(type, "", name) + "请指定有效的组合类型");
            }
        } else if (subType == GoodsConst.ExaminationSubType.INSPECTION) {
            if (StringUtils.isEmpty(extendSpec)) {
                throw new ParamRequiredException("extendSpec");
            }
            switch (extendSpec) {
                case GoodsConst.GoodsExtendSpec.EYE_INSPECT:
                    // 眼科单项才校验
                    if (getCombineType() == GoodsConst.GoodsCombine.NO_COMBINE && StringUtils.isEmpty(bizRelevantId)) {
                        throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "检查检验项目要绑定设备");
                    }
                    break;
                case GoodsConst.GoodsExtendSpec.RIS_INSPECT:
                default:
                    break;
            }
            if (packageCostPrice == null) { //2位小数
                packageCostPrice = BigDecimal.ZERO;
            }
            if (getCombineType() != GoodsConst.GoodsCombine.COMBINE && getCombineType() != GoodsConst.GoodsCombine.NO_COMBINE) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, GoodsUtils.goodsFullName(type, "", name) + "请指定有效的组合类型");
            }
        }
        if (packageCostPrice != null) {
            packageCostPrice = packageCostPrice.setScale(Goods.CLIENT_PRICE_PRECISION, RoundingMode.UP);
        }


        super.parameterCheck(clinicConfig);
    }

    /**
     * 检查检验不对外销售，不用清理价格
     * 复写为空的实现，父类的实现是清空price和设置dismounting为整包销售
     */
    @Override
    public void fixSellAndPrice() {

    }

    /**
     * 为了基类的处理，增加的空的占位方法
     * 无此属性
     */
    @Override
    public String getManufacturerFull() {
        return null;
    }

    /**
     * 为了基类的处理，增加的空的占位方法
     * 无此属性
     */
    @Override
    public String getBarCode() {
        return null;
    }

    @Override
    public List<TraceableCodeNoInfo> getTraceableCodeNoInfoList() {
        return null;
    }

    /**
     * 为了基类的处理，增加的空的占位方法
     * 无此属性
     */
    @Override
    public BigDecimal getInTaxRat() {
        return null;
    }

    /**
     * 为了基类的处理，增加的空的占位方法
     * 无此属性
     */
    @Override
    public BigDecimal getOutTaxRat() {
        return null;
    }

    /**
     * 为了基类的处理，增加的空的占位方法
     * 无此属性
     */
    @Override
    public String getMedicineCadn() {
        return null;
    }

    /**
     * 为了基类的处理，增加的空的占位方法
     * 无此属性
     */
    @Override
    public void setMedicineCadn(String cadn) {

    }


    @Override
    public void setDismounting(Short dismounting) {

    }

    @Override
    public Integer getAntibiotic() {
        return null;
    }

    @Override
    public BigDecimal getDddOfAntibiotic() {
        return null;
    }

    @Override
    public String getUnitOfAntibiotic() {
        return null;
    }

    @Override
    public int getPriceType() {
        return GoodsConst.PriceType.PRICE;
    }

    @Override
    public BigDecimal getPriceMakeupPercent() {
        return null;
    }

    @Override
    public Long getProfitCategoryType() {
        return null;
    }

    @Override
    public Integer getOtcType() {
        return null;
    }

    @Override
    public Integer getDosageFormType() {
        return null;
    }

    @Override
    public BigDecimal getShelfLife() {
        return null;
    }

    @Override
    public Integer getBaseMedicineType() {
        return null;
    }

    @Override
    public Integer getMaintainType() {
        return null;
    }

    @Override
    public Integer getStorageType() {
        return null;
    }

    @Override
    public String getMha() {
        return null;
    }

    @Override
    public Integer getDangerIngredient() {
        return null;
    }

    @Override
    public String getPharmacologicId() {
        return null;
    }
}
