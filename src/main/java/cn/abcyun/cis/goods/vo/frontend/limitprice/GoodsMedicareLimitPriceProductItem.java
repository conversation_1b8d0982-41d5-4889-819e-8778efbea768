package cn.abcyun.cis.goods.vo.frontend.limitprice;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.ShebaoPayLimitPriceProductRule;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-02-23 16:44:57
 */
@Data
public class GoodsMedicareLimitPriceProductItem {
    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    @ApiModelProperty(value = "医保限价规则")
    private ShebaoPayLimitPriceProductRule productRule;

    @ApiModelProperty(value = "序号")
    private int sort;

    public void checkParam(int type) {
        if (StringUtils.isEmpty(goodsId)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "商品ID不能为空");
        }
        GoodsUtils.checkShebaoLimitPriceProductRule(productRule);
        if (type == 0) {
            if (productRule.getPriceType() == GoodsConst.ShebaoLimitPriceType.SALE_PRICE) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "药品限价类型错误");
            }
        } else {
            if (productRule.getPriceType() == GoodsConst.ShebaoLimitPriceType.COST_PRICE) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "项目限价类型错误");
            }
        }
    }
}
