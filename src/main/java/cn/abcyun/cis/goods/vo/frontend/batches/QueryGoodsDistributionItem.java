package cn.abcyun.cis.goods.vo.frontend.batches;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-05 16:35:30
 */
@Data
public class QueryGoodsDistributionItem {

    private String goodsId;
    private GoodsItem goods;
    private BigDecimal lastPackageCostPrice;
    private String lastStockInOrderSupplier;

    private List<ClinicDistributionInfo> clinicList = new ArrayList<>();

    @Data
    public static class ClinicDistributionInfo {
        private String clinicId;
        private BigDecimal currentCount;
        private BigDecimal currentPieceCount;
        private BigDecimal currentPackageCount;
        private String dispGoodsCount;
        private BigDecimal lastMonthSellCount;
        private Integer turnoverDays;
        private Boolean shortageWarnFlag;
        private String lastInDate;
    }
}
