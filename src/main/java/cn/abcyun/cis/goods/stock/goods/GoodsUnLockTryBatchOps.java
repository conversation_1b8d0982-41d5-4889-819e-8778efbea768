package cn.abcyun.cis.goods.stock.goods;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.ScGoodsGoodsLockReq;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockBatchItem;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.goods.consts.CountUnitFlag;
import cn.abcyun.cis.goods.model.GoodsBatch;
import cn.abcyun.cis.goods.model.GoodsStockLocking;
import cn.abcyun.cis.goods.stock.stockmanager.GoodsStockCutManager;
import cn.abcyun.cis.goods.utils.GoodsStockUtils;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.utils.UserFillUtils;
import cn.abcyun.cis.goods.vo.frontend.lock.GoodsLockResultItem;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

/******************************************************************************************
 * 产品在第一版锁库的时候是容许锁成负数的(无库存可以开出 + 锁库打开)
 * 2024.11产品想收回锁成负库存(无库存可以开出 锁库打开 排斥不能同时打开)
 *      查数据发现 总共2400加开锁库，有800家开了无库存可以开出，收不回来了。
 *      产品又做了个补丁,新开诊所默认不允许无库存开出和锁库不能同时打开. 老诊所保持现有配置，但是重新设置需要关闭保持互斥（这个补丁其实也无用，锁数量始终收不回来。）
 * 产品2024.11月做成锁库就锁批次，因为上面这个原因还是会有锁数量存在。
 * (锁库就锁批次)锁上后这个锁别人不能抢，也不能抢别人。但是这个锁在库存不够时，能换其他没锁的库存
 * ------------------------------------------------------------
 *****************************************************************************************
 * ｜        锁类型     ｜    上锁阶段           ｜     锁释放阶段                            ｜
 * ｜ 锁数量            ｜ ❌ 随便换             ｜✅ 也是随便换                               ｜
 *
 * 这个类的逻辑是新版锁库逻辑：开锁库就锁批次
 * 1.锁批次 ，批次不够锁数量(为什么还存在锁数量，背景上面已经注释)
 * 2.下面这个类会处理 锁数量，锁批次的情况
 *      1.库存充足：锁批次
 *          会引入扣库效期问题:效期不再是发药而是锁库；
 *          无换锁时时机:会加剧批次不可销售,收费单/发药单库存提示不足问题-
 *              (用户啥都没干锁的批次没了，整体库存又是够的)）之前进价加成锁库虽然有这个问题但是是必须锁死用户需要处理的
 *      2.库存部分够： 锁批次 + 锁数量（这里产品定逻辑是锁到批次上锁成负数还是锁数量？）
 *      3.库存没有：锁数量(完全没批次可锁)
 *      --------
 *****************************************************************************************/
public class GoodsUnLockTryBatchOps extends GoodsLockTryBatchOps {

    private final Map<Long, GoodsStockLocking> batchIdToGoodsStockLocking = new HashMap<>();

    /**
     * 只要不必现上锁阶段的锁大都可以随便改
     */
    protected int initBeforeLocking() {
        int ret = super.initBeforeLocking();
        if (ret != GoodsLockResultItem.SUCCESS) {
            return ret;
        }
        BigDecimal lockingPackageCount = BigDecimal.ZERO;
        BigDecimal lockingPieceCount = BigDecimal.ZERO;
        BigDecimal lockingTotalPieceCount = BigDecimal.ZERO;
        BigDecimal lockingLeftTotalPieceCount = BigDecimal.ZERO;

        /*
         * 所有发药 部分退费 释放的锁库批次
         * */
        for (GoodsStockLocking locking : goodsStockOpsHelper.getGoodsStockSummary().getGoodsLockingList()) {
            if (!GoodsUtils.compareLongEqual(locking.getLockId(), clientReqLockId)) {
                continue;
            }
            /*
             * 无库存不容许开出 ，可能没批次为空
             * */
            if (locking.getBatchId() != null) {
                batchIdToGoodsStockLocking.put(locking.getBatchId(), locking);
            }
            lockingTotalPieceCount = GoodsStockUtils.add(lockingTotalPieceCount, locking.getLockingTotalPieceCount());
            lockingPieceCount = GoodsStockUtils.add(lockingPieceCount, locking.getLockingPieceCount());
            lockingPackageCount = GoodsStockUtils.add(lockingPackageCount, locking.getLockingPackageCount());

            lockingLeftTotalPieceCount = GoodsStockUtils.add(lockingLeftTotalPieceCount, locking.getLockLeftTotalPieceCount());
        }

        // 删除都应该成功- 收费收3 发1 收费退费2，这个时候传的是opType == 1
        if(scGoodsLockingGoodsLockReqItem.getOpType() == ScGoodsGoodsLockReq.DEL){
            return GoodsLockResultItem.SUCCESS;
        }

        /*
         * 总数量不能往大了改
         * 指定的批次内部可以不限制随便。
         * 协议保持和锁库一样  scGoodsLockingGoodsLockReqItem 是换锁要换到的数量
         * */
        if (scGoodsLockingGoodsLockReqItem.getLockUnitFlag() == CountUnitFlag.BIG) {
            if (scGoodsLockingGoodsLockReqItem.getLockingPackageCount().compareTo(lockingPackageCount) > 0) {
                sLogger.error("lockId:{} 申请锁定的大单位数量:{} 大于已经锁定的大单位数量:{}",
                        clientReqLockId, scGoodsLockingGoodsLockReqItem.getLockingPackageCount(), lockingPackageCount);
                return GoodsLockResultItem.NO_ENOUGH_LOCK_STOCK;
            }
        } else {
            if (reqLockStandardStockCount.getOverallPieceCount().compareTo(lockingPieceCount) > 0) {
                sLogger.error("lockId:{} 申请锁定的总数量:{} 大于已经锁定的总数量:{}",
                        clientReqLockId, reqLockStandardStockCount.getOverallPieceCount(), lockingPieceCount);
                return GoodsLockResultItem.NO_ENOUGH_LOCK_STOCK;
            }
        }
        return ret;
    }

    /**
     * 释放锁库只是这个部分设置locking有差异
     * @param findLockCountLockingRecord 之前锁数量的locking
     */
    protected int doUpdateAfterBatchLocking(BigDecimal clientReqLockingPieceCountDelta,
                                            GoodsStockLocking findLockCountLockingRecord,
                                            Set<Long> thisTimeLockedLockIdSet,
                                            Long lockId) {
        /*
         * 还有库存不够，那就是要锁数量
         * 这个分支一走，这次锁库数量完全被锁上了
         * */
        if (clientReqLockingPieceCountDelta.compareTo(BigDecimal.ZERO) > 0) {
            /*
             * 产品放出去的功能收不回来了，有八百家 无库存开出+ 锁库
             * */
            if (findLockCountLockingRecord == null && forceLock == YesOrNo.NO) {
                return GoodsLockResultItem.NO_ENOUGH_BATCH_STOCK;
            }
            /*
             * 锁数量 找locking有三种情况
             * 1.之前有锁数量的locking 直接用
             * 2.之前有锁批次的，把这个转成锁数量 （批次无库存了）
             * 3.没locking 新生成
             * */
            GoodsStockLocking lockingUse = null;
            if (findLockCountLockingRecord != null) {
                lockingUse = findLockCountLockingRecord;
            } else {
                for (GoodsStockLocking locking : svrExistGoodsStockLockingList) {
                    if (!locking.isLocking()) {
                        continue;
                    }
                    if (thisTimeLockedLockIdSet.contains(locking.getId())) {
                        continue;
                    }
                    if (lockingUse != null) {
                        //⚠️ 和释放锁库的差异仅仅在这一行代码状态的设置上
                        realChangeLockingPieceCount = MathUtils.wrapBigDecimalAdd(realChangeLockingPieceCount,MathUtils.wrapBigDecimalSubtract(BigDecimal.ZERO,locking.getLockLeftTotalPieceCount()));
                        locking.setLockLeftTotalPieceCount(BigDecimal.ZERO);
                        locking.setStatus(GoodsStockLocking.Status.LOCKING_RELEASE);
                        createStockLockingList.add(locking); //加进去是要写redis,如果不加进去锁库库存一直在，批次好像批次不在一样https://www.tapd.cn/tapd_fe/43780818/bug/detail/1143780818001081709
                        UserFillUtils.fillLastModifiedBy(locking, lockingGoodsLockReq.getOperatorId());
                        continue;
                    }
                    lockingUse = locking;
                }
            }
            /*
             * 锁数量
             * */
            BigDecimal beforeLockingPieceCount = BigDecimal.ZERO;
            if (lockingUse != null) {
                beforeLockingPieceCount = lockingUse.getLockLeftTotalPieceCount();
            }
            GoodsStockLocking newCountLocking = lockANewCountIfNeed(lockingUse, clientReqLockingPieceCountDelta, lockId);
            realChangeLockingPieceCount = MathUtils.wrapBigDecimalAdd(realChangeLockingPieceCount, MathUtils.wrapBigDecimalSubtract(newCountLocking.getLockLeftTotalPieceCount(), beforeLockingPieceCount));
            createStockLockingList.add(newCountLocking); //修正和修改都应该save
            if (lockingUse == null) {
                logList.add(addGoodsStockLockingLog(clinicConfig.getChainId(), newCountLocking.getLockId(), newCountLocking.getId(), "锁库锁数量新增锁数量", null, lockingGoodsLockReq.getLockScene(), JsonUtils.dumpAsJsonNode(scGoodsLockingGoodsLockReqItem)));
            }
            clientReqLockingPieceCountDelta = BigDecimal.ZERO;
            thisTimeLockedLockIdSet.add(newCountLocking.getId());
        }
        /*
         * 还要再检查一次检查是否处理完
         * */
        if (clientReqLockingPieceCountDelta.compareTo(BigDecimal.ZERO) == 0) {
            for (GoodsStockLocking locking : svrExistGoodsStockLockingList) {
                if (!locking.isLocking()) {
                    continue;
                }
                /*
                 * 这一次锁库 锁的批次Id集合，不在里面的记录都应该删除
                 * 这个类是锁阶段， 记录要被设置成删除
                 * */
                if (thisTimeLockedLockIdSet.contains(locking.getId())) {
                    continue;
                }
                realChangeLockingPieceCount = MathUtils.wrapBigDecimalAdd(realChangeLockingPieceCount, MathUtils.wrapBigDecimalSubtract(BigDecimal.ZERO, locking.getLockLeftTotalPieceCount()));
                locking.setLockLeftTotalPieceCount(BigDecimal.ZERO);
                locking.setStatus(GoodsStockLocking.Status.LOCKING_RELEASE);
                UserFillUtils.fillLastModifiedBy(locking, lockingGoodsLockReq.getOperatorId());
                logList.add(addGoodsStockLockingLog(clinicConfig.getChainId(), locking.getLockId(), locking.getId(), "锁库锁数量删除锁库", null, lockingGoodsLockReq.getLockScene(), JsonUtils.dumpAsJsonNode(scGoodsLockingGoodsLockReqItem)));
                createStockLockingList.add(locking); //加进去是要写redis,如果不加进去锁库库存一直在，批次好像批次不在一样https://www.tapd.cn/tapd_fe/43780818/bug/detail/1143780818001081709
            }
        }
        return GoodsLockResultItem.SUCCESS;
    }

    /**
     * 这个类是处理锁批次的锁阶段，删除就是删除
     */
    protected void doDeleteLocking() {
        if (CollectionUtils.isEmpty(svrExistGoodsStockLockingList)) {
            return;
        }
        GoodsLockResultItem lockResultItem = createClientRspItem(GoodsLockResultItem.SUCCESS);
        /*
         * 请求的lockId还是批次，不管，反正就是把整个lockId拉出来的锁库记录全删了
         * */
        BigDecimal releasePieceCount = BigDecimal.ZERO;
        BigDecimal reqTotalLockPieceCount = BigDecimal.ZERO;
        BigDecimal reqTotalLockPackageCount = BigDecimal.ZERO;
        for (GoodsStockLocking goodsStockLocking : svrExistGoodsStockLockingList) {
            releasePieceCount = GoodsStockUtils.add(releasePieceCount, goodsStockLocking.getLockLeftTotalPieceCount());
            reqTotalLockPieceCount = GoodsStockUtils.add(reqTotalLockPieceCount, goodsStockLocking.getLockingPieceCount());
            reqTotalLockPackageCount = GoodsStockUtils.add(reqTotalLockPackageCount, goodsStockLocking.getLockingPackageCount());
            goodsStockLocking.setLockLeftTotalPieceCount(BigDecimal.ZERO);
            goodsStockLocking.setStatus(GoodsStockLocking.Status.LOCKING_RELEASE); //几个复写的差异在状态
            goodsStockLocking.setLastModified(Instant.now());
            logList.add(addGoodsStockLockingLog(clinicConfig.getChainId(), goodsStockLocking.getLockId(), goodsStockLocking.getId(), "删除", null, lockingGoodsLockReq.getLockScene(), JsonUtils.dumpAsJsonNode(scGoodsLockingGoodsLockReqItem)));
            createStockLockingList.add(goodsStockLocking);//删除
        }


        GoodsStockLocking goodsStockLocking = svrExistGoodsStockLockingList.get(0);
        lockResultItem.setLockId(clientReqLockId);
        lockResultItem.setLockingPieceNum(BigDecimal.valueOf(goodsStockLocking.getLockingPieceNum()));
        lockResultItem.setOptType(GoodsStockLocking.OptType.TRY_BATCH);
        lockResultItem.setOpType(ScGoodsGoodsLockReq.DEL);
        lockResultItem.setPharmacyNo(goodsStockLocking.getPharmacyNo());
        lockResultItem.setPackageCostPrice(goodsStockLocking.getPackageCostPrice());
        lockResultItem.setPackageCount(reqTotalLockPackageCount);
        lockResultItem.setPieceCount(reqTotalLockPieceCount);
        BigDecimal changedLockPieceCount = releasePieceCount.negate();
        lockResultItem.setChangedLockPieceCount(changedLockPieceCount); //外围会用这个进行goodsStat的增量加减
        lockResultItem.setLockingTotalPieceCount(null);
        retList.add(lockResultItem);
    }

    /***
     * 用户指定批次&指定数量上来
     * 这里是是释放锁库，在现有的基础上换锁
     * 锁数量这个场景，理论上目前不会进入这个分支，只是把逻辑预留起
     * */
    protected List<GoodsBatch> dealUserSpecificyCutBatchList(Map<Long, BigDecimal> batchIdToShortagePieceCount, Set<Long> toLockingBatchIdSet) {
        List<GoodsBatch> lockingBatchList = new ArrayList<>();
        /*
         * 外围的汇总数量天大
         * */
        if (getPreLockingTotalPieceCount().compareTo(BigDecimal.ZERO) == 0) {
            return lockingBatchList;
        }


        for (GoodsLockBatchItem goodsLockBatchItem : scGoodsLockingGoodsLockReqItem.getGoodsLockBatchItemList()) {
            /*
             * 从锁数量转过来的
             * */
            if (goodsLockBatchItem.getBatchId() == null) {
                continue;
            }
            /*
             * 批次都不在了
             * */
            GoodsBatch findBatch = this.batchIdToBatch.get(goodsLockBatchItem.getBatchId());
            if (findBatch == null) {
                continue;
            }
            /*
             * 批次已经被加过了？ 如果清理里面指定的两个batchId一样，第二个会被这里丢弃
             * */
            if (toLockingBatchIdSet.contains(findBatch.getBatchId())) {
                continue;
            }

            /*
             * 这里是往小了改，所以这些批次是之前锁住的，可以不管
             * eg。区别这里释放库存，和发药释放锁库不一样。
             * */

            /*
             * 这个批次要把这个lockId释放排除掉 ，null 代表是包含了所有锁库库存量
             * */
            // min(锁库剩余, 请求锁库数量)
            BigDecimal minTmp = getPreLockingTotalPieceCount().min(goodsLockBatchItem.getLockingTotalPieceCount());

            batchIdToCountLimit.put(findBatch.getBatchId(), minTmp);
            if (minTmp.compareTo(goodsLockBatchItem.getLockingTotalPieceCount()) != 0) {
                batchIdToShortagePieceCount.put(findBatch.getBatchId(), goodsLockBatchItem.getLockingTotalPieceCount().subtract(minTmp));
            }

            lockingBatchList.add(findBatch);
            toLockingBatchIdSet.add(findBatch.getBatchId());
            this.subPreLockingTotalPieceCount(batchIdToCountLimit.get(findBatch.getBatchId()));
        }
        //之前锁库的批次按效期排一把
        GoodsStockCutManager.sortGoodsStockForStockCutImpl(lockingBatchList,goods,scGoodsLockingGoodsLockReqItem.getLockUnitFlag(),scGoodsLockingGoodsLockReqItem.getLockingTotalPieceCount(),clientReqLockId);
        return lockingBatchList;
    }

    /***
     * 锁批次，优先以之前锁的批次作为第一优先级
     * */
    protected List<GoodsBatch> findAndSortTheLockBatchList() {
        /*
         * 外围指定的总数量为准
         * */
        this.initPreLockingTotalPieceCount();
        Set<Long> toLockingBatchIdSet = new HashSet<>();
        List<GoodsBatch> lockingBatchList = new ArrayList<>();
        /*
         * 用户指定批次 ，指定数量
         * */
        Map<Long, BigDecimal> batchIdToShortagePieceCount = new HashMap<>();
        if (!CollectionUtils.isEmpty(scGoodsLockingGoodsLockReqItem.getGoodsLockBatchItemList())) {
            lockingBatchList.addAll(dealUserSpecificyCutBatchList(batchIdToShortagePieceCount, toLockingBatchIdSet));
        }
        lockingBatchList.addAll(dealPreLockingBatchList(batchIdToShortagePieceCount, toLockingBatchIdSet));

        /*
         * 剩余批次 第三优先级
         */
        lockingBatchList.addAll(dealLeftBatchList(toLockingBatchIdSet));
        return lockingBatchList;
    }

}