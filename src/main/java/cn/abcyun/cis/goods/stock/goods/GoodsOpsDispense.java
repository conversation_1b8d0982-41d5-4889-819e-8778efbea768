package cn.abcyun.cis.goods.stock.goods;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispenseConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import cn.abcyun.bis.rpc.sdk.cis.model.message.goodslog.*;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisCache;
import cn.abcyun.cis.goods.consts.CountUnitFlag;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.domain.GoodsStockMountTrible;
import cn.abcyun.cis.goods.entity.TraceableCodeOpsHelper;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.model.*;
import cn.abcyun.cis.goods.repository.GoodsStockLockingRepository;
import cn.abcyun.cis.goods.service.GoodsStatService;
import cn.abcyun.cis.goods.stock.batch.GoodsBatchOpsBase;
import cn.abcyun.cis.goods.stock.stock.CreateGoodsStockLogBatParameter;
import cn.abcyun.cis.goods.stock.stock.GoodsStockOpsHelper;
import cn.abcyun.cis.goods.utils.*;
import cn.abcyun.cis.goods.utils.protocol.GoodsStockLogUtils;
import cn.abcyun.cis.goods.view.GoodsDispenseDataItem;
import cn.abcyun.cis.goods.view.GoodsDispenseReq;
import cn.abcyun.cis.goods.vo.frontend.pharmacy.GoodsPharmacyView;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import reactor.util.function.Tuple4;
import reactor.util.function.Tuples;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 药品的发药
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GoodsOpsDispense extends GoodsOpsBase {
    private static final Logger sLogger = LoggerFactory.getLogger(GoodsOpsDispense.class);
    /**
     * 发药总请求
     */
    private GoodsDispenseReq clientReq;
    private GoodsStockLockingRepository goodsStockLockingRepository;
    /**
     * 药品发药请求
     */
    private GoodsDispenseDataItem goodsDispenseDataItem;

    /**
     * 发药BatchId
     */
    private String batId;


    /**
     * 进行goods的药品库存的发药操作
     *
     * @param goods                  要发药的药品资料
     * @param goodsStockOpsHelper    发药的小单位数量 -
     * @param clinicConfig           发药门店id（chainId在goods里面）
     * @param batId                  本次发药日志的batId
     * @param clientReq              客户端整个请求体
     * @param goodsDispenseDataItem  客户端本次发药的请求体
     * @param list                   通过GoodsStockManager选出来的进行库存扣减的stock列表。发药是最近有效期排在前面，发药先把要过期的阀了
     * @param traceableCodeOpsHelper 发药项本次发药的追溯码
     */
    public GoodsOpsDispense(GoodsRedisCache goods,
                            GoodsPharmacyView stockCutPharmacyNo, //库存发生的药房号
                            int goodsStockCutOpsType,
                            GoodsStockOpsHelper goodsStockOpsHelper, //不是必传参数
                            ClinicConfig clinicConfig,
                            String batId,
                            GoodsDispenseReq clientReq,
                            GoodsDispenseDataItem goodsDispenseDataItem,
                            List<GoodsBatchOpsBase> list,
                            GoodsStatService goodsStatService,
                            GoodsStockLockingRepository goodsStockLockingRepository,
                            SupplierUtils supplierUtils,
                            List<GoodsStockLockingReportLog> reportedDispenseGoodsStockLockingReportLogList,
                            List<GoodsStockLockingReportLog> reportedUnDispenseGoodsStockLockingReportLogList,
                            Map<String, GoodsStockLogMsgBody> goodsIdToLogMsgBody,//订单的发送goodsStockLogMsgBody的消息体
                            TraceableCodeOpsHelper traceableCodeOpsHelper,
                            TraceableCodeOpsHelper totalTraceableCodeOpsHelper
    ) {
        CreateGoodsStockLogBatParameter parameter = new CreateGoodsStockLogBatParameter();
        parameter.setGoodsSnap(GoodsUtils.genGoodsDbSnap(goods));
        parameter.setStockLogAction(GoodsConst.StockLogAction.ACTION_DISPENSE);
        parameter.setChainId(goods.getOrganId());
        parameter.setClinicId(clinicConfig.getClinicId());
        parameter.setEmployeeId(clientReq.getOperatorId());
        parameter.setStockLogBatid(batId);
        parameter.setPatientOrderId(clientReq.getPatientOrderId());
        parameter.setOrderId(clientReq.getDispenseSheetId());
        parameter.setOrderDetailId(goodsDispenseDataItem.getDispenseItemId());
        if (goodsDispenseDataItem.getTotalPrice() != null) {
            parameter.setClientReqTotalOriginPrice(goodsDispenseDataItem.getTotalPrice().abs());
        } else {
            parameter.setClientReqTotalOriginPrice(BigDecimal.ZERO);
        }

        this.goodsStockLockingRepository = goodsStockLockingRepository;
        this.clientReq = clientReq;
        this.batId = batId;
        this.goodsDispenseDataItem = goodsDispenseDataItem;
        if (reportedDispenseGoodsStockLockingReportLogList != null) {
            this.reportedDispenseGoodsStockLockingReportLogList = reportedDispenseGoodsStockLockingReportLogList;
        }
        if (reportedUnDispenseGoodsStockLockingReportLogList != null) {
            this.reportedUnDispenseGoodsStockLockingReportLogList = reportedUnDispenseGoodsStockLockingReportLogList;
        }
        this.goodsTraceableCodeOpsHelper = traceableCodeOpsHelper;
        this.totalGoodsTraceableCodeOpsHelper = totalTraceableCodeOpsHelper;

        super.initByGoodsRedisCache(goods,
                stockCutPharmacyNo,
                goodsStockCutOpsType,
                goodsStockOpsHelper,
                clinicConfig,
                goodsStatService,
                supplierUtils,
                parameter,
                list,
                goodsIdToLogMsgBody);
    }

    /**
     * 发药检查
     */
    @Override
    protected void checkGoodsStockCanOps() {
        if (notHasGoods()) {
            sLogger.info("发药goods is null");
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER);
        }
    }


    /**
     * 存不足扣减时
     * 发药
     */
    @Override
    protected void onStockModifyNoEnough(StockCutCountHelper stockCutCountHelper, BigDecimal notEnough) {
        boolean hasErrorTips = false;

        StringBuilder sb = new StringBuilder();
        if (stockCutCountHelper.getForceLockBatch() == YesOrNo.YES && !StringUtils.isEmpty(stockCutCountHelper.getTipsMsg())) {
            hasErrorTips = true;
            sb.append(stockCutCountHelper.getTipsMsg());
        } else {
            if (stockCutCountHelper.getGoodsStockOpsHelper() != null) {
                if (!stockCutCountHelper.getGoodsStockOpsHelper().getExpiredGoodsBatchList().isEmpty()) {
                    hasErrorTips = true;
                    for (int i = 0; i < stockCutCountHelper.getGoodsStockOpsHelper().getExpiredGoodsBatchList().size(); i++) {
                        GoodsBatch goodsBatch = stockCutCountHelper.getGoodsStockOpsHelper().getExpiredGoodsBatchList().get(i);
                        sb.append("<br>批次ID：").append(goodsBatch.getBatchId()).append("，批次过期禁售");
                    }
                }
                if (!stockCutCountHelper.getGoodsStockOpsHelper().getDisabledGoodsBatchList().isEmpty()) {
                    hasErrorTips = true;
                    for (int i = 0; i < stockCutCountHelper.getGoodsStockOpsHelper().getDisabledGoodsBatchList().size(); i++) {
                        GoodsBatch goodsBatch = stockCutCountHelper.getGoodsStockOpsHelper().getDisabledGoodsBatchList().get(i);
                        sb.append("<br>批次ID：").append(goodsBatch.getBatchId()).append("-");
                        int status = goodsBatch.getStatus();
                        if (status == GoodsStock.GoodsStockStatus.STOP_SALE_BY_USER_GOODS_DISABLE) {
                            sb.append("药品已停用");
                        }
                        if (status == GoodsStock.GoodsStockStatus.STOP_SALE_BY_USER_BATCH_DISABLE) {
                            sb.append("批次禁售");
                        }
                        if (status == GoodsStock.GoodsStockStatus.STOP_SALE_BY_EXPIRE) {
                            sb.append("批次禁售");
                        }
                    }
                }
                /*
                 * 批次不足的特别提示
                 * */
                if (!CollectionUtils.isEmpty(stockCutCountHelper.getStockLockingList())) {
                    hasErrorTips = true;
                    for (GoodsStockLocking goodsStockLocking : stockCutCountHelper.getStockLockingList()) {
                        if(MathUtils.wrapBigDecimalCompare(goodsStockLocking.getLockLeftTotalPieceCount(), BigDecimal.ZERO) <= 0){
                            continue;
                        }
                        if (goodsStockLocking.getOptType() == GoodsStockLocking.OptType.FORCE_BATCH) {
                            if(goodsStockLocking.getBatchId() != null){
                                sb.append("<br>批次ID：").append(goodsStockLocking.getBatchId()).append("-强制锁库批次库存不足");
                            } else{
                                sb.append("<br>：").append("强制锁批次已无批次可用");
                            }
                        }
                        if (GoodsStockLocking.canNotChangeLockingBatchOnPartDispense(goodsStockLocking.getOptType()) && MathUtils.wrapBigDecimalCompare(goodsStockLocking.getLockLeftTotalPieceCount(), BigDecimal.ZERO) > 0) {
                            if(goodsStockLocking.getBatchId() != null){
                                sb.append("<br>批次ID：").append(goodsStockLocking.getBatchId()).append("-因部分发药强制锁库定批次库存不足");
                            } else{
                                sb.append("<br>：").append("因部分发药强制锁库定批次已无批次可用");
                            }
                        }
                    }
                }
                if (!StringUtils.isEmpty(stockCutCountHelper.getDisplaySpec())) {
                    String nowDisplaySpec = goodsRedisCache.getDisplaySpec();
                    if (!StringUtils.isEmpty(nowDisplaySpec) && !GoodsUtils.compareStrEqual(stockCutCountHelper.getDisplaySpec(), nowDisplaySpec)) {
                        hasErrorTips = true;
                        sb.append("<br>药品规格发生变化：").append(stockCutCountHelper.getDisplaySpec()).append("->").append(nowDisplaySpec).append("）");
                    }
                }
            }
        }

        GoodsStockMountTrible stdCount = GoodsStockUtils.toPiecePackageUnit(notEnough.abs(), goodsRedisCache.getPieceNum(), goodsRedisCache.getTypeId());
        String readableGoodsCount = stdCount.getReadableStockCount((int) goodsRedisCache.getType(), (int) goodsRedisCache.getSubType(), goodsRedisCache.getPieceUnit(), goodsRedisCache.getPackageUnit());
        sLogger.error("发药库存不足:不足库存量={},{},{},this:{}", goodsRedisCache.getId(), readableGoodsCount, sb, this.goodsOpsResult);
        if (stockCutCountHelper.getForceLockBatch() == YesOrNo.YES) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_DISPENSE_DISPENSE_IS_SHORTAGE,
                    "【" + getGoodsFullName() + "】批次库存不足无法发药，不足库存量：" + readableGoodsCount + (hasErrorTips ? sb.toString() : "")
            );
        } else {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_DISPENSE_DISPENSE_IS_SHORTAGE,
                    "【" + getGoodsFullName() + "】库存不足无法发药，不足库存量：" + readableGoodsCount + (hasErrorTips ? sb.toString() : "")
            );
        }

    }


    @Override
    protected void doCalculateStockLogBatchOriginTotalPrice() {
        /*
            如果发药没有指定批次的售价，那么就走平摊的逻辑，将发药项的实收平摊到每个批次上，或者虽然指定了批次但是批次的实收为空（指定追溯码发药时），那么也一次平摊的逻辑。
            否则就直接将发药时指定的批次价格赋值给每个批次。
         */
        if (goodsDispenseDataItem == null || CollectionUtils.isEmpty(goodsDispenseDataItem.getBatchCountList())) {
            super.doCalculateStockLogBatchOriginTotalPrice();
            return;
        }

        boolean needCalculate = goodsDispenseDataItem.getBatchCountList().stream().filter(Objects::nonNull).anyMatch(batchCount -> batchCount.getBatchTotalPrice() == null);
        if (needCalculate) {
            super.doCalculateStockLogBatchOriginTotalPrice();
            return;
        }

        Map<Long, GoodsDispenseDataItem.BatchCount> batchIdToBatchCount = goodsDispenseDataItem.getBatchCountList().stream().collect(Collectors.toMap(GoodsDispenseDataItem.BatchCount::getBatchId, Function.identity(), (a, b) -> a));
        // 存在一个批次进行调拨领用的情况，会有多个stock，按批次摊到每个stock上
        Map<Long, List<GoodsStockLogMsg>> batchIdToLogMsgList = goodsStockLogMsgList.stream().collect(Collectors.groupingBy(GoodsStockLogMsg::getBatchId));
        batchIdToLogMsgList.forEach((batchId, logMsgList) -> {
            GoodsDispenseDataItem.BatchCount batchCount = batchIdToBatchCount.get(batchId);
            if (batchCount == null || batchCount.getBatchTotalPrice() == null) {
                logMsgList.forEach(it -> it.setClientReqOriginFlatPrice(BigDecimal.ZERO));
                return;
            }
            if (logMsgList.size() == 1) {
                GoodsStockLogMsg logMsg = logMsgList.get(0);
                logMsg.setClientReqOriginFlatPrice(batchCount.getBatchTotalPrice());
                return;
            }
            doFlatTotalPrice(logMsgList, batchCount.getBatchTotalPrice());
        });
    }


    /**
     * 从clientDispenseItem收集追溯码
     *
     * @param clientDispenseItem 发药数据项
     * @return 追溯码列表，非空数组
     */
    public static List<TraceableCode> collectTraceableCodesFromBatches(GoodsDispenseDataItem clientDispenseItem) {
        return clientDispenseItem.getAllTraceCodeList();
    }

    /**
     * 强控模式下
     * 生成reportLog
     */
    protected void createGoodsStockSupervisionReportLogForceControl(TraceableCodeOpsHelper goodsTraceableCodeOpsHelper) {

        /*
         * 开社保的要锁批次。如果是部分发，会把锁换成强锁
         * 强控模式下 发药服务在发药钱锁了一次库，理论上这里一定会有 进不了IF
         * */
        if (CollectionUtils.isEmpty(stockCutCountHelper.getStockLockingList())) {
            sLogger.error("严重错误 没有锁库批次 goodsId:{}{}", goodsRedisCache.getId(), GoodsUtils.goodsFullName(goodsRedisCache));
            return;
        }

        /*
         * 强控模式下只有首次发药才上报
         * */
        if (goodsDispenseDataItem.getIsTheFirstTimeDispensing() == YesOrNo.NO //第一次发药
                && goodsDispenseDataItem.getIsTheFirstTimeReDispensing() == YesOrNo.NO //重新首次发药
                && goodsDispenseDataItem.getIsTheFirstTimePartDispensing() == YesOrNo.NO // 部分后的首次发药
        ) {
            sLogger.info("goodsId:{},{}强控模式下不是首次发药不需要生成监管log", goodsRedisCache.getId(), GoodsUtils.goodsFullName(goodsRedisCache));
            return;
        }
        //做下兼容 如果部分发发药指定了全部追溯码，优先用那边的的
        if (!CollectionUtils.isEmpty(goodsTraceableCodeOpsHelper.getClientTraceableCodeList())) {
            dealCreateGoodsStockSupervisionReportLogByDispenseServiceSetCode(goodsTraceableCodeOpsHelper);
        } else {//没有，可能是没指定 找发药的
            if (!CollectionUtils.isEmpty(goodsDispenseDataItem.getReportedDispenseLockingLogList())) { //上一轮发过
                dealCreateGoodsStockSupervisionReportLogByPreSupervisionLog(goodsTraceableCodeOpsHelper, goodsDispenseDataItem.getReportedDispenseLockingLogList());
            } else if (!CollectionUtils.isEmpty(goodsDispenseDataItem.getReportedUnDispenseLockingLogList())) { // 上一轮退过
                dealCreateGoodsStockSupervisionReportLogByPreSupervisionLog(goodsTraceableCodeOpsHelper, goodsDispenseDataItem.getReportedUnDispenseLockingLogList());
            } else { //真的没指定追溯码
                dealCreateGoodsStockSupervisionReportLogByDispenseServiceSetCode(goodsTraceableCodeOpsHelper);
            }
        }
    }

    /**
     * 生成这个goods本次发药的traceCodeLog
     *
     * @param goodsTraceableCodeOpsHelper 这个helper里面放的是整个goods所有次发药的追溯码
     */
    protected void createGoodsStockSupervisionReportLog(TraceableCodeOpsHelper goodsTraceableCodeOpsHelper) {
        //有发生进销存
        if (CollectionUtils.isEmpty(goodsStockLogMsgList)) {
            return;
        }
        if (GoodsUtils.newVersionSuperVerionReport(clinicConfig)) {
            //非本地药房，但是这一单刷了医保的，还是报 用进销存log报。有进销存log，不一定有批次
            if (clientReq.getPharmacyType() != GoodsConst.PharmacyType.LOCAL_PHARMACY) {
                supervisionLogType = 2;
            } else {
                supervisionLogType = 1;
                createGoodsStockSupervisionReportLogForceControl(goodsTraceableCodeOpsHelper);
            }
        }

        //码上放心上报走log
    }

    /**
     * 重新发药，直接拿着之前的日志上报，锁的批次可能会发生变化
     */
    private void dealCreateGoodsStockSupervisionReportLogByPreSupervisionLog(TraceableCodeOpsHelper goodsTraceableCodeOpsHelper, List<GoodsStockLockingReportLog> reportLockingLogList) {
        if (CollectionUtils.isEmpty(reportLockingLogList)) {
            sLogger.info("没有上报信息 goodsId:{},{}", goodsRedisCache.getId(), GoodsUtils.goodsFullName(goodsRedisCache));
            return;
        }
        Map<String, List<GoodsStockLockingReportLog>> batIdToLockingList = reportLockingLogList.stream().collect(Collectors.groupingBy(GoodsStockLockingReportLog::getBatId));
        if (batIdToLockingList.isEmpty()) {
            sLogger.info("没有批次batIdToLockingList信息 goodsId:{},{}", goodsRedisCache.getId(), GoodsUtils.goodsFullName(goodsRedisCache));
            return;
        }
        List<GoodsStockLockingReportLog> firstList = null;
        for (GoodsStockLogMsg goodsStockLogMsg : goodsStockLogMsgList) {
            //相同发药的 x x x
            if (goodsStockLogMsg.getBatId() != null && batIdToLockingList.containsKey(goodsStockLogMsg.getBatId())) {
                firstList = batIdToLockingList.get(goodsStockLogMsg.getOriginBatId());
                break;
            }
            //相同退药的
            if (goodsStockLogMsg.getOriginBatId() != null && batIdToLockingList.containsKey(goodsStockLogMsg.getOriginBatId())) {
                firstList = batIdToLockingList.get(goodsStockLogMsg.getOriginBatId());
                break;
            }
        }

        if(firstList == null){
            firstList = batIdToLockingList.values().stream().findFirst().orElse(null);
        }
        if(firstList == null){
            sLogger.info("没有批次batIdToLockingList信息 goodsId:{},{}", goodsRedisCache.getId(), GoodsUtils.goodsFullName(goodsRedisCache));
            return;
        }
        for (GoodsStockLockingReportLog goodsStockLockingReportLog : firstList) {
            GoodsStockLogMsg chooseStockLog = null;
            for (GoodsStockLogMsg goodsStockLogMsg : goodsStockLogMsgList) {
                if (GoodsUtils.compareLongEqual(goodsStockLogMsg.getBatchId(), goodsStockLockingReportLog.getBatchId())) {
                    chooseStockLog = goodsStockLogMsg;
                }
            }
            if (chooseStockLog == null) {
                sLogger.info("未找到goodsStockLog 随机生成雪花ID {}", goodsDispenseDataItem);
            }
            long flag = goodsDispenseDataItem.getFlag();
            if (GoodsUtils.checkFlagOn(clientReq.getDispensingTag(), DispenseConst.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG)) {
                flag = GoodsUtils.onFlagLong(flag, LogFlag.SHE_BAO_PAY);
            }
            if (clinicConfig.isOpenSheBao()) {
                flag = GoodsUtils.onFlagLong(flag, LogFlag.SUPERVISON_LOG);
            }

            goodsStockLockingReportLogList.add(createReReportLogFromReportLog(flag,
                    chooseStockLog != null ? chooseStockLog.getSnowId() : AbcIdUtils.getUUIDLong(),
                    chooseStockLog != null ? chooseStockLog.getCreated() : Instant.now(),  // goodslog reportlog dwdsupervisionlog created same
                    batId,
                    goodsTraceableCodeOpsHelper.getShebaoDismountingFlag(),
                    goodsTraceableCodeOpsHelper.getCustomMaxHisPackageCount(),
                    goodsStockLockingReportLog));
        }
    }

    /**
     * 通过发药服务指定的追溯码来生成监管上报log
     */
    private void dealCreateGoodsStockSupervisionReportLogByDispenseServiceSetCode(TraceableCodeOpsHelper totalGoodsTraceableCodeOpsHelper) {
        BigDecimal pieceNum = BigDecimal.valueOf(goodsRedisCache.getPieceNum());
        boolean isLockingPieceChanged = false;
        // 基于锁库的批次摊
        boolean isForceLockBatch = false;
        List<FlatTraceCodeUtils.FlatTraceCodeCell> cells = new ArrayList<>();
        BigDecimal totalLockingPieceCountA = BigDecimal.ZERO;
        BigDecimal totalLockingPieceCountB = BigDecimal.ZERO;
        //GoodsStockCutmManager.loadGoodsStockImpl 加载出来的locking
        for (GoodsStockLocking goodsStockLocking : stockCutCountHelper.getStockLockingList()) {
            // releaseSwitchLock 把没锁到批次的排除掉，不要去摊
            if (goodsStockLocking.getStatus() == GoodsStockLocking.Status.DELETED) {
                continue;
            }
            if (MathUtils.wrapBigDecimalCompare(pieceNum, BigDecimal.valueOf(goodsStockLocking.getLockingPieceNum())) != 0) {
                isLockingPieceChanged = true;
            }
            totalLockingPieceCountA = MathUtils.wrapBigDecimalAdd(totalLockingPieceCountA, goodsStockLocking.getLockingTotalPieceCount());
            if (goodsStockLocking.getStatus() == GoodsStockLocking.Status.LOCKING_RELEASE) {
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_INDEX, "dealCreateGoodsStockSupervisionReportLogByDispenseServiceSetCode 有21状态的locking:{}", stockCutCountHelper.getStockLockingList());
            } else {
                totalLockingPieceCountB = MathUtils.wrapBigDecimalAdd(totalLockingPieceCountB, goodsStockLocking.getLockingTotalPieceCount());
            }
            if (!isForceLockBatch) {
                //结算强锁批次
                isForceLockBatch = goodsStockLocking.getOptType() == GoodsStockLocking.OptType.FORCE_BATCH || goodsStockLocking.getOptType() == GoodsStockLocking.OptType.FORCE_BATCH_FOR_SUPERVISION;
            }
            FlatTraceCodeUtils.FlatTraceCodeCell flatTraceCodeCell = new FlatTraceCodeUtils.FlatTraceCodeCell(goodsStockLocking.getId().toString(), goodsStockLocking.getLockingPieceCount(), goodsStockLocking.getLockingPackageCount(), pieceNum, null);
            cells.add(flatTraceCodeCell);
        }

        if (!isLockingPieceChanged) {
            // 【准】发药单上的总发药量 这个计算成小的数量可能有误差，但是始终以这个误差走没问题
            BigDecimal standardTotalRequestDispenseStockCount = GoodsStockUtils.toPieceUnit(
                    goodsDispenseDataItem.getTotalDispenseFormItemPieceCount(),
                    goodsDispenseDataItem.getTotalDispenseFormItemPackageCount(),
                    goodsRedisCache.getPieceNum());
            if (MathUtils.wrapBigDecimalCompare(totalLockingPieceCountA, standardTotalRequestDispenseStockCount) != 0) {
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_INDEX, "dealCreateGoodsStockSupervisionReportLogByDispenseServiceSetCode 非删除的锁库数量不一样:{}-{}:{}", totalLockingPieceCountA, standardTotalRequestDispenseStockCount, stockCutCountHelper.getStockLockingList());
            }
            if (MathUtils.wrapBigDecimalCompare(totalLockingPieceCountB, standardTotalRequestDispenseStockCount) != 0) {
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_INDEX, "dealCreateGoodsStockSupervisionReportLogByDispenseServiceSetCode 非删除和非21的锁库数量不一样:{}-{}:{}", totalLockingPieceCountB, standardTotalRequestDispenseStockCount, stockCutCountHelper.getStockLockingList());
            }
        } else {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_INDEX, "dealCreateGoodsStockSupervisionReportLogByDispenseServiceSetCode 锁库规格发生变化:{}:{}", pieceNum, stockCutCountHelper.getStockLockingList());
        }

        //摊的追溯码
        TraceableCodeOpsHelper helper = new TraceableCodeOpsHelper();
        helper.setTraceableCodeList(totalGoodsTraceableCodeOpsHelper.getTraceableCodeList());
        helper.setClientTraceableCodeList(new ArrayList<>());
        if (!CollectionUtils.isEmpty(totalGoodsTraceableCodeOpsHelper.getClientTraceableCodeList())) {
            for (TraceableCode traceableCode : totalGoodsTraceableCodeOpsHelper.getClientTraceableCodeList()) {
                TraceableCode rpcTraceableCode = new TraceableCode();
                rpcTraceableCode.setNo(traceableCode.getNo());
                rpcTraceableCode.setType(traceableCode.getType());
                rpcTraceableCode.setTrdnFlag(traceableCode.getTrdnFlag());
                rpcTraceableCode.setDismountingSn(traceableCode.getDismountingSn());
                rpcTraceableCode.setHisPieceCount(traceableCode.getHisPieceCount());
                rpcTraceableCode.setHisPackageCount(traceableCode.getHisPackageCount());
                helper.getClientTraceableCodeList().add(rpcTraceableCode);
            }
        }
        //拆零标识
        helper.setShebaoDismountingFlag(totalGoodsTraceableCodeOpsHelper.getShebaoDismountingFlag());
        helper.setCustomMaxHisPackageCount(totalGoodsTraceableCodeOpsHelper.getCustomMaxHisPackageCount());

        //摊
        FlatTraceCodeUtils.flat(clinicConfig, pieceNum.intValue(), isForceLockBatch, cells, helper);

        long flag = goodsDispenseDataItem.getFlag();
        if (GoodsUtils.checkFlagOn(clientReq.getDispensingTag(), DispenseConst.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG)) {
            flag = GoodsUtils.onFlagLong(flag, LogFlag.SHE_BAO_PAY);
        }
        if (clinicConfig.isOpenSheBao()) {
            flag = GoodsUtils.onFlagLong(flag, LogFlag.SUPERVISON_LOG);
        }
        //处理摊的结果，生成日志
        Map<String, FlatTraceCodeUtils.FlatTraceCodeCell> keyIdToCell = ListUtils.toMap(cells, FlatTraceCodeUtils.FlatTraceCodeCell::getKeyId);
        for (int i = 0; i < stockCutCountHelper.getStockLockingList().size(); i++) {
            GoodsStockLocking locking = stockCutCountHelper.getStockLockingList().get(i);
            // releaseSwitchLock 把没锁到批次的排除掉，不要去摊
            if (locking.getStatus() == GoodsStockLocking.Status.DELETED) {
                continue;
            }
            FlatTraceCodeUtils.FlatTraceCodeCell cell = keyIdToCell.get(locking.getId().toString());
            if (cell == null) {
                sLogger.error("没找到摊的结果:locking：{}", locking);
                continue;
            }
            GoodsStockLogMsg stockLogMsg = null;
            for (GoodsStockLogMsg it : goodsStockLogMsgList) {
                if (GoodsUtils.compareLongEqual(it.getBatchId(), locking.getBatchId())) {
                    stockLogMsg = it;
                    break;
                }
            }
            if (stockLogMsg == null) {
                sLogger.info("没找到对应进销存日志，理论上不会进来:{},goodsStockLogMsgList:{}", goodsDispenseDataItem, goodsStockLogMsgList);
            }
            goodsStockLockingReportLogList.add(createReportLogFromGoodsStockLocking(
                    locking,
                    GoodsStockLockingReportLog.Type.DISPENSE,
                    flag,
                    stockLogMsg != null ? stockLogMsg.getSnowId() : AbcIdUtils.getUUIDLong(),
                    stockLogMsg != null ? stockLogMsg.getCreated() : Instant.now(), // goodslog reportlog dwdsupervisionlog created same
                    createGoodsStockLogBatParameter.getStockLogBatid(),
                    null,
                    createGoodsStockLogBatParameter.getPatientOrderId(),
                    createGoodsStockLogBatParameter.getOrderId(),
                    createGoodsStockLogBatParameter.getOrderDetailId(),
                    helper.getCustomMaxHisPackageCount(),
                    cell));
        }

    }

    /**
     * 生成上报数据库记录
     *
     * @param locking        锁库记录
     * @param patientOrderId 单据id
     */
    public GoodsStockLockingReportLog createReportLogFromGoodsStockLocking(GoodsStockLocking locking,
                                                                           int type,
                                                                           long flag,
                                                                           Long snowId,
                                                                           Instant created,//尽量保证 reportlog goodslog superlog 三表created时间一样
                                                                           String batId,
                                                                           String originalBatId,
                                                                           String patientOrderId,
                                                                           String sheetId,
                                                                           String formItemId,
                                                                           BigDecimal customMaxHisPackageCount,
                                                                           FlatTraceCodeUtils.FlatTraceCodeCell cell) {
        GoodsStockLockingReportLog reportLog = new GoodsStockLockingReportLog();
        reportLog.setId(locking.getId());
        reportLog.setLockId(locking.getLockId());
        reportLog.setChainId(clinicConfig.getChainId());
        reportLog.setClinicId(clinicConfig.getClinicId());
        reportLog.setGoodsId(goodsRedisCache.getId());
        reportLog.setBatchId(locking.getBatchId());
        reportLog.setSnowId(snowId);
        reportLog.setType(type); //锁库
        reportLog.setFlag(flag);
        reportLog.setBatId(batId);
        reportLog.setOriginBatId(originalBatId);
        reportLog.setPatientOrderId(patientOrderId);
        reportLog.setDispenseSheetId(sheetId);
        reportLog.setDispenseFormItemId(formItemId);
        reportLog.setPieceCount(GoodsStockUtils.safeNeg(locking.getLockingPieceCount()));
        reportLog.setPackageCount(GoodsStockUtils.safeNeg(locking.getLockingPackageCount()));
        GoodsSnapV3 goodsSnapV3 = GoodsUtils.genGoodsDbSnap(goodsRedisCache);
        goodsSnapV3.setShebaoDismountingFlag(cell.getShebaoDismountingFlag());
        goodsSnapV3.setCustomMaxHisPackageCount(customMaxHisPackageCount);  //用上游留下来的快照
        // goodsSnapV3.setCustomMaxHisPackageCount(GoodsUtils.genGoodsDbSnap(goodsRedisCache));
        goodsSnapV3.setTraceableCodeList(cell.getTraceableCodeList());
        reportLog.setGoods(goodsSnapV3);
        UserFillUtils.fillCreatedBy(reportLog, createGoodsStockLogBatParameter.getEmployeeId());
        reportLog.setCreated(created);
        return reportLog;
    }


    /**
     * 通过锁库日志处理上报日志
     *
     * @return 商品库存日志消息体
     */
    public GoodsTraceCodeSupervisionOrderMsg.GoodsItemLog createGoodsStockReportLogsMessageLog() {
        if (supervisionLogType == null) {
            return null;
        }
        if (supervisionLogType == 1) {
            return createGoodsStockLogMsgBodyFromReportLog();
        } else if (supervisionLogType == 2) {
            //走log TODO 用本次的还是所有的helper
            return createGoodsStockLogMsgBodyFromStockLog(goodsTraceableCodeOpsHelper);
        }
        return null;
    }

    /**
     * 上报日志
     */
    private GoodsTraceCodeSupervisionOrderMsg.GoodsItemLog createGoodsStockLogMsgBodyFromReportLog() {
        if (CollectionUtils.isEmpty(goodsStockLockingReportLogList)) {
            return null;
        }
        GoodsTraceCodeSupervisionOrderMsg.GoodsItemLog msgBody = new GoodsTraceCodeSupervisionOrderMsg.GoodsItemLog();
        msgBody.setGoodsId(goodsRedisCache.getId());
        msgBody.setEmployeeId(createGoodsStockLogBatParameter.getEmployeeId());
        msgBody.setClinicId(createGoodsStockLogBatParameter.getClinicId());


        msgBody.setLogMsgList(new ArrayList<>());
        int size = goodsStockLockingReportLogList.size();
        for (int i = 0; i < goodsStockLockingReportLogList.size(); i++) {
            GoodsStockLockingReportLog log = goodsStockLockingReportLogList.get(i);
            GoodsBatch goodsBatch = goodsRedisCache.getGoodsStockSummary().getGoodsBatch(log.getBatchId(), goodsDispenseDataItem.getPharmacyNo());
            if (goodsBatch == null || CollectionUtils.isEmpty(goodsBatch.getGoodsStockList())) {
                sLogger.error(AbcLogMarker.MARKER_MESSAGE_INDEX, "createSupervisionGoodsStockLog log:{} 库存足换了批次 {}", log, goodsBatch);
                continue;
            }
            //find the last goods stockLog
            GoodsStockLogMsg chooseStockLog = null;
            for (GoodsStockLogMsg goodsStockLogMsg : goodsStockLogMsgList) {
                if (GoodsUtils.compareLongEqual(goodsStockLogMsg.getBatchId(), log.getBatchId())) {
                    chooseStockLog = goodsStockLogMsg;
                }
            }
            if (chooseStockLog == null) {
                sLogger.info("未找到goodsStockLog {}", goodsDispenseDataItem);
            }
            long flag = goodsDispenseDataItem.getFlag(); //本次发药flag
            flag = flag | log.getFlag(); //log上的flag
            flag = GoodsUtils.onFlagLong(flag, LogFlag.FORCE_CONTROL); //强控标记
            if (GoodsUtils.checkFlagOn(clientReq.getDispensingTag(), DispenseConst.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG)) {
                flag = GoodsUtils.onFlagLong(flag, LogFlag.SHE_BAO_PAY);
            }

            // 创建批次日志
            GoodsSupervisionLogMsg stockLogMsg = GoodsStockLogUtils.createSupervisionGoodsStockLog(
                    clinicConfig,
                    createGoodsStockLogBatParameter.getStockLogAction(),
                    goodsRedisCache.getPieceNum(),
                    goodsRedisCache.getPackagePrice(),
                    goodsRedisCache.getPiecePrice(),
                    log.getPatientOrderId(),
                    log.getDispenseSheetId(),
                    goodsDispenseDataItem.getDispenseItemId(),
                    goodsDispenseDataItem.getSourceSheetId(), //----sourceOrderId
                    goodsDispenseDataItem.getSourceItemId(), //----sourceOrderDetailId
                    goodsDispenseDataItem.getSourceRefundItemId(),
                    createGoodsStockLogBatParameter.getStockLogBatid(), // 首次发药的 batId
                    null, // 退药退的发药的 orginalBatId
                    // 成本价
                    goodsBatch.getPackageCostPrice(),

                    // 变动数量
                    GoodsStockUtils.safeNeg(log.getPieceCount()),
                    GoodsStockUtils.safeNeg(log.getPackageCount()),
                    chooseStockLog != null ? chooseStockLog.getBatchLeftPieceCount() : BigDecimal.ZERO,
                    chooseStockLog != null ? chooseStockLog.getBatchLeftPackageCount() : BigDecimal.ZERO,
                    //多次发药，这里不太准，上报要用，先写入一个
                    chooseStockLog != null ? chooseStockLog.getAfterGoodsPieceCount() : BigDecimal.ZERO,
                    chooseStockLog != null ? chooseStockLog.getAfterGoodsPackageCount() : BigDecimal.ZERO,
                    // 客户端请求数量
                    goodsDispenseDataItem.getTotalDispenseFormItemPieceCount(), // clientReqPieceCount
                    goodsDispenseDataItem.getTotalDispenseFormItemPackageCount(), // clientReqPackageCount
                    goodsDispenseDataItem.getTotalDispenseFormItemPrice(),
                    log.getLockId(),
                    chooseStockLog != null ? chooseStockLog.getSnowId() : AbcIdUtils.getUUIDLong(),
                    clientReq.getScene(),
                    flag, // 标记
                    clientReq.getChannelTransactionId(), //结算单ID
                    i, size,
                    supplierUtils,
                    clientReq.getPatientOrderNo(),
                    goodsRedisCache, // goodsRedisCache
                    JsonUtils.dump(log.getGoods()),
                    //时间和log对齐，方便监控数据
                    chooseStockLog != null ? chooseStockLog.getCreated() : Instant.now(), // goodslog reportlog dwdsupervisionlog make  created time same
                    goodsBatch
            );
            msgBody.getLogMsgList().add(stockLogMsg);
        }
        return msgBody;

    }

    /**
     * 从进销存log生成 追溯码监管Log
     */
    public GoodsTraceCodeSupervisionOrderMsg.GoodsItemLog createGoodsStockLogMsgBodyFromStockLog(TraceableCodeOpsHelper goodsTraceableCodeOpsHelper) {
        GoodsTraceCodeSupervisionOrderMsg.GoodsItemLog msgBody = new GoodsTraceCodeSupervisionOrderMsg.GoodsItemLog();
        msgBody.setGoodsId(goodsRedisCache.getId());
        msgBody.setEmployeeId(createGoodsStockLogBatParameter.getEmployeeId());
        msgBody.setClinicId(createGoodsStockLogBatParameter.getClinicId());


        msgBody.setLogMsgList(new ArrayList<>());
        //按batchId分组
        Map<Long, List<GoodsStockLogMsg>> batchIdToLogMsgList = goodsStockLogMsgList.stream().collect(Collectors.groupingBy(GoodsStockLogMsg::getBatchId));
        int size = batchIdToLogMsgList.size();
        AtomicInteger index = new AtomicInteger(0);
        for (Map.Entry<Long, List<GoodsStockLogMsg>> entry : batchIdToLogMsgList.entrySet()) {
            Long batchId = entry.getKey();
            List<GoodsStockLogMsg> stockLogList = entry.getValue();
            GoodsStockLogMsg log = stockLogList.get(stockLogList.size() - 1);
            GoodsBatch goodsBatch = goodsRedisCache.getGoodsStockSummary().getGoodsBatch(log.getBatchId(), goodsDispenseDataItem.getPharmacyNo());
            if (goodsBatch == null || CollectionUtils.isEmpty(goodsBatch.getGoodsStockList())) {
                sLogger.error(AbcLogMarker.MARKER_MESSAGE_INDEX, "createSupervisionGoodsStockLog log:{} 库存足换了批次 {}", log, goodsBatch);
                continue;
            }


            /*
             * 合并两条log得goods
             * */
            BigDecimal changePieceCount = BigDecimal.ZERO;
            BigDecimal changePackageCount = BigDecimal.ZERO;
            String goods = stockLogList.get(0).getGoods();
            if (stockLogList.size() == 1) {
                changePieceCount = GoodsStockUtils.safeNeg(stockLogList.get(0).getChangePieceCount());
                changePackageCount = GoodsStockUtils.safeNeg(stockLogList.get(0).getChangePackageCount());
            } else {
                GoodsSnapV3 goodsSnap = JsonUtils.readValue(goods, GoodsSnapV3.class);

                for (GoodsStockLogMsg goodsStockLogMsg : stockLogList) {
                    //累加变更
                    changePieceCount = MathUtils.wrapBigDecimalAdd(changePieceCount, goodsStockLogMsg.getChangePieceCount());
                    changePackageCount = MathUtils.wrapBigDecimalAdd(changePackageCount, goodsStockLogMsg.getChangePackageCount());
                    GoodsSnapV3 goodsSnapIn = JsonUtils.readValue(goods, GoodsSnapV3.class);
                    if (goodsSnap.getTraceableCodeList() == null) {
                        goodsSnap.setTraceableCodeList(new ArrayList<>());
                    }
                    if (goodsSnapIn != null && goodsSnapIn.getTraceableCodeList() != null) {
                        for (cn.abcyun.cis.goods.entity.TraceableCode traceableCode : goodsSnapIn.getTraceableCodeList()) {
                            goodsSnap.setShebaoDismountingFlag(goodsSnapIn.getShebaoDismountingFlag());
                            goodsSnap.setCustomMaxHisPackageCount(goodsSnapIn.getCustomMaxHisPackageCount());
                            cn.abcyun.cis.goods.entity.TraceableCode traceableCode1 = goodsSnap.getTraceableCodeList().stream().filter(it -> GoodsUtils.compareStrEqual(it.getNo(), traceableCode.getNo())).findFirst().orElse(null);
                            if (traceableCode1 == null) {
                                goodsSnap.getTraceableCodeList().add(traceableCode);
                            } else {
                                traceableCode.setHisPieceCount(MathUtils.wrapBigDecimalAdd(traceableCode.getHisPieceCount(), traceableCode1.getHisPieceCount()));
                                traceableCode.setHisPackageCount(MathUtils.wrapBigDecimalAdd(traceableCode.getHisPackageCount(), traceableCode1.getHisPackageCount()));
                            }
                        }
                    }
                }
                // TODO shebaoDismountingFlag,customMaxHisPackageCount

                goods = JsonUtils.dump(goodsSnap);
            }

            long flag = goodsDispenseDataItem.getFlag();
            flag = flag | log.getFlag();
            if (GoodsUtils.checkFlagOn(clientReq.getDispensingTag(), DispenseConst.DispensingTag.DISPENSING_TAG_SHEBAO_PAY_FLAG)) {
                flag = GoodsUtils.onFlagLong(flag, LogFlag.SHE_BAO_PAY);
            }


            // 创建批次日志
            GoodsSupervisionLogMsg stockLogMsg = GoodsStockLogUtils.createSupervisionGoodsStockLog(
                    clinicConfig,
                    createGoodsStockLogBatParameter.getStockLogAction(),
                    goodsRedisCache.getPieceNum(),
                    goodsRedisCache.getPackagePrice(),
                    goodsRedisCache.getPiecePrice(),
                    log.getPatientOrderId(),
                    log.getOrderId(),
                    goodsDispenseDataItem.getDispenseItemId(),
                    goodsDispenseDataItem.getSourceSheetId(), //----sourceOrderId
                    goodsDispenseDataItem.getSourceItemId(), //----sourceOrderDetailId
                    goodsDispenseDataItem.getSourceRefundItemId(),
                    createGoodsStockLogBatParameter.getStockLogBatid(), // 首次发药的 batId
                    null, // 退药退的发药的 orginalBatId
                    // 成本价
                    log.getPackageCostPrice(),

                    // 变动数量
                    GoodsStockUtils.safeNeg(changePieceCount),
                    GoodsStockUtils.safeNeg(changePackageCount),
                    log.getBatchLeftPieceCount(),
                    log.getBatchLeftPackageCount(),
                    log.getAfterGoodsPieceCount(),
                    log.getAfterGoodsPackageCount(),

                    // 客户端请求数量
                    goodsDispenseDataItem.getTotalDispenseFormItemPieceCount(), // clientReqPieceCount
                    goodsDispenseDataItem.getTotalDispenseFormItemPackageCount(), // clientReqPackageCount
                    goodsDispenseDataItem.getTotalDispenseFormItemPrice(),
                    log.getLockId(),
                    log.getSnowId(), //z
                    clientReq.getScene(),
                    flag, // TODO ...
                    clientReq.getChannelTransactionId(), //结算单ID
                    index.getAndIncrement(), size,
                    supplierUtils,
                    clientReq.getPatientOrderNo(),
                    goodsRedisCache, // goodsRedisCache
                    goods,
                    //时间和log对齐，方便监控数据
                    log.getCreated(), // goodslog reportlog dwdsupervisionlog created same
                    goodsBatch
            );
            msgBody.getLogMsgList().add(stockLogMsg);

        }
        return msgBody;
    }

    /**
     * 部分发药追溯码生成
     */
    public GoodsStockLockingReportLog createReReportLogFromReportLog(long flag,
                                                                     Long snowId,
                                                                     Instant created,
                                                                     String batId,
                                                                     Integer shebaoDismountingFlag,
                                                                     BigDecimal customMaxHisPackageCount, //当前这一次的
                                                                     GoodsStockLockingReportLog reportLog) {
        GoodsStockLockingReportLog partDispenseReportLog = JsonUtils.readValue(JsonUtils.dump(reportLog), GoodsStockLockingReportLog.class);
        partDispenseReportLog.setId(AbcIdUtils.getUUIDLong());
        partDispenseReportLog.setType(GoodsStockLockingReportLog.Type.DISPENSE);
        partDispenseReportLog.setOriginBatId(null);
        partDispenseReportLog.setBatId(batId);
        partDispenseReportLog.setSnowId(snowId);
        partDispenseReportLog.setFlag(flag);
        if (partDispenseReportLog.getGoods() != null) {
            partDispenseReportLog.getGoods().setShebaoDismountingFlag(shebaoDismountingFlag);
            partDispenseReportLog.getGoods().setCustomMaxHisPackageCount(customMaxHisPackageCount);
        }
        partDispenseReportLog.setPieceCount(GoodsStockUtils.safeNeg(partDispenseReportLog.getPieceCount()));
        partDispenseReportLog.setPackageCount(GoodsStockUtils.safeNeg(partDispenseReportLog.getPackageCount()));
        UserFillUtils.fillCreatedBy(partDispenseReportLog, createGoodsStockLogBatParameter.getEmployeeId());
        partDispenseReportLog.setCreated(created);
        return partDispenseReportLog;
    }

    @Override
    protected void writeLeftTraceableCodeToGoodsStockLogV2(List<GoodsStockLogMsg> goodsStockLogMsgList, TraceableCodeOpsHelper goodsTraceableCodeOpsHelper) {
        if (CollectionUtils.isEmpty(goodsStockLogMsgList) || goodsTraceableCodeOpsHelper == null
                || CollectionUtils.isEmpty(goodsTraceableCodeOpsHelper.getClientTraceableCodeList())) {
            super.writeLeftTraceableCodeToGoodsStockLogV2(goodsStockLogMsgList, goodsTraceableCodeOpsHelper);
            return;
        }

        boolean notForceLockBatch = true;
        if (stockCutCountHelper != null && !CollectionUtils.isEmpty(stockCutCountHelper.getStockLockingList())
                && stockCutCountHelper.getStockLockingList().stream().anyMatch(locking -> Objects.equals(locking.getOptType(), GoodsStockLocking.OptType.FORCE_BATCH) || Objects.equals(locking.getOptType(), GoodsStockLocking.OptType.FORCE_BATCH_FOR_SUPERVISION))) {
            notForceLockBatch = false;
        }

        List<GoodsDispenseDataItem.BatchCount> batchCountList = goodsDispenseDataItem.getBatchCountList();
        if (notForceLockBatch || CollectionUtils.isEmpty(batchCountList) || batchCountList.stream().allMatch(batchCount -> CollectionUtils.isEmpty(batchCount.getTotalTraceableCodeList()))) {
            // 没有指定批次发药，或者客户端没有指定批次的追溯码，则调用父类去分摊
            super.writeLeftTraceableCodeToGoodsStockLogV2(goodsStockLogMsgList, goodsTraceableCodeOpsHelper);
            return;
        }

        // 如果指定了批次，且每个批次都有追溯码，则直接写入
        Map<Long, List<GoodsStockLogMsg>> batchIdToStockLogMsgList = ListUtils.groupByKey(goodsStockLogMsgList, GoodsStockLogMsg::getBatchId);
        Map<Long, GoodsDispenseDataItem.BatchCount> batchIdToBatchCount = ListUtils.toMap(batchCountList, GoodsDispenseDataItem.BatchCount::getBatchId);

        // 如果存在同一个 batchId 多个 stockLog 的，则调用父类去处理
        if (batchIdToStockLogMsgList.values().stream().anyMatch(logMsgList -> logMsgList.size() > 1)) {
            sLogger.warn("批次发药时，存在同一个批次多个stockLog的情况，orderDetailId: {}", createGoodsStockLogBatParameter.getOrderDetailId());
            super.writeLeftTraceableCodeToGoodsStockLogV2(goodsStockLogMsgList, goodsTraceableCodeOpsHelper);
            return;
        }

        batchIdToStockLogMsgList.forEach((batchId, logMsgList) -> {
            GoodsDispenseDataItem.BatchCount batchCount = batchIdToBatchCount.get(batchId);
            if (batchCount == null || CollectionUtils.isEmpty(batchCount.getTotalTraceableCodeList())) {
                return;
            }
            GoodsStockLogMsg logMsg = logMsgList.get(0);

            if (StringUtils.isBlank(logMsg.getGoods())) {
                return;
            }
            GoodsSnapV3 goodsSnap = JsonUtils.readValue(logMsg.getGoods(), GoodsSnapV3.class);

            // 直接写入追溯码
            List<cn.abcyun.cis.goods.entity.TraceableCode> stockLogTraceableCodes = batchCount.getTotalTraceableCodeList().stream().map(traceCode -> {
                GoodsStockTraceableCode svrCode = goodsTraceableCodeOpsHelper.getTraceableCodeList().stream().filter(it -> GoodsUtils.compareStrEqual(it.getNo(), traceCode.getNo())).findFirst().orElse(null);
                return cn.abcyun.cis.goods.entity.TraceableCode.ofByMsgLog(traceCode, svrCode);
            }).collect(Collectors.toList());

            goodsSnap.setTraceableCodeList(stockLogTraceableCodes);

            if (stockLogTraceableCodes.stream().allMatch(code -> code.getTrdnFlag() != null)) {
                goodsSnap.setCollectTraceableCodeCount((int) stockLogTraceableCodes.stream().filter(code -> code.getTrdnFlag() == 0).count());
            }
            goodsSnap.setShebaoDismountingFlag(goodsTraceableCodeOpsHelper.getShebaoDismountingFlag());
            goodsSnap.setCustomMaxHisPackageCount(goodsTraceableCodeOpsHelper.getCustomMaxHisPackageCount());

            logMsg.setGoods(JsonUtils.dump(goodsSnap));
        });

    }

    /**
     * 这个函数的功能的作用仅仅只是把无batchId的locking设置上批次
     * 不管锁库库存量，锁库库存量后面函数调整
     */
    protected void fixNonLockBatchIdLocking() {
        if (stockCutCountHelper == null || CollectionUtils.isEmpty(stockCutCountHelper.getStockLockingList())) {
            return;
        }

        /*
         *stockCutCountHelper.getStockLockingList()： 锁是全的
         *  goodsStockLogMsgList: 部分发
         * */
        int nonLockingBatchLocking = 0;
        for (GoodsStockLocking goodsStockLocking : stockCutCountHelper.getStockLockingList()) {
            if (goodsStockLocking.getBatchId() == null) {
                nonLockingBatchLocking++;
            }
        }

        if (nonLockingBatchLocking == 0) {
            return;
        }


        Map<Long, List<GoodsStockLogMsg>> batchIdToLogMsgList = goodsStockLogMsgList.stream().collect(Collectors.groupingBy(GoodsStockLogMsg::getBatchId));
        Map<Long, BigDecimal> batchIdToThisStockCutLeftTotalPieceCount = new HashMap<>(); //这一次进销存操作批次扣的库存量 (部分发)
        for (Map.Entry<Long, List<GoodsStockLogMsg>> e : batchIdToLogMsgList.entrySet()) {
            Long key = e.getKey();
            List<GoodsStockLogMsg> logList = e.getValue();
            batchIdToThisStockCutLeftTotalPieceCount.put(key, logList.stream().reduce(BigDecimal.ZERO,
                    (total, item) -> {
                        BigDecimal stdCount = GoodsStockUtils.toPieceUnit(item.getChangePieceCount(), item.getChangePackageCount(), item.getPieceNum()).abs();
                        return total.add(stdCount);
                    }, BigDecimal::add));
        }
        GoodsStockLocking noBatchIdLocking = null; //没有批次ID
        GoodsStockLocking mergedToBatchIdLocking = null;
        for (GoodsStockLocking goodsStockLocking : stockCutCountHelper.getStockLockingList()) {
            if (goodsStockLocking.getBatchId() == null) { //锁数量的跳过
                noBatchIdLocking = goodsStockLocking;
                continue;
            }

            //待分配的锁库库存量
            BigDecimal leftCnt = batchIdToThisStockCutLeftTotalPieceCount.get(goodsStockLocking.getBatchId()); //实际的批次不在锁的批次里面
            if (leftCnt == null) {
                continue;
            }
            //本locking能贡献部分锁数量后，生效的锁库数量
            leftCnt = leftCnt.subtract(leftCnt.min(goodsStockLocking.getLockLeftTotalPieceCount()));
            batchIdToThisStockCutLeftTotalPieceCount.put(goodsStockLocking.getBatchId(), leftCnt);
            if (leftCnt.compareTo(BigDecimal.ZERO) <= 0) {
                batchIdToThisStockCutLeftTotalPieceCount.remove(goodsStockLocking.getBatchId());
            }
        }


        /*
         * 进销存日志的批次ID没在锁库记录里面
         * */
        for (Map.Entry<Long, BigDecimal> entry : batchIdToThisStockCutLeftTotalPieceCount.entrySet()) {
            Long batchId = entry.getKey();
            if (stockCutCountHelper.getStockLockingList().stream().noneMatch(it -> GoodsUtils.compareLongEqual(it.getBatchId(), batchId))) {
                //批次ID变了，这条锁库记录的数量可能也要变
                noBatchIdLocking.setBatchId(batchId);
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_INDEX, "fixNonLockBatchIdLocking 存在没有锁批次的锁库记录，给这个锁库记录设置上批次ID 这里只换批次ID不换数量:{},{},{}", getGoodsId(), GoodsUtils.goodsFullName(goodsRedisCache), noBatchIdLocking);
                break;
            }

        }
        //锁数量的这条的批次已经在其他锁库里面锁过了
        if (noBatchIdLocking.getBatchId() == null) {
            for (Map.Entry<Long, BigDecimal> entry : batchIdToThisStockCutLeftTotalPieceCount.entrySet()) {
                Long batchId = entry.getKey();
                mergedToBatchIdLocking = stockCutCountHelper.getStockLockingList().stream().filter(it -> GoodsUtils.compareLongEqual(it.getBatchId(), batchId)).findFirst().orElse(null);
                break;
            }
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_INDEX, "fixNonLockBatchIdLocking 存在没有锁批次的锁库记录，存在锁批次的锁已经有locking了 合并locking:{},{},{} 合并到 {}", getGoodsId(), GoodsUtils.goodsFullName(goodsRedisCache), noBatchIdLocking, mergedToBatchIdLocking);
            if (mergedToBatchIdLocking != null) {
                mergedToBatchIdLocking.setLockLeftTotalPieceCount(mergedToBatchIdLocking.getLockLeftTotalPieceCount().add(noBatchIdLocking.getLockLeftTotalPieceCount()));
                mergedToBatchIdLocking.setLockingTotalPieceCount(mergedToBatchIdLocking.getLockingTotalPieceCount().add(noBatchIdLocking.getLockingTotalPieceCount()));
                mergedToBatchIdLocking.setLockingPieceCount(mergedToBatchIdLocking.getLockingPieceCount().add(noBatchIdLocking.getLockingPieceCount()));
                mergedToBatchIdLocking.setLockingPackageCount(mergedToBatchIdLocking.getLockingPackageCount().add(noBatchIdLocking.getLockingPackageCount()));
                noBatchIdLocking.setLockLeftTotalPieceCount(BigDecimal.ZERO);
                noBatchIdLocking.setLockingTotalPieceCount(BigDecimal.ZERO);
                noBatchIdLocking.setStatus(GoodsStockLocking.Status.DELETED);
                noBatchIdLocking.setLastModified(Instant.now());
            }
        }

    }

    /**
     * 如果锁可以换，那么就需要释放锁
     */
    protected void releaseSwitchLock() {
        if (stockCutCountHelper == null
                || stockCutCountHelper.getLockId() == null
                || CollectionUtils.isEmpty(stockCutCountHelper.getStockLockingList()) //所有的锁 ，里面可能存在一条lockId= null的记录
                || stockCutCountHelper.getForceLockBatch() == YesOrNo.YES) {
            return;
        }
        /*
         * 根据log调整locking
         * 每个批次的logList
         * */
        Map<Long, List<GoodsStockLogMsg>> batchIdToLogMsgList = goodsStockLogMsgList.stream().collect(Collectors.groupingBy(GoodsStockLogMsg::getBatchId));
        Map<Long, Tuple4<BigDecimal, BigDecimal, GoodsStockMountTrible, BigDecimal>> batchIdToThisStockCutLeftTotalPieceCount = new HashMap<>(); //这一次进销存操作批次扣的库存量 (部分发)
        for (Map.Entry<Long, List<GoodsStockLogMsg>> e : batchIdToLogMsgList.entrySet()) {
            Long key = e.getKey();
            List<GoodsStockLogMsg> logList = e.getValue();
            BigDecimal pieceCount = BigDecimal.ZERO;
            BigDecimal packageCount = BigDecimal.ZERO;
            BigDecimal pacakgeCostPrice = null;
            for (GoodsStockLogMsg goodsStockLogMsg : logList) {
                pieceCount = MathUtils.wrapBigDecimalAdd(pieceCount, goodsStockLogMsg.getChangePieceCount().abs());
                packageCount = MathUtils.wrapBigDecimalAdd(packageCount, goodsStockLogMsg.getChangePackageCount().abs());
                pacakgeCostPrice = goodsStockLogMsg.getPackageCostPrice();
            }
            GoodsStockMountTrible stdAbsCount = GoodsStockUtils.toStandardStockCount(pieceCount, packageCount, getGoodsRedisCache().getPieceNum(), getGoodsRedisCache().getTypeId());
            batchIdToThisStockCutLeftTotalPieceCount.put(key, Tuples.of(pieceCount, packageCount, stdAbsCount, pacakgeCostPrice));
        }

        Set<Long> hasGoodsStockLockingBatchIdSet = new HashSet<>();
        GoodsStockLocking copyFromStockLocking = null;
        if (patchReleaseLockingList == null) {
            patchReleaseLockingList = new ArrayList<>();
            patchReleaseLockingLogList = new ArrayList<>();
        }
        List<GoodsStockLocking> newLockingList = new ArrayList<>();
        //一次发完，要对齐锁库和进销存log
        if ((goodsDispenseDataItem.getIsTheFirstTimeDispensing() == YesOrNo.YES
                || goodsDispenseDataItem.getIsTheFirstTimeReDispensing() == YesOrNo.YES)
                && GoodsUtils.newVersionSuperVerionReport(clinicConfig)
                && goodsDispenseDataItem.getIsPartDispensed() == YesOrNo.NO
        ) {
            for (Map.Entry<Long, Tuple4<BigDecimal, BigDecimal, GoodsStockMountTrible, BigDecimal>> entry : batchIdToThisStockCutLeftTotalPieceCount.entrySet()) {
                Long batchId = entry.getKey();
                GoodsBatchOpsBase found = null;
                for (GoodsBatchOpsBase it : goodsBatchStockOpsBases) {
                    if (it.getGoodsBatch().getBatchId().compareTo(batchId) == 0) {
                        found = it;
                        break;
                    }
                }
                GoodsBatch batch = found.getGoodsBatch();
                if (batch == null) {
                    sLogger.info("releaseSwitchLock 未找到批次信息 {}", batchId);
                    continue;
                }
                Tuple4<BigDecimal, BigDecimal, GoodsStockMountTrible, BigDecimal> stockPair = entry.getValue();
                GoodsStockMountTrible stdAbsCount = stockPair.getT3();
                GoodsStockLocking locking = stockCutCountHelper.getStockLockingList().stream().filter(it -> GoodsUtils.compareLongEqual(it.getBatchId(), batchId)).findFirst().orElse(null);
                if (locking == null) {
                    locking = new GoodsStockLocking();
                    locking.setId(AbcIdUtils.getUUIDLong());
                    locking.setLockId(stockCutCountHelper.getLockId());
                    locking.setLockingOrderId(clientReq.getPatientOrderId());
                    locking.setChainId(clinicConfig.getChainId());
                    locking.setClinicId(clinicConfig.getClinicId());
                    locking.setGoodsId(goodsDispenseDataItem.getGoodsId());
                    locking.setBatchId(batchId);
                    locking.setPharmacyNo(goodsDispenseDataItem.getPharmacyNo());
                    locking.setPharmacyType(getGoodsPharmacy().getType());
                    locking.setStatus(GoodsStockLocking.Status.LOCKING_USED);
                    locking.setLockingPieceNum(getPieceNum());
                    locking.setPriceType(getPriceType());
                    locking.setPackageCostPrice(stockPair.getT4());
                    locking.setOptType(stockCutCountHelper.getStockLockingList().get(0).getOptType());
                    locking.setType(stockCutCountHelper.getStockLockingList().get(0).getType());
                    UserFillUtils.fillCreatedBy(locking, clientReq.getOperatorId());
                    newLockingList.add(locking);
                    batch.getBatchLockHelper().getLockingList().add(locking);
                    patchReleaseLockingList.add(locking);
                }
                locking.setStatus(GoodsStockLocking.Status.LOCKING_USED);
                //没变化直接走
                if (MathUtils.wrapBigDecimalCompare(locking.getLockingTotalPieceCount(), stockPair.getT3().getOverallPieceCount()) == 0) {
                    continue;
                }
                if (goodsDispenseDataItem.getCountUnitFlag() == CountUnitFlag.BIG) {
                    locking.setLockingPackageCount(stdAbsCount.getOverallPackageCount());
                    locking.setLockingPieceCount(BigDecimal.ZERO);
                    locking.setLockingTotalPieceCount(stdAbsCount.getOverallPieceCount());
                    locking.setLockLeftTotalPieceCount(BigDecimal.ZERO);
                } else {
                    locking.setLockingPackageCount(BigDecimal.ZERO);
                    locking.setLockingPieceCount(stdAbsCount.getOverallPieceCount());
                    locking.setLockingTotalPieceCount(stdAbsCount.getOverallPieceCount());
                    locking.setLockLeftTotalPieceCount(BigDecimal.ZERO);
                }
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_INDEX, "releaseSwitchLock 首次发并发完：有差异 修正GoodsStockLocking GoodsStockLockingGoodsId:{},{} goodsStockLocking:{}", getGoodsId(), GoodsUtils.goodsFullName(goodsRedisCache), locking);
            }


            //删不在进销存log的locking
            for (GoodsStockLocking goodsStockLocking : stockCutCountHelper.getStockLockingList()) {
                if (goodsStockLocking.getBatchId() == null || !batchIdToThisStockCutLeftTotalPieceCount.containsKey(goodsStockLocking.getBatchId())) {
                    goodsStockLocking.setStatus(GoodsStockLocking.Status.DELETED);
                    UserFillUtils.fillLastModifiedBy(goodsStockLocking, clientReq.getOperatorId());
                    sLogger.info(AbcLogMarker.MARKER_MESSAGE_INDEX, "首次发并发完：删除 GoodsStockLockinggoodsId:{},{} goodsStockLocking:{}", getGoodsId(), GoodsUtils.goodsFullName(goodsRedisCache), goodsStockLocking);
                }
            }
            if (!CollectionUtils.isEmpty(newLockingList)) {
                sLogger.info("首次发并发完：releaseSwitchLock 新增锁：{}", newLockingList);
                goodsStockLockingRepository.saveAll(newLockingList);
                stockCutCountHelper.getStockLockingList().addAll(newLockingList);
            }
            return;
        }


        /*
         * 其他场景
         * */
        BigDecimal changeLockingPieceCount = GoodsStockUtils.subtract(this.goodsOpsResult.getAddPieceCountSucc(), this.goodsOpsResult.getChangedLockingPieceCount());
        if (changeLockingPieceCount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        //需要监管上报的锁
        boolean needSupLockingStock = stockCutCountHelper.getStockLockingList().stream().anyMatch(it -> GoodsStockLocking.canNotChangeLockingBatchOnPartDispense(it.getOptType()));

        // 不需要监管上报 或者是部分发药，锁不能动 这些情况下直接用老的逻辑，释放goodslocking上的库存
        // 部分发药，第一次进到老逻辑只是释放库存 最后一次发药会直接改库存量 有不有问题
        if (!needSupLockingStock || goodsDispenseDataItem.getIsPartDispensed() == YesOrNo.YES) {
            super.releaseSwitchLock();
            return;
        }

        // [全发]走到下面就是要处理locking，因为lockingLog药生成来上报

        sLogger.info("releaseSwitchLock 发药 是否需要将官发药:{},发药请求是否部分发药:{}", needSupLockingStock, goodsDispenseDataItem);
        // ----------------------
        // 历史锁库信息处理
        // 1.同步成进销存log得库存量
        // 2.同步删除没库存量的药品
        // ----------------------
        for (GoodsStockLocking locking : stockCutCountHelper.getStockLockingList()) {
            if (locking.getBatchId() == null) {
                //fixNonLockBatchIdLocking 已经处理了，理论上进不来
                locking.setStatus(GoodsStockLocking.Status.DELETED); //锁数量没锁到批次的locking 标识删除
                patchReleaseLockingList.add(locking);
                if (copyFromStockLocking == null) {
                    copyFromStockLocking = locking;
                }
                sLogger.info("releaseSwitchLock 没锁到批次直接释放：{}", locking);
                continue;
            }

            //没有进销存日志，但是之前的锁库锁到这个批次上了
            List<GoodsStockLogMsg> logMsgList = batchIdToLogMsgList.get(locking.getBatchId());
            if (CollectionUtils.isEmpty(logMsgList)) {
                patchReleaseLockingList.add(locking);
                locking.setLockLeftTotalPieceCount(BigDecimal.ZERO);
                locking.setLockingTotalPieceCount(BigDecimal.ZERO);
                locking.setStatus(GoodsStockLocking.Status.DELETED);
                locking.setLastModified(Instant.now());
                sLogger.info("发药releaseSwitchLock 锁的批次实际扣库没扣:{}", locking);
                continue;
            }

            copyFromStockLocking = locking;
            // 计算该批次实际扣库的总数量
            BigDecimal actualCutPieceCount = logMsgList.stream()
                    .map(log -> log.getChangePieceCount().add(log.getChangePackageCount().multiply(BigDecimal.valueOf(log.getPieceNum()))).abs())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            GoodsStockMountTrible stdDispenseAbsCount = GoodsStockUtils.toStandardStockCount(actualCutPieceCount, BigDecimal.ZERO, getGoodsRedisCache().getPieceNum(), getGoodsRedisCache().getTypeId());

            /*
             * 如果进销存log扣的库存量和锁的库存量是不一样多的
             * 需要把locking的库存量调整成进销存log得库存量
             * */
            BigDecimal changedLockingPieceCount = actualCutPieceCount.subtract(locking.getLockingTotalPieceCount());
            if (changedLockingPieceCount.compareTo(BigDecimal.ZERO) != 0) {
                hasGoodsStockLockingBatchIdSet.add(locking.getBatchId());
                if (goodsDispenseDataItem.getCountUnitFlag() == CountUnitFlag.BIG) {
                    locking.setLockingPackageCount(stdDispenseAbsCount.getOverallPackageCount());
                    locking.setLockingPieceCount(BigDecimal.ZERO);
                    locking.setLockingTotalPieceCount(stdDispenseAbsCount.getOverallPieceCount());
                    locking.setLockLeftTotalPieceCount(BigDecimal.ZERO);
                } else {
                    locking.setLockingPackageCount(BigDecimal.ZERO);
                    locking.setLockingPieceCount(stdDispenseAbsCount.getOverallPieceCount());
                    locking.setLockingTotalPieceCount(stdDispenseAbsCount.getOverallPieceCount());
                    locking.setLockLeftTotalPieceCount(BigDecimal.ZERO);
                }
                patchReleaseLockingList.add(locking);
                sLogger.info("releaseSwitchLock 需要带走锁库库存 changedLockingPieceCount:{}goodsId:{},goodsOpsResult:{},locking:{}", changedLockingPieceCount, getGoodsId(), goodsOpsResult);
            } else {
                hasGoodsStockLockingBatchIdSet.add(locking.getBatchId()); //这个批次 不能生成
            }
        }

        // ----------------------
        //补锁库信息
        // ----------------------
        for (Map.Entry<Long, List<GoodsStockLogMsg>> entry : batchIdToLogMsgList.entrySet()) {
            Long batchId = entry.getKey();
            List<GoodsStockLogMsg> logMsgList = entry.getValue();
            if (hasGoodsStockLockingBatchIdSet.contains(batchId)) {
                sLogger.info("releaseSwitchLock 批次信息 已经处理:{}", batchId);
                continue;
            }
            GoodsBatchOpsBase found = null;
            for (GoodsBatchOpsBase it : goodsBatchStockOpsBases) {
                if (it.getGoodsBatch().getBatchId().compareTo(batchId) == 0) {
                    found = it;
                    break;
                }
            }
            GoodsBatch batch = found.getGoodsBatch();
            if (batch == null) {
                sLogger.info("releaseSwitchLock 未找到批次信息 {}", batchId);
                continue;
            }
            // 计算该批次实际扣库的总数量
            BigDecimal actualCutPieceCount = logMsgList.stream()
                    .map(log -> log.getChangePieceCount().add(log.getChangePackageCount().multiply(BigDecimal.valueOf(log.getPieceNum()))).abs())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            GoodsStockMountTrible stdAbsCount = GoodsStockUtils.toStandardStockCount(actualCutPieceCount, BigDecimal.ZERO, getGoodsRedisCache().getPieceNum(), getGoodsRedisCache().getTypeId());

            GoodsStockLocking locking = new GoodsStockLocking();
            locking.setId(AbcIdUtils.getUUIDLong());
            locking.setLockId(stockCutCountHelper.getLockId());
            if (goodsDispenseDataItem.getCountUnitFlag() == CountUnitFlag.BIG) {
                locking.setLockingPackageCount(stdAbsCount.getOverallPackageCount());
                locking.setLockingPieceCount(BigDecimal.ZERO);
                locking.setLockingTotalPieceCount(stdAbsCount.getOverallPieceCount());
                locking.setLockLeftTotalPieceCount(BigDecimal.ZERO);
            } else {
                locking.setLockingPackageCount(BigDecimal.ZERO);
                locking.setLockingPieceCount(stdAbsCount.getOverallPieceCount());
                locking.setLockingTotalPieceCount(stdAbsCount.getOverallPieceCount());
                locking.setLockLeftTotalPieceCount(BigDecimal.ZERO);
            }
            locking.setLockingOrderId(clientReq.getPatientOrderId());
            locking.setChainId(clinicConfig.getChainId());
            locking.setClinicId(clinicConfig.getClinicId());
            locking.setGoodsId(goodsDispenseDataItem.getGoodsId());
            locking.setBatchId(batchId);
            locking.setPharmacyNo(goodsDispenseDataItem.getPharmacyNo());
            locking.setPharmacyType(getGoodsPharmacy().getType());
            locking.setStatus(GoodsStockLocking.Status.LOCKING_USED);
            locking.setLockingPieceNum(getPieceNum());
            locking.setPriceType(getPriceType());
            if (copyFromStockLocking != null) {
                locking.setOptType(copyFromStockLocking.getOptType());
                locking.setType(copyFromStockLocking.getType());
                locking.setPackageCostPrice(logMsgList.get(0).getPackageCostPrice());
            }
            UserFillUtils.fillCreatedBy(locking, clientReq.getOperatorId());
            newLockingList.add(locking);
            batch.getBatchLockHelper().getLockingList().add(locking);
            patchReleaseLockingList.add(locking);
        }
        if (!CollectionUtils.isEmpty(newLockingList)) {
            sLogger.info("releaseSwitchLock 新增锁：{}", newLockingList);
            goodsStockLockingRepository.saveAll(newLockingList);
            stockCutCountHelper.getStockLockingList().addAll(newLockingList);
        }
    }

}