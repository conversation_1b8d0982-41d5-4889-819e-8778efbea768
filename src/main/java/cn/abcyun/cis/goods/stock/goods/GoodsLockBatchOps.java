package cn.abcyun.cis.goods.stock.goods;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.ScGoodsGoodsLockReq;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockBatchItem;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.goods.consts.CountUnitFlag;
import cn.abcyun.cis.goods.domain.GoodsStockMountTrible;
import cn.abcyun.cis.goods.model.GoodsBatch;
import cn.abcyun.cis.goods.model.GoodsStock;
import cn.abcyun.cis.goods.model.GoodsStockLocking;
import cn.abcyun.cis.goods.stock.stockmanager.GoodsStockCutManager;
import cn.abcyun.cis.goods.utils.AbcIdUtils;
import cn.abcyun.cis.goods.utils.GoodsStockUtils;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.utils.UserFillUtils;
import cn.abcyun.cis.goods.vo.frontend.lock.GoodsLockResultItem;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

/*****************************************************************************************
 * ｜        锁类型     ｜    上锁阶段           ｜     锁释放阶段                            ｜
 * ｜ 强制批次锁         ｜ ✅ 尽量不要换         ｜❌ 在现有基础上换锁/发药释放锁                ｜
 * 1.强制锁批次
 * 2.还可以换锁阶段
 *****************************************************************************************/
public class GoodsLockBatchOps extends GoodsLockOpsBase {
    protected int getOptType() {
        return GoodsStockLocking.OptType.FORCE_BATCH;
    }

    protected int callGrandparentInitBeforeLocking() {
        return super.initBeforeLocking();
    }

    @Override
    protected int initBeforeLocking() {
        int ret = callGrandparentInitBeforeLocking();
        if (ret != GoodsLockResultItem.SUCCESS) {
            return ret;
        }
        /*
         * 存在这样的诊所 开单先买出去，收费，后面才入库
         *
         * 如果锁库请求里面传了强制锁库 forceLock == 1  不检查是否入库 ，只在锁数量的情况下有效
         * 锁批次/或指定批次锁 没入库，批次都没有一定锁不成功
         * */
        if (scGoodsLockingGoodsLockReqItem.getOpType() != ScGoodsGoodsLockReq.DEL
                && (pharmacyGoodsStat == null || pharmacyGoodsStat.getEsInorder() == YesOrNo.NO)) {
            sLogger.error("锁库错误:指定批次锁库{}未入库过", scGoodsLockingGoodsLockReqItem);
            return GoodsLockResultItem.NO_STOCK_IN;
        }
        BigDecimal lockingPackageCount = BigDecimal.ZERO;
        BigDecimal lockingPieceCount = BigDecimal.ZERO;
        BigDecimal lockingTotalPieceCount = BigDecimal.ZERO;
        BigDecimal lockingLeftTotalPieceCount = BigDecimal.ZERO;

        /*
         * 所有发药 部分退费 释放的锁库批次
         * */
        for (GoodsStockLocking locking : goodsStockOpsHelper.getGoodsStockSummary().getGoodsLockingList()) {
            if (!GoodsUtils.compareLongEqual(locking.getLockId(), clientReqLockId)) {
                continue;
            }
            batchIdToGoodsStockLocking.put(locking.getBatchId(), locking);
            lockingTotalPieceCount = GoodsStockUtils.add(lockingTotalPieceCount, locking.getLockingTotalPieceCount());
            lockingPieceCount = GoodsStockUtils.add(lockingPieceCount, locking.getLockingPieceCount());
            lockingPackageCount = GoodsStockUtils.add(lockingPackageCount, locking.getLockingPackageCount());

            lockingLeftTotalPieceCount = GoodsStockUtils.add(lockingLeftTotalPieceCount, locking.getLockLeftTotalPieceCount());
        }
        /*
         * 客户端指定锁ID上来锁库或解锁
         * 2024.03 一个锁的lockId被门诊，收费，发药持有，不能轻易替换，
         * 所以会出现替换锁库的情况，要保持lockId不变
         * */
        if (clientReqLockId != null) {
            /*
             * 指定批次锁，每个批次要指定总的小包装数量
             * 否则 用户指定0.33盒 到底是0.99片 1片 1.01片呢
             * */
            if (scGoodsLockingGoodsLockReqItem.getLockUnitFlag() == CountUnitFlag.BIG) {
                if (!CollectionUtils.isEmpty(scGoodsLockingGoodsLockReqItem.getGoodsLockBatchItemList())) {
                    for (GoodsLockBatchItem batchItem : scGoodsLockingGoodsLockReqItem.getGoodsLockBatchItemList()) {
                        GoodsStockLocking locking = batchIdToGoodsStockLocking.get(batchItem.getBatchId());
                        /*
                         * 如果数量和前面锁的一样，修正一把
                         * */
                        if (locking != null &&
                                GoodsStockUtils.bigDecimalCompareSame(locking.getLockingPackageCount(), batchItem.getLockingBatchPackageCount())
                                && GoodsStockUtils.bigDecimalCompareSame(locking.getLockingPieceCount(), batchItem.getLockingBatchPieceCount())
                        ) {
                            batchItem.setLockingTotalPieceCount(locking.getLockingTotalPieceCount());
                        }
                        /*
                         * 修改锁数量变了，没传就要抛异常
                         * */
                        if (batchItem.getLockingTotalPieceCount() == null) {
                            sLogger.error("指定批次锁，需要执行锁的小的总的数量:{}", scGoodsLockingGoodsLockReqItem);
                            return GoodsLockResultItem.NEED_SPECIFY_LOCKING_TOTAL_PIECE_COUNT;
                        }
                    }
                }
            } else {
                if (!CollectionUtils.isEmpty(scGoodsLockingGoodsLockReqItem.getGoodsLockBatchItemList())) {
                    for (GoodsLockBatchItem batchItem : scGoodsLockingGoodsLockReqItem.getGoodsLockBatchItemList()) {
                        //库存模块存在同时指定大 指定小的情况 开单模块只能药么大药么小
                        if (scGoodsLockingGoodsLockReqItem.getLockUnitFlag() == CountUnitFlag.MIXED) {
                            GoodsStockMountTrible stdCnt = GoodsStockUtils.toStandardStockCount(
                                    batchItem.getLockingBatchPieceCount(),
                                    batchItem.getLockingBatchPackageCount(),
                                    goodsStockOpsHelper.getGoodsStockSummary().getPieceNum(),
                                    goodsStockOpsHelper.getGoodsStockSummary().getType(),
                                    goodsStockOpsHelper.getGoodsStockSummary().getSubType());
                            batchItem.setLockingTotalPieceCount(stdCnt.getOverallPieceCount());
                        } else if (scGoodsLockingGoodsLockReqItem.getLockUnitFlag() == CountUnitFlag.SMALL) {
                            batchItem.setLockingTotalPieceCount(batchItem.getLockingBatchPieceCount());
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(svrExistGoodsStockLockingList)) {
                return GoodsLockResultItem.SUCCESS;
            }
            //删除 锁库
            if (scGoodsLockingGoodsLockReqItem.getOpType() == ScGoodsGoodsLockReq.DEL) {
                sLogger.error("Goods删除锁库请求lockId{},历史锁库记录:{}", clientReqLockId, svrExistGoodsStockLockingList);
                return GoodsLockResultItem.SUCCESS;
            }
        } else {
            /*
             * 首次上锁
             * 指定批次锁，每个批次要指定总的小包装数量
             * 否则 用户指定0.33盒 到底是0.99片 1片 1.01片呢
             * */
            if (scGoodsLockingGoodsLockReqItem.getLockUnitFlag() == CountUnitFlag.BIG) {
                if (!CollectionUtils.isEmpty(scGoodsLockingGoodsLockReqItem.getGoodsLockBatchItemList())) {
                    if (scGoodsLockingGoodsLockReqItem.getGoodsLockBatchItemList().stream().anyMatch(it -> it.getLockingTotalPieceCount() == null)) {
                        sLogger.error("指定批次锁，需要指定锁的小的总的数量:{}", scGoodsLockingGoodsLockReqItem);
                        return GoodsLockResultItem.NEED_SPECIFY_LOCKING_TOTAL_PIECE_COUNT;
                    }
                }
            } else {
                if (!CollectionUtils.isEmpty(scGoodsLockingGoodsLockReqItem.getGoodsLockBatchItemList())) {
                    for (GoodsLockBatchItem batchItem : scGoodsLockingGoodsLockReqItem.getGoodsLockBatchItemList()) {
                        //库存模块存在同时指定大 指定小的情况 开单模块只能药么大药么小
                        if (scGoodsLockingGoodsLockReqItem.getLockUnitFlag() == CountUnitFlag.MIXED) {
                            GoodsStockMountTrible stdCnt = GoodsStockUtils.toStandardStockCount(
                                    batchItem.getLockingBatchPieceCount(),
                                    batchItem.getLockingBatchPackageCount(),
                                    goodsStockOpsHelper.getGoodsStockSummary().getPieceNum(),
                                    goodsStockOpsHelper.getGoodsStockSummary().getType(),
                                    goodsStockOpsHelper.getGoodsStockSummary().getSubType());
                            batchItem.setLockingTotalPieceCount(stdCnt.getOverallPieceCount());
                        } else if (scGoodsLockingGoodsLockReqItem.getLockUnitFlag() == CountUnitFlag.SMALL) {
                            batchItem.setLockingTotalPieceCount(batchItem.getLockingBatchPieceCount());
                        }
                    }
                }

            }
        }
        return GoodsLockResultItem.SUCCESS;
    }

    @Override
    protected int checkCanLock() {
        //请求强制锁库直接锁，不判断库存是否够
        if (isLockingInUsing == YesOrNo.NO && forceLock == YesOrNo.YES) {
            return super.checkCanLock();
        }
        //删除锁库一定会成功，我们在进来的时候先执行所有的删除操作，把对应的locking记录标记为删除
        //这个时候锁库数量已经在内存被释放出来了，后面的update或add都能执行
        if (scGoodsLockingGoodsLockReqItem.getOpType() == ScGoodsGoodsLockReq.DEL) {
            //删除的锁库数量还是用后台的数， 之前锁的什么内容，看goods配置和内容
            changeLockingPieceCount = svrExistGoodsStockLockingList.stream().filter(GoodsStockLocking::isLocking).reduce(BigDecimal.ZERO, (total, item) -> GoodsStockUtils.add(total, item.getLockLeftTotalPieceCount()), BigDecimal::add).negate();
            if (!GoodsUtils.comparePriceEqual(changeLockingPieceCount, reqLockStandardStockCount.getOverallPieceCount())) {
                sLogger.error("锁库日志 删除锁库 {},changeLockingPieceCount:{} 请求锁库库存:{}", scGoodsLockingGoodsLockReqItem, changeLockingPieceCount, reqLockStandardStockCount.getOverallPieceCount());
            }
            return GoodsLockResultItem.SUCCESS;
        }

        // 批次锁库，目前还没指定批次，看当前goods的可用库存
        //新建,直接看goods剩下的库存是否够锁
        if (scGoodsLockingGoodsLockReqItem.getOpType() == ScGoodsGoodsLockReq.ADD) {
            //修改锁库，先拿老的锁库数量
            BigDecimal batchLockingCount = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(svrExistGoodsStockLockingList)) {
                batchLockingCount = svrExistGoodsStockLockingList.stream().filter(GoodsStockLocking::isLocking).reduce(BigDecimal.ZERO, (total, item) -> GoodsStockUtils.add(total, item.getLockingTotalPieceCount()), BigDecimal::add);
            }
            //得到需要变更的锁库数量
            changeLockingPieceCount = GoodsStockUtils.subtract(reqLockStandardStockCount.getOverallPieceCount(), batchLockingCount);
        } else if (scGoodsLockingGoodsLockReqItem.getOpType() == ScGoodsGoodsLockReq.UPDATE) {
            //修改锁库，先拿老的锁库数量
            BigDecimal batchLockingCount = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(svrExistGoodsStockLockingList)) {
                if (GoodsUtils.compareIntegerEqual(svrExistGoodsStockLockingList.get(0).getLockingPieceNum(), goods.getPieceNum())) {
                    batchLockingCount = svrExistGoodsStockLockingList.stream().reduce(BigDecimal.ZERO, (total, item) -> GoodsStockUtils.add(total, item.getLockingTotalPieceCount()), BigDecimal::add);
                } else {
                    batchLockingCount = svrExistGoodsStockLockingList.stream().filter(GoodsStockLocking::isLocking).peek(goodsStockLocking -> {
                        GoodsStockMountTrible stdLockingCount = GoodsStockUtils.toStandardStockCount(goodsStockLocking.getLockingPieceCount(), goodsStockLocking.getLockingPackageCount(), goods.getPieceNum(), goods.getTypeId());
                        goodsStockLocking.setLockingTotalPieceCount(stdLockingCount.getOverallPieceCount());
                        if (goodsStockLocking.getLockLeftTotalPieceCount() != null &&
                                goodsStockLocking.getLockLeftTotalPieceCount().compareTo(BigDecimal.ZERO) != 0 &&
                                !GoodsUtils.comparePriceEqual(goodsStockLocking.getLockLeftTotalPieceCount(), goodsStockLocking.getLockingTotalPieceCount())) {
                            GoodsStockMountTrible stdLockingCountOld = GoodsStockUtils.toStandardStockCount(goodsStockLocking.getLockLeftTotalPieceCount(), BigDecimal.ZERO, goodsStockLocking.getLockingPieceNum(), goods.getTypeId());
                            GoodsStockMountTrible stdLockingCountBefore = GoodsStockUtils.toStandardStockCount(BigDecimal.ZERO, stdLockingCountOld.getOverallPackageCount(), goods.getPieceNum(), goods.getTypeId());
                            goodsStockLocking.setLockLeftTotalPieceCount(stdLockingCountBefore.getOverallPieceCount());
                        } else {
                            goodsStockLocking.setLockLeftTotalPieceCount(stdLockingCount.getOverallPieceCount());
                        }
                        goodsStockLocking.setLockingPieceNum(goods.getPieceNum());
                        sLogger.info("修改规格:{}", goodsStockLocking);
                    }).reduce(BigDecimal.ZERO, (total, item) -> GoodsStockUtils.add(total, item.getLockingTotalPieceCount())
                            , BigDecimal::add);
                }
            } else {
                //转成ADD去操作，走到这个分支可能是后台硬删把锁库记录删了
                scGoodsLockingGoodsLockReqItem.setOpType(ScGoodsGoodsLockReq.ADD);
            }
            //得到需要变更的锁库数量
            changeLockingPieceCount = GoodsStockUtils.subtract(reqLockStandardStockCount.getOverallPieceCount(), batchLockingCount);
        }
        /*
         * 修改收费了的锁，只能往小了修改
         * */
        if (isLockingInUsing == YesOrNo.YES && changeLockingPieceCount.compareTo(BigDecimal.ZERO) > 0) {
            sLogger.error("修改收费了的锁库，只能往小了修改:changeLockingPieceCount{}:{}", changeLockingPieceCount, scGoodsLockingGoodsLockReqItem);
            return GoodsLockResultItem.LOCKING_IS_IN_DISPENSING;
        }
        return GoodsLockResultItem.SUCCESS;
    }


    /***
     * 锁批次，优先以之前锁的批次作为第一优先级
     * */
    protected List<GoodsBatch> findAndSortTheLockBatchList() {
        /*
         * 外围指定的总数量为准
         * */
        this.initPreLockingTotalPieceCount();
        Set<Long> toLockingBatchIdSet = new HashSet<>();
        List<GoodsBatch> lockingBatchList = new ArrayList<>();

        /*
         * 用户指定批次 ，指定数量 来锁.锁不够抛异常
         * */
        Map<Long, BigDecimal> batchIdToShortagePieceCount = new HashMap<>();
        if (!CollectionUtils.isEmpty(scGoodsLockingGoodsLockReqItem.getGoodsLockBatchItemList())) {
            lockingBatchList.addAll(dealUserSpecificyCutBatchList(batchIdToShortagePieceCount, toLockingBatchIdSet));
            /*
             * https://www.tapd.cn/tapd_fe/43780818/bug/detail/1143780818001086391
             * 指定批次，指定数量来强制锁库，如果批次不能锁成功直接锁库失败。
             * 因为收费那边已经按这个批次来算非了，如果多个强制锁批次，还在这些批次之间换锁 反而有问题
             * */
            if (getPreLockingTotalPieceCount() != null && getPreLockingTotalPieceCount().compareTo(BigDecimal.ZERO) != 0) {
                if (forceLock == YesOrNo.YES) {
                    sLogger.error("强制指定批次锁，强制暴力锁库 还是要锁{},{}:不足库存量:{},请求:{},锁批次：{}", goods.getId(), GoodsUtils.goodsFullName(goods), getPreLockingTotalPieceCount(), scGoodsLockingGoodsLockReqItem.getGoodsLockBatchItemList(), lockingBatchList);
                    return lockingBatchList;
                } else {
                    sLogger.error("强制指定批次锁，无法锁成功:不足库存量{},{}:{},请求:{},锁批次：{}", goods.getId(), GoodsUtils.goodsFullName(goods), getPreLockingTotalPieceCount(), scGoodsLockingGoodsLockReqItem.getGoodsLockBatchItemList(), lockingBatchList);
                    return new ArrayList<>();
                }
            }
            return lockingBatchList;
        }

        /*
         * 老的锁库记录指定的数量(强制锁优先)
         * */
        if (!CollectionUtils.isEmpty(svrExistGoodsStockLockingList)) {
            lockingBatchList.addAll(dealPreLockingBatchList(batchIdToShortagePieceCount, toLockingBatchIdSet));
        }

        /*
         * 指定追溯码的批次
         * */
        if (!CollectionUtils.isEmpty(scGoodsLockingGoodsLockReqItem.getBatchTraceCodeCountList())) {
            lockingBatchList.addAll(dealTraceCodeBatchList(toLockingBatchIdSet));
        }

        /*
         * 剩余批次 第三优先级
         */
        lockingBatchList.addAll(dealLeftBatchList(toLockingBatchIdSet));

        /*
         * 批次不足的换最优批次
         * */
        dealSwitchShortageBatchList(batchIdToShortagePieceCount, lockingBatchList);

        return lockingBatchList;
    }

    /**
     * 剩余批次排到后面等待扣
     */
    protected List<GoodsBatch> dealLeftBatchList(Set<Long> toLockingBatchIdSet) {
        //先按有效期排一轮
        List<GoodsBatch> pathList =  super.dealLeftBatchList(toLockingBatchIdSet);
        if (CollectionUtils.isEmpty(pathList)) {
            return pathList;
        }


        /*
        * 因为是强制锁批次，再看每个批次库的上限
        * */
        for (GoodsBatch goodsBatch : pathList) {
            /*
             * 这个批次要把这个lockId释放排除掉 ，null 代表不是所有
             * */
            BigDecimal stockTotalPCount = goodsBatch.getTotalStockPieceCount(clientReqLockId);
            if (cutPackageOnly == 1) {
                stockTotalPCount = stockTotalPCount.subtract(stockTotalPCount.remainder(BigDecimal.valueOf(goods.getPieceNum())));
            }

            if (stockTotalPCount.compareTo(BigDecimal.ZERO) <= 0) {
                sLogger.error("锁库日志 强制锁批次 锁库数量变大:{} 批次库存不足 reqLockBatchId:{},goodsBatch:{}", changeLockingPieceCount, goodsBatch, pharmacyNo);
                continue;
            }
            // min(锁库剩余, 上次锁库剩余)
            BigDecimal minTmp = getPreLockingTotalPieceCount().min(stockTotalPCount); //解决 开3盒 两个批次 1盒2片 2盒3片 （1盒，2盒）-> 13 应该想要 锁2盒3片
            // min(批次可用库存, min(锁库剩余, 上次锁库剩余))
            batchIdToCountLimit.put(goodsBatch.getBatchId(), minTmp);
            this.subPreLockingTotalPieceCount(batchIdToCountLimit.get(goodsBatch.getBatchId()));

        }
        return pathList;
    }

    /**
     * 2024 锁库清道夫，锁库就锁批次
     * 1.如果之前有锁库，先根据之前的锁库记录，把锁库批次那出来排序
     * 2.如果指定追溯码锁库，把追溯码的批次排前面
     */
    protected int doUpdateLocking() {
        List<GoodsBatch> lockingBatchList = findAndSortTheLockBatchList();
        Long lockId;
        if (scGoodsLockingGoodsLockReqItem.getLockId() != null) {
            lockId = scGoodsLockingGoodsLockReqItem.getLockId();
        } else {
            lockId = AbcIdUtils.getUUIDLong();
        }

        /*
         * 这一次锁库 锁的批次Id集合，不在里面的记录都应该删除
         * 这个类是锁阶段， 记录要被设置成删除
         * */
        Set<Long> thisTimeLockedLockIdSet = new HashSet<>();

        /*
         * 第一轮 锁库
         * */
        BigDecimal clientReqLockingPieceCountDelta = reqLockStandardStockCount.getOverallPieceCount(); //大转
        BigDecimal originalLockingPackageCount = scGoodsLockingGoodsLockReqItem.getLockingPackageCount();
        BigDecimal originalLockingLeftPackageCount = scGoodsLockingGoodsLockReqItem.getLockingPackageCount();
        BigDecimal summeryLockingPackageCount = BigDecimal.ZERO;
        //锁数量的记录
        GoodsStockLocking findLockCountLockingRecord = svrExistGoodsStockLockingList.stream().filter(it -> it.isLocking() && it.getBatchId() == null).findFirst().orElse(null);

        for (int i = 0; i < lockingBatchList.size(); i++) {
            GoodsBatch goodsBatch = lockingBatchList.get(i);
            /*
             * 本次锁库数量已经被满足，退了，后面的批次也不锁了
             * */
            if (clientReqLockingPieceCountDelta.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            Long batchId = goodsBatch.getBatchId();

            /*
             * 找这个批次的锁库记录或新建锁库记录
             * 1.之前就有锁库记录
             * 2.用之前锁数量的记录
             * 3.没有，新建锁库记录
             * */
            GoodsStockLocking findLocking = svrExistGoodsStockLockingList.stream().filter(it -> it.isLocking() && GoodsUtils.compareLongEqual(it.getBatchId(), batchId)).findFirst().orElse(null);
            boolean isANLocking = false; // 要真的能锁上才会处理写db
            boolean changeCountToBatchLocking = false; // 要真的能锁上才会处理写db
            if (findLocking == null) {
                //是否有锁数量
                if (findLockCountLockingRecord == null) {
                    findLocking = lockANewBatch(goodsBatch, lockId);
                    if (findLocking == null) {
                        continue;
                    }
                    isANLocking = true;
                } else {
                    findLocking = findLockCountLockingRecord; //锁数量转成锁批次
                    changeCountToBatchLocking = true;
                }
            }
            /*
             * 最少的要扣的库存量
             * */
            BigDecimal lowestReqLockingCount = batchIdToCountLimit.getOrDefault(batchId, BigDecimal.ZERO);

            /*
             * 外围整体请求还有要锁的库存量
             * */
            BigDecimal lockingCount;
            if (getPreLockingTotalPieceCount().compareTo(BigDecimal.ZERO) > 0) {
                lockingCount = clientReqLockingPieceCountDelta.min(lowestReqLockingCount.add(getPreLockingTotalPieceCount())); //本次锁库总要扣的减去每个锁库批次保留后剩余的
                sLogger.error("锁库日志 锁库阶段 锁库库存还有余量需要带走 药品ID:{},批次:{} 限定请求锁库库存:{},本次总待锁库存:{},最小需要锁的库存:{}", goodsBatch.getGoodsId(), goodsBatch.getBatchId(), lowestReqLockingCount, getPreLockingTotalPieceCount(), lockingCount);
            } else {
                lockingCount = clientReqLockingPieceCountDelta.min(lowestReqLockingCount);
            }


            /*
             * 批次的数量 把这个锁的排掉
             * */
            BigDecimal batchStockTotalPieceCount = goodsBatch.getTotalStockPieceCount(lockId);
            if (cutPackageOnly == 1) {
                batchStockTotalPieceCount = batchStockTotalPieceCount.subtract(batchStockTotalPieceCount.remainder(BigDecimal.valueOf(goods.getPieceNum())));
            }
            /*
             * 因为存在直接强制锁批次，啥都没有导致 没有计算上限的情况
             * */
            if (lockingCount.compareTo(batchStockTotalPieceCount) > 0) {
                if (forceLock == YesOrNo.YES && i == lockingBatchList.size() - 1) {
                    sLogger.error("锁库日志 强制锁批次，库存不足 尽量锁 库存不足 药品ID:{},{},批次:{} 排掉锁库ID:{}的可用库存:{},限定请求锁库库存:{},本次总待锁库存:{},最小需要锁的库存:{}", goodsBatch.getGoodsId(), GoodsUtils.goodsFullName(goods),goodsBatch.getBatchId(), clientReqLockId, batchStockTotalPieceCount, lowestReqLockingCount, getPreLockingTotalPieceCount(), lockingCount);
                } else {
                    sLogger.error("锁库日志 锁库阶段 库存不足 药品ID:{},{},批次:{} 排掉锁库ID:{}的可用库存:{},限定请求锁库库存:{},本次总待锁库存:{},最小需要锁的库存:{}", goodsBatch.getGoodsId(),GoodsUtils.goodsFullName(goods), goodsBatch.getBatchId(), clientReqLockId, batchStockTotalPieceCount, lowestReqLockingCount, getPreLockingTotalPieceCount(), lockingCount);
                    continue;
                }
            }
            if (forceLock == YesOrNo.YES && i == lockingBatchList.size() - 1) {

            } else {
                if (lockingCount.compareTo(BigDecimal.ZERO) <= 0) {
                    //保护下
                    sLogger.info("锁库日志:批次{}库存不够锁库，lockingCount:{},批次剩余库存{},clientReqLockingPieceCountDelta:{},lowestReqLockingCount:{},getPreLockingTotalPieceCount():{}", batchId, lockingCount, batchStockTotalPieceCount, clientReqLockingPieceCountDelta, lowestReqLockingCount, getPreLockingTotalPieceCount());
                    continue;
                }
            }
            thisTimeLockedLockIdSet.add(findLocking.getId());
            findLocking.setBatchId(batchId);
            findLocking.setOptType(getOptType());
            BigDecimal beforeLockingPieceCount = findLocking.getLockingTotalPieceCount();

            //----走到这里锁上了


            /*
             * 带走了  --- 外围整体请求还有要锁的库存量
             * */
            if (lockingCount.compareTo(lowestReqLockingCount) > 0) {
                subPreLockingTotalPieceCount(lockingCount.subtract(lowestReqLockingCount));
            }
            clientReqLockingPieceCountDelta = clientReqLockingPieceCountDelta.subtract(lockingCount);


            /*
             * 小转大有精度损失
             * */
            GoodsStockMountTrible thisBatchLockCount = GoodsStockUtils.toStandardStockCount(
                    lockingCount,
                    BigDecimal.ZERO,
                    goodsStockOpsHelper.getGoodsStockSummary().getPieceNum(),
                    goodsStockOpsHelper.getGoodsStockSummary().getType(),
                    goodsStockOpsHelper.getGoodsStockSummary().getSubType());
            /*
             * 小单位已经扣完了，大单位还有零头，带走
             * */
            if (scGoodsLockingGoodsLockReqItem.getLockUnitFlag() == CountUnitFlag.BIG && clientReqLockingPieceCountDelta.compareTo(BigDecimal.ZERO) == 0) {
                BigDecimal leftUnLockingPackageCount = GoodsStockUtils.subtract(originalLockingPackageCount, summeryLockingPackageCount);
                if (!GoodsUtils.comparePriceEqual(leftUnLockingPackageCount, thisBatchLockCount.getOverallPackageCount())) {
                    thisBatchLockCount = GoodsStockUtils.toStandardStockCount(
                            BigDecimal.ZERO,
                            originalLockingLeftPackageCount,
                            goodsStockOpsHelper.getGoodsStockSummary().getPieceNum(),
                            goodsStockOpsHelper.getGoodsStockSummary().getType(),
                            goodsStockOpsHelper.getGoodsStockSummary().getSubType());
                    /*
                     * 这里有可能变大 变小，还是让 扣的小单位就 lockingCount
                     * */
                    thisBatchLockCount.setOverallPieceCount(lockingCount);
                    sLogger.error("批次锁库，小单位已经扣完了，大单位还有零头，带走:{}", thisBatchLockCount);
                }
            }
            //锁的这个批次的库存量 客户端请求是什么单位就存什么单位
            if (scGoodsLockingGoodsLockReqItem.getLockUnitFlag() == CountUnitFlag.SMALL) {
                findLocking.setLockingPieceCount(thisBatchLockCount.getOverallPieceCount());
                findLocking.setLockingPackageCount(BigDecimal.ZERO);
                findLocking.setLockingTotalPieceCount(thisBatchLockCount.getOverallPieceCount());
                findLocking.setLockLeftTotalPieceCount(thisBatchLockCount.getOverallPieceCount());
            } else if (scGoodsLockingGoodsLockReqItem.getLockUnitFlag() == CountUnitFlag.BIG) {
                summeryLockingPackageCount = GoodsStockUtils.add(summeryLockingPackageCount, thisBatchLockCount.getOverallPackageCount());
                findLocking.setLockingPackageCount(thisBatchLockCount.getOverallPackageCount());
                findLocking.setLockingPieceCount(BigDecimal.ZERO);
                findLocking.setLockingTotalPieceCount(thisBatchLockCount.getOverallPieceCount());
                findLocking.setLockLeftTotalPieceCount(thisBatchLockCount.getOverallPieceCount());
                originalLockingLeftPackageCount = GoodsStockUtils.subtract(originalLockingLeftPackageCount, thisBatchLockCount.getOverallPackageCount());
                if (originalLockingLeftPackageCount.compareTo(BigDecimal.ZERO) < 0) {
                    originalLockingLeftPackageCount = BigDecimal.ZERO;
                }
            } else {
                summeryLockingPackageCount = GoodsStockUtils.add(summeryLockingPackageCount, thisBatchLockCount.getOverallPackageCount());
                findLocking.setLockingPieceCount(thisBatchLockCount.getPieceCount());
                findLocking.setLockingPackageCount(thisBatchLockCount.getPackageCount());
                findLocking.setLockingTotalPieceCount(thisBatchLockCount.getOverallPieceCount());
                findLocking.setLockLeftTotalPieceCount(thisBatchLockCount.getOverallPieceCount());
            }

            realChangeLockingPieceCount = MathUtils.wrapBigDecimalAdd(realChangeLockingPieceCount, MathUtils.wrapBigDecimalSubtract(findLocking.getLockLeftTotalPieceCount(), beforeLockingPieceCount));
            createStockLockingList.add(findLocking); //更新,刷GoodsStat
            if (isANLocking) {
                svrExistGoodsStockLockingList.add(findLocking);
                logList.add(addGoodsStockLockingLog(clinicConfig.getChainId(), findLocking.getLockId(), findLocking.getId(), "锁库锁数量新增锁批次", null, lockingGoodsLockReq.getLockScene(), JsonUtils.dumpAsJsonNode(scGoodsLockingGoodsLockReqItem)));
                retList.add(generateLockingRsp(GoodsStockUtils.subtract(findLocking.getLockingTotalPieceCount(), beforeLockingPieceCount), findLocking, goodsBatch));
                // 新增锁库要重新加进goodsBatch
                goodsBatch.getBatchLockHelper().getLockingList().add(findLocking);
            } else if (changeCountToBatchLocking) {
                logList.add(addGoodsStockLockingLog(clinicConfig.getChainId(), findLocking.getLockId(), findLocking.getId(), "锁数量转成锁批次", null, lockingGoodsLockReq.getLockScene(), JsonUtils.dumpAsJsonNode(scGoodsLockingGoodsLockReqItem)));
                retList.add(generateLockingRsp(GoodsStockUtils.subtract(findLocking.getLockingTotalPieceCount(), beforeLockingPieceCount), findLocking, goodsBatch));
                findLockCountLockingRecord = null; //锁成功了再清理变量
            } else {
                logList.add(addGoodsStockLockingLog(clinicConfig.getChainId(), findLocking.getLockId(), findLocking.getId(), "锁批次修改", null, lockingGoodsLockReq.getLockScene(), JsonUtils.dumpAsJsonNode(scGoodsLockingGoodsLockReqItem)));
                retList.add(generateLockingRsp(GoodsStockUtils.subtract(findLocking.getLockingTotalPieceCount(), beforeLockingPieceCount), findLocking, goodsBatch));
            }
        }

        /*
         * 还有库存不够
         * 这个类是处理强制锁批次的，库存不够就是批次不够，要抛异常了
         * */
        if (clientReqLockingPieceCountDelta.compareTo(BigDecimal.ZERO) > 0) {
            //更新锁库数量
            if (forceLock == YesOrNo.YES) {
                sLogger.info("虽然库存不够，因为是强制锁所以能锁多少是多少 {},{},锁库差的库存量:{},不会去锁无批次", goods.getId(), GoodsUtils.goodsFullName(goods), clientReqLockingPieceCountDelta);
                return GoodsLockResultItem.SUCCESS;
            }
            return GoodsLockResultItem.NO_ENOUGH_BATCH_STOCK;
        }

        /*
         * 检查是否处理完
         * */
        if (clientReqLockingPieceCountDelta.compareTo(BigDecimal.ZERO) == 0) {
            for (GoodsStockLocking locking : svrExistGoodsStockLockingList) {
                /*
                 * 这一次锁库 锁的批次Id集合，不在里面的记录都应该删除
                 * 这个类是锁阶段， 记录要被设置成删除
                 * */
                if (thisTimeLockedLockIdSet.contains(locking.getId())) {
                    continue;
                }
                /*
                 *https://www.tapd.cn/tapd_fe/43780818/bug/detail/1143780818001081770
                 * 先删，后再删锁，导致这里把另外删的也加入到了retList里面，多返回了一个释放锁的
                 * */
                if (!locking.isLocking()) {
                    continue;
                }
                realChangeLockingPieceCount = MathUtils.wrapBigDecimalAdd(realChangeLockingPieceCount, MathUtils.wrapBigDecimalSubtract(BigDecimal.ZERO, locking.getLockLeftTotalPieceCount()));
                locking.setLockLeftTotalPieceCount(BigDecimal.ZERO);
                locking.setStatus(GoodsStockLocking.Status.DELETED);
                createStockLockingList.add(locking); //更新,刷GoodsStat
                logList.add(addGoodsStockLockingLog(clinicConfig.getChainId(), locking.getLockId(), locking.getId(), "锁批次删除锁库", null, lockingGoodsLockReq.getLockScene(), JsonUtils.dumpAsJsonNode(scGoodsLockingGoodsLockReqItem)));
                retList.add(generateLockingRsp(locking.getLockingTotalPieceCount().negate(), locking, null));
                UserFillUtils.fillLastModifiedBy(locking, lockingGoodsLockReq.getOperatorId());
            }
            return GoodsLockResultItem.SUCCESS;
        }


        return GoodsLockResultItem.SUCCESS;
    }

    /**
     * 这个类是处理锁批次的锁阶段，删除就是删除
     */
    protected void doDeleteLocking() {
        if (CollectionUtils.isEmpty(svrExistGoodsStockLockingList)) {
            return;
        }
        /*
         * 请求的lockId还是批次，不管，反正就是把整个lockId拉出来的锁库记录全删了
         * */
        for (GoodsStockLocking goodsStockLocking : svrExistGoodsStockLockingList) {
            goodsStockLocking.setStatus(GoodsStockLocking.Status.DELETED);
            goodsStockLocking.setLastModified(Instant.now());
            logList.add(addGoodsStockLockingLog(clinicConfig.getChainId(), goodsStockLocking.getLockId(), goodsStockLocking.getId(), "删除", null, lockingGoodsLockReq.getLockScene(), JsonUtils.dumpAsJsonNode(scGoodsLockingGoodsLockReqItem)));
            createStockLockingList.add(goodsStockLocking);//删除


            GoodsLockResultItem batchReleaseResult = createClientRspItem(GoodsLockResultItem.SUCCESS);
            batchReleaseResult.setLockId(goodsStockLocking.getLockId());
            batchReleaseResult.setId(goodsStockLocking.getId());
            batchReleaseResult.setOpType(ScGoodsGoodsLockReq.DEL);
            batchReleaseResult.setPackageCostPrice(goodsStockLocking.getPackageCostPrice());
            batchReleaseResult.setPackageCount(goodsStockLocking.getLockingPackageCount());
            batchReleaseResult.setPieceCount(goodsStockLocking.getLockingPieceCount());
            batchReleaseResult.setChangedLockPieceCount(goodsStockLocking.getLockLeftTotalPieceCount().negate());
            batchReleaseResult.setLockingTotalPieceCount(null);
            retList.add(batchReleaseResult);


            goodsStockLocking.setLockLeftTotalPieceCount(BigDecimal.ZERO);
        }
    }

    /**
     * 锁到一个新的批次上
     *
     * @param goodsBatch 要新上锁的批次
     * @param lockId     锁的id
     * @return 返回锁的结果
     */
    protected GoodsStockLocking lockANewBatch(GoodsBatch goodsBatch, Long lockId) {
        //批次的总库存

        // DB对象
        GoodsStockLocking newLocking = new GoodsStockLocking();
        newLocking.setId(AbcIdUtils.getUUIDLong());
        newLocking.setLockId(lockId); //用同一个锁ID，一个锁ID可以锁多个批次，这里锁住，确定了进价医保上浮比例要用
        newLocking.setChainId(clinicConfig.getChainId());
        newLocking.setClinicId(clinicConfig.getClinicId());
        newLocking.setType(lockingGoodsLockReq.getType());
        newLocking.setPharmacyNo(scGoodsLockingGoodsLockReqItem.getPharmacyNo());
        newLocking.setPharmacyType(scGoodsLockingGoodsLockReqItem.getPharmacyType());
        newLocking.setGoodsId(scGoodsLockingGoodsLockReqItem.getGoodsId());
        newLocking.setBatchId(goodsBatch.getBatchId());//锁的这个pic
        newLocking.setStatus(GoodsStockLocking.Status.LOCKING);
        newLocking.setOptType(getOptType());
        //2024.04进销存成本算法用lockOrderId存patientOrderId
        newLocking.setLockingOrderId(lockingGoodsLockReq.getPatientOrderId());
        newLocking.setLockingPieceNum(goods.getPieceNum());
        newLocking.setPriceType(goods.getPriceType());
        newLocking.setPackageCostPrice(goodsBatch.getPackageCostPrice());
        newLocking.setLockingPieceCount(BigDecimal.ZERO);
        newLocking.setLockingPackageCount(BigDecimal.ZERO);
        newLocking.setLockingTotalPieceCount(BigDecimal.ZERO);
        newLocking.setLockLeftTotalPieceCount(BigDecimal.ZERO);
        UserFillUtils.fillCreatedBy(newLocking, lockingGoodsLockReq.getOperatorId());

        return newLocking;
    }

    /**
     * 内部虽然锁的是批次，这个锁批次逻辑不流出去，只返回锁数量的出去
     */
    protected GoodsLockResultItem generateLockingRsp(BigDecimal changeLockingPieceCount, GoodsStockLocking newLocking, GoodsBatch batch) {
        GoodsLockResultItem lockResultItem = createClientRspItem(GoodsLockResultItem.SUCCESS);
        lockResultItem.setPharmacyNo(newLocking.getPharmacyNo());
        lockResultItem.setLockId(newLocking.getLockId());
        lockResultItem.setLockingPieceNum(BigDecimal.valueOf(newLocking.getLockingPieceNum()));
        lockResultItem.setBatchId(newLocking.getBatchId());
        lockResultItem.setPackageCount(newLocking.getLockingPackageCount());
        lockResultItem.setPieceCount(newLocking.getLockingPieceCount());
        lockResultItem.setLockingTotalPieceCount(newLocking.getLockingTotalPieceCount());
        if (newLocking.getLockLeftTotalPieceCount().compareTo(BigDecimal.ZERO) == 0) {
            lockResultItem.setOpType(ScGoodsGoodsLockReq.DEL);
        }

        if (batch != null) {
            lockResultItem.setExpiryDate(batch.getExpiryDate());
            lockResultItem.setBatchNo(batch.getBatchNo());
            lockResultItem.setPackageCostPrice(batch.getPackageCostPrice());
        }

        lockResultItem.setChangedLockPieceCount(changeLockingPieceCount);
        return lockResultItem;
    }
}