package cn.abcyun.cis.goods.stock.stock;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.message.goodslog.LogFlag;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisCache;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.model.GoodsBatch;
import cn.abcyun.cis.goods.model.GoodsSnapV3;
import cn.abcyun.cis.goods.model.GoodsStock;
import cn.abcyun.cis.goods.model.GoodsStockBatchExtend;
import cn.abcyun.cis.goods.utils.GoodsStockUtils;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.utils.SupplierUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 领用出库 批次进销存操作
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GoodsStockOpsReceptionOut extends GoodsStockOpsBase {

    /**
     * 入库 库存操作
     *  因为是入库，入库需要新生成 GoodsStock和GoodsStockLock
     * @param  goods 要入库的Goods对象 ，注意一个goods可能被入多次
     * @param  newGoodsStock
     * @param  goodsSnap 入库goods的快照信息
     * @param  employeeId 操作人Id
     * @param batId 日志的batId
     * @param stockLogOrderId 入库单
     * @param  stockLogOrderDetailId 入库单条目
     * @param action 入库的action
     * */
    public GoodsStockOpsReceptionOut(GoodsRedisCache goods,
                                     GoodsSnapV3 goodsSnap,
                                     GoodsStock newGoodsStock,
                                     GoodsBatch goodsBatch,
                                     SupplierUtils supplierUtils,
                                     GoodsStockBatchExtend goodsStockBatchExtend,
                                     int goodsStockCutOpsType,
                                     ClinicConfig clinicConfig,
                                     GoodsStockOpsHelper goodsStockOpsHelper, //不是必传参数
                                     String batId,
                                     String clinicId,
                                     String employeeId,
                                     String orderNo,
                                     String stockLogOrderId,
                                     String stockLogOrderDetailId,
                                     String action) {
        CreateGoodsStockLogParameter parameter = new CreateGoodsStockLogParameter();
        parameter.setStockLogAction(action);
        parameter.setStockLogActionV2(GoodsConst.StockLogActionV2.STOCK_RECEPTION_OUT);
        parameter.setBatId(batId); //batId随机生成
        parameter.setGoodsSnap(goodsSnap);
        parameter.setChainId(goods.getOrganId());
        parameter.setClinicId(clinicId);
        parameter.setPatientOrderId( null ); // 入库这里是 null
        parameter.setOrderId(stockLogOrderId);  //orderId存的入库单的数据库Id
        parameter.setOrderDetailId(stockLogOrderDetailId); //orderDetailId存的是入库项项的数据库ID
        /*
         *             originPieceCount: stockOut.pieceCount, //原始指令：制剂数
         *             originPackageCount: stockOut.packageCount, //原始指令：包装数
         *             originTotalPrice: (stockOut.pieceCount / goods.pieceNum + stockOut.packageCount) * goods.packagePrice,
         * */
        parameter.setClientReqOriginPackageCount(newGoodsStock.getPackageCount());
        parameter.setClientReqOriginPieceCount(newGoodsStock.getPieceCount());
        parameter.setClientReqTotalOriginPrice(GoodsStockUtils.calTotalPkgPrice(newGoodsStock.getPieceCount(),newGoodsStock.getPackageCount(),goods.getPieceNum(),goods.getPackagePrice()));
        parameter.setEmployeeId(employeeId);
        parameter.setOrderNo(orderNo);
        if(clinicConfig.isOpenSheBao()){
            parameter.setFlag(GoodsUtils.onFlagLong(parameter.getFlag(), LogFlag.SUPERVISON_LOG));
        }
        super.initByGoodsRedisCache(goods, newGoodsStock,goodsBatch,supplierUtils, goodsStockBatchExtend, null, goodsStockCutOpsType, clinicConfig, goodsStockOpsHelper, parameter);
    }
}
