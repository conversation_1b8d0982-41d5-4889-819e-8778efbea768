package cn.abcyun.cis.goods.stock.stockmanager;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.cis.goods.mapper.GoodsStockMapper;
import cn.abcyun.cis.goods.model.GoodsStock;
import cn.abcyun.cis.goods.repository.*;
import cn.abcyun.cis.goods.service.rpc.AliHealthService;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * GoodsLockingStockManager
 *
 * <AUTHOR>
 * @since 2024/11/20 21:44
 **/
public class GoodsStockManagerV2 extends GoodsStockCutManager {

    /**
     * 构造函数
     *
     * @param chainId
     * @param goodsStockRepository              库存组件
     * @param goodsStockBatchExtendRepository
     * @param goodsStockLockingRepository
     * @param goodsStockMapper
     * @param goodsStockTraceableCodeRepository
     * @param goodsStockTraceableCodeLogRepository
     * @param pharmacyType                      加载某个类型药房的GoodsStock
     **/
    public GoodsStockManagerV2(String chainId, GoodsStockRepository goodsStockRepository, GoodsStockBatchExtendRepository goodsStockBatchExtendRepository, GoodsStockLockingRepository goodsStockLockingRepository, GoodsStockMapper goodsStockMapper, GoodsStockTraceableCodeRepository goodsStockTraceableCodeRepository, GoodsStockTraceableCodeLogV3Repository goodsStockTraceableCodeLogRepository, AliHealthService aliHealthService, int pharmacyType) {
        super(chainId, goodsStockRepository, goodsStockBatchExtendRepository, goodsStockLockingRepository, goodsStockMapper, goodsStockTraceableCodeRepository, goodsStockTraceableCodeLogRepository, aliHealthService,pharmacyType);
    }

    @Override
    protected List<GoodsStock> queryStockInChainAndNonEmptyStock(List<String> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return new ArrayList<>();
        }
        return goodsStockRepository.findAllByGoodsIdInAndInnerHasStockAndPharmacyType( goodsIds, YesOrNo.YES, pharmacyType);
    }

    protected List<GoodsStock> queryStockInClinicAndNonEmptyStock(String clinicId, List<String> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return new ArrayList<>();
        }
        return goodsStockRepository.findAllByClinicIdAndGoodsIdInAndInnerHasStockAndPharmacyType(clinicId, goodsIds, YesOrNo.YES, pharmacyType);
    }
}
