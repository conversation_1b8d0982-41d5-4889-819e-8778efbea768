package cn.abcyun.cis.goods.rpc.client;


import cn.abcyun.cis.core.config.FeignForwardHeaderV2Configuration;
import cn.abcyun.cis.goods.vo.backend.RpcGoodsSearchSuggestRsp;
import cn.abcyun.cis.core.config.FeignForwardHeaderV2Configuration;
import cn.abcyun.cis.goods.vo.frontend.ClientGoodsRecommendReq;
import cn.abcyun.cis.goods.vo.frontend.ClientGoodsRecommendRsp;
import cn.abcyun.common.model.AbcServiceResponseBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;




@FeignClient(
        name = "abc-cis-sc-cdss",
        url = "http://abc-cis-sc-cdss",
        configuration = FeignForwardHeaderV2Configuration.class)
public interface AbcCisRecommendFeignClient {

    /**
     * 推荐药品接口接口
     *
     * @param req ClientGoodsRecommendReq
     * @return AbcServiceResponseBody<ClientGoodsRecommendRsp>
     */
    @PostMapping(value = "/rpc/sc/cdss/recommend")
    AbcServiceResponseBody<ClientGoodsRecommendRsp> recommendGoods(
            @RequestBody ClientGoodsRecommendReq req
    );
}

