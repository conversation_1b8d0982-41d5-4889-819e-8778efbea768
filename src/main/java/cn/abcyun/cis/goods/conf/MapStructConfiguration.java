package cn.abcyun.cis.goods.conf;

import org.mapstruct.MapperConfig;
import org.mapstruct.NullValueMappingStrategy;

/**
 * MapStruct配置类
 *
 * <AUTHOR>
 * @since 2023/11/2 13:14
 **/
@MapperConfig(
        // 当源对象属性为null时，映射为空列表或空Map，而不是 null
        nullValueIterableMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT,
        nullValueMapMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT
)
public class MapStructConfiguration {
}
