package cn.abcyun.cis.goods.dto.stock;

import lombok.Data;

import java.math.BigDecimal;

/**
 * MyBatis 加载批次信息
 * {@link cn.abcyun.cis.goods.model.GoodsStock}
 * */

@Data
public class GoodsStockInTransDto {

    private String fromOrganId;
    private String toOrganId;
    private BigDecimal lockingPieceCount = BigDecimal.ZERO;
    private BigDecimal lockingPackageCount = BigDecimal.ZERO;// '整包库存',
    private String orderNo = "";
}
