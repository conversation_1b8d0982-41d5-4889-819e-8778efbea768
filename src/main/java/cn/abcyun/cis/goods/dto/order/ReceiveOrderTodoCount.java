package cn.abcyun.cis.goods.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 收货单待办统计
 *
 * <AUTHOR>
 * @since 2024-01-28 11:10:10
 **/
@ApiModel(value = "ReceiveOrderTodoCount", description = "收货单待办统计")
@Data
public class ReceiveOrderTodoCount {

    /**
     * 待收货
     */
    @ApiModelProperty("待收货")
    private Integer waitReceiveCount;
    /**
     * 待审核
     */
    @ApiModelProperty("待审核")
    private Integer waitApprovalCount;

}
