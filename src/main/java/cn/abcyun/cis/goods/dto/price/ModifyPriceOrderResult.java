package cn.abcyun.cis.goods.dto.price;

import cn.abcyun.cis.goods.model.Goods;
import cn.abcyun.cis.goods.model.GoodsModifyPriceOrder;
import cn.abcyun.cis.goods.model.GoodsModifyPriceOrderItem;
import lombok.Data;

import java.util.*;

/**
 * 添加到默认草稿调价单结果
 *
 * <AUTHOR>
 * @since 2025/2/20 14:41
 **/
@Data
public class ModifyPriceOrderResult {
    private Boolean updatePriceNow = false;
    private Map<String ,Long> goodsIdToOrderItemId= new HashMap<>();
    private Set<String> updateGoodsStatClinicIdList = new HashSet<>();

    /**
     * 调价单
     */
    private GoodsModifyPriceOrder goodsModifyPriceOrder;

    /**
     * 本次调价明细
     */
    private List<GoodsModifyPriceOrderItem> curGoodsModifyPriceOrderItems;

    /**
     * 涉及商品列表
     */
    private List<Goods> goodsList;

}
