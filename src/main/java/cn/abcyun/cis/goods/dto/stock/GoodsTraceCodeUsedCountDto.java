package cn.abcyun.cis.goods.dto.stock;

import cn.abcyun.cis.goods.model.GoodsStock;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * MyBatis 加载批次信息
 * {@link GoodsStock}
 */

@Data
public class GoodsTraceCodeUsedCountDto {
    private String goodsId;
    private String no;
    /**
     * 销售变更小单位数量（包含修改剩余量）
     */
    private BigDecimal changePieceCount;
    /**
     * 销售变更大单位数量（包含修改剩余量）
     */
    private BigDecimal changePackageCount;
    /**
     * 库存变更小单位数量（入库、出库等...）
     */
    private BigDecimal stockChangePieceCount;
    /**
     * 库存变更大单位数量（入库、出库等...）
     */
    private BigDecimal stockChangePackageCount;
    /**
     * 最近一次出库时间
     */
    private Instant stockOutTime;
}
