package cn.abcyun.cis.goods.service.exam;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.exam.QueryExamSamplePipeByIdsReq;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.util.FillUtils;
import cn.abcyun.cis.goods.cache.redis.ChoosePharmacyHelper;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisCache;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisUtils;
import cn.abcyun.cis.goods.converter.ExaminationSamplePipeConverter;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.model.ExaminationSamplePipe;
import cn.abcyun.cis.goods.model.Goods;
import cn.abcyun.cis.goods.model.GoodsComposeNaked;
import cn.abcyun.cis.goods.repository.ExaminationSamplePipeRepository;
import cn.abcyun.cis.goods.repository.GoodsComposeNakedRepository;
import cn.abcyun.cis.goods.repository.GoodsRepository;
import cn.abcyun.cis.goods.service.rpc.CisClinicService;
import cn.abcyun.cis.goods.utils.AbcIdUtils;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.utils.UserFillUtils;
import cn.abcyun.cis.goods.vo.frontend.exam.ClientExamDisableReq;
import cn.abcyun.cis.goods.vo.frontend.exam.ExaminationSamplePipeView;
import cn.abcyun.cis.goods.vo.frontend.exam.ExaminationSampleSimpleView;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.model.AbcListPage;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 检验样本容器(v2_goods_examination_sample_pipe)表服务实现类
 *
 * <AUTHOR>
 * @date 2022-09-21 18:32:55
 */
@Service
@AllArgsConstructor
public class ExaminationSamplePipeService {

    @Autowired
    private GoodsRedisUtils goodsRedisUtils;
    private AbcIdGenerator abcIdGenerator;
    private ExaminationSamplePipeRepository examinationSamplePipeRepository;
    private ExaminationSamplePipeConverter examinationSamplePipeConverter;
    @Autowired
    private CisClinicService cisClinicService;
    @Autowired
    private GoodsRepository goodsRepository;
    @Autowired
    private GoodsComposeNakedRepository goodsComposeNakedRepository;


    public AbcListPage<ExaminationSamplePipeView> getExaminationSamplePipes(String chainId, String clinicId, Integer withDeleted, ExaminationSamplePipeView examinationSamplePipeReq) {
        List<ExaminationSamplePipe> examinationSamplePipes;

        // 根据withDeleted参数决定是否包含已删除的数据
        if (withDeleted != null && withDeleted == 1) {
            // 包含已删除数据，查询所有记录
            examinationSamplePipes = examinationSamplePipeRepository.findAllByChainIdOrderByCreatedDesc(chainId);
        } else {
            // 不包含已删除数据，只查询未删除记录
            examinationSamplePipes = examinationSamplePipeRepository.findAllByChainIdAndIsDeletedOrderByCreatedDesc(chainId, 0);
        }

        AbcListPage<ExaminationSamplePipeView> page = new AbcListPage<>();
        List<String> sampleIdList = examinationSamplePipes.stream().map(ExaminationSamplePipe::getId).collect(Collectors.toList());
        Map<String, ExaminationSamplePipeView> sampleIdToPipeView = goodsRedisUtils.getExamPipeFromLocalByIds(chainId, sampleIdList);
        page.setRows(examinationSamplePipes.stream().map(sample -> {
            ExaminationSamplePipeView view = examinationSamplePipeConverter.toView(sample);
            ExaminationSamplePipeView redisPipeView = sampleIdToPipeView.get(view.getId());
            if (Objects.nonNull(redisPipeView)) {
                view.setItemList(redisPipeView.getItemList());
            }
            return view;
        }).collect(Collectors.toList()));
        return page;
    }

    public ExaminationSamplePipeView saveExaminationSamplePipe(String chainId, String clinicId, String employeeId, ExaminationSamplePipeView examinationSamplePipeReq) {
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(clinicId);
        examinationSamplePipeReq.checkParam(clinicConfig);
        ExaminationSamplePipe existExaminationSamplePipe = examinationSamplePipeRepository.findByChainIdAndCodeAndIsDeleted(chainId, examinationSamplePipeReq.getCode(), 0).orElse(null);
        if (Objects.nonNull(existExaminationSamplePipe)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_EXAM_SAMPLE_PIPE_CODE_REPEAT);
        }
        // 创建采样组goods
        Goods goods = examinationSamplePipeConverter.createSampleGoods(chainId, examinationSamplePipeReq.getName(), employeeId);
        ExaminationSamplePipe examinationSamplePipe = examinationSamplePipeConverter.toEntity(examinationSamplePipeReq);
        examinationSamplePipe.setId(AbcIdUtils.getUID());
        examinationSamplePipe.setChainId(chainId);
        examinationSamplePipe.setIsDeleted(0);
        examinationSamplePipe.setGoodsId(goods.getId());
        FillUtils.fillCreatedBy(examinationSamplePipe, employeeId);
        if (!CollectionUtils.isEmpty(examinationSamplePipeReq.getItemList())) {
            List<String> relateGoodsIdList = examinationSamplePipeReq.getItemList().stream().map(ExaminationSamplePipeView.SamplePipeRelatedGoods::getGoodsId).distinct().collect(Collectors.toList());
            Map<String, GoodsRedisCache> goodsIdToRedisCache = goodsRedisUtils.getMapGoodsRedisCacheWithoutBatch(relateGoodsIdList,
                    ChoosePharmacyHelper.ofSpecificPharmacy(GoodsConst.PharmacyType.LOCAL_PHARMACY, GoodsUtils.PHARMACY_NO_0), clinicConfig);
            List<GoodsComposeNaked> relateComposeList = new ArrayList<>();
            BigDecimal packagePrice = examinationSamplePipeConverter.toSampleRelateGoods(examinationSamplePipeReq.getItemList(),
                    chainId, goods.getId(), employeeId, goodsIdToRedisCache, relateComposeList);
            // 有关联goods
            goods.onComposeFlag(GoodsConst.ComposeFlag.HAS_COMPOSE_CHILDREN);
            examinationSamplePipe.setRelateGoodsFlag(YesOrNo.YES);
            goods.setPackagePrice(packagePrice);
            if (!CollectionUtils.isEmpty(relateComposeList)) {
                goodsComposeNakedRepository.saveAll(relateComposeList);
            }
        }
        goodsRepository.save(goods);
        examinationSamplePipeRepository.save(examinationSamplePipe);
        /**
         * 清理Redis缓存
         * */
        GoodsUtils.runAfterTransaction(() -> goodsRedisUtils.clearChainExamPipeFromRedis(chainId));
        return examinationSamplePipeConverter.toView(examinationSamplePipe);
    }

    public ExaminationSamplePipeView deleteExaminationSamplePipeById(String id, String chainId, String clinicId, String employeeId) {
        ExaminationSamplePipe examinationSamplePipe = examinationSamplePipeRepository.findByChainIdAndIdAndIsDeleted(chainId, id, 0).orElse(null);
        if (Objects.isNull(examinationSamplePipe)) {
            throw new NotFoundException();
        }
        examinationSamplePipe.setIsDeleted(1);
        FillUtils.fillLastModifiedBy(examinationSamplePipe, employeeId);
        examinationSamplePipeRepository.save(examinationSamplePipe);
        /**
         * 清理Redis缓存
         * */
        GoodsUtils.runAfterTransaction(() -> goodsRedisUtils.clearChainExamPipeFromRedis(chainId));
        return examinationSamplePipeConverter.toView(examinationSamplePipe);
    }

    public ExaminationSamplePipeView updateExaminationSamplePipeById(String id, String chainId, String clinicId, String employeeId, ExaminationSamplePipeView examinationSamplePipeReq) {
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(clinicId);
        examinationSamplePipeReq.checkParam(clinicConfig);
        ExaminationSamplePipe examinationSamplePipe = examinationSamplePipeRepository.findByChainIdAndIdAndIsDeleted(chainId, id, 0).orElse(null);
        if (Objects.isNull(examinationSamplePipe)) {
            throw new NotFoundException();
        }
        Goods goods;
        if (StringUtils.isEmpty(examinationSamplePipe.getGoodsId())) {
            goods = examinationSamplePipeConverter.createSampleGoods(chainId, examinationSamplePipeReq.getName(), employeeId);
            examinationSamplePipe.setGoodsId(goods.getId());
            goodsRepository.save(goods);
        } else {
            goods = goodsRepository.findByIdAndOrganId(examinationSamplePipe.getGoodsId(), chainId).orElse(null);
            if (Objects.isNull(goods)) {
                throw new NotFoundException();
            }
        }

        /***
         *
         * */
        ExaminationSamplePipe existExaminationSamplePipe = examinationSamplePipeRepository.findByChainIdAndCodeAndIsDeleted(chainId, examinationSamplePipeReq.getCode(), 0).orElse(null);
        if (Objects.nonNull(existExaminationSamplePipe) && !Objects.equals(existExaminationSamplePipe.getId(), id)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_EXAM_SAMPLE_PIPE_CODE_REPEAT);
        }

        if (!GoodsUtils.compareStrEqual(goods.getName(), examinationSamplePipeReq.getName())) {
            goods.setName(examinationSamplePipeReq.getName());
            goods.setPy(GoodsUtils.fixGoodsPy(examinationSamplePipeReq.getName(), null));
            UserFillUtils.fillLastModifiedUserId(goods, employeeId);
        }
        List<GoodsComposeNaked> goodsComposeList = new ArrayList<>();
        if (examinationSamplePipe.getRelateGoodsFlag() == YesOrNo.YES) {
            goodsComposeList.addAll(goodsComposeNakedRepository.findAllByChainIdAndParentGoodsIdInAndComposeTypeAndIsDeleted(chainId, Collections.singletonList(examinationSamplePipe.getGoodsId()), GoodsComposeNaked.ComposeType.SAMPLE, YesOrNo.NO));
        }
        // 处理采样组关联goods
        doHandleSampleRelateGoods(chainId, employeeId, examinationSamplePipe, examinationSamplePipeReq.getItemList(), goodsComposeList, clinicConfig, goods);
        examinationSamplePipeConverter.copyProperties(examinationSamplePipe, examinationSamplePipeReq);
        FillUtils.fillLastModifiedBy(examinationSamplePipe, employeeId);
        examinationSamplePipeRepository.save(examinationSamplePipe);

        /**
         *事物提交 清理redis缓存
         * */
        GoodsUtils.runAfterTransaction(() -> {
            goodsRedisUtils.clearChainExamPipeFromRedis(chainId);
            goodsRedisUtils.clearGoodsRedisCache(chainId, Collections.singletonList(goods.getId()));
        });
        return examinationSamplePipeConverter.toView(examinationSamplePipe);
    }

    private void doHandleSampleRelateGoods(String chainId, String employeeId, ExaminationSamplePipe examinationSamplePipe,
                                           List<ExaminationSamplePipeView.SamplePipeRelatedGoods> samplePipeRelatedGoods,
                                           List<GoodsComposeNaked> goodsComposeList, ClinicConfig clinicConfig,
                                           Goods sampleGoods) {
        if (Objects.isNull(examinationSamplePipe)) {
            return;
        }
        if (examinationSamplePipe.getRelateGoodsFlag() == YesOrNo.NO && CollectionUtils.isEmpty(samplePipeRelatedGoods)) {
            return;
        }
        List<GoodsComposeNaked> updateList = new ArrayList<>();
        List<GoodsComposeNaked> deleteList = new ArrayList<>();
        BigDecimal packagePrice;
        if (CollectionUtils.isEmpty(samplePipeRelatedGoods)) {
            // 客户端没有已有的全删除
            goodsComposeList.forEach(naked -> {
                naked.setIsDeleted(YesOrNo.YES);
                UserFillUtils.fillLastModifiedUserId(naked, employeeId);
                updateList.add(naked);
            });
            examinationSamplePipe.setRelateGoodsFlag(YesOrNo.NO);
            packagePrice = BigDecimal.ZERO;
            sampleGoods.offComposeFlag(GoodsConst.ComposeFlag.HAS_COMPOSE_CHILDREN);
        } else {
            List<String> sampleGoodsIdList = samplePipeRelatedGoods.stream().map(ExaminationSamplePipeView.SamplePipeRelatedGoods::getGoodsId).distinct().collect(Collectors.toList());
            Map<String, GoodsRedisCache> goodsIdToRedisCache = goodsRedisUtils.getMapGoodsRedisCacheWithoutBatch(sampleGoodsIdList,
                    ChoosePharmacyHelper.ofSpecificPharmacy(GoodsConst.PharmacyType.LOCAL_PHARMACY, GoodsUtils.PHARMACY_NO_0), clinicConfig);
            Map<String, GoodsComposeNaked> goodsIdToCompose = goodsComposeList.stream().collect(Collectors.toMap(GoodsComposeNaked::getGoodsId, Function.identity(), (a, b) -> a));
            List<String> existGoodsIdList = new ArrayList<>();
            // 处理客户端请求的goods
            samplePipeRelatedGoods.forEach(req -> {
                GoodsRedisCache goodsRedisCache = goodsIdToRedisCache.get(req.getGoodsId());
                if (goodsRedisCache == null) {
                    throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "关联goods加载失败");
                }
                if (req.getComposeUseDismounting() == GoodsConst.DismountingStatus.DISMOUNTING && goodsRedisCache.getDismounting() == GoodsConst.DismountingStatus.PACKAGE) {
                    throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_IS_NOT_DISMOUNTING);
                }
                GoodsComposeNaked composeNaked = goodsIdToCompose.get(req.getGoodsId());
                if (composeNaked == null) {
                    composeNaked = examinationSamplePipeConverter.buildSampleGoodsCompose(chainId, examinationSamplePipe.getGoodsId(), employeeId, req, goodsRedisCache);
                } else {
                    examinationSamplePipeConverter.setSampleGoodsCount(composeNaked, req, goodsRedisCache);
                    existGoodsIdList.add(req.getGoodsId());
                }
                updateList.add(composeNaked);
            });
            // 库中是否不包含在客户端请求中，需删除
            goodsIdToCompose.forEach((goodsId, composeNaked) -> {
                if (!existGoodsIdList.contains(goodsId)) {
                    composeNaked.setIsDeleted(YesOrNo.YES);
                    UserFillUtils.fillLastModifiedUserId(composeNaked, employeeId);
                    deleteList.add(composeNaked);
                }
            });
            examinationSamplePipe.setRelateGoodsFlag(YesOrNo.YES);
            packagePrice = updateList.stream().map(GoodsComposeNaked::getComposePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (!sampleGoods.checkComposeFlag(GoodsConst.ComposeFlag.HAS_COMPOSE_CHILDREN)) {
                sampleGoods.onComposeFlag(GoodsConst.ComposeFlag.HAS_COMPOSE_CHILDREN);
            }
        }
        sampleGoods.setPackagePrice(packagePrice);
        if (!CollectionUtils.isEmpty(updateList)) {
            goodsComposeNakedRepository.saveAll(updateList);
        }
        if (!CollectionUtils.isEmpty(deleteList)) {
            goodsComposeNakedRepository.saveAll(deleteList);
        }
    }

    public ExaminationSamplePipeView getExaminationSamplePipeById(String id, String chainId, String clinicId) {
        return goodsRedisUtils.getExamPipeFromLocalByIds(chainId, Arrays.asList(id)).get(id);
    }

    public AbcListPage<ExaminationSamplePipeView> getExaminationSamplePipesByIds(QueryExamSamplePipeByIdsReq req) {
        String chainId = req.getChainId();
        List<Long> samplePipeIds = req.getSamplePipeIds();
        if (StringUtils.isEmpty(chainId) || CollectionUtils.isEmpty(samplePipeIds)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "chainId、samplePipeIds");
        }
        AbcListPage<ExaminationSamplePipeView> page = new AbcListPage<>();
        page.setRows(
                goodsRedisUtils.getExamPipeFromLocalByIds(req.getChainId(),
                        req.getSamplePipeIds()
                                .stream()
                                .filter(Objects::nonNull)
                                .map(id -> id.toString())
                                .collect(Collectors.toList()))
                        .values().stream().collect(Collectors.toList())
        );
        return page;
    }

    public List<ExaminationSampleSimpleView> getExaminationSampleSample(String chainId, String clinicId) {
        List<ExaminationSamplePipe> examSampleList = examinationSamplePipeRepository.findAllByChainIdAndIsDeleted(chainId, YesOrNo.NO);
        return examSampleList.stream().map(examSample -> {
            ExaminationSampleSimpleView view = new ExaminationSampleSimpleView();
            view.setId(examSample.getId());
            view.setName(examSample.getName());
            view.setCode(examSample.getCode());
            view.setStatus(examSample.getStatus());
            return view;
        }).collect(Collectors.toList());
    }

    public void disableExaminationSample(ClientExamDisableReq clientReq) {
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(clientReq.getClinicId());
        if (Objects.isNull(clinicConfig)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.GET_CLINIC_CONFIG_ERROR);
        }
        if (clinicConfig.isSubClinic()) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "不支持的操作");
        }
        ExaminationSamplePipe examinationSamplePipe = examinationSamplePipeRepository.findByChainIdAndIdAndIsDeleted(clientReq.getChainId(), clientReq.getId(), YesOrNo.NO).orElse(null);
        if (Objects.isNull(examinationSamplePipe)) {
            throw new NotFoundException();
        }
        Goods goods = goodsRepository.findByIdAndOrganId(examinationSamplePipe.getGoodsId(), clientReq.getChainId()).orElse(null);
        if (Objects.isNull(goods)) {
            throw new NotFoundException();
        }
        examinationSamplePipe.setStatus(clientReq.getDisableStatus());
        FillUtils.fillLastModifiedBy(examinationSamplePipe, clientReq.getEmployeeId());
        if (clientReq.getDisableStatus() == GoodsConst.GoodsStatus.OK) {
            goods.setStatus(GoodsConst.GoodsStatus.OK);
            goods.setDisable(GoodsConst.EnableStatus.ENABLE);
        } else {
            goods.setStatus(GoodsConst.GoodsStatus.NO_VISIBLE);
            goods.setDisable(GoodsConst.EnableStatus.DISABLE);
        }
        UserFillUtils.fillLastModifiedUserId(goods, clientReq.getEmployeeId());

        GoodsUtils.runAfterTransaction(() -> {
            goodsRedisUtils.clearGoodsRedisCache(clientReq.getChainId(), Collections.singletonList(goods.getId()));
            goodsRedisUtils.clearChainExamPipeFromRedis(clientReq.getChainId());
        });
    }
}
