package cn.abcyun.cis.goods.service.update.disablegoods;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.EmployeeBasic;
import cn.abcyun.bis.rpc.sdk.cis.model.registration.CheckEmployeeRegistrationFeeRelatedGoodsRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.registration.v2.RegistrationCategory;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.model.ClinicBasicFeeNameDisplay;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.model.Goods;
import cn.abcyun.cis.goods.vo.frontend.stockin.GoodsStockRowErrorDetail;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-04-22 13:55
 */
@Slf4j
@Service
public class FeeGoodsDisableService extends NonStockGoodsDisableService {

    @Override
    public void checkRelatedGoods(String goodsId, String chainId, String clinicId, int clinicType, int viewMode, Goods goods, ClinicConfig clinicConfig) {
        // 调挂号检查一下
        if (!clinicConfig.isHospital()) {
            return;
        }
        ClinicBasicFeeNameDisplay feeNameDisplay = propertyService.getPropertyValueByKey(PropertyKey.CLINIC_BASIC_FEE_NAME_DISPLAY, clinicId, ClinicBasicFeeNameDisplay.class);
        String formatMsg;
        if (feeNameDisplay != null && !StringUtils.isEmpty(feeNameDisplay.getRegistrationFee())) {
            formatMsg = feeNameDisplay.getRegistrationFee();
        } else {
            formatMsg = "挂号费";
        }
        List<CheckEmployeeRegistrationFeeRelatedGoodsRsp> checkEmployeeRegistrationFeeRelatedGoodsRsps = registrationService.checkChainEmployeeRegistrationFeeRelatedGoodsId(chainId, clinicId, clinicType, viewMode, goodsId);
        if (CollectionUtils.isNotEmpty(checkEmployeeRegistrationFeeRelatedGoodsRsps)) {
            GoodsStockRowErrorDetail errorDetail = new GoodsStockRowErrorDetail();
            errorDetail.setErrorTitle("请从这些项目里面关联项目/套餐移除掉再删除");
            AtomicInteger errorIndex = new AtomicInteger(0);
            checkEmployeeRegistrationFeeRelatedGoodsRsps.forEach(checkEmployeeRegistrationFeeRelatedGoodsRsp -> {
                List<CheckEmployeeRegistrationFeeRelatedGoodsRsp.RegistrationCategoryRelatedGoodsRsp> registrationCategoryRelatedRsps = checkEmployeeRegistrationFeeRelatedGoodsRsp.getRegistrationCategoryRelatedRsps();
                if (CollectionUtils.isEmpty(registrationCategoryRelatedRsps)) {
                    return;
                }
                registrationCategoryRelatedRsps.forEach(registrationCategoryRelatedRsp -> {
                    errorDetail.putGoodsStockRowErrorDetailItem(
                            errorIndex.get(),
                            MessageFormatter.format("{}" + formatMsg, RegistrationCategory.getDisplayName(registrationCategoryRelatedRsp.getRegistrationCategory())).getMessage(),
                            MessageFormatter.arrayFormat("{} - {}", new Object[]{checkEmployeeRegistrationFeeRelatedGoodsRsp.getClinicName(), Joiner.on("、").join(Optional.ofNullable(registrationCategoryRelatedRsp.getEmployees()).orElseGet(Collections::emptyList).stream().map(EmployeeBasic::getName).filter(StringUtils::isNotBlank).collect(Collectors.toList()))}).getMessage()
                    );
                    errorDetail.addErrMsg(errorIndex.get(), "组成关联项目");
                    errorIndex.incrementAndGet();
                });
            });
            // 查挂号费
            errorDetail.appendBusinessRelatedGoodsInfo(
                    GoodsStockRowErrorDetail.BusinessRelatedGoodsInfo.Business.REGISTRATION_FEE,
                    checkEmployeeRegistrationFeeRelatedGoodsRsps
            );
            errorDetail.checkAndThrowException();
            //throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "[" + goodsName + "]已经被挂号项目关联，不能删除");
        }
    }
}
