package cn.abcyun.cis.goods.service.query.batchprocessor;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.commons.util.FillUtils;
import cn.abcyun.cis.goods.consts.Constant;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.model.*;
import cn.abcyun.cis.goods.service.rpc.CisClinicService;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.utils.UserFillUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-15 09:06:54
 */
@Slf4j
public class GoodsTypeIdBatchModifyProcessor extends GoodsBatchModifyProcessor<GoodsSysType> {

    private final Map<String, List<GoodsStat>> goodsIdToGoodsStatList;
    private final Long modifyField;

    public GoodsTypeIdBatchModifyProcessor(String clinicId, List<Goods> goodsList, GoodsSysType modifyData,
                                           String employeeId, Map<String, List<GoodsStat>> goodsIdToGoodsStatList,
                                           Long modifyField) {
        super(clinicId, goodsList, modifyData, employeeId);
        this.goodsIdToGoodsStatList = goodsIdToGoodsStatList;
        this.modifyField = modifyField;
    }

    @Override
    protected boolean needModify(Goods goods) {
        return true;
    }

    @Override
    protected boolean doModify(Goods goods) {
        return true;
    }

    @Override
    public void doProcess(CisClinicService clinicService) {
        if (CollectionUtils.isEmpty(goodsList) || modifyData == null) {
            return;
        }
        Goods firstGoods = goodsList.get(0);
        Integer typeId = firstGoods.getTypeId();
        // 配方饮片或非配方饮片支持修改
        if (typeId != GoodsConst.GoodsTypeId.MEDICINE_CHINESE_PIECES_TYPEID && typeId != GoodsConst.GoodsTypeId.MEDICINE_NON_FORMULATED_DECOCTION_PIECES_TYPEID) {
            log.info("类型不支持修改,typeId={}", typeId);
            return;
        }
        boolean allSameTypeId = goodsList.stream().allMatch(it -> it.getTypeId() == typeId);
        if (!allSameTypeId) {
            log.info("类型不一致，不修改,typeId={}", typeId);
            return;
        }
        int newTypeId = modifyData.getId().intValue();
        String goodsCMSpec = modifyData.getGoodsCMSpec();
        if (typeId == newTypeId) {
            log.info("类型一致，不修改,typeId={}", typeId);
            return;
        }
        ClinicConfig clinicConfig = clinicService.getClinicConfig(clinicId);
        if (goodsLogList == null) {
            goodsLogList = new ArrayList<>();
        }
        for (Goods goods : goodsList) {
            this.successGoodsIds.add(goods.getId());
            GoodsSnapV3 beforeSnap = GoodsUtils.genClientGoodsSnap(goods);
            goods.setCMSpec(goodsCMSpec);
            goods.setMaterialSpec(goodsCMSpec);
            goods.setTypeId(newTypeId);
            // 换类型清空自定义分类 todo 经营范围是否修改？
            if (!GoodsUtils.checkFlagOn(modifyField.intValue(), Constant.BatchModifyFlag.CATEGORY_BY_ID)) {
                goods.setCustomTypeId(null);
                goods.setCustomTypeName(null);
            }
            UserFillUtils.fillLastModifiedUserId(goods, employeeId);
            List<GoodsStat> goodsStatList = goodsIdToGoodsStatList.get(goods.getId());
            if (!CollectionUtils.isEmpty(goodsStatList)) {
                goodsStatList.forEach(stat -> {
                    stat.setTypeId(newTypeId);
                    stat.setGoodsType(modifyData.getGoodsType());
                    stat.setGoodsSubType(modifyData.getGoodsSubType());
                });
            }
            GoodsLog updateGoodsLog = GoodsUtils.createUpdateGoodsLog(GoodsLog.ACTION_BATCH_MODIFY, beforeSnap, goods, null, clinicId,
                    employeeId, clinicConfig);
            goodsLogList.add(updateGoodsLog);
        }
    }
}
