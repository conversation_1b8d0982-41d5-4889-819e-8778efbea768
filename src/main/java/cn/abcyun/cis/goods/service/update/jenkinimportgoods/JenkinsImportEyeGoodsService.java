package cn.abcyun.cis.goods.service.update.jenkinimportgoods;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.commons.util.FillUtils;
import cn.abcyun.cis.goods.model.GoodsLog;
import cn.abcyun.cis.goods.model.GoodsSnapV3;
import cn.abcyun.cis.goods.model.GoodsSpuSpec;
import cn.abcyun.cis.goods.utils.AbcIdUtils;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.utils.UserFillUtils;
import cn.abcyun.cis.goods.utils.protocol.GoodsListProtocolFillUtils;
import cn.abcyun.cis.goods.vo.frontend.goodsinfo.spu.SpuSpecRange;
import cn.abcyun.cis.goods.vo.frontend.goodsupdate.ServerCreateEyeGoodsReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/***
 * 眼视光goods
 * */
@EqualsAndHashCode(callSuper = true)
@Data
public class JenkinsImportEyeGoodsService extends JenkinsImportGoodsServiceBase {

    /**
     * 新建Goods需要复写的类
     */
    @Override
    protected void fillRepoGoodsFiled() {
        doCreateOrUpdateSpuSpecImpl();
        doUpdateGoodsImpl(true);
    }

    /**
     * 检验项目只检查或启用本店的是否有机器能启用否
     */
    protected void updateGoods() {
        if (clinicConfig.isSubClinic()) {

        } else {
            GoodsSnapV3 goodsSnapBefore = new GoodsSnapV3();
            GoodsListProtocolFillUtils.genGoodsSnapV3(goodsSnapBefore, goods, GoodsUtils.isCompositeGoods(goods),clinicConfig);
            doCreateOrUpdateSpuSpecImpl();
            doUpdateGoodsImpl(false);
            /**
             * 修改Log
             * */
            doCreateGoodsLog();
            //Refix action  and snap shot
            goodsLog.setBefore(goodsSnapBefore);
            goodsLog.setAction(GoodsLog.ACTION_MODIFY);
        }
    }

    private void doCreateOrUpdateSpuSpecImpl() {

        ServerCreateEyeGoodsReq clientReq = (ServerCreateEyeGoodsReq) serverCreateGoodsAbstractReq;
        goodsSpuSpec = clientReq.getJenkinsGoodsImportItem().getGoodsSpuSpec();
        if (goodsSpuSpec == null) {
            goodsSpuSpec = new GoodsSpuSpec();
            Long goodsSpuSpecId = AbcIdUtils.getUUIDLong();
            goodsSpuSpec.setId(goodsSpuSpecId);
            goodsSpuSpec.setChainId(chainId);
            goodsSpuSpec.setSpuGoodsId(clientReq.getSpuGoodsId());
            goodsSpuSpec.setIsSku(1);
            FillUtils.fillCreatedBy(goodsSpuSpec, employeeId);
        }
        goodsSpuSpec.setType(clientReq.getSubType());
        goodsSpuSpec.setCustomType(clientReq.getCustomType());
        goodsSpuSpec.setGroupSpecId(clientReq.getGroupSpecId());
        SpuSpecRange first = clientReq.getFirst();
        SpuSpecRange second = clientReq.getSecond();
        goodsSpuSpec.setFirstStart(first != null ? first.getStart() : null);
        goodsSpuSpec.setFirstEnd(first != null ? first.getEnd() : null);
        goodsSpuSpec.setFirstStep(first != null ? first.getStep() : null);
        goodsSpuSpec.setSecondStart(second != null ? second.getStart() : null);
        goodsSpuSpec.setSecondEnd(second != null ? second.getEnd() : null);
        goodsSpuSpec.setSecondStep(second != null ? second.getStep() : null);
        goodsSpuSpec.setSort(clientReq.getSort());
        goodsSpuSpec.setMyopiaCombinedLuminosity(clientReq.getMyopiaCombinedLuminosity());
        goodsSpuSpec.setHyperopiaCombinedLuminosity(clientReq.getHyperopiaCombinedLuminosity());
        goodsSpuSpec.setGroupName(clientReq.getGroupName());
        goodsSpuSpec.setColor(clientReq.getColor());
        goodsSpuSpec.setSpec(clientReq.getSpec());
        goodsSpuSpec.setGoodsId(goods.getId());
        FillUtils.fillLastModifiedBy(goodsSpuSpec, employeeId);
        jenkinsGoodsImportReq.setGoodsSpuSpec(goodsSpuSpec);

    }

    private void doUpdateGoodsImpl(boolean createFlag) {

        ServerCreateEyeGoodsReq clientReq = (ServerCreateEyeGoodsReq) serverCreateGoodsAbstractReq;
        /**
         * ID 相关
         * */
        goods.setSpuGoodsId(clientReq.getSpuGoodsId());
        goods.setExtendSpecId(goodsSpuSpec.getId());
        goods.setGoodsSpuSpec(goodsSpuSpec);
        /**
         * 类型和规格相关
         * */
        goods.setType(clientReq.getType());
        goods.setSubType(clientReq.getSubType());
        goods.setTypeId(clientReq.getTypeId());
        goods.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        goods.setName(clientReq.getName());


        /**
         * 价格和进销税等,治疗理疗有成本价
         * */
        if (clientReq.getDismounting() != null) {
            goods.setDismounting(clientReq.getDismounting().shortValue());
        }
        goods.setPiecePrice(clientReq.getPackagePrice());
        goods.setPackagePrice(clientReq.getPackagePrice());
        goods.setPieceNum(clientReq.getPieceNum());
        goods.setPieceUnit(clientReq.getPieceUnit() != null ? clientReq.getPieceUnit() : clientReq.getPackageUnit());
        goods.setPackageUnit(clientReq.getPackageUnit());
        goods.setPackageCostPrice(clientReq.getPackageCostPrice());

        goods.setBarCode(clientReq.getBarCode());
        goods.setRemark(clientReq.getRemark());
        goods.setProcessPrice(clientReq.getProcessPrice());
        goods.setManufacturerFull(clientReq.getManufacturerFull());
        goods.setManufacturer(clientReq.getManufacturer());
        goods.setManufacturerPy(GoodsUtils.fixGoodsPy(goods.getManufacturerFull(), null));
        if (createFlag || ( StringUtils.isNotBlank(clientReq.getShortId()) && !Objects.equals(goods.getShortId(), clientReq.getShortId())) ){
            isUseShortIdClientSpecify = true;
        }
        goods.setShortId(clientReq.getShortId());

        /**
         * 设置goods的进销税
         */
        goods.setDefaultInOutTax(clientReq.getDefaultInOutTax());
        goods.setInTaxRat(clientReq.getInTaxRat());
        goods.setOutTaxRat(clientReq.getOutTaxRat());
        goods.setMedicineNmpn(clientReq.getMedicineNmpn());


        /**
         * 默认值,中药初始化的潜规则
         * */
        if (clientReq.getIsSell() != null) {
            goods.setIsSell(clientReq.getIsSell().shortValue()); //强制对外销售
        }
        if (createFlag) {
            // 设置启用状态
            if (clientReq.getV2DisableStatus() != null && clientReq.getV2DisableStatus() != GoodsUtils.GoodsV2DisableStatus.ENABLE) {
                goods.setDisable(GoodsConst.EnableStatus.DISABLE);
                goods.setInorderConfig(GoodsUtils.GoodsV2DisableStatus.DISABLE_INORDER_ONLY_AND_GOON_SELL);
                goods.setSellConfig(GoodsUtils.GoodsV2DisableStatus.DISABLE_INORDER_AND_SELL);
            }
        }
        UserFillUtils.fillLastModifiedUserId(goods, employeeId);
        goods.setGoodsSpuSpec(goodsSpuSpec);

        if (clinicConfig.getHisType() != Organ.HisType.CIS_HIS_TYPE_HOSPITAL) {
            /**
             * 产品定制
             * */
            goods.setFeeTypeId(GoodsUtils.fixClinicFeeTypeId(goods.getType(), goods.getSubType(), goods.getTypeId()));
        } else {
            goods.setFeeTypeId(clientReq.getFeeTypeId());
        }
        goods.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);
        if(!createFlag ){
            goods.setGoodsVersion( goods.getGoodsVersion() + 1);
        }
    }
}
