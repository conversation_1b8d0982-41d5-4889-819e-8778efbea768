package cn.abcyun.cis.goods.service.update;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.model.Goods;
import cn.abcyun.cis.goods.service.update.modgoods.GoodsUpdateComposeService;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.vo.frontend.goodsupdate.ServerCreateSurgeryGoodsReq;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Service
public class GoodsUpdateSurgeryGoodsService extends GoodsUpdateComposeService {

    @Override
    protected boolean mainUpdateRepoGoodsFiled() {
        ServerCreateSurgeryGoodsReq clientReq = (ServerCreateSurgeryGoodsReq) serverCreateGoodsAbstractReq;
        boolean changeFiled = super.mainUpdateRepoGoodsFiled();

        /**
         * 类型和规格相关
         * */
        // 不容许进行类型的变化
        if (goods.getType() != clientReq.getType()) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "其他商品不能变更类型");
        }
        if (goods.getSubType() != clientReq.getSubType()) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "其他商品不能变更类型");
        }
        if (!GoodsUtils.compareIntegerEqual(goods.getTypeId(), clientReq.getTypeId())) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "其他商品不能变更类型");
        }
        if (!GoodsUtils.compareLongEqual(goods.getCustomTypeId(), clientReq.getCustomTypeId())) {
            changeFiled = true;
            goods.setCustomTypeId(clientReq.getCustomTypeId());
        }
        /**
         * 名字和供应商相关
         * */
        if (!GoodsUtils.compareStrEqual(goods.getName(), clientReq.getName())) {
            changeFiled = true;
            goods.setName(clientReq.getName());
        }
        /**
         * 价格和进销税等,治疗理疗有成本价
         * */
        if (!GoodsUtils.comparePriceEqual(goods.getPackagePrice(), clientReq.getPackagePrice())) {
            changeFiled = true;
            goods.setPackagePrice(clientReq.getPackagePrice());
        }
        if (!GoodsUtils.compareIntegerEqual(goods.getPieceNum(), clientReq.getPieceNum())) {
            changeFiled = true;
            goods.setPieceNum(clientReq.getPieceNum());
        }
        if (!GoodsUtils.compareStrEqual(goods.getPackageUnit(), clientReq.getPackageUnit())) {
            changeFiled = true;
            goods.setPackageUnit(clientReq.getPackageUnit());
        }
        if (!GoodsUtils.comparePriceEqual(goods.getPackageCostPrice(), clientReq.getPackageCostPrice())) {
            changeFiled = true;
            goods.setPackageCostPrice(clientReq.getPackageCostPrice());
        }

        if (!CollectionUtils.isEmpty(clientReq.getChildren())) {
            goods.setCombineType(GoodsConst.GoodsCombine.COMBINE);
        }
        return changeFiled;
    }

    protected void createChildGoodsIfNotExist(Map<String, Goods> childGoodsIdToExistGoods) {
        if (!clinicConfig.isHeadClinic()) {
            return;
        }

        loadExistSuguryInnerGoodsByNameAndCreatedIfNotExisted(childGoodsIdToExistGoods);

        if (clinicConfig.isHospital()) {
            return;
        }


        loadExistChildInnerFeeGoodsByNameAndCreatedIfNotExists(childGoodsIdToExistGoods);
    }

    protected List<Goods> getGoodsSpecificGoodsList() {
        if (!(serverCreateGoodsAbstractReq instanceof ServerCreateSurgeryGoodsReq)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER);
        }
        ServerCreateSurgeryGoodsReq clientReq = (ServerCreateSurgeryGoodsReq) serverCreateGoodsAbstractReq;
        /**
         * 把所有这个查询条件规格的药品全部捞出来
         * */
        return goodsRepository.findAllByOrganIdAndTypeAndPackageUnitAndStatusLessThanAndName(
                chainId,
                clientReq.getType(),
                clientReq.getPackageUnit(),
                GoodsConst.GoodsStatus.NO_VISIBLE,
                clientReq.getName());
    }
}
