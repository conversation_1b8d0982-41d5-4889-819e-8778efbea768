package cn.abcyun.cis.goods.service.update.newgoods;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.model.Goods;
import cn.abcyun.cis.goods.vo.frontend.goodsupdate.ServerCreateComposeGoodsReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 套餐创建
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GoodsCreateGoodsComposeService extends GoodsCreateComposeService {
    private static final Logger sLogger = LoggerFactory.getLogger(GoodsCreateGoodsComposeService.class);

    /**
     * 规格检查
     * 前置：客户端请求 serverCreateGoodsAbstractReq
     * 后置: serverCreateGoodsAbstractReq 是一个符合要求的请求
     * 如果req指定了goodsId ，会把service里面的goods加载好
     */
    protected List<Goods> getGoodsSpecificGoodsList() {
        if (!(serverCreateGoodsAbstractReq instanceof ServerCreateComposeGoodsReq)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER);
        }
        ServerCreateComposeGoodsReq clientReq = (ServerCreateComposeGoodsReq) serverCreateGoodsAbstractReq;

        /**
         * 把所有这个查询条件规格的药品全部捞出来
         * */
        return goodsRepository.findAllByOrganIdAndTypeAndPackageUnitAndStatusLessThanAndName(
                chainId,
                clientReq.getType(),
                clientReq.getPackageUnit(),
                GoodsConst.GoodsStatus.NO_VISIBLE,
                clientReq.getName());
    }

    /**
     * 新建Goods需要复写的类
     */
    @Override
    protected void fillRepoGoodsFiled() {
        if (!(serverCreateGoodsAbstractReq instanceof ServerCreateComposeGoodsReq)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER);
        }
        ServerCreateComposeGoodsReq clientReq = (ServerCreateComposeGoodsReq) serverCreateGoodsAbstractReq;

        /**
         * ID 相关
         * */

        /**
         * 类型和规格相关
         * */
        goods.setType((short) clientReq.getType());
        goods.setSubType((short) clientReq.getSubType());
        goods.setTypeId(clientReq.getTypeId());
        goods.setCustomTypeId(clientReq.getCustomTypeId());

        /**
         * 名字和供应商相关
         * */
        goods.setName(clientReq.getName());

        /**
         * 价格和进销税等,治疗理疗有成本价
         * */
        if (clientReq.getDismounting() != null){
            goods.setDismounting(clientReq.getDismounting().shortValue());
        }
        goods.setPieceNum(clientReq.getPieceNum());
        goods.setPieceUnit(clientReq.getPieceUnit());
        goods.setPackageUnit(clientReq.getPackageUnit());

        /**
         * 默认值,中药初始化的潜规则
         * */
        if (clientReq.getIsSell() != null) {
            goods.setIsSell(clientReq.getIsSell().shortValue());
        }
        goods.setCombineType(clientReq.getCombineType());
        // 性别和适用人群
        if (clientReq.getGender() != null) {
            goods.setGender(clientReq.getGender());
        }
        if (clientReq.getApplyPopulation() != null) {
            goods.setApplyPopulation(clientReq.getApplyPopulation());
        }
        /**
         * 放到最后，改价
         * */
        super.fillRepoGoodsFiled();
    }
}
