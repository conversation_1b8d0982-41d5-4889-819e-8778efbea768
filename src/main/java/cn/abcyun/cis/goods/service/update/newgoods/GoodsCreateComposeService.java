package cn.abcyun.cis.goods.service.update.newgoods;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsSheBaoMatchedCodesInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.ShebaoPayLimitPriceRule;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.GetClinicsShebaoMatchedCodesRsp;
import cn.abcyun.cis.commons.amqp.message.goods.GoodsBroadMessage;
import cn.abcyun.cis.commons.amqp.message.goods.GoodsUpdateMessage;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.goods.cache.redis.ChoosePharmacyHelper;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisCache;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisUtils;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.domain.GoodsStatManager;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.model.*;
import cn.abcyun.cis.goods.utils.*;
import cn.abcyun.cis.goods.utils.protocol.GoodsListProtocolFillUtils;
import cn.abcyun.cis.goods.vo.frontend.goodsupdate.ServerCreateComposeChildrenBase;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 组合类型创建的基类
 * 子类有：检查检验 套餐 护理医嘱
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GoodsCreateComposeService extends GoodsCreateServiceBase {
    private static final Logger sLogger = LoggerFactory.getLogger(GoodsCreateComposeService.class);


    /**
     * 检查套餐是否符合要求
     * 套餐这里只是进行一般有效性的检查
     * <p>
     * TODO  robins 1总部对码同步到子店 这里要判断 serverCreateGoodsAbstractReq 是总部的码还是子店的码
     * TODO  robins 1总部对码且用户没改过，不能给子店捡出来费用项Goods
     */
    @Override
    protected void loadOrCreateComposeChildGoods() {
        // 套餐一定是组合类型
        if (!GoodsUtils.isCompositeGoods(serverCreateGoodsAbstractReq.getCombineType()) && !GoodsUtils.isFeeCompositeGoods(serverCreateGoodsAbstractReq.getFeeComposeType())) {
            return;
        }

        // 把子项的GoodsId加载出来
        Set<String> childGoodsIdSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getChildren())) {
            childGoodsIdSet.addAll(serverCreateGoodsAbstractReq.getChildren().stream().map(ServerCreateComposeChildrenBase::getId).filter(it -> !StringUtils.isEmpty(it)).collect(Collectors.toSet()));
        }
        if (!CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getFeeComposeList())) {
            childGoodsIdSet.addAll(serverCreateGoodsAbstractReq.getFeeComposeList().stream().map(ServerCreateComposeChildrenBase::getId).filter(it -> !StringUtils.isEmpty(it)).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getAssociationComposeList())) {
            childGoodsIdSet.addAll(serverCreateGoodsAbstractReq.getAssociationComposeList().stream().map(ServerCreateComposeChildrenBase::getId).filter(it -> !StringUtils.isEmpty(it)).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getGoodsGroupComposeList())) {
            childGoodsIdSet.addAll(serverCreateGoodsAbstractReq.getGoodsGroupComposeList().stream().map(ServerCreateComposeChildrenBase::getId).filter(it -> !StringUtils.isEmpty(it)).collect(Collectors.toList()));
        }
        Map<String, Goods> childGoodsIdToGoods = new HashMap<>();
        //非库存商品的成本价
        Map<String, GoodsRedisCache> childGoodsIdToGoodsRedisCache = new HashMap<>();
        //没有子项
        if (!childGoodsIdSet.isEmpty()) {

            //加载子项Goods
            List<Goods> childGoodsList = goodsRepository.findAllByIdInAndOrganIdAndStatus(new ArrayList<>(childGoodsIdSet), chainId, GoodsConst.GoodsStatus.OK);
            if (CollectionUtils.isEmpty(childGoodsList) || childGoodsList.size() < childGoodsIdSet.size()) {
                sLogger.error("套餐子项goods加载异常,加载的goos数量比goodsId少={}", JsonUtils.dump(childGoodsList));
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_NOT_FOUND, "套餐子项的Goods加载失败");
            }

            /*
             * 检查药品是否被停用
             * */
            for (Goods goods : childGoodsList) {
                if (GoodsUtils.isEyeGoods(goods.getType())) {
                    throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, GoodsUtils.goodsFullName(goods) + "是眼镜类型，不能加入套餐");
                }
                // 体检套餐只能加入体检子项
                if (serverCreateGoodsAbstractReq.getType() == GoodsConst.GoodsType.PHYSICAL_EXAMINATION_COMPOSE && goods.getType() != GoodsConst.GoodsType.PHYSICAL_EXAMINATION) {
                    throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "体检套餐只能加入体检的子项");
                }
                if (goods.getChainDisable() == GoodsConst.EnableStatus.DISABLE) {
                    sLogger.error("套餐子项goods被禁用={}", JsonUtils.dump(goods));
                    throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_IS_DISABLE, GoodsUtils.goodsFullName(goods) + "已经被禁用,不能加入套餐");
                }
                if (GoodsUtils.isStockGoods(goods.getType()) && GoodsUtils.goodsV2DisableStatus(goods.getChainInorderConfig(), goods.getChainSellConfig()) != GoodsUtils.GoodsV2DisableStatus.ENABLE) {
                    sLogger.error("套餐子项goods被禁用={}", JsonUtils.dump(goods));
                    throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_IS_DISABLE, GoodsUtils.goodsFullName(goods) + "已经被禁用,不能加入套餐");
                }
            }
            //组织成map
            childGoodsIdToGoods.putAll(childGoodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (a, b) -> a)));
            /*
             *  【诊所管家】【项目对码的收费项】
             *  需要把已经存在的收费子项的GoodsExtend加载出来
             ******************************************************************/
            GoodsStatManager.call("Ext_B_5");
            childFeeGoodsExtendList.addAll(goodsExtendRepository.findAllByGoodsIdInAndOrganIdIn(
                    new ArrayList<>(childGoodsIdSet),
                    Collections.singletonList(chainId)));

            if (!childFeeGoodsExtendList.isEmpty()) {
                childFeeGoodsIdToGoodsExtend.putAll(childFeeGoodsExtendList.stream().collect(Collectors.toMap(GoodsExtend::getGoodsId, Function.identity(), (a, b) -> a)));
            }
            /*
             * 把套餐子项的goodsRedisCache加载下，用处 后去成本
             * */
            if (childGoodsList.stream().anyMatch(it -> GoodsUtils.isStockGoods(it.getType()))) {
                childGoodsIdToGoodsRedisCache.putAll(goodsRedisUtils.getMapGoodsRedisCacheWithoutBatch(childGoodsList.stream().filter(GoodsUtils::isStockGoods).map(Goods::getId).collect(Collectors.toList()),
                        ChoosePharmacyHelper.ofSpecificPharmacy(GoodsConst.PharmacyType.LOCAL_PHARMACY, GoodsUtils.PHARMACY_NO_0),//TODO  这里取那个药房的成本价？？找产品定
                        clinicConfig));
            }
        }

        goodsComposeSubChildrenBindInfo(serverCreateGoodsAbstractReq.getChildren());

        //如果有的场景 需要给child提前建Goods可以到这里批量建，建好了加到childGoodsIdToGoods里面出来
        createChildGoodsIfNotExist(childGoodsIdToGoods);


        // 把子项和母项的Goods 设置进去
        if (!CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getChildren())) {
            for (ServerCreateComposeChildrenBase child : serverCreateGoodsAbstractReq.getChildren()) {
                GoodsRedisCache childGoodsRedisCache = StringUtils.isEmpty(child.getId()) ? null : childGoodsIdToGoodsRedisCache.get(child.getId());
                initGoodsComposeSubChildren(childGoodsIdToGoods.get(child.getId()), childGoodsRedisCache, child);
            }
        }
        if (!CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getFeeComposeList())) {
            for (ServerCreateComposeChildrenBase child : serverCreateGoodsAbstractReq.getFeeComposeList()) {
                GoodsRedisCache childGoodsRedisCache = StringUtils.isEmpty(child.getId()) ? null : childGoodsIdToGoodsRedisCache.get(child.getId());
                initGoodsComposeSubChildren(childGoodsIdToGoods.get(child.getId()), childGoodsRedisCache, child);
            }
        }
        if (!CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getAssociationComposeList())) {
            for (ServerCreateComposeChildrenBase child : serverCreateGoodsAbstractReq.getAssociationComposeList()) {
                GoodsRedisCache childGoodsRedisCache = StringUtils.isEmpty(child.getId()) ? null : childGoodsIdToGoodsRedisCache.get(child.getId());
                initGoodsComposeSubChildren(childGoodsIdToGoods.get(child.getId()), childGoodsRedisCache, child);
            }
        }
        if (!CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getGoodsGroupComposeList())) {
            for (ServerCreateComposeChildrenBase child : serverCreateGoodsAbstractReq.getGoodsGroupComposeList()) {
                GoodsRedisCache childGoodsRedisCache = StringUtils.isEmpty(child.getId()) ? null : childGoodsIdToGoodsRedisCache.get(child.getId());
                initGoodsComposeSubChildren(childGoodsIdToGoods.get(child.getId()), childGoodsRedisCache, child);
            }
        }
    }

    /**
     * children绑定信息-子类自己实现
     *
     * @param children 孩子
     */
    protected void goodsComposeSubChildrenBindInfo(List<ServerCreateComposeChildrenBase> children) {

    }

    /**
     * 给套餐或费用项子项初始化 子Goods
     */
    protected void initGoodsComposeSubChildren(Goods childGoods, GoodsRedisCache childGoodsRedisCache, ServerCreateComposeChildrenBase child) {

        /*
         * 不能自己加自己
         * */
        if (!StringUtils.isEmpty(child.getId()) && goods != null && child.getId().compareTo(goods.getId()) == 0) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "不能自己把自己加到组合项目里面");
        }
        child.bindChainId(chainId, clinicId, employeeId, child.getType(), child.getSubType(), child.getTypeId());
        child.bindRepository(
                goodsRedisUtils,
                goodsRepository,
                goodsShortIdRepository,
                goodsLogRepository,
                goodsHistoryVersionRepository,
                goodsMapper,
                mqProducer);
        child.parameterCheck(childGoods, goods, childGoodsRedisCache);
    }

    /**
     * 如果有的场景 需要给child提前建Goods可以到这里批量建，建好了加到childGoodsIdToGoods里面出来
     */
    protected void createChildGoodsIfNotExist(Map<String, Goods> childGoodsIdToExistGoods) {

    }

    protected Goods createChildComposeGoods(Map<String, Goods> childGoodsIdToExistGoods, short type, short subType, Integer typeId, String extendSpec, ServerCreateComposeChildrenBase clientCreateItem) {
        Goods newChildGoods = new Goods();
        newChildGoods.setPriceType(GoodsConst.PriceType.PRICE);
        newChildGoods.setId(AbcIdUtils.getUUID());
        newChildGoods.setShortId(GoodsShortIdUtils.shortIdReadable(chainId, goodsShortIdRepository,
                goodsChainConfigRepository.findByChainId(chainId).map(GoodsChainConfig::getAdditionalConfig).map(ChainAdditionalConfig::getShortIdRuleConfig).orElse(null)));
        newChildGoods.setEsNeedSyncFlag(GoodsUtils.SwitchFlag.ON);
        newChildGoods.setIsSell(GoodsConst.SellStatus.NO_SELL);
        newChildGoods.setType(type);
        newChildGoods.setSubType(subType);
        newChildGoods.setTypeId(typeId);
        newChildGoods.setFeeTypeId(typeId);
        newChildGoods.setExtendSpec(extendSpec);
        newChildGoods.setStatus(GoodsConst.GoodsStatus.OK);
        newChildGoods.setOrganId(chainId);
        newChildGoods.setCombineType(GoodsConst.GoodsCombine.NO_COMBINE);
        newChildGoods.setPy(PinyinUtils.genSearchText(clientCreateItem.getName(), clientCreateItem.getEnName()));
        newChildGoods.setName(clientCreateItem.getName());
        newChildGoods.setEnName(clientCreateItem.getEnName());
        /*
         * 子项目也要写入设备ID
         * */
        newChildGoods.setBizRelevantId(clientCreateItem.getBizRelevantId());

        newChildGoods.setPackagePrice(clientCreateItem.getPackagePrice());
        newChildGoods.setPackageCostPrice(clientCreateItem.getPackageCostPrice());
        /*
         * 默认整个次进去
         * */
        newChildGoods.setDismounting(GoodsConst.DismountingStatus.PACKAGE);
        newChildGoods.setPackageUnit(StringUtils.isEmpty(clientCreateItem.getPackageUnit()) ? "次" : clientCreateItem.getPackageUnit());
        newChildGoods.setPieceUnit(clientCreateItem.getPieceUnit());
        newChildGoods.setManufacturer(getGoodsManufacturer(clientCreateItem.getManufacturerFull()));
        newChildGoods.setManufacturerFull(clientCreateItem.getManufacturerFull());
        newChildGoods.setMaterialSpec(clientCreateItem.getMaterialSpec());
        newChildGoods.setPieceNum(ObjectUtils.defaultIfNull(clientCreateItem.getPieceNum(), 1));
        UserFillUtils.fillCreatedUserId(newChildGoods, employeeId);
        /*
         * 模版Goods过来的要也模版设置为准
         * */
        if (clientCreateItem.getIsSell() != null) {
            newChildGoods.setIsSell(clientCreateItem.getIsSell().shortValue());
        } else {
            newChildGoods.setIsSell(GoodsConst.SellStatus.NO_SELL);
        }
        newChildGoods.setBizExtensions(clientCreateItem.getBizExtensions());
        newChildGoods.setDeviceType(clientCreateItem.getDeviceType());
        newCreateChildGoodsList.add(newChildGoods);
        GoodsUpdateMessage message = new GoodsUpdateMessage();
        GoodsBroadMessage broadMessage = new GoodsBroadMessage();
        BeanUtils.copyProperties(newChildGoods, broadMessage);
        broadMessage.setGoodsId(newChildGoods.getId());
        broadMessage.setNationalCode(clientCreateItem.getNationalCode());
        broadMessage.setNationalCodeId(clientCreateItem.getNationalCodeId());
        broadMessage.setChainId(newChildGoods.getOrganId());
        broadMessage.setClinicId(clinicId);

        message.setType(GoodsUpdateMessage.MSG_TYPE_GOODS_CREATED);
        message.setGoods(broadMessage);
        message.setGoodsId(newChildGoods.getId());
        newCreateGoodsUpdateMessageList.add(message);
        GoodsExtend extend = GoodsUtils.createGoodsExtend(newChildGoods.getId(), clinicConfig, employeeId);
        extend.setShebaoCodeNationalCode(clientCreateItem.getNationalCode());
        extend.setMedicalFeeGradeNational(clientCreateItem.getMedicalFeeGrade());
        extend.setShebaoCodeNationalCurrentPriceLimited(clientCreateItem.getPriceLimit());
        //费用项对码这里对不出DISABLE，可以认为就是一对码
        if (!StringUtils.isEmpty(clientCreateItem.getNationalCode())) {
            extend.setShebaoCodeNationalMatchedStatus((short) SheBaoUtils.SheBaoStatus.MATCH);
        } else {
            extend.setShebaoCodeNationalMatchedStatus((short) SheBaoUtils.SheBaoStatus.NOT_MATCH);
        }
        childComposeGoodsExtendList.add(extend);

        //单项 所有子店都要看到，全部生成
        needReFlushGoodsIdList.add(newChildGoods.getId());
        GoodsLog goodsLog = new GoodsLog();
        goodsLog.setGoodsId(newChildGoods.getId()); //       varchar(32) null comment '商品id',
        goodsLog.setAction(GoodsLog.ACTION_CREATE);//         varchar(20) null comment '动作：创建、修改、删除、子店定价',
        goodsLog.setOrganId(chainId);//        varchar(32) null comment '门店id',
        goodsLog.setBefore(null);
        goodsLog.setAfter(GoodsUtils.genGoodsDbSnap(newChildGoods));
        goodsLog.setPieceNum(newChildGoods.getPieceNum());
        goodsLog.setCreatedUserId(employeeId);
        goodsLog.setCreatedDate(Instant.now());
        newGoodsLogList.add(goodsLog);
        GoodsHistoryVersion goodsHistoryVersion = new GoodsHistoryVersion();
        goodsHistoryVersion.setChainId(chainId);
        goodsHistoryVersion.setGoodsVersion(newChildGoods.getGoodsVersion());
        goodsHistoryVersion.setGoodsId(newChildGoods.getId());
        GoodsSnapV3 goodsSnap = new GoodsSnapV3();
        goodsHistoryVersion.setGoods(GoodsListProtocolFillUtils.genGoodsSnapV3(goodsSnap, newChildGoods, GoodsUtils.isCompositeGoods(newChildGoods), clinicConfig));
        goodsHistoryVersion.fixGoodsCustomTypeName(goodsRedisUtils);
        goodsHistoryVersion.setId(AbcIdUtils.getUUIDLong());
        goodsHistoryVersion.setCreated(Instant.now());
        goodsHistoryVersion.setCreatedBy(employeeId);
        newHistoryVersionList.add(goodsHistoryVersion);
        clientCreateItem.setId(newChildGoods.getId());
        clientCreateItem.setChildGoods(newChildGoods);

        childGoodsIdToExistGoods.put(newChildGoods.getId(), newChildGoods);
        return newChildGoods;
    }


    /***
     * 新建内置费用Goods
     * */
    public static Goods createInnerFeeGoods(String chainId,
                                            ClinicConfig clinicConfig,
                                            String employeeId,
                                            List<Goods> newCreateChildGoodsList,
                                            List<GoodsExtend> childFeeGoodsExtendList,
                                            Map<String, GoodsExtend> childFeeGoodsIdToGoodsExtend,
                                            List<String> needReFlushGoodsIdList,
                                            List<GoodsUpdateMessage> newCreateGoodsUpdateMessageList,
                                            List<GoodsLog> newGoodsLogList,
                                            List<GoodsHistoryVersion> newHistoryVersionList,
                                            GoodsRedisUtils goodsRedisUtils,
                                            String name, String enName, String packageUnit,
                                            JsonNode bizExtensions, Integer deviceType,
                                            String nationalCode, String nationalCodeId, Integer matchMode,
                                            String centralCode,
                                            BigDecimal pacakgePrice, BigDecimal packageCostPrice,
                                            Integer shebaoPayMode,
                                            Integer medicalFeeGrade,
                                            BigDecimal priceLimit,
                                            Map<String, BigDecimal> nationalSelfPayProp,
                                            ShebaoPayLimitPriceRule shebaoPayLimitPriceRule
    ) {
        Goods newChildGoods = new Goods();
        newChildGoods.setPriceType(GoodsConst.PriceType.PRICE);
        newChildGoods.setId(AbcIdUtils.getUUID());
        newChildGoods.setShortId(AbcIdUtils.getUID());
        newChildGoods.setEsNeedSyncFlag(GoodsUtils.SwitchFlag.ON);
        newChildGoods.setIsSell(GoodsConst.SellStatus.NO_SELL);
        newChildGoods.setType(GoodsConst.GoodsType.OTHER_GOODS);
        newChildGoods.setInnerFlag(GoodsUtils.SwitchFlag.ON); //这个goods不可见
        newChildGoods.setSubType(GoodsConst.GoodsOtherGoodsSubType.OTHER);
        newChildGoods.setTypeId(GoodsConst.GoodsTypeId.OTHER_GOODS_TYPEID);
        newChildGoods.setStatus(GoodsConst.GoodsStatus.OK);
        newChildGoods.setOrganId(chainId);
        newChildGoods.setCombineType(GoodsConst.GoodsCombine.NO_COMBINE);
        newChildGoods.setPy(PinyinUtils.genSearchText(name, enName));
        newChildGoods.setName(name);
        newChildGoods.setEnName(enName);
        newChildGoods.setPackagePrice(pacakgePrice);
        newChildGoods.setPackageCostPrice(packageCostPrice);
        newChildGoods.setPriceType(GoodsConst.PriceType.PRICE);
        /*
         * 默认整个 次进去
         * */
        newChildGoods.setDismounting(GoodsConst.DismountingStatus.PACKAGE);
        newChildGoods.setPackageUnit("次");
        if (!StringUtils.isEmpty(packageUnit)) {
            newChildGoods.setPackageUnit(packageUnit);
            if (packageUnit.length() > 10) {
                newChildGoods.setPackageUnit(packageUnit.substring(0, 10));
            }
        }
        UserFillUtils.fillCreatedUserId(newChildGoods, employeeId);
        newChildGoods.setIsSell(GoodsConst.SellStatus.NO_SELL);
        newChildGoods.setBizExtensions(bizExtensions);
        newChildGoods.setDeviceType(deviceType);
        newCreateChildGoodsList.add(newChildGoods);


        //Extend ... 单店建到自己的extend里面
        GoodsExtend extend = GoodsUtils.createGoodsExtend(newChildGoods.getId(), clinicConfig, employeeId);
        setGoodsExtendShebaoInfo(extend,
                nationalCode,
                nationalCodeId,
                centralCode,
                matchMode,
                shebaoPayMode,
                medicalFeeGrade,
                priceLimit,
                nationalSelfPayProp,
                shebaoPayLimitPriceRule);
        childFeeGoodsExtendList.add(extend);

        //TODO 这里是门店的收费项
        childFeeGoodsIdToGoodsExtend.put(extend.getGoodsId(), extend);
        //把所有门店都刷出来
        needReFlushGoodsIdList.add(newChildGoods.getId());
        GoodsUpdateMessage message = new GoodsUpdateMessage();
        GoodsBroadMessage broadMessage = new GoodsBroadMessage();
        BeanUtils.copyProperties(newChildGoods, broadMessage);
        broadMessage.setGoodsId(newChildGoods.getId());
        broadMessage.setNationalCode(nationalCode);
        broadMessage.setNationalCodeId(nationalCodeId);
        broadMessage.setCentralCode(centralCode);
        broadMessage.setMatchMode(matchMode);
        broadMessage.setChainId(newChildGoods.getOrganId());
        broadMessage.setClinicId(clinicConfig.getClinicId());

        message.setType(GoodsUpdateMessage.MSG_TYPE_GOODS_CREATED);
        message.setGoods(broadMessage);
        message.setGoodsId(newChildGoods.getId());
        newCreateGoodsUpdateMessageList.add(message);
        GoodsLog goodsLog = new GoodsLog();
        goodsLog.setGoodsId(newChildGoods.getId()); //       varchar(32) null comment '商品id',
        goodsLog.setAction(GoodsLog.ACTION_CREATE);//         varchar(20) null comment '动作：创建、修改、删除、子店定价',
        goodsLog.setOrganId(chainId);//        varchar(32) null comment '门店id',
        goodsLog.setBefore(null);
        goodsLog.setAfter(GoodsUtils.genGoodsDbSnap(newChildGoods));
        goodsLog.getAfter().setShebaoCode(nationalCode);
        goodsLog.getAfter().setShebaoCodeId(nationalCodeId);
        goodsLog.setPieceNum(newChildGoods.getPieceNum());
        goodsLog.setCreatedUserId(employeeId);
        goodsLog.setCreatedDate(Instant.now());
        newGoodsLogList.add(goodsLog);
        GoodsHistoryVersion goodsHistoryVersion = new GoodsHistoryVersion();
        goodsHistoryVersion.setChainId(chainId);
        goodsHistoryVersion.setGoodsVersion(newChildGoods.getGoodsVersion());
        goodsHistoryVersion.setGoodsId(newChildGoods.getId());
        GoodsSnapV3 goodsSnap = new GoodsSnapV3();
        goodsHistoryVersion.setGoods(GoodsListProtocolFillUtils.genGoodsSnapV3(goodsSnap, newChildGoods, GoodsUtils.isCompositeGoods(newChildGoods), clinicConfig));
        goodsHistoryVersion.fixGoodsCustomTypeName(goodsRedisUtils);
        goodsHistoryVersion.setId(AbcIdUtils.getUUIDLong());
        goodsHistoryVersion.setCreated(Instant.now());
        goodsHistoryVersion.setCreatedBy(employeeId);
        newHistoryVersionList.add(goodsHistoryVersion);
        return newChildGoods;

    }

    /**
     * 诊所管家 项目对码自动生成的费用项Goods
     * 收到这个基类方法里面
     * 门店/总部 会进入到这个分支
     */
    protected Goods createChildInnerFeeGoods(Map<String, Goods> childGoodsIdToExistGoods, ServerCreateComposeChildrenBase clientCreateItem) {
        Goods newChildGoods = createInnerFeeGoods(chainId,
                clinicConfig,
                employeeId,
                newCreateChildGoodsList,
                childFeeGoodsExtendList,
                childFeeGoodsIdToGoodsExtend,
                needReFlushGoodsIdList,
                newCreateGoodsUpdateMessageList,
                newGoodsLogList,
                newHistoryVersionList,
                goodsRedisUtils,
                clientCreateItem.getName(), clientCreateItem.getEnName(), clientCreateItem.getPackageUnit(),
                clientCreateItem.getBizExtensions(), clientCreateItem.getDeviceType(),
                clientCreateItem.getNationalCode(), clientCreateItem.getNationalCodeId(), clientCreateItem.getMatchMode(),
                clientCreateItem.getCentralCode(),
                clientCreateItem.getPackagePrice(), clientCreateItem.getPackageCostPrice(),
                clientCreateItem.getShebaoPayMode(),
                clientCreateItem.getMedicalFeeGrade(),
                clientCreateItem.getPriceLimit(),
                clientCreateItem.getSelfPayProp(),
                clientCreateItem.getShebaoPayLimitPriceRule()
        );
        clientCreateItem.setId(newChildGoods.getId());
        clientCreateItem.setChildGoods(newChildGoods);
        childGoodsIdToExistGoods.put(newChildGoods.getId(), newChildGoods);
        return newChildGoods;
    }

    protected static void setGoodsExtendShebaoInfo(GoodsExtend extend,
                                                   String nationalCode,
                                                   String nationalCodeId,
                                                   String centralCode,
                                                   Integer matchMode,
                                                   Integer shebaoPayMode,
                                                   Integer medicalFeeGrade,
                                                   BigDecimal priceLimit,
                                                   Map<String, BigDecimal> nationalSelfPayProp,
                                                   ShebaoPayLimitPriceRule shebaoPayLimitPriceRule


    ) {
        extend.setShebaoCodeNationalCode(nationalCode);
        //TODO 前端配合生成extend，不用等社保返回消息
        extend.setMedicalFeeGradeNational(medicalFeeGrade);
        //费用项对码这里对不出DISABLE，可以认为就是一对码
        if (!StringUtils.isEmpty(nationalCode)) {
            extend.setShebaoCodeNationalMatchedStatus((short) SheBaoUtils.SheBaoStatus.MATCH);
        } else {
            extend.setShebaoCodeNationalMatchedStatus((short) SheBaoUtils.SheBaoStatus.NOT_MATCH);
        }
        extend.setShebaoCodeNationalCodeId(nationalCodeId);
        extend.setShebaoPayMode(shebaoPayMode);
        extend.setShebaoCodeNationalCurrentPriceLimited(priceLimit);
        if (CollectionUtils.isEmpty(nationalSelfPayProp) && shebaoPayLimitPriceRule == null && matchMode == null) {
            return;
        }
        ShebaoExtendInfo shebaoExtendInfo = new ShebaoExtendInfo();
        shebaoExtendInfo.setNationalSelfPayProp(nationalSelfPayProp);
        shebaoExtendInfo.setMatchMode(matchMode);
        shebaoExtendInfo.setNanJingCenterCode(centralCode);
        extend.setShebaoExtendInfo(shebaoExtendInfo);
        extend.setLimitPriceRule(shebaoPayLimitPriceRule);
    }

    /**
     * 创建goodsCompose
     * 新建和修改逻辑还是有些不一样，这里没有下层到基类
     * 修改goods @link  GoodsUpdateComposeService#doClinicUpdateGoodsCompose()}
     * 修改goods @link  GoodsUpdateComposeService#chainUpdateGoodsCompose()}
     */
    @Override
    protected void chainCreateGoodsCompose() {
        //   无组合类型
        if (!GoodsUtils.isCompositeGoods(serverCreateGoodsAbstractReq.getCombineType())
                && !GoodsUtils.isFeeCompositeGoods(serverCreateGoodsAbstractReq.getFeeComposeType())) {
            return;
        }


        saveShebaoDuimaShebaoPayMode();
        //套餐
        if (!CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getChildren())) {
            goods.setGoodsComposes(createChainCompose(serverCreateGoodsAbstractReq.getChildren(), GoodsComposeNaked.ComposeType.COMPOSE, clinicConfig));
            allGoodsComposeList.addAll(goods.getGoodsComposes());
        }
        //费用项
        if (!CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getFeeComposeList())) {
            goods.setGoodsFeeComposes(createChainCompose(serverCreateGoodsAbstractReq.getFeeComposeList(), GoodsComposeNaked.ComposeType.FEE, clinicConfig));
            allGoodsComposeList.addAll(goods.getGoodsFeeComposes());
        }
        // 关联项
        if (!CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getAssociationComposeList())) {
            goods.setGoodsAssociationComposes(createChainCompose(serverCreateGoodsAbstractReq.getAssociationComposeList(), GoodsComposeNaked.ComposeType.ASSOCIATION, clinicConfig));
            allGoodsComposeList.addAll(goods.getGoodsAssociationComposes());
        }
        // 商品组
        if (!CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getGoodsGroupComposeList())) {
            goods.setGoodsGroupComposes(createChainCompose(serverCreateGoodsAbstractReq.getGoodsGroupComposeList(), GoodsComposeNaked.ComposeType.GOODS_GROUP, clinicConfig));
            allGoodsComposeList.addAll(goods.getGoodsGroupComposes());
        }

        /*
         * 批量新增
         * */
        if (!CollectionUtils.isEmpty(allGoodsComposeList)) {
            goodsComposeNakedRepository.saveAll(allGoodsComposeList);
            /*
             * 有children，把flag设置上
             * 通过type去composeOpt里面加载goodsComposeOpt有点浪费db查询
             * */
            goods.onComposeFlag(GoodsConst.ComposeFlag.HAS_COMPOSE_CHILDREN);
            List<String> updateGoodsId = ListUtils.extractUniqueProperty(allGoodsComposeList, GoodsComposeNaked::getGoodsId);
            GoodsUtils.runAfterTransaction(() -> goodsRedisUtils.clearGoodsRedisCache(chainId, updateGoodsId));
        }

        //汇总总部价格
        summeryChainParentGoodsPackagePrice();

        initChainGoodsFeeComposeType();

        doSaveNewCreateComposeChildGoods();
    }

    /**
     * 建总部套餐关系
     *
     * @param composeType 关系类型 0 套餐或组合项
     *                    修改Goods @link  GoodsUpdateComposeService#chainUpdateGoodsCompose()}
     */
    private List<GoodsComposeNaked> createChainCompose(List<ServerCreateComposeChildrenBase> list, int composeType, ClinicConfig clinicConfig) {
        /*
         * 接下来要找新建的和修改的
         * */
        int composeSortIndex = 0;
        List<GoodsComposeNaked> goodsComposes = new ArrayList<>();
        for (ServerCreateComposeChildrenBase composeChild : list) {
            if (composeChild.getChildGoods() == null) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "请检查子项商品信息是否正确(" + composeChild.getName() + ")");
            }
            GoodsComposeNaked goodsCompose = new GoodsComposeNaked();
            goodsCompose.setId(AbcIdUtils.getUUIDLong());
            goodsCompose.setGoodsId(composeChild.getId());
            goodsCompose.setChainId(goods.getOrganId());
            //把goods和goodsStat设置进去
            goodsCompose.setChildGoods(composeChild.getChildGoods());
            if (goodsCompose.getChildGoods() != null) {
                if (composeType == GoodsComposeNaked.ComposeType.COMPOSE) {
                    goodsCompose.getChildGoods().onComposeFlag(GoodsConst.ComposeFlag.COMPOSE);
                } else if (composeType == GoodsComposeNaked.ComposeType.GOODS_GROUP) {
                    goodsCompose.getChildGoods().onComposeFlag(GoodsConst.ComposeFlag.COMPOSITE_GOODS_GROUP);
                } else {
                    goodsCompose.getChildGoods().onComposeFlag(GoodsConst.ComposeFlag.COMPOSE_FEE);
                }
            }
            goodsCompose.setParentGoodsId(goods.getId());
            goodsCompose.setComposeType(composeType);
            goodsCompose.setComposePackageCount(composeChild.getComposePackageCount());
            goodsCompose.setComposePackagePrice(composeChild.getComposePackagePrice());
            goodsCompose.setComposePieceCount(composeChild.getComposePieceCount());
            goodsCompose.setComposePiecePrice(composeChild.getComposePiecePrice());
            goodsCompose.setComposePrice(composeChild.getComposePrice());
            goodsCompose.setComposeUseDismounting(composeChild.getComposeUseDismounting());
            goodsCompose.setComposeSort(composeSortIndex++);
            if (Objects.nonNull(composeChild.getDisableComposePrint())) {
                goodsCompose.setExtendInfo(new GoodsComposeExtendInfo(composeChild.getDisableComposePrint()));
            }
            goodsCompose.setComposeFractionPrice(composeChild.getComposeFractionPrice());
            goodsCompose.setSurgeryGradeCode(composeChild.getSurgeryGradeCode());
            goodsCompose.setSurgerySiteCode(composeChild.getSurgerySiteCode());
            goodsCompose.setOperationCode(composeChild.getOperationCode());
            goodsCompose.setSurgeryIncisionHealingCode(composeChild.getSurgeryIncisionHealingCode());

//            /*
//             * 非医院的费用项，把支付方式和限价信息写到opt表上。
//             * goods-extend表上记录最近一次设置的支付方式和限价信息 ？？？
//             * */
//            if (composeType == GoodsComposeNaked.ComposeType.FEE
//                    && !clinicConfig.isHospital()) {
//                goodsCompose.setShebaoPayMode(composeChild.getShebaoPayMode());
//                goodsCompose.setLimitPriceRule(composeChild.getShebaoPayLimitPriceRule());
//            }
            UserFillUtils.fillCreatedUserId(goodsCompose, employeeId);
            goodsComposes.add(goodsCompose);
        }
        return goodsComposes;
    }

    protected void doSaveNewCreateComposeChildGoods() {
        //诊所管家费用项list
        if (!CollectionUtils.isEmpty(childFeeGoodsExtendList)) {
            goodsExtendRepository.saveAll(childFeeGoodsExtendList);
        }
        if (!CollectionUtils.isEmpty(childComposeGoodsExtendList)) {
            goodsExtendRepository.saveAll(childComposeGoodsExtendList);
        }
        if (!CollectionUtils.isEmpty(newCreateChildFeeMedicalStatList)) {
            goodsMedicalStatRepository.saveAll(newCreateChildFeeMedicalStatList);
        }
    }

    private void saveShebaoDuimaShebaoPayMode() {
        //多库房 诊所管家自动对对码 子店设置要保存支付方式
        if (!CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getFeeComposeList()) && !clinicConfig.isHospital()) {
            for (ServerCreateComposeChildrenBase serverCreateComposeChildrenBase : serverCreateGoodsAbstractReq.getFeeComposeList()) {
                if (serverCreateComposeChildrenBase.getShebaoPayMode() == null) {
                    continue;
                }
                GoodsExtend goodsExtendChild = childFeeGoodsIdToGoodsExtend.get(serverCreateComposeChildrenBase.getId());
                if (goodsExtendChild == null) {
                    goodsExtendChild = GoodsUtils.createGoodsExtend(serverCreateComposeChildrenBase.getId(), clinicConfig, employeeId);
                    setGoodsExtendShebaoInfo(goodsExtendChild,
                            serverCreateComposeChildrenBase.getNationalCode(),
                            serverCreateComposeChildrenBase.getNationalCodeId(),
                            serverCreateComposeChildrenBase.getCentralCode(),
                            serverCreateComposeChildrenBase.getMatchMode(),
                            serverCreateComposeChildrenBase.getShebaoPayMode(),
                            serverCreateComposeChildrenBase.getMedicalFeeGrade(),
                            serverCreateComposeChildrenBase.getPriceLimit(),
                            serverCreateComposeChildrenBase.getSelfPayProp(),
                            serverCreateComposeChildrenBase.getShebaoPayLimitPriceRule()
                    );
                    childFeeGoodsIdToGoodsExtend.put(goodsExtendChild.getGoodsId(), goodsExtendChild);
                }
                goodsExtendChild.setShebaoPayMode(serverCreateComposeChildrenBase.getShebaoPayMode());
                if (!clinicConfig.isSingleMode()) {
                    for (Organ organ : organList) {
                        ClinicConfig subConfig = cisClinicService.getClinicConfig(organ.getId());
                        GoodsExtend subGoodsExtend = GoodsUtils.createGoodsExtend(serverCreateComposeChildrenBase.getId(), subConfig, employeeId);
                        if (subConfig.notSameRegion()) {
                            subGoodsExtend.setShebaoCodeNationalMatchedStatus((short) SheBaoUtils.SheBaoStatus.NOT_MATCH);
                        } else {
                            setGoodsExtendShebaoInfo(subGoodsExtend,
                                    serverCreateComposeChildrenBase.getNationalCode(),
                                    serverCreateComposeChildrenBase.getNationalCodeId(),
                                    serverCreateComposeChildrenBase.getCentralCode(),
                                    serverCreateComposeChildrenBase.getMatchMode(),
                                    serverCreateComposeChildrenBase.getShebaoPayMode(),
                                    serverCreateComposeChildrenBase.getMedicalFeeGrade(),
                                    serverCreateComposeChildrenBase.getPriceLimit(),
                                    serverCreateComposeChildrenBase.getSelfPayProp(),
                                    serverCreateComposeChildrenBase.getShebaoPayLimitPriceRule()
                            );
                            subGoodsExtend.setShebaoPayMode(serverCreateComposeChildrenBase.getShebaoPayMode());
                        }
                        childFeeGoodsExtendList.add(subGoodsExtend);
                    }
                }
                // 子项也也需要刷
                needReFlushGoodsIdList.add(serverCreateComposeChildrenBase.getId());
            }

            if (!childFeeGoodsIdToGoodsExtend.isEmpty()) {
                goodsExtendRepository.saveAll(childFeeGoodsIdToGoodsExtend.values());
                GoodsUtils.runAfterTransaction(() -> goodsRedisUtils.clearGoodsRedisCache(chainId, new ArrayList<>(childFeeGoodsIdToGoodsExtend.keySet())));
            }
        }
    }

    @Override
    protected List<GoodsExtend> createGoodsExtend() {
        if (StringUtils.isEmpty(serverCreateGoodsAbstractReq.getNationalCode()) && CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getFeeComposeList())) {
            return null;
        }
        List<GoodsExtend> goodsExtendList = new ArrayList<>();
        goodsExtend = GoodsUtils.createGoodsExtend(goods.getId(), clinicConfig, employeeId);
        goods.setChildGoodsExtend(goodsExtend);
        goodsExtend.setShebaoPayMode(serverCreateGoodsAbstractReq.getShebaoPayMode());
        if (!StringUtils.isEmpty(serverCreateGoodsAbstractReq.getNationalCode())) {
            sLogger.info("chainId:{},goodsId:{},手动对码:{}", chainId, goods.getId(), serverCreateGoodsAbstractReq.getNationalCode());
            goodsExtend.setShebaoCodeNationalCode(serverCreateGoodsAbstractReq.getNationalCode());
            goodsExtend.setShebaoCodeNationalCodeId(serverCreateGoodsAbstractReq.getNationalCodeId());
            goodsExtend.setMedicalFeeGradeNational(serverCreateGoodsAbstractReq.getMedicalFeeGrade());
            goodsExtend.setShebaoCodeNationalMatchedStatus((short) SheBaoUtils.SheBaoStatus.MATCH);
            goodsExtend.setCancelShebaoDisableFlag(null);
            goodsExtend.setShebaoCodeNationalCurrentPriceLimited(serverCreateGoodsAbstractReq.getPriceLimit());
            if (GoodsUtils.compareStrEqual(SheBaoUtils.ShebaoDisabled, serverCreateGoodsAbstractReq.getNationalCode())) {
                goodsExtend.setShebaoCodeNationalMatchedStatus((short) SheBaoUtils.SheBaoStatus.NOT_PERIMIT);
                goodsExtend.setCancelShebaoDisableFlag(YesOrNo.YES);
                goodsExtend.setMedicalFeeGradeNational(null);
                goodsExtend.setShebaoCodeNationalCurrentPriceLimited(null);
                goodsExtend.setShebaoCodeNationalCodeId(null);
            }
        } else {
            if ((!clinicConfig.isHospital() && CollectionUtils.isEmpty(serverCreateGoodsAbstractReq.getFeeComposeList()))
                    || (clinicConfig.isHospital() && goods.getType() == GoodsConst.GoodsType.OTHER_GOODS)) {
                /*
                 * 诊所未传feeComposeList、医院的费用项
                 * 从社保拉自动对码
                 * */
                goodsExtend.setCancelShebaoDisableFlag(null);
                List<GetClinicsShebaoMatchedCodesRsp.GetClinicsShebaoMatchedCodesRspItem> matchResult = cisShebaoService.getClinicsMatchedSheBaoCodes(Collections.singletonList(clinicConfig.getClinicId()), Collections.singletonList(goods));
                if (!CollectionUtils.isEmpty(matchResult) && !CollectionUtils.isEmpty(matchResult.get(0).getItemList())) {
                    GoodsSheBaoMatchedCodesInfo matchedCodesInfo = matchResult.get(0).getItemList().get(0);
                    initAutoTmpShebaoCodeToExtend(goodsExtend, matchedCodesInfo);
                }
            }
        }
        goodsExtendList.add(goodsExtend);
        if (!clinicConfig.isSingleMode()) {
            // 连锁
            for (Organ organ : organList) {
                ClinicConfig subClinicConfig = cisClinicService.getClinicConfig(organ.getId());
                GoodsExtend subGoodsExtend = GoodsUtils.createGoodsExtend(goods.getId(), subClinicConfig, employeeId);
                if (!subClinicConfig.notSameRegion()) {
                    subGoodsExtend.setShebaoCodeNationalMatchedStatus(goodsExtend.getShebaoCodeNationalMatchedStatus());
                    subGoodsExtend.setShebaoCodeNationalCode(goodsExtend.getShebaoCodeNationalCode());
                    subGoodsExtend.setShebaoCodeNationalCodeId(goodsExtend.getShebaoCodeNationalCodeId());
                    subGoodsExtend.setMedicalFeeGradeNational(goodsExtend.getMedicalFeeGradeNational());
                    subGoodsExtend.setShebaoPayMode(goodsExtend.getShebaoPayMode());
                    subGoodsExtend.setShebaoCodeNationalCurrentPriceLimited(goodsExtend.getShebaoCodeNationalCurrentPriceLimited());
                    subGoodsExtend.setShebaoCodeNationalFuturePriceLimited(goodsExtend.getShebaoCodeNationalFuturePriceLimited());
                } else {
                    subGoodsExtend.setShebaoCodeNationalMatchedStatus((short) SheBaoUtils.SheBaoStatus.NOT_MATCH);
                }
                goodsExtendList.add(subGoodsExtend);
            }
        }
        return goodsExtendList;
    }

    protected boolean initChainGoodsFeeComposeType() {
        boolean specified = super.initChainGoodsFeeComposeType();
        if(!GoodsUtils.isHasFeeListGoodsType(goods.getType())){
            return true;
        }
        //费用类型比其他优先级高
        if (allGoodsComposeList.stream().filter(it -> it.getComposeType() == GoodsConst.ComposeType.FEE).anyMatch(it -> it.getIsDeleted() == YesOrNo.NO)) {
            goods.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_COMPOSE_FEE);
        } else {
            /*
             * 2025.06
             * 1.前端有个兼容逻辑，
             *          如果组合项目的 feeComposeType == 0 , 他们会把 子项目上的对码，母项目上的对码
             *          展示到费用项目哪里，看上去像是 有费用项目一样
             *          如果为1 就真的展示为空 没有
             * 2.后台直接将0的设置成 1，下掉老的逻辑
             */
            if (specified) { //这个判断是让里面的设置成1 的优先级最低
                return true;
            }

            /*
             * 医院 没那么复杂，直接 0
             * */
            if (clinicConfig.isHospital()) {
                goods.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);
                return true;
            }

            //不容许医保支付
            if (GoodsUtils.compareStrEqual(serverCreateGoodsAbstractReq.getNationalCode(), SheBaoUtils.ShebaoDisabled)) {
                goods.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);
            } else {
                // 叉掉所有的码变成空的
                goods.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);
//                goods.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL_FROM_COMPOSE_FEE);
//                needSendShebaoNationalCode = false;
            }
        }
        return true;
    }
}
