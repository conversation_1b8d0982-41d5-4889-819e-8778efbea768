/*
  处理查询goods列表的服务
 */
package cn.abcyun.cis.goods.service.stockcheck;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.cis.commons.util.FillUtils;
import cn.abcyun.cis.goods.cache.redis.ChoosePharmacyHelper;
import cn.abcyun.cis.goods.amqp.RocketMqProducer;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.cis.commons.message.ToBMessage;
import cn.abcyun.cis.commons.message.XinGeMessageBody;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.MD5Utils;
import cn.abcyun.cis.goods.amqp.LogMQProducer;
import cn.abcyun.cis.goods.amqp.MQProducer;
import cn.abcyun.cis.goods.amqp.MQUpsertEsProducer;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisCache;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisUtils;
import cn.abcyun.cis.goods.domain.GoodsStatManager;
import cn.abcyun.cis.goods.domain.GoodsStockMountTrible;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.mapper.GoodsMapper;
import cn.abcyun.cis.goods.mapper.GoodsStatMapper;
import cn.abcyun.cis.goods.mapper.GoodsStockMapper;
import cn.abcyun.cis.goods.model.GoodsCoworkStockCheckJob;
import cn.abcyun.cis.goods.model.GoodsCoworkStockCheckTask;
import cn.abcyun.cis.goods.model.GoodsStock;
import cn.abcyun.cis.goods.repository.*;
import cn.abcyun.cis.goods.service.GoodsOrderNoService;
import cn.abcyun.cis.goods.service.GoodsStatService;
import cn.abcyun.cis.goods.service.GoodsTraceCodeService;
import cn.abcyun.cis.goods.service.query.GoodsSysTypeService;
import cn.abcyun.cis.goods.service.rpc.AliHealthService;
import cn.abcyun.cis.goods.service.rpc.ApprovalProxyService;
import cn.abcyun.cis.goods.service.rpc.CisClinicService;
import cn.abcyun.cis.goods.service.rpc.CisGoodsLogService;
import cn.abcyun.cis.goods.service.todo.GoodsTodoCountService;
import cn.abcyun.cis.goods.stock.service.GoodsStockCheckOrderServiceBase;
import cn.abcyun.cis.goods.utils.*;
import cn.abcyun.cis.goods.utils.protocol.GoodsStockCheckJobUtils;
import cn.abcyun.cis.goods.utils.protocol.GoodsStockCheckUtils;
import cn.abcyun.cis.goods.vo.frontend.pharmacy.GoodsPharmacyView;
import cn.abcyun.cis.goods.vo.frontend.stockcheck.*;
import cn.abcyun.cis.goods.vo.frontend.stockin.GoodsStockRowErrorDetail;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GoodsStockCheckService {

    private static final Logger sLogger = LoggerFactory.getLogger(GoodsStockCheckService.class);
    /**
     * 每次盘点单可以盘点的数量
     */
    private static final int MAX_STOCK_COUNT = 3000;
    private List<Short> noFinishStatusList = new ArrayList<>(Arrays.asList(GoodsCoworkStockCheckTask.Status.CREATED,
            GoodsCoworkStockCheckTask.Status.CHECKING,
            GoodsCoworkStockCheckTask.Status.FINISHED));
    public static List<Short> notDeleteStatusList = new ArrayList<>(Arrays.asList(
            GoodsCoworkStockCheckTask.Status.CREATED,
            GoodsCoworkStockCheckTask.Status.CHECKING,
            GoodsCoworkStockCheckTask.Status.FINISHED,
            GoodsCoworkStockCheckTask.Status.COMMIT));
    public static List<Short> editableStatusList = new ArrayList<>(Arrays.asList(GoodsCoworkStockCheckTask.Status.CREATED,
            GoodsCoworkStockCheckTask.Status.CHECKING, GoodsCoworkStockCheckTask.Status.FINISHED));
    private List<Short> createTypeList = new ArrayList<>(Arrays.asList(GoodsCoworkStockCheckTask.Type.SINGLE,
            GoodsCoworkStockCheckTask.Type.CO_MAIN));
    public static List<Short> queryJobStatusList = Lists.newArrayList(GoodsCoworkStockCheckJob.Status.CREATED,
            GoodsCoworkStockCheckJob.Status.CHECKING, GoodsCoworkStockCheckJob.Status.FINISHED);
    @Autowired
    private GoodsCoworkTaskRepository goodsCoworkTaskRepository;

    @Autowired
    private GoodsCoworkJobRepository goodsCoworkJobRepository;
    @Autowired
    private SupplierUtils supplierUtils;
    @Autowired
    private GoodsStockRepository goodsStockRepository;
    @Autowired
    private GoodsStockBatchExtendRepository goodsStockBatchExtendRepository;
    @Autowired
    private GoodsStockLockingRepository goodsStockLockingRepository;
    @Autowired
    private GoodsStockLockingLogRepository goodsStockLockingLogRepository;
    @Autowired
    private GoodsStatRepository goodsStockCacheRepository;
    @Autowired
    private GoodsCustomTypeRepository goodsCustomTypeRepository;
    @Autowired
    private LogMQProducer logMQProducer;
    @Autowired
    private GoodsMapper goodsMapper;
    @Autowired
    private GoodsStockMapper goodsStockMapper;
    @Autowired
    private GoodsStockCheckOrderService goodsStockCheckOrderService;
    @Autowired
    private GoodsTodoCountService goodsTodoCountService;
    @Autowired
    private EmployeeUtils employeeUtils;
    @Autowired
    private OrganUtils organUtils;
    @Autowired
    private MQProducer mqProducer;
    @Autowired
    private MQUpsertEsProducer mQUpsertEsProducer;
    @Autowired
    private GoodsStatMapper goodsStatMapper;
    @Autowired
    private RocketMqProducer rocketMqProducer;
    @Autowired
    private CisClinicService clinicService;
    @Autowired
    private LoadGoodsUtils loadGoodsUtils;
    @Autowired
    private CisClinicService cisClinicService;
    @Autowired
    private GoodsStatService goodsStatService;
    @Autowired
    private GoodsStockCheckOrderLogRepository goodsStockCheckOrderLogRepository;
    @Autowired
    private GoodsStockCheckOrderRepository goodsStockCheckOrderRepository;
    @Autowired
    private GoodsStockCheckOrderItemRepository goodsStockCheckOrderItemRepository;
    @Autowired
    private GoodsStatRepository goodsStatRepository;
    @Autowired
    private GoodsSysTypeService goodsSysTypeService;
    @Autowired
    private CisGoodsLogService cisGoodsLogService;
    @Autowired
    private GoodsRedisUtils goodsRedisUtils;
    @Autowired
    private ApprovalProxyService approvalProxyService;
    @Autowired
    private GoodsOrderNoService goodsOrderNoService;
    @Autowired
    private AliHealthService aliHealthService;
    @Autowired
    private GoodsStockTraceableCodeRepository goodsStockTraceableCodeRepository;
    @Autowired
    private GoodsStockTraceableCodeLogV3Repository goodsStockTraceableCodeLogRepository;
    @Autowired
    private GoodsTraceCodeService goodsTraceCodeService;
    @Autowired
    private GoodsStockTraceableCodeRelationRepository goodsStockTraceableCodeRelationRepository;

    /**
     * 新建协作盘点任务
     */
    public CoworkGoodsStockCheckTaskRsp createCoworkGoodsStockCheckTask(CreateCoworkGoodsStockCheckTaskReq clientReq) throws CisGoodsServiceException {
        clientReq.parameterCheck(); //检查终端参数是否正常
        if (clientReq.getTaskId() != null && clientReq.getTaskId() > 0) { //增加某一条
            GoodsCoworkStockCheckTask mainTask = goodsCoworkTaskRepository.findByClinicIdAndTaskId(clientReq.getClinicId(),
                    clientReq.getTaskId()).orElse(null);
            if (mainTask == null
                    || (mainTask.getStatus() == GoodsCoworkStockCheckTask.Status.COMMIT
                    || mainTask.getStatus() == GoodsCoworkStockCheckTask.Status.DELETED)) {
                sLogger.info("主任务已经结束={}", JsonUtils.dump(mainTask));
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_COWORK_ERROR_CREATE_TASK_ERROR.getCode(), "主任务已经结束,不能再添加子任务了");
            }
            if (mainTask.getType() != GoodsCoworkStockCheckTask.Type.CO_MAIN) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_COWORK_ERROR_CREATE_TASK_ERROR.getCode(), "只能往多人盘点主任务下添加子任务");
            }
            List<GoodsCoworkStockCheckTask> childTaskList = GoodsStockCheckUtils.genSubGoodsStockCheckRepo(clientReq, mainTask, 0);
            goodsCoworkTaskRepository.saveAll(childTaskList);
            //通知 事务外
            notifySubTaskOwnerStockCheckTaskCreate(mainTask, childTaskList, clientReq);
            //第一个任务是主任务
            mainTask.setSubTaskItems(childTaskList);
            mainTask.setTaskCount(mainTask.getTaskCount() + childTaskList.size());
            CoworkGoodsStockCheckTaskRsp clientRsp = GoodsStockCheckUtils.genGoodsStockCheckTaskView(mainTask, employeeUtils, goodsRedisUtils, goodsSysTypeService, cisClinicService);
            return clientRsp;
        }

        /**
         * 需求：https://www.tapd.cn/22044681/prong/stories/view/1122044681001001647?url_cache_key=eaa40ab66796810f83437752e1d66809&action_entry_type=stories
         * 一个类型在门店里面只能创建一个进行中的盘点任务
         * */
        checkCanCreateCoworkStockCheckTask(clientReq, employeeUtils);

        //获取这个类别下药品的类别
        List<String> checkScopeGoodsIdList =  goodsStatMapper.findCheckScopeGoodsIdList(clientReq.getClinicId(),
                clientReq.getChainId(),
                clientReq.getPharmacyNo(),
                clientReq.getStockCheckScope() != null ? clientReq.getStockCheckScope().getType() : null,
                clientReq.getStockCheckScope() != null ? clientReq.getStockCheckScope().getSubType() : null,
                clientReq.getStockCheckScope() != null ? clientReq.getStockCheckScope().getCMSpec() : null,
                clientReq.getStockCheckScope() != null ? clientReq.getStockCheckScope().getCustomTypeIdList() : null,
                clientReq.getStockCheckScope() != null ? clientReq.getStockCheckScope().getOtherCustomTypeTypeIdList(): null,
                clientReq.getStockCheckScope() != null ? clientReq.getStockCheckScope().getTypeIdList() : null
        );
        if (CollectionUtils.isEmpty(checkScopeGoodsIdList)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "本次盘点范围内的药品数量为空,请检查盘点范围是否设置正确");
        }
        int kindCount =checkScopeGoodsIdList.size();
        if (kindCount > MAX_STOCK_COUNT) {
            sLogger.error("本次盘点药品数量={}", kindCount);
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "本次盘点药品/物资数量已超过" + MAX_STOCK_COUNT + "，请选择更小范围后继续");
        }

        GoodsPharmacyView goodsPharmacy = clinicService.getClinicConfig(clientReq.getClinicId()).getClinicPharmacyNotDeletedByNo(clientReq.getPharmacyNo());

        //写DB
        GoodsCoworkStockCheckTask mainTask = GoodsStockCheckUtils.genGoodsStockCheckRepo(clientReq, kindCount,goodsPharmacy);
        List<GoodsCoworkStockCheckTask> childTaskList = GoodsStockCheckUtils.genSubGoodsStockCheckRepo(clientReq, mainTask, kindCount);
        goodsCoworkTaskRepository.save(mainTask);
        goodsCoworkTaskRepository.saveAll(childTaskList);

        //通知
        notifySubTaskOwnerStockCheckTaskCreate(mainTask, childTaskList, clientReq);

        //第一个任务是主任务
        mainTask.setSubTaskItems(childTaskList);
        CoworkGoodsStockCheckTaskRsp clientRsp = GoodsStockCheckUtils.genGoodsStockCheckTaskView(mainTask, employeeUtils, goodsRedisUtils, goodsSysTypeService, cisClinicService);

        return clientRsp;
    }

    /**
     * 拉取employeed的所有可以进行中的任务
     * Client:App
     */
    public CoworkGoodsStockCheckTaskListRsp getMyCoworkGoodsStockCheckTask(String clinicId, String employeeId,Integer pharmacyNo) throws CisGoodsServiceException {
        CoworkGoodsStockCheckTaskListRsp rsp = new CoworkGoodsStockCheckTaskListRsp();

        List<GoodsCoworkStockCheckTask> myTasks = goodsCoworkTaskRepository.findAllByClinicIdAndOwnerIdAndStatusInAndPharmacyNo(clinicId, employeeId, editableStatusList,GoodsUtils.getDefaultLocalPharmacyNoIfNoSpecific(pharmacyNo));
        if (CollectionUtils.isEmpty(myTasks)) {
            sLogger.info("你没有盘点任务需要处理");
            return rsp;
        }
        rsp.setTaskInfos(new ArrayList<>());

        for (GoodsCoworkStockCheckTask myTask : myTasks) {
            if (myTask.getType() == GoodsCoworkStockCheckTask.Type.CO_MAIN) { //主任务不需要什么盘点操作
                continue;
            }
            rsp.getTaskInfos().add(GoodsStockCheckUtils.genTaskInfoView(myTask, employeeUtils, goodsRedisUtils, goodsSysTypeService, cisClinicService));
        }
        return rsp;
    }
//
//    public RpcCoworkTaskRsp getRpcMyCoworkGoodsStockCheckTask(String clinicId, String employeeId,Integer pharmacyNo) throws CisGoodsServiceException {
//        RpcCoworkTaskRsp rsp = new RpcCoworkTaskRsp();
//
//        List<GoodsCoworkStockCheckTask> myTasks = goodsCoworkTaskRepository.findAllByClinicIdAndOwnerIdAndStatusInAndPharmacyNo(clinicId, employeeId, editableStatusList,GoodsUtils.getDefaultLocalPharmacyNoIfNoSpecific(pharmacyNo));
//        if (CollectionUtils.isEmpty(myTasks)) {
//            sLogger.info("你没有盘点任务需要处理");
//            return rsp;
//        }
//
//        rsp.setItems(new ArrayList<>());
//        Map<Long, GoodsCoworkStockCheckTask> mainTaskMap = new HashMap<>();
//        Map<Long, List<GoodsCoworkStockCheckTask>> workTaskMap = new HashMap<>();
//        for (GoodsCoworkStockCheckTask myTask : myTasks) {
//            if (myTask.getType() == GoodsCoworkStockCheckTask.Type.CO_MAIN) {
//                mainTaskMap.put(myTask.getParentTaskId(), myTask);
//            } else {
//                List<GoodsCoworkStockCheckTask> subList = workTaskMap.get(myTask.getParentTaskId());
//                if (CollectionUtils.isEmpty(subList)) {
//                    subList = new ArrayList<>();
//                    workTaskMap.put(myTask.getParentTaskId(), subList);
//                }
//                subList.add(myTask);
//            }
//        }
//
//
//        mainTaskMap.forEach((parentTaskId, myTask) -> {
//            CoworkTaskItemView item = new CoworkTaskItemView();
//            item.setType(GoodsCoworkStockCheckTask.Type.CO_MAIN);
//            item.setName(myTask.getTaskName());
//            if (myTask.getFinishCount() == 0) {
//                item.setStatus("未开始");
//            } else {
//                item.setStatus("进行中");
//                item.setProgress(myTask.getFinishCount() + "/" + myTask.getTaskCount()); //名字 eg  2020-10-10全量盘点 进行中  （2/4）
//            }
//
//            item.setParentTaskId(myTask.getTaskId().toString()); //父任务id
////            item.setKindCount(myTask.getKindCount()); //已经盘了多少种类
//            item.setCreatedUser(employeeUtils.getEmployeeView(myTask.getOwnerId()));
//            OrganView organ = organUtils.getOrganView(myTask.getClinicId());
//            if (organ != null) {
//                item.setOrgan(organ);
//            }
//            item.setCreatedDate(myTask.getCreated());
//            rsp.getItems().add(item);
//        });
//        workTaskMap.forEach((parentTaskId, workTaskList) -> {
//            GoodsCoworkStockCheckTask myTask = workTaskList.get(0);
//            int waitingCheckCount = 0;
//
//            int finishCount = 0;
//            for (GoodsCoworkStockCheckTask stockCheckTask : workTaskList) {
//                if (stockCheckTask.getStatus() == GoodsCoworkStockCheckTask.Status.CREATED
//                        || stockCheckTask.getStatus() == GoodsCoworkStockCheckTask.Status.CHECKING
//                ) {
//                    waitingCheckCount++;
//                } else {
//                    finishCount++;
//                }
//            }
//            CoworkTaskItemView item = new CoworkTaskItemView();
//
//            item.setType(GoodsCoworkStockCheckTask.Type.CO_CHILD);
//            item.setName(myTask.getParentTaskName());
//            item.setProgress(waitingCheckCount + "");
//            item.setStatus("待盘点");
//            if (waitingCheckCount == 0) {
//                item.setStatus("已完成");
//            } else {
//                item.setProgress(finishCount + "/" + workTaskList.size()); //名字 eg  2020-10-10全量盘点 进行中  （2/4）
//            }
//
//            item.setParentTaskId(myTask.getParentTaskId().toString()); //父任务id
////            item.setKindCount(myTask.getKindCount()); //已经盘了多少种类
//            item.setCreatedUser(employeeUtils.getEmployeeView(myTask.getOwnerId()));
//            OrganView organ = organUtils.getOrganView(myTask.getClinicId());
//            if (organ != null) {
//                item.setOrgan(organ);
//            }
//            item.setCreatedDate(myTask.getCreated());
//            rsp.getItems().add(item);
//        });
//        return rsp;
//    }

    /**
     * 拉取某个协作盘点任务信息
     */
    public CoworkGoodsStockCheckTaskRsp getCoworkGoodsStockCheckTask(GetGoodsCoworkTaskReq clientReq) throws CisGoodsServiceException {

        clientReq.parameterCheck();

        GoodsCoworkStockCheckTask stockCheckTask = goodsCoworkTaskRepository.findByClinicIdAndTaskId(clientReq.getClinicId(),
                clientReq.getLongTaskId()).orElse(null);

        if (stockCheckTask == null) {
            sLogger.info("未找到协作盘点任务，请检查你输入的盘点ID:{}", JsonUtils.dump(clientReq));
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "未找到协作盘点任务，请检查你输入的盘点ID");
        }
        if (stockCheckTask.getType() == GoodsCoworkStockCheckTask.Type.CO_MAIN) {
            List<GoodsCoworkStockCheckTask> childTaskList = goodsCoworkTaskRepository.findAllByClinicIdAndParentTaskIdAndType(clientReq.getClinicId(),
                    clientReq.getLongTaskId(),
                    GoodsCoworkStockCheckTask.Type.CO_CHILD);
            if (!CollectionUtils.isEmpty(childTaskList)) {
                stockCheckTask.setSubTaskItems(new ArrayList<>());
                for (GoodsCoworkStockCheckTask task : childTaskList) {
                    if (task.getStatus() == GoodsCoworkStockCheckTask.Status.DELETED) { //子任务 不返回
                        continue;
                    }
                    if (stockCheckTask.getStatus() == GoodsCoworkStockCheckTask.Status.COMMIT) { //完成的任务，所有人都要能看到
                        stockCheckTask.getSubTaskItems().add(task);
                        continue;
                    }
                    if (clientReq.getType() != null) {
                        if (clientReq.getType() == 1) {
                            stockCheckTask.getSubTaskItems().add(task);
                        } else if (clientReq.getType() == 2 && clientReq.getEmployeeId().equals(task.getOwnerId())) {
                            stockCheckTask.getSubTaskItems().add(task);
                        }
                    } else {
                        //主任务创建者
                        if (clientReq.getEmployeeId().equals(stockCheckTask.getOwnerId())) { //看所有
                            stockCheckTask.getSubTaskItems().add(task);
                        } else {
                            if (clientReq.getEmployeeId().equals(task.getOwnerId())) { //把自己的返回去
                                stockCheckTask.getSubTaskItems().add(task);
                            }
                        }
                    }
                }
            }
        }
        CoworkGoodsStockCheckTaskRsp clientRsp = GoodsStockCheckUtils.genGoodsStockCheckTaskView(stockCheckTask, employeeUtils, goodsRedisUtils, goodsSysTypeService,cisClinicService);

        return clientRsp;
    }

    /**
     * 删除某个盘点任务
     * TODO
     * 1.这里需要进行很多边界条件的判断，能不嫩删除
     * 2.删除后汇总库存的变化
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp deleteCoworkGoodsStockCheckTask(GetGoodsCoworkTaskReq clientReq) throws CisGoodsServiceException {

        clientReq.parameterCheck();
        GoodsCoworkStockCheckTask goodsCoworkStockCheckTask = goodsCoworkTaskRepository.findByClinicIdAndTaskId(clientReq.getClinicId(),
                clientReq.getLongTaskId()).orElse(null);
        if (goodsCoworkStockCheckTask == null) {
            sLogger.info("未找到协作盘点任务，请检查你输入的盘点ID:{}", JsonUtils.dump(clientReq));
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "未找到协作盘点任务，请检查你输入的盘点ID");
        }

        if (goodsCoworkStockCheckTask.getStatus() == GoodsCoworkStockCheckTask.Status.DELETED) {
            sLogger.info("盘点任务已经被删除，不需要重复删除:{}", JsonUtils.dump(goodsCoworkStockCheckTask));
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "盘点任务已经被删除，不需要重复删除");
        }

        //修改自己的状态

        clientReq.setParentTaskId(goodsCoworkStockCheckTask.getParentTaskId().toString());
        //如果删除的是主任务
        if (goodsCoworkStockCheckTask.getType() == GoodsCoworkStockCheckTask.Type.CO_MAIN) {
            checkTaskHasCreateCheckOrder(goodsCoworkStockCheckTask);
            List<GoodsCoworkStockCheckTask> childTaskList = goodsCoworkTaskRepository.findAllByClinicIdAndParentTaskIdAndType(clientReq.getClinicId(),
                    clientReq.getLongTaskId(),
                    GoodsCoworkStockCheckTask.Type.CO_CHILD);
            if (!CollectionUtils.isEmpty(childTaskList)) {
                for (GoodsCoworkStockCheckTask childTask : childTaskList) {
                    childTask.setStatus(GoodsCoworkStockCheckTask.Status.DELETED);
                    List<GoodsCoworkStockCheckJob> jobList =
                            goodsCoworkJobRepository.findAllByClinicIdAndParentTaskIdAndTaskIdAndStatusIn(clientReq.getClinicId(), goodsCoworkStockCheckTask.getTaskId(), childTask.getTaskId(), queryJobStatusList);
                    if (!CollectionUtils.isEmpty(jobList)) {
                        for (GoodsCoworkStockCheckJob checkJob : jobList) {
                            checkJob.setStatus(GoodsCoworkStockCheckJob.Status.DELETED);
                            FillUtils.fillLastModifiedBy(checkJob, clientReq.getEmployeeId());
                        }
                    }
                }
            }
            goodsCoworkStockCheckTask.setTaskCount(0);

        } else {
            GoodsCoworkStockCheckTask parentTask = goodsCoworkTaskRepository.findByClinicIdAndTaskId(clientReq.getClinicId(),
                    goodsCoworkStockCheckTask.getParentTaskId()).orElse(null);
            if (parentTask == null) {
                sLogger.info("未找到协作盘点任务，请检查你输入的盘点ID:{}", JsonUtils.dump(clientReq));
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "未找到协作盘点任务，请检查你输入的盘点ID");
            }
            checkTaskHasCreateCheckOrder(parentTask);
            parentTask.setTaskCount(parentTask.getTaskCount() - 1);
            if (goodsCoworkStockCheckTask.getStatus() == GoodsCoworkStockCheckTask.Status.FINISHED) {
                parentTask.setFinishCount(parentTask.getFinishCount() - 1);
            }
            List<GoodsCoworkStockCheckJob> jobList =
                    goodsCoworkJobRepository.findAllByClinicIdAndParentTaskIdAndTaskIdAndStatusIn(clientReq.getClinicId(),
                            parentTask.getTaskId(), goodsCoworkStockCheckTask.getTaskId(), queryJobStatusList);
            if (!CollectionUtils.isEmpty(jobList)) {
                for (GoodsCoworkStockCheckJob checkJob : jobList) {
                    checkJob.setStatus(GoodsCoworkStockCheckJob.Status.DELETED);
                    FillUtils.fillLastModifiedBy(checkJob, clientReq.getEmployeeId());
                }
            }
        }


        //TODO robins 更新任务的统计量
        goodsCoworkStockCheckTask.setStatus(GoodsCoworkStockCheckTask.Status.DELETED);
        return new OpsCommonRsp();
    }

    private void checkTaskHasCreateCheckOrder(GoodsCoworkStockCheckTask checkTask) {
        log.info("checkTaskHasCreateCheckOrder checkTask={}", checkTask);
        if (checkTask == null || checkTask.getType() != GoodsCoworkStockCheckTask.Type.CO_MAIN
                || checkTask.getOrderId() == null) {
            return;
        }
        throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "任务已经生成盘点单，不能被删除，请刷新页面查看");
    }

    @Async("asyncTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void refreshGoodsStockCheckJobForDelete(String chainId, String clinciId, int headerClinicType, int headerViewMode, Long parentTaskId) throws CisGoodsServiceException {

        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(clinciId);
        List<GoodsCoworkStockCheckTask> allTasks = goodsCoworkTaskRepository.findAllByClinicIdAndParentTaskIdAndStatusIn(
                clinciId,
                parentTaskId,
                notDeleteStatusList);
        if (CollectionUtils.isEmpty(allTasks)) {
            sLogger.info("refreshGoodsStockCheckJob 盘点任务加载失败");
            return;
        }
        Map<Long, GoodsCoworkStockCheckTask> mapTasks = allTasks
                .stream()
                .collect(Collectors.toMap(GoodsCoworkStockCheckTask::getTaskId, Function.identity(), (a, b) -> a));
        //是否可修改的检查
        //是否可修改的检查G


        List<GoodsCoworkStockCheckJob> allJobs = goodsCoworkJobRepository.findAllByClinicIdAndParentTaskIdAndStatusInOrderBySortAsc(clinciId,
                        parentTaskId, queryJobStatusList);
        if (CollectionUtils.isEmpty(allJobs)) {
            sLogger.info("refreshGoodsStockCheckJob 盘点工作加载失败");
            return;
        }
        List<GoodsCoworkStockCheckJob> mainJobs = allJobs.stream().filter(it -> it.getTaskId().equals(it.getParentTaskId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mainJobs)) {
            sLogger.info("refreshGoodsStockCheckJob 盘点工作加载失败");
            return;
        }

        //准备加载goods信息
        List<String> uniqGoodsIdList = mainJobs.stream().map(GoodsCoworkStockCheckJob::getGoodsId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(uniqGoodsIdList)) {
            sLogger.info("refreshGoodsStockCheckJob 请求的任务里面商品信息为空");
            return;
        }

        //先检查规格药品规格是否有变化
        GoodsCoworkStockCheckTask parentTask = mapTasks.get(parentTaskId);


        Map<String, GoodsRedisCache> goodsIdToGoodsRedisCache = loadGoodsAndClinicPrice(
                uniqGoodsIdList,
                clinicConfig,
                parentTask.getPharmacyType(),
                parentTask.getPharmacyNo(),
                null, "请移除后再盘点");


        /**
         * 准备账面库存量，要写入落地数据了
         * */
        List<Long> batchIdList = allJobs
                .stream()
                .filter(it -> it.getBatchId() != null)
                .map(GoodsCoworkStockCheckJob::getBatchId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, List<GoodsStock>> batchIdToGoodsStockList = new HashMap<>();
        if (!CollectionUtils.isEmpty(batchIdList)) {
            GoodsStatManager.call("Stock_10_BthChk3");
            List<GoodsStock> batchIdStockList = goodsStockRepository.findAllByBatchIdInAndClinicId(batchIdList,clinciId);
            if (!CollectionUtils.isEmpty(batchIdStockList)) {
                batchIdToGoodsStockList.putAll(batchIdStockList.stream().collect(Collectors.groupingBy(GoodsStock::getBatchId)));
            }
        }

        GoodsStockCheckManager goodsStockCheckManager = new GoodsStockCheckManager(parentTask, allJobs, goodsIdToGoodsRedisCache, true);
        goodsStockCheckManager.refershForDeleteStockCheckTasks( batchIdToGoodsStockList, goodsIdToGoodsRedisCache,  goodsCoworkJobRepository);

        if (parentTask != null) {
            Integer sortIndex = 0;
            List<GoodsCoworkStockCheckJob> mainJobList = goodsStockCheckManager.getAllMainSvrJobs().stream().filter(it -> it.getTaskId().compareTo(it.getParentTaskId()) == 0).collect(Collectors.toList());
            for (GoodsCoworkStockCheckJob checkJob : mainJobList) {
                checkJob.setSort(sortIndex++);
            }
            parentTask.setSig(computeSig(mainJobList, goodsIdToGoodsRedisCache, null, null));
        }
    }

    /**
     * 构造盘点输出的数据结构
     * allJobsList 所有的盘点工作 in
     * curJobsList out 本任务的盘点工作
     * mapTaskJobList out 按goodsid的批次号为key ，value为所有批次的盘点任务
     */
    private void buildOutputDataStruct(List<GoodsCoworkStockCheckJob> allJobsList,
                                       List<GoodsCoworkStockCheckJob> curJobsList,
                                       Map<String, List<GoodsCoworkStockCheckJob>> mapTaskJobList, //里面放的是所有子工作的
                                       GoodsCoworkStockCheckTask stockCheckTask
    ) {
        String mapKey = null;
        List<GoodsCoworkStockCheckJob> helperList = null;
        if (!CollectionUtils.isEmpty(allJobsList)) {
            for (GoodsCoworkStockCheckJob job : allJobsList) {
                if (job.getTaskId().compareTo(stockCheckTask.getTaskId()) == 0) {
                    curJobsList.add(job);
                }

                if (job.getParentTaskId().compareTo(job.getTaskId()) != 0) {
                    mapKey = GoodsStockCheckUtils.uniqKeyGoodsIdAndBatchId(job.getGoodsId(), job.getBatchId());
                    helperList = mapTaskJobList.get(mapKey);
                    if (CollectionUtils.isEmpty(helperList)) {
                        helperList = new ArrayList<>();
                        mapTaskJobList.put(mapKey, helperList);
                    }
                    helperList.add(job);
                }
            }
        }
    }

    /**
     * 加载taskId下的盘点药品工作列表
     * 主要步骤：
     * 1.加载出当前任务已经盘点的药品任务
     * 2.根据盘点任务里面的goodsId加载加载goods信息，并检查goods是否被禁用
     * 3.没提交之前这里其实都是草稿，所以goods相关的信息都要用从db里面读取出来的goods信息，包括pieceNum,price等
     * <p>
     * 关于batchId和batchNo
     * batchId是v2_goods_stock表id（也不是所有stock记录都有，某些药品类型库存记录才有）
     * batchNo 入库单上输入的生成批号
     */
    public Map<String, GoodsRedisCache> getGoodsStockCheckJob(GetGoodsCoworkTaskReq req, CoworkGoodsStockCheckJobRsp rsp) throws CisGoodsServiceException {
        req.parameterCheck();

        //Step1.加载检查Task任务
        List<GoodsCoworkStockCheckTask> allTasks = goodsCoworkTaskRepository.findAllByClinicIdAndParentTaskIdAndStatusIn(
                req.getClinicId(),
                req.getLongParentTaskId(),
                notDeleteStatusList);
        if (CollectionUtils.isEmpty(allTasks)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "任务加载失败");
        }
        Map<Long, GoodsCoworkStockCheckTask> mapTasks = allTasks.stream().collect(Collectors.toMap(GoodsCoworkStockCheckTask::getTaskId, Function.identity(), (a, b) -> a));
        GoodsCoworkStockCheckTask stockCheckTask = mapTasks.get(req.getLongTaskId());
        if (stockCheckTask == null
                || stockCheckTask.getStatus() == GoodsCoworkStockCheckTask.Status.DELETED) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "你查看的盘点任务已经被删除");
        }

        //Step2.加载已经盘点的商品列表
        List<GoodsCoworkStockCheckJob> jobsList = new ArrayList<>();
        Map<String, List<GoodsCoworkStockCheckJob>> mapSubTaskJobList = new HashMap<>();
        List<GoodsCoworkStockCheckJob> allJobsList = new ArrayList<>();

        if (req.getOffset() == null && req.getLimit() == null) //不分页
        {
            if (stockCheckTask.getType() == GoodsCoworkStockCheckTask.Type.CO_MAIN) {
                allJobsList =
                        goodsCoworkJobRepository.findAllByClinicIdAndParentTaskIdAndStatusInOrderBySortAsc(req.getClinicId(),
                        stockCheckTask.getParentTaskId(), queryJobStatusList);
                buildOutputDataStruct(allJobsList, jobsList, mapSubTaskJobList, stockCheckTask);
            } else {
                jobsList = goodsCoworkJobRepository.findAllByClinicIdAndParentTaskIdAndTaskIdAndStatusInOrderBySortAsc(req.getClinicId(),
                        stockCheckTask.getParentTaskId(),
                        stockCheckTask.getTaskId(), queryJobStatusList);
            }
        } else {
            Pageable pageable = PageRequest.of(req.getPage(), req.getLimit());
            jobsList = goodsCoworkJobRepository.findByClinicIdAndParentTaskIdAndTaskIdAndStatusInOrderBySortAsc(req.getClinicId(),
                    stockCheckTask.getParentTaskId(),
                    stockCheckTask.getTaskId(), queryJobStatusList, pageable);
            if (!CollectionUtils.isEmpty(jobsList)) {
                allJobsList = goodsCoworkJobRepository.findAllByClinicIdAndParentTaskIdAndGoodsIdInAndStatusInOrderBySortAsc(req.getClinicId(),
                        stockCheckTask.getParentTaskId(),
                        jobsList.stream().map(GoodsCoworkStockCheckJob::getGoodsId).distinct().collect(Collectors.toList()),
                        queryJobStatusList);
                jobsList.clear();
                buildOutputDataStruct(allJobsList, jobsList, mapSubTaskJobList, stockCheckTask);
            }
            rsp.setCount(goodsCoworkJobRepository.countByClinicIdAndParentTaskIdAndTaskIdAndStatusIn(req.getClinicId(),
                    stockCheckTask.getParentTaskId(),
                    stockCheckTask.getTaskId(), queryJobStatusList));
            rsp.setLimit(req.getLimit());
            rsp.setOffset(req.getOffset());
        }

        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(stockCheckTask.getClinicId());

        if (CollectionUtils.isEmpty(jobsList)) {
            sLogger.info("请求的任务还没有盘点商品信息 stockCheckTask={}", JsonUtils.dump(stockCheckTask));
            fillMessage(req, rsp, stockCheckTask,clinicConfig);
            return null;
        }


        //Step3 加载batchNo,入库单修改后v2——goods_stock表里面的batchNo也会变，读这个表里面的字段就可以了
        List<Long> batchIdList = jobsList
                .stream()
                .filter(it -> it.getBatchId() != null)
                .map(GoodsCoworkStockCheckJob::getBatchId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, List<GoodsStock>> batchIdToGoodsStockList = new HashMap<>();
        if (!CollectionUtils.isEmpty(batchIdList)) {
            GoodsStatManager.call("Stock_10_BthChk4");
            batchIdToGoodsStockList.putAll(goodsStockRepository.findAllByBatchIdInAndClinicId(batchIdList,stockCheckTask.getClinicId()).stream().collect(Collectors.groupingBy(GoodsStock::getBatchId)));
        }

        //Step4.盘点数据已经落地定死,直接读数据即可
        if (stockCheckTask.canNotEdit()) {
            rsp.setList(GoodsStockCheckUtils.genFinGoodsStockCheckJobView(
                    stockCheckTask,
                    jobsList,
                    batchIdToGoodsStockList,
                    mapSubTaskJobList,
                    mapTasks));
            fillMessage(req, rsp, stockCheckTask,clinicConfig);
            calStockCheckJobTotalPrice(clinicConfig, rsp);
            return null;
        }

        //Step5.加载最新的goods信息,加载所有可用的goods
        List<String> uniqGoodsIdList = jobsList.stream().map(GoodsCoworkStockCheckJob::getGoodsId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(uniqGoodsIdList)) {
            sLogger.info("请求的任务里面商品信息为空 jobsList={}", JsonUtils.dump(jobsList));
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "商品加载失败,请稍后重试");
        }
        //
        Map<String, GoodsRedisCache> mapGoodsAll = loadGoodsAndClinicPrice(
                uniqGoodsIdList,
                clinicConfig,
                stockCheckTask.getPharmacyType(),
                stockCheckTask.getPharmacyNo(),
                null,
                null);


        boolean calTotalPrice = true;
        /**
         * 多人盘点药房号
         * */
        rsp.setPharmacy(GoodsUtils.toSimpleGoodsPharmacyView(clinicConfig.getClinicPharmacyIncDeletedByNo(stockCheckTask.getPharmacyNo())));
        //如果是主任务，检查下是否能提交合并，业务交互就是这样
        if (stockCheckTask.getType() == GoodsCoworkStockCheckTask.Type.CO_MAIN
                && stockCheckTask.getStatus() == GoodsCoworkStockCheckTask.Status.FINISHED) {
            //1.检查是否能提交合并
            GoodsStockCheckManager goodsStockCheckManager = new GoodsStockCheckManager(stockCheckTask, allJobsList, mapGoodsAll, true);
            goodsStockCheckManager.checkCanSubmit(
                    batchIdToGoodsStockList,
                    mapGoodsAll,
                    mapTasks,
                    employeeUtils);
            rsp.setTotalPriceChange(goodsStockCheckManager.getTotalPriceChange());
            rsp.setTotalCostPriceChange(goodsStockCheckManager.getTotalCostPriceChange());
            calTotalPrice = false;

        }
        //Step8 组装协议返回
        rsp.setList(GoodsStockCheckUtils.genGoodsStockCheckJobView(
                stockCheckTask,
                jobsList,
                mapGoodsAll,
                batchIdToGoodsStockList,
                mapSubTaskJobList,
                mapTasks));

        if (rsp.getTaskInfo() != null) {
            rsp.setKindCount(rsp.getTaskInfo().getKindCount());
        }

        //对于合并状态的主任务，要按变更的绝对值降序排序
        if (stockCheckTask.getType() == GoodsCoworkStockCheckTask.Type.CO_MAIN
                && stockCheckTask.getStatus() == GoodsCoworkStockCheckTask.Status.FINISHED) {
            rsp.getList().sort(new Comparator<StockCheckItemView>() {
                @Override
                public int compare(StockCheckItemView o1, StockCheckItemView o2) {
                    BigDecimal o1OrderValue = o1.getPackageCountChange().add(o1.getPieceCountChange().divide(BigDecimal.valueOf(o1.getPieceNum()), 1, RoundingMode.UP)).abs();
                    BigDecimal o2OrderValue = o2.getPackageCountChange().add(o2.getPieceCountChange().divide(BigDecimal.valueOf(o2.getPieceNum()), 1, RoundingMode.UP)).abs();
                    return o2OrderValue.compareTo(o1OrderValue);

                }
            });
        }
        fillMessage(req, rsp, stockCheckTask,clinicConfig);
        if (calTotalPrice) {
            calStockCheckJobTotalPrice(clinicConfig, rsp);
        }


        return mapGoodsAll;
    }

    private void calStockCheckJobTotalPrice(ClinicConfig clinicConfig, CoworkGoodsStockCheckJobRsp rsp) {
        if (!clinicConfig.isAbcPharmacy()) {
            return;
        }
        if (CollectionUtils.isEmpty(rsp.getList())) {
            return;
        }
        AtomicReference<BigDecimal> totalCostPriceChange = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> totalPriceChange = new AtomicReference<>(BigDecimal.ZERO);
        rsp.getList().forEach(item -> {
            totalPriceChange.set(GoodsStockUtils.add(totalPriceChange.get(), item.getTotalPriceChange()));
            totalCostPriceChange.set(GoodsStockUtils.add(totalCostPriceChange.get(), item.getTotalCostPriceChange()));
        });
        rsp.setTotalCostPriceChange(totalCostPriceChange.get());
        rsp.setTotalPriceChange(totalPriceChange.get());
    }

    /**
     * App已经调试完了
     * 前端需要batchs结构的协议，实现上用getGoodsStockCheckJob的返回重组了一下协议
     */
    public CoworkGoodsStockCheckJobForBatchEditRsp getGoodsStockCheckForBatchEditJob(GetGoodsCoworkTaskReq req) throws CisGoodsServiceException {
        CoworkGoodsStockCheckJobForBatchEditRsp batchEditRsp = new CoworkGoodsStockCheckJobForBatchEditRsp();
        //前端需要把goods的总库存返回回去，为了减少一次db，把getGoodsStockCheckJob里面的goodsStat返回出来
        CoworkGoodsStockCheckJobRsp checkJobRsp = new CoworkGoodsStockCheckJobRsp();
        Map<String, GoodsRedisCache> mapGoodsStat = getGoodsStockCheckJob(req, checkJobRsp);
        batchEditRsp.setPharmacy(checkJobRsp.getPharmacy());
        batchEditRsp.setChainId(checkJobRsp.getChainId());
        batchEditRsp.setClinicId(checkJobRsp.getClinicId());
        batchEditRsp.setEmployeeId(checkJobRsp.getEmployeeId());

        batchEditRsp.setTaskInfo(checkJobRsp.getTaskInfo()); //盘点任务的任务ID
        batchEditRsp.setCreatedUser(checkJobRsp.getCreatedUser());
        batchEditRsp.setCreatedDate(checkJobRsp.getCreatedDate());
        batchEditRsp.setOrgan(checkJobRsp.getOrgan()); //盘点诊所
        batchEditRsp.setSig(checkJobRsp.getSig()); //盘点任务的签名md5，submit的时候用于快速检查是否有变化
        batchEditRsp.setKindCount(checkJobRsp.getKindCount()); //放到这里是为了兼容老的协议
        Map<String, CoworkGoodsStockCheckJobForBatchEditRsp.StockCheckItemForEditView> map = new HashMap<>();
        List<String> sortList = new ArrayList<>();
        if (CollectionUtils.isEmpty(checkJobRsp.getList())) {
            return batchEditRsp;
        }
        for (StockCheckItemView stockCheckItemView : checkJobRsp.getList()) {

            if (stockCheckItemView == null) {
                continue;
            }

            CoworkGoodsStockCheckJobForBatchEditRsp.StockCheckItemForEditView helperView = map.get(stockCheckItemView.getGoodsId());
            if (helperView == null) {
                helperView = new CoworkGoodsStockCheckJobForBatchEditRsp.StockCheckItemForEditView();
                helperView.setGoods(stockCheckItemView.getGoods()); //填充goods信息
                //填充整个goods的现有库存
                if (mapGoodsStat != null) {
                    GoodsRedisCache gs = mapGoodsStat.get(stockCheckItemView.getGoodsId());
                    if (gs != null) {
                        GoodsStockMountTrible stdCount =  gs.getStandardPackageCount();
                        if(stdCount != null){
                            helperView.setBeforePackageCount(stdCount.getPackageCount());
                            helperView.setBeforePieceCount(stdCount.getPieceCount());
                        }
                    }
                }
                map.put(stockCheckItemView.getGoodsId(), helperView);
                sortList.add(stockCheckItemView.getGoodsId());
            }

            //把批次信息拷贝到batchs里面
            StockCheckItemBaseView baseView = new StockCheckItemBaseView();
            BeanUtils.copyProperties(stockCheckItemView, baseView);
            helperView.getBatchs().add(baseView);
        }
        for (String s : sortList) {
            CoworkGoodsStockCheckJobForBatchEditRsp.StockCheckItemForEditView editView = map.get(s);
            batchEditRsp.getList().add(editView);
        }
        return batchEditRsp;
    }

    /**
     * 修改单人盘点任务
     */
    public OpsCommonRsp modifyGoodsStockCheckJob(ModifyCoworkGoodsStockCheckJobReq clientReq) throws CisGoodsServiceException {
        clientReq.parameterCheck();

        //把整个父任务的盘点药品列表加载出来
        List<GoodsCoworkStockCheckTask> allStockCheckTasks = goodsCoworkTaskRepository.findAllByClinicIdAndParentTaskIdAndStatusIn(
                clientReq.getClinicId(),
                clientReq.getLongParentTaskId(),
                notDeleteStatusList);
        if (CollectionUtils.isEmpty(allStockCheckTasks)) {
            sLogger.info("盘点任务加载失败");
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_COWORK_ERROR_TASK_NOT_FOUND);
        }
        Map<Long, GoodsCoworkStockCheckTask> taskIdToTask = allStockCheckTasks
                .stream()
                .collect(Collectors.toMap(GoodsCoworkStockCheckTask::getTaskId, Function.identity(), (a, b) -> a));

        //是否可以进行盘点,并返回当前盘点任务
        GoodsCoworkStockCheckTask curStockCheckTask = checkAndSetTaskToRightStatusModify(taskIdToTask,
                clientReq.getLongTaskId(),
                clientReq.getLongParentTaskId());

        //注释
        curStockCheckTask.addCommentToEnd(clientReq.getEmployeeId(), clientReq.getComment());

        //预处理输入
        List<String> uniqGoodsIdList = clientReq.prepareRequestForScServer();

        //准备加载goods信息
        if (CollectionUtils.isEmpty(uniqGoodsIdList)) {
            sLogger.info("请求的任务里面商品信息为空");
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "商品加载失败,请稍后重试");
        }
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(curStockCheckTask.getClinicId());
        Map<String, GoodsRedisCache> mapGoodsAll = loadGoodsAndClinicPrice(
                uniqGoodsIdList,
                clinicConfig,
                curStockCheckTask.getPharmacyType(),
                curStockCheckTask.getPharmacyNo(),
                null,
                "请移除后再盘点");
        // 查询goods在该药房是否入过库
        checkGoodsHasInStockOfPharmacy(mapGoodsAll, curStockCheckTask.getPharmacyNo());

        //检查是否在本次盘点范围内
        checkGoodsInTypeIds(mapGoodsAll, curStockCheckTask);

        //加载草稿的盘点任务
        List<GoodsCoworkStockCheckJob> allStockCheckJobs =
                goodsCoworkJobRepository.findAllByClinicIdAndParentTaskIdAndStatusInOrderBySortAsc(clientReq.getClinicId(),
                clientReq.getLongParentTaskId(), queryJobStatusList);
        if (CollectionUtils.isEmpty(allStockCheckJobs)) {
            allStockCheckJobs = new ArrayList<>();
        }

        /**
         * 接下来准备开始修改了
         * 策略：子
         * 三个集合： 新增 ，修改 ,删除
         * */
        GoodsStockCheckManager goodsStockCheckManager = new GoodsStockCheckManager(curStockCheckTask,
                allStockCheckJobs,
                clientReq,
                mapGoodsAll,
                true);
        goodsStockCheckManager.dealSubStockCheckTasks(goodsCoworkJobRepository, clientReq.getEmployeeId());
        List<GoodsStockCheckJobUtils.StockCheckOneGoodsJob> invalidJobList = goodsStockCheckManager.invalidBachJobList();
        OpsCommonRsp rsp = new OpsCommonRsp();

        //产品上这里不关注
        doNotifyConflictBatchImpl(invalidJobList, mapGoodsAll, rsp);
        GoodsCoworkStockCheckTask parentTask = taskIdToTask.get(curStockCheckTask.getParentTaskId());
        if (parentTask != null) {
            Integer sortIndex = 0;
            List<GoodsCoworkStockCheckJob> mainJobList = goodsStockCheckManager.getAllMainSvrJobs().stream().filter(it -> it.getTaskId().compareTo(it.getParentTaskId()) == 0).collect(Collectors.toList());
            for (GoodsCoworkStockCheckJob checkJob : mainJobList) {
                checkJob.setSort(sortIndex++);
            }
            parentTask.setSig(computeSig(mainJobList, mapGoodsAll, null, null));
        }
        return rsp;
    }


    /**
     * 完成盘点
     */
    public OpsCommonRsp accomplishGoodsStockCheckJob(ModifyCoworkGoodsStockCheckJobReq clientReq) throws CisGoodsServiceException {
        clientReq.parameterCheck();


        List<GoodsCoworkStockCheckTask> allStockCheckTasks = goodsCoworkTaskRepository.findAllByClinicIdAndParentTaskIdAndStatusIn(
                clientReq.getClinicId(),
                clientReq.getLongParentTaskId(),
                notDeleteStatusList);
        if (CollectionUtils.isEmpty(allStockCheckTasks)) {
            sLogger.info("盘点任务加载失败");
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_COWORK_ERROR_TASK_NOT_FOUND);
        }

        Map<Long, GoodsCoworkStockCheckTask> mapTasks = allStockCheckTasks.stream().collect(Collectors.toMap(GoodsCoworkStockCheckTask::getTaskId, Function.identity(), (a, b) -> a));

        //是否可修改的检查G
        GoodsCoworkStockCheckTask curTask = checkAndSetTaskToRightStatusAccomplish(
                allStockCheckTasks,
                mapTasks,
                clientReq.getLongTaskId(),
                clientReq.getLongParentTaskId());
        curTask.addCommentToEnd(clientReq.getEmployeeId(), clientReq.getComment());
        List<GoodsCoworkStockCheckJob> allJobs =
                goodsCoworkJobRepository.findAllByClinicIdAndParentTaskIdAndStatusInOrderBySortAsc(clientReq.getClinicId(), clientReq.getLongParentTaskId(), queryJobStatusList);
        if (CollectionUtils.isEmpty(allJobs)) {
            allJobs = new ArrayList<>();
        }
        //List<GoodsCoworkStockCheckJob> curentTaskJobs = allJobs.stream().filter(it -> it.getTaskId().compareTo(clientReq.getLongTaskId()) == 0).collect(Collectors.toList());
        List<String> allGoodsIdList = allJobs.stream().map(GoodsCoworkStockCheckJob::getGoodsId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allGoodsIdList)) {
            allGoodsIdList = new ArrayList<>();
        }
        //预处理输入
        List<String> uniqGoodsIdList = clientReq.prepareRequestForScServer();
        if (CollectionUtils.isEmpty(uniqGoodsIdList)) {
            sLogger.info("请求的任务里面商品信息为空");
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "商品加载失败,请稍后重试");
        }
        allGoodsIdList.addAll(uniqGoodsIdList);
        allGoodsIdList = allGoodsIdList.stream().distinct().collect(Collectors.toList());
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(curTask.getClinicId());
        Map<String, GoodsRedisCache> mapGoodsAll = loadGoodsAndClinicPrice(
                allGoodsIdList,
                clinicConfig,
                curTask.getPharmacyType(),
                curTask.getPharmacyNo(),
                uniqGoodsIdList,
                "请移除后再盘点");//这里只要检查终端上的goodsid是否需要disable

        //检查是否有锁库
//        checkGoodsInStockLock(mapGoodsAll, allGoodsIdList, clientReq.getEmployeeId());
        // 查询goods在该药房是否入过库
        checkGoodsHasInStockOfPharmacy(mapGoodsAll, curTask.getPharmacyNo());
        //检查是否在本次盘点范围内
        checkGoodsInTypeIds(mapGoodsAll, curTask);


        //盘点单数据的处理
        GoodsStockCheckManager goodsStockCheckManager = new GoodsStockCheckManager(
                curTask,
                allJobs,
                clientReq,
                mapGoodsAll,
                false);
        goodsStockCheckManager.dealSubStockCheckTasks(goodsCoworkJobRepository, clientReq.getEmployeeId());

        //这里检查到了商品在不同的盘点任务之间，有的是按批次盘点的，有的是按全量盘点的，提示用户，但是还是要存下来
        List<GoodsStockCheckJobUtils.StockCheckOneGoodsJob> inValidJobsList = goodsStockCheckManager.invalidBachJobList();
        OpsCommonRsp rsp = new OpsCommonRsp();
        if (inValidJobsList != null) {
            rsp.setCode(1);
            rsp.setMessage(JsonUtils.dump(inValidJobsList));
        }

        GoodsCoworkStockCheckTask parentTask = mapTasks.get(curTask.getParentTaskId());
        if (parentTask != null) {
            Integer sortIndex = 0;
            List<GoodsCoworkStockCheckJob> mainJobList = goodsStockCheckManager.getAllMainSvrJobs().stream().filter(it -> it.getTaskId().compareTo(it.getParentTaskId()) == 0).collect(Collectors.toList());
            for (GoodsCoworkStockCheckJob checkJob : mainJobList) {
                checkJob.setSort(sortIndex++);
            }
            parentTask.setSig(computeSig(mainJobList, mapGoodsAll, null, null));
        }
        curTask.setSig(System.currentTimeMillis() + "");

        return rsp;
    }

    private void checkGoodsHasInStockOfPharmacy(Map<String, GoodsRedisCache> mapGoodsAll, int pharmacyNo) {
        GoodsStockRowErrorDetail errorDetail = new GoodsStockRowErrorDetail();
        errorDetail.setErrorTitle("药品物资未入库，请入库后再盘点");

        int rowIndex = 0;
        List<GoodsRedisCache> allGoodsRedisCacheList = mapGoodsAll.values().stream().collect(Collectors.toList());
        for (GoodsRedisCache goods : allGoodsRedisCacheList) {
            if (goods.getStatus() >= GoodsConst.GoodsStatus.NO_VISIBLE) {
                continue;
            }
            if (goods.isStockInOfPharmacy(pharmacyNo)) {
                continue;
            }
            errorDetail.putGoodsStockRowErrorDetailItem(rowIndex, GoodsUtils.goodsFullName(goods), null, goods);
            errorDetail.addErrMsg(rowIndex, "未入库");
            rowIndex++;
        }
        /**
         * 数据有异常
         * */
        if (errorDetail.isErrorHappen()) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_TABLE_FORMAT_PARAMETER, errorDetail);
        }
    }


    /**
     * 准备汇总提交本次多人盘点任务
     * 1.汇总提交已后台数据为准
     * 2.这里终端传一堆盘点任务数据上来，产品上这里设计的很灵活，完成盘点的子任务是还可以被修改的，导致提交汇总人看到的数据在提交时可能已经发生变化
     * 这里要检测出变化，并提示提交人
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp submitGoodsStockCheckJobV2(SubmitCoworkGoodsStockCheckTaskReq req, GoodsStockCheckCreateOrderByCoWork checkCreateOrderByCoWork) throws CisGoodsServiceException {

        checkCreateOrderByCoWork.bindRepository(goodsStatMapper,
                loadGoodsUtils,
                cisClinicService,
                goodsStatService,
                goodsStockCheckOrderRepository,
                goodsStockCheckOrderItemRepository,
                goodsStockCheckOrderLogRepository,
                goodsStatRepository,
                goodsStockRepository,
                goodsStockBatchExtendRepository,
                goodsStockLockingRepository,
                goodsCustomTypeRepository,
                goodsStockLockingLogRepository,
                mQUpsertEsProducer,
                goodsRedisUtils,
                goodsMapper,
                goodsStockMapper,
                employeeUtils,
                organUtils,
                supplierUtils,
                mqProducer,
                rocketMqProducer ,
                goodsTodoCountService,
                cisGoodsLogService,
                goodsSysTypeService,
                logMQProducer,
                approvalProxyService,
                goodsOrderNoService,
                aliHealthService,
                goodsTraceCodeService,
                goodsStockTraceableCodeRepository,
                goodsStockTraceableCodeRelationRepository,
                goodsStockTraceableCodeLogRepository);

        checkCreateOrderByCoWork.bindCoWorkCreateOrderParameter(req, employeeUtils,
                goodsCoworkTaskRepository,
                goodsCoworkJobRepository);

        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKCHECK_CHEKING, req.getTaskId(), GoodsRedisUtils.CHECKING_CHECK);
        try {
            checkCreateOrderByCoWork.createGoodsCheckOrder();
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKCHECK_CHEKING, req.getTaskId(), false);
            throw exp;
        }
        //数量有变更，请缓存
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKCHECK_CHEKING, req.getTaskId(), true);


        //数量有变更，请缓存
        goodsStockCheckOrderService.clearClinicDefaultCheckOrderListCache(req.getClinicId());
        if (!GoodsPrivUtils.isSingleMode(req.getHeaderClinicType(), req.getHeaderViewMode())) {
            goodsStockCheckOrderService.clearClinicDefaultCheckOrderListCache(req.getChainId());
        }
        OpsCommonRsp rsp = new OpsCommonRsp(OpsCommonRsp.SUCC, "Success");
        return rsp;
    }

    @Async("longTimeAsyncTaskExecutor")
    public void asyncTrans(GoodsStockCheckOrderServiceBase gds) {
        flushGoodsStat(gds);
        checkGoodsDisable(gds);
    }

    @Transactional(rollbackFor = Exception.class)
    public void flushGoodsStat(GoodsStockCheckOrderServiceBase gds) {
        gds.anotherTansToFlushGoodsStat();
    }

    @Transactional(rollbackFor = Exception.class)
    public void checkGoodsDisable(GoodsStockCheckOrderServiceBase gds) {
        gds.anotherTansToCheckGoodsDisable();
    }


    private void fillMessage(GetGoodsCoworkTaskReq req, CoworkGoodsStockCheckJobRsp rsp, GoodsCoworkStockCheckTask stockCheckTask, ClinicConfig clinicConfig) {
        rsp.setTaskInfo(GoodsStockCheckUtils.genTaskInfoView(stockCheckTask, employeeUtils, goodsRedisUtils, goodsSysTypeService, cisClinicService));
        rsp.setChainId(req.getChainId());
        rsp.setClinicId(req.getClinicId());
        rsp.setEmployeeId(req.getEmployeeId());
        rsp.setCreatedUser(employeeUtils.getEmployeeView(stockCheckTask.getOwnerId(), req.getChainId()));
        rsp.setOrgan(organUtils.getOrganView(stockCheckTask.getClinicId()));
        rsp.setSig(stockCheckTask.getSig());
        /**
         * 多人盘点药房号
         * */
        rsp.setPharmacy(GoodsUtils.toSimpleGoodsPharmacyView(clinicConfig.getClinicPharmacyIncDeletedByNo(stockCheckTask.getPharmacyNo())));
        List<CommentDBJson> dbCommentList = stockCheckTask.getCommentList();
        if (!CollectionUtils.isEmpty(dbCommentList)) {
            rsp.setComment(new ArrayList<>());
            for (CommentDBJson commentDBJson : dbCommentList) {
                CommentView view = new CommentView();
                BeanUtils.copyProperties(commentDBJson, view);
                Employee employee = employeeUtils.getEmployee(view.getEmployeeId(), req.getChainId());
                if (employee != null) {
                    view.setEmployeeName(employee.getName());
                }
                rsp.getComment().add(view);
            }
        }
    }

    public static String computeSig(List<GoodsCoworkStockCheckJob> allJobs, Map<String, GoodsRedisCache> mapGoodsAll, List<GoodsSpecChangeExceptionView> listChangeJobsGoods, Map<Long, List<GoodsStock>> batchIdToGoodsStockList) {
        StringBuffer sBuffer = new StringBuffer();

        for (GoodsCoworkStockCheckJob job : allJobs) {
            sBuffer.append(job.getGoodsId());
            sBuffer.append(job.getPieceCount().intValue());
            sBuffer.append(job.getPackageCount().intValue());
            GoodsRedisCache goods = mapGoodsAll.get(job.getGoodsId());
            if (goods != null) {
                sBuffer.append(goods.getPieceNum());
                sBuffer.append(goods.getPieceUnit());
                sBuffer.append(goods.getPackageUnit());
            }
            if (listChangeJobsGoods != null) //非空才计算具体的
            {
                GoodsItem oldGoods = job.getGoods();
                if (goods != null && oldGoods != null) {
                    if ((goods.getPieceNum() != null && oldGoods.getPieceNum() != null && goods.getPieceNum().compareTo(oldGoods.getPieceNum().intValue()) != 0)
                            || (goods.getPackageUnit() != null && oldGoods.getPackageUnit() != null && goods.getPackageUnit().compareTo(oldGoods.getPackageUnit()) != 0)
                            || (goods.getPieceUnit() != null && oldGoods.getPieceUnit() != null && goods.getPieceUnit().compareTo(oldGoods.getPieceUnit()) != 0)
                    ) {
                        GoodsSpecChangeExceptionView view = new GoodsSpecChangeExceptionView();
                        GoodsSpecChangeExceptionView.GoodsChangeView before = new GoodsSpecChangeExceptionView.GoodsChangeView();
                        BeanUtils.copyProperties(oldGoods, before);
                        GoodsSpecChangeExceptionView.GoodsChangeView now = new GoodsSpecChangeExceptionView.GoodsChangeView();
                        BeanUtils.copyProperties(goods, now);
                        view.setGoodsBefore(before);
                        view.setGoodsNow(now);
                        view.setPackageCount(job.getPackageCount());
                        if (job.getBatchId() != null) {
                            List<GoodsStock> goodsStockList = batchIdToGoodsStockList.get(job.getBatchId());
                            if (!CollectionUtils.isEmpty(goodsStockList) && goodsStockList.get(0) != null) {
                                view.setBatchNo(goodsStockList.get(0).getBatchNo());
                            }

                        }
                        listChangeJobsGoods.add(view);
                    }
                }

            }

        }

        String md5 = MD5Utils.sign(sBuffer.toString());
        return md5;
    }


    /**
     * 检查是否可以添加协作盘点任务
     */
    private void checkCanCreateCoworkStockCheckTask(CreateCoworkGoodsStockCheckTaskReq clientReq, EmployeeUtils employeeUtils) throws CisGoodsServiceException {
        List<GoodsCoworkStockCheckTask> notFinishTasks = goodsCoworkTaskRepository.findAllByClinicIdAndStatusInAndPharmacyNo(
                clientReq.getClinicId(),
                noFinishStatusList,
                GoodsUtils.getDefaultLocalPharmacyNoIfNoSpecific(clientReq.getPharmacyNo()));
        if (CollectionUtils.isEmpty(notFinishTasks)) {
            return;
        }

        GoodsCoworkStockCheckTask checkingStock = notFinishTasks.
                stream().
                filter(GoodsCoworkStockCheckTask::isCreateType).
                filter(it -> it.isTypeInChecking(clientReq.getStockCheckScope(), goodsCustomTypeRepository, clientReq.getChainId())).findFirst().orElse(null);
        if (checkingStock != null) {
            sLogger.info("新建任务,你还有在流程中的盘点药品类型:{}", JsonUtils.dump(notFinishTasks));
            Employee employee = employeeUtils.getEmployee(checkingStock.getOwnerId(), checkingStock.getChainId());
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_COWORK_ERROR_CREATE_TASK_ERROR.getCode(), (employee != null ? "与" + employee.getName() + "创建的" : "") + "盘点任务范围冲突，请修改范围后重试");
        }
    }

    /**
     * 通知子任务负责人有任务要处理
     * dbTaskList 新生成的盘点任务列表，第1个为主任务
     * clientReq 客户端生成盘点任务的请求
     */
    private void notifySubTaskOwnerStockCheckTaskCreate(GoodsCoworkStockCheckTask mainTask, List<GoodsCoworkStockCheckTask> childTask, CreateCoworkGoodsStockCheckTaskReq clientReq) {
        ToBMessage toBMessage = new ToBMessage();
        toBMessage.setChainId(clientReq.getChainId());
        toBMessage.setClinicId(clientReq.getClinicId());
        toBMessage.setChannel(ToBMessage.MessageChannel.XinGe);
        List<String> employedIds = childTask.stream().map(GoodsCoworkStockCheckTask::getOwnerId).distinct().collect(Collectors.toList());
        toBMessage.setEmployeeIds(employedIds);
        toBMessage.setMsgId(AbcIdUtils.getUUID());
        XinGeMessageBody xinGeMessageBody = new XinGeMessageBody();
        xinGeMessageBody.setTitle("盘点任务");
        xinGeMessageBody.setContent("你有一条来自" + mainTask.getParentTaskName() + "的盘点任务需要处理");
        xinGeMessageBody.setAction("abcyun://inventory/check?chainId=" + mainTask.getChainId() + "&clinicId=" + mainTask.getClinicId());
        toBMessage.setBody(JsonUtils.dumpAsJsonNode(xinGeMessageBody));

        //mqProducer.syncNotifyToBMessage(toBMessage);
        rocketMqProducer.sendToBMessage(toBMessage);
    }


    /**
     * 检查是否有没在盘点范围内的药品
     */
    private void checkGoodsInTypeIds(Map<String, GoodsRedisCache> mapGoods, GoodsCoworkStockCheckTask checkTask) throws CisGoodsServiceException {

        List<GoodsRedisCache> notTypeIdList = mapGoods.values().stream().filter(goods -> !checkTask.isGoodsInScope(goods)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notTypeIdList)) {
            return;
        }
        GoodsStockRowErrorDetail errorDetail = new GoodsStockRowErrorDetail();
        errorDetail.setErrorTitle("盘点药品不在盘点范围内");

        int rowIndex = 0;
        for (GoodsRedisCache goods : notTypeIdList) {
            errorDetail.putGoodsStockRowErrorDetailItem(rowIndex,
                    GoodsUtils.goodsFullName(goods),
                    null
            );
            errorDetail.addErrMsg(rowIndex,GoodsUtils.goodsTypeReadName(goods.getType(), goods.getSubType(), goods.getCMSpec()));
            rowIndex++;
        }
        /**
         * 数据有异常
         * */
        if (errorDetail.isErrorHappen()) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_TABLE_FORMAT_PARAMETER, errorDetail);
        }
    }

    /**
     * 返回提示的冲突信息
     */
    private void doNotifyConflictBatchImpl(List<GoodsStockCheckJobUtils.StockCheckOneGoodsJob> invalidJobList,
                                           Map<String, GoodsRedisCache> mapGoodsAll,
                                           OpsCommonRsp rsp) {
        if (!CollectionUtils.isEmpty(invalidJobList)) {
            StringBuffer sb = new StringBuffer();
            sb.append(CisGoodsServiceError.CIS_COWORK_ERROR_BATCH_ERROR.getMessage());
            int i = 0;
            for (GoodsStockCheckJobUtils.StockCheckOneGoodsJob stockCheckOneGoodsJob : invalidJobList) {
                GoodsRedisCache goods = mapGoodsAll.get(stockCheckOneGoodsJob.getGoodsId());
                if (goods == null) {
                    continue;
                }
                if (i++ != 0) {
                    sb.append(",");
                }

                sb.append(goods.getMedicineCadn());
            }
            rsp.setCode(CisGoodsServiceError.CIS_COWORK_ERROR_BATCH_ERROR.getCode());
            rsp.setMessage(sb.toString());
        }
    }

    /**
     * 检查是否可以进行盘点
     */
    private void checkCanStockChecking(GoodsCoworkStockCheckTask stockCheckTask) throws CisGoodsServiceException {
        if (stockCheckTask == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_COWORK_ERROR_TASK_NOT_FOUND);
        }

        if (stockCheckTask.getStatus() == GoodsCoworkStockCheckTask.Status.COMMIT
                || stockCheckTask.getStatus() == GoodsCoworkStockCheckTask.Status.DELETED) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "任务已经被提交或删除，不能再盘点");
        }
    }

    /**
     * 检查并设置 任务状态
     */
    private GoodsCoworkStockCheckTask checkAndSetTaskToRightStatusModify(
            Map<Long, GoodsCoworkStockCheckTask> mapTasks,
            Long taskId,
            Long parentTaskId) throws CisGoodsServiceException {
        GoodsCoworkStockCheckTask curStockCheckTask = mapTasks.get(taskId);
        checkCanStockChecking(curStockCheckTask);
        //完成状态的单子不能再修改
        if (curStockCheckTask.getStatus() == GoodsCoworkStockCheckTask.Status.FINISHED) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_COWORK_MODIFY_CANDELJOB_AFTER_FINISH);
        }
        if (curStockCheckTask.getType() == GoodsCoworkStockCheckTask.Type.CO_CHILD) { //如果是子任务,还要检查父任务状态是否容许盘点
            GoodsCoworkStockCheckTask parentTask = mapTasks.get(parentTaskId);
            checkCanStockChecking(parentTask);
            parentTask.setStatus(GoodsCoworkStockCheckTask.Status.CHECKING);
        }
        curStockCheckTask.setStatus(GoodsCoworkStockCheckTask.Status.CHECKING);
        return curStockCheckTask;
    }

    /**
     * 加载goods信息和自主定价
     * curentTaskJobs 检查这里面的goids是否有被禁用的
     * 如果这个为null name检查uniqGoodsIdList里面是否有被禁用的
     * 如果为空数组，则表示不作检查 限定检查范围
     * @param  checkDisableGoodsList
     */
//"种商品物资已被停用,请从子任务中删除后再完成盘点"
    //"处于禁用中,请移除后再盘点"
    private Map<String, GoodsRedisCache> loadGoodsAndClinicPrice(List<String> loadGoodsIdList,
                                                       ClinicConfig clinicConfig,
                                                       int pharmacyType,
                                                       Integer pharmacyNo,
                                                       List<String> checkDisableGoodsList,
                                                       String tips) throws CisGoodsServiceException {
        /**
         * 加载nakedgoods
         * */
        List<GoodsRedisCache> allSourceGoodsList =goodsRedisUtils.loadQueryGoodsDataFromRedis(
                loadGoodsIdList,
                clinicConfig,
                null,
                GoodsUtils.SwitchFlag.ON,
                ChoosePharmacyHelper.ofSpecificPharmacy(pharmacyType, pharmacyNo )
                );
        if (CollectionUtils.isEmpty(allSourceGoodsList)) {
            sLogger.info("加载goods列表异常 isEmpty() uniqGoodsIdList={}", JsonUtils.dump(loadGoodsIdList));
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_NOT_FOUND, "商品加载失败,请稍后重试");
        }

        /**
         * 先检查禁用的
         * */
        List<GoodsRedisCache> disableSourceGoodsList = allSourceGoodsList
                .stream()
                .filter(goods ->  goods.getDisable() == GoodsConst.EnableStatus.DISABLE )
                .collect(Collectors.toList());

        /**
         * 有禁用的药品
         * */
        if (!CollectionUtils.isEmpty(disableSourceGoodsList)) {
            Map<String, GoodsRedisCache> mapDisableGoods = disableSourceGoodsList.stream().collect(Collectors.toMap(GoodsRedisCache::getId, Function.identity(), (a, b) -> a));
            List<GoodsRedisCache> currentDisableSourceGoodsList = new ArrayList<>();
            if (checkDisableGoodsList != null) {
                for (String goodsId : checkDisableGoodsList) {
                    GoodsRedisCache disableGoods = mapDisableGoods.get(goodsId);
                    if (disableGoods != null) {
                        currentDisableSourceGoodsList.add(disableGoods);
                    }
                }
            } else {
                currentDisableSourceGoodsList.addAll(disableSourceGoodsList);
            }

            if (!CollectionUtils.isEmpty(currentDisableSourceGoodsList)) {
                GoodsStockRowErrorDetail errorDetail = new GoodsStockRowErrorDetail();
                errorDetail.setErrorTitle("禁用药品"+tips!=null?tips:"");

                int rowIndex = 0;
                for (GoodsRedisCache goods : currentDisableSourceGoodsList) {
                    errorDetail.putGoodsStockRowErrorDetailItem(rowIndex,GoodsUtils.goodsFullName(goods),null);
                    errorDetail.addErrMsg(rowIndex,"禁用");
                    rowIndex ++;
                }
                /**
                 * 数据有异常
                 * */
                if (errorDetail.isErrorHappen() ) {
                    throw  new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_TABLE_FORMAT_PARAMETER,errorDetail );
                }
            }
        }

        return allSourceGoodsList.stream().collect(Collectors.toMap(GoodsRedisCache::getId,Function.identity(),(a,b)->a));
    }

    /**
     * 检查是否有商品在出库中
     */
    private void checkGoodsInStockLock(Map<String, GoodsRedisCache> mapGoods, List<String> goodsIdList, String organId) throws CisGoodsServiceException {
        GoodsStockRowErrorDetail errorDetail = new GoodsStockRowErrorDetail();
        errorDetail.setErrorTitle("药品物资锁库中,请先处理后再盘点");

        int rowIndex = 0;
        for (GoodsRedisCache goods : mapGoods.values().stream().filter(goodsRedisCache -> goodsRedisCache.isGoodsStockLocking()).collect(Collectors.toList())) {
            errorDetail.putGoodsStockRowErrorDetailItem(rowIndex,
                    /**
                     * 药名
                     * */
                    GoodsUtils.goodsFullName(goods),
                    null
                    );
            GoodsStockMountTrible stdLockingCount = goods.getStandardLockingCount();
            if(stdLockingCount != null){
                errorDetail.addErrMsg(rowIndex,stdLockingCount.getReadableStockCount((int) goods.getType(), (int) goods.getSubType(),
                        goods.getPieceUnit(),goods.getPackageUnit()
                ));
            }
            rowIndex ++;
        }
        /**
         * 数据有异常
         * */
        if (errorDetail.isErrorHappen()) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_TABLE_FORMAT_PARAMETER, errorDetail);
        }
    }

    /**
     * 检查并设置 任务状态
     */
    private GoodsCoworkStockCheckTask checkAndSetTaskToRightStatusAccomplish(
            List<GoodsCoworkStockCheckTask> allStockCheckTasks,
            Map<Long, GoodsCoworkStockCheckTask> mapTasks,
            Long taskId,
            Long parentTaskId) throws CisGoodsServiceException {
        GoodsCoworkStockCheckTask curStockCheckTask = mapTasks.get(taskId);
        checkCanStockChecking(curStockCheckTask);
        short preStatus = curStockCheckTask.getStatus();
        curStockCheckTask.setStatus(GoodsCoworkStockCheckTask.Status.FINISHED);
        GoodsCoworkStockCheckTask parentStockCheckTask = mapTasks.get(parentTaskId);

        boolean allFinished = allStockCheckTasks
                .stream()
                .filter(it -> it.getType() == GoodsCoworkStockCheckTask.Type.CO_CHILD)
                .allMatch(it -> it.getStatus() == GoodsCoworkStockCheckTask.Status.FINISHED
                        || it.getStatus() == GoodsCoworkStockCheckTask.Status.DELETED);
        if (allFinished) {
            parentStockCheckTask.setStatus(GoodsCoworkStockCheckTask.Status.FINISHED);
        }

        //这里比较恶心，完成了还可以点
        if (preStatus != GoodsCoworkStockCheckTask.Status.FINISHED) {
            parentStockCheckTask.setFinishCount(parentStockCheckTask.getFinishCount() + 1);
        }

        return curStockCheckTask;
    }

    public void dealEmployeeLeaveClinicMessage(String chainId, String clinicId, String employeeId) {
        // 员工离职，删除盘点任务，如果是子任务，只删除子任务，并且更新主任务的任务数；如果是主任务都删除
        List<GoodsCoworkStockCheckTask> allEmployeeCheckTaskList = goodsCoworkTaskRepository.findAllByClinicIdAndOwnerIdAndStatusIn(clinicId, employeeId, Arrays.asList(GoodsCoworkStockCheckTask.Status.CREATED));
        if (CollectionUtils.isEmpty(allEmployeeCheckTaskList)) {
            // 没有未完成的任务，看是否有已完成的主任务，需要删除
            doDeleteFinishTask(clinicId, employeeId);
            return;
        }
        log.info("dealEmployeeLeaveClinicMessage taskList:{}", JsonUtils.dump(allEmployeeCheckTaskList));
        List<Long> taskIdList = allEmployeeCheckTaskList.stream().map(GoodsCoworkStockCheckTask::getTaskId).distinct().collect(Collectors.toList());
        // 主任务
        List<GoodsCoworkStockCheckTask> mainTaskList = allEmployeeCheckTaskList.stream().filter(it -> it.getType() == GoodsCoworkStockCheckTask.Type.CO_MAIN).collect(Collectors.toList());
        // 子任务
        List<GoodsCoworkStockCheckTask> childTaskList = allEmployeeCheckTaskList.stream().filter(it -> it.getType() == GoodsCoworkStockCheckTask.Type.CO_CHILD).collect(Collectors.toList());
        List<Long> parentTaskIdList = mainTaskList.stream().map(GoodsCoworkStockCheckTask::getParentTaskId).distinct().collect(Collectors.toList());
        List<Long> childParentTaskIdList = childTaskList.stream().map(GoodsCoworkStockCheckTask::getParentTaskId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(childParentTaskIdList)) {
            // 子任务所在的所有任务
            List<GoodsCoworkStockCheckTask> updateCountParentTaskList = new ArrayList<>();
            Map<Long, Integer> parentIdToDeleteCount = new HashMap<>();
            List<GoodsCoworkStockCheckTask> allChildTaskList = goodsCoworkTaskRepository.findAllByClinicIdAndParentTaskIdIn(clinicId, childParentTaskIdList);
            Map<Long, List<GoodsCoworkStockCheckTask>> parentIdToTaskList = allChildTaskList.stream().collect(Collectors.groupingBy(GoodsCoworkStockCheckTask::getParentTaskId));
            parentIdToTaskList.forEach((parentTaskId, taskList) -> {
                if (taskIdList.contains(parentTaskId)) {
                    // 如果主任务id在删除任务中，不用修改任务数，都要删除
                    return;
                }
                for (GoodsCoworkStockCheckTask task : taskList) {
                    if (task.getType() == GoodsCoworkStockCheckTask.Type.CO_MAIN) {
                        updateCountParentTaskList.add(task);
                    } else {
                        if (!taskIdList.contains(task.getTaskId())) {
                            continue;
                        }
                        // 需要删除
                        parentIdToDeleteCount.compute(parentTaskId, (k, deleteCount) -> deleteCount == null ? 1 : deleteCount + 1);
                    }
                }
            });
            updateCountParentTaskList.forEach(it -> {
                Integer deleteCount = parentIdToDeleteCount.get(it.getParentTaskId());
                if (deleteCount == null) {
                    return;
                }
                it.setTaskCount(it.getTaskCount() - deleteCount);
            });
        }
        if (!CollectionUtils.isEmpty(parentTaskIdList)) {
            // 主任务中其他子任务
            List<GoodsCoworkStockCheckTask> otherTaskList = goodsCoworkTaskRepository.findAllByClinicIdAndParentTaskIdInAndType(clinicId, parentTaskIdList, GoodsCoworkStockCheckTask.Type.CO_CHILD);
            otherTaskList.forEach(it -> {
                if (taskIdList.contains(it.getTaskId())) {
                    return;
                }
                taskIdList.add(it.getTaskId());
                allEmployeeCheckTaskList.add(it);
            });
        }
        // 查询所有job
        allEmployeeCheckTaskList.forEach(it -> it.setStatus(GoodsCoworkStockCheckTask.Status.DELETED));
        List<GoodsCoworkStockCheckJob> allJobList = goodsCoworkJobRepository.findAllByChainIdAndClinicIdAndTaskIdInAndStatusIn(chainId, clinicId,
                taskIdList, queryJobStatusList);
        allJobList.forEach(it -> {
            it.setStatus(GoodsCoworkStockCheckJob.Status.DELETED);
            FillUtils.fillLastModifiedBy(it, GoodsUtils.DEFAULT_ID);
        });

        doDeleteFinishTask(clinicId, employeeId);
    }

    private void doDeleteFinishTask(String clinicId, String employeeId) {
        List<GoodsCoworkStockCheckTask> finishedMainTaskList = goodsCoworkTaskRepository.findAllByClinicIdAndOwnerIdAndStatusAndType(clinicId, employeeId, GoodsCoworkStockCheckTask.Status.FINISHED, GoodsCoworkStockCheckTask.Type.CO_MAIN);
        if (CollectionUtils.isEmpty(finishedMainTaskList)) {
            return;
        }
        log.info("dealEmployeeLeaveClinicMessage mainFinishedTaskList:{}", JsonUtils.dump(finishedMainTaskList));
        List<Long> mainTaskIdList = finishedMainTaskList.stream().map(GoodsCoworkStockCheckTask::getParentTaskId).distinct().collect(Collectors.toList());
        List<GoodsCoworkStockCheckTask> allTaskList = goodsCoworkTaskRepository.findAllByClinicIdAndParentTaskIdIn(clinicId, mainTaskIdList);
        allTaskList.forEach(it -> it.setStatus(GoodsCoworkStockCheckTask.Status.DELETED));
    }

}
