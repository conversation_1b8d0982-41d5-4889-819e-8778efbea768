package cn.abcyun.cis.goods.service;
/*
 * 外部接口 服务分发
 * 1.由这个Service分发到就具体业务实现的Service上
 * 2.事务相关的注解都加到个Service的方法注解上
 */


import cn.abcyun.bis.rpc.sdk.bis.model.goods.mall.MallGoodsSkuAbstractVO;
import cn.abcyun.bis.rpc.sdk.bis.model.order.mall.MallOrderListToPurchaseOrder;
import cn.abcyun.bis.rpc.sdk.bis.model.supplier.SupplierGoodsItem;
import cn.abcyun.bis.rpc.sdk.bis.model.supplier.SupplierGoodsMappingView;
import cn.abcyun.bis.rpc.sdk.cis.message.goods.SpuStatusChangeMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.useroperationlog.OpSource;
import cn.abcyun.bis.rpc.sdk.cis.message.useroperationlog.OpType;
import cn.abcyun.bis.rpc.sdk.cis.message.useroperationlog.OperationObjectType;
import cn.abcyun.bis.rpc.sdk.cis.model.approval.ApprovalConstant;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.*;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.exam.BatchUpdateExamAssayDeviceReq;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.exam.DeleteExamAssayDeviceReq;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.exam.QueryExamSamplePipeByIdsReq;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.receive.UpdateReceiveOrderTransportRecordReq;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslog.GoodsStockLogItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslog.QueryByStockIdsReq;
import cn.abcyun.bis.rpc.sdk.cis.model.search.SearchResultRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.GetImplNationalGoodsWarningCountRsp;
import cn.abcyun.bis.rpc.sdk.property.base.TableKey;
import cn.abcyun.bis.rpc.sdk.property.model.TableHeaderEmployeeItem;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.model.CisClinicType;
import cn.abcyun.cis.commons.transform.CalculateTraceableCodeCountReq;
import cn.abcyun.cis.commons.transform.CalculateTraceableCodeCountRsp;
import cn.abcyun.cis.commons.transform.SpecificationAnalyzer;
import cn.abcyun.cis.commons.transform.SpecificationTransformResponse;
import cn.abcyun.cis.commons.util.*;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.core.redis.RedisLock;
import cn.abcyun.cis.core.util.SpringUtils;
import cn.abcyun.cis.core.util.Validators;
import cn.abcyun.cis.goods.amqp.RocketMqProducer;
import cn.abcyun.cis.goods.cache.redis.*;
import cn.abcyun.cis.goods.conf.GoodsShebaoConfig;
import cn.abcyun.cis.goods.conf.SuggestConfiguration;
import cn.abcyun.cis.goods.consts.Constant;
import cn.abcyun.cis.goods.consts.StockInOrderType;
import cn.abcyun.cis.goods.domain.*;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.dto.list.NonStockGoodsListGoodsDto;
import cn.abcyun.cis.goods.dto.list.QueryStockGoodsListGoodsReqDto;
import cn.abcyun.cis.goods.dto.list.StockGoodsListGoodsDtoCount;
import cn.abcyun.cis.goods.dto.list.StockGoodsListGoodsDtoV2;
import cn.abcyun.cis.goods.dto.price.ModifyPriceOrderResult;
import cn.abcyun.cis.goods.dto.stockin.StockInOrderEnableSettleByOrderSummaryInfo;
import cn.abcyun.cis.goods.entity.GoodsStockDeliveryOrder;
import cn.abcyun.cis.goods.entity.GoodsStockInspectOrder;
import cn.abcyun.cis.goods.entity.GoodsStockReceiveOrder;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.exception.CisGoodsServiceNoRollbackException;
import cn.abcyun.cis.goods.listener.GoodsModifyPriceOrderListener;
import cn.abcyun.cis.goods.mapper.GoodsMapper;
import cn.abcyun.cis.goods.model.*;
import cn.abcyun.cis.goods.model.GoodsSysType;
import cn.abcyun.cis.goods.model.b2b.GoodsPurchaseOrder;
import cn.abcyun.cis.goods.repository.*;
import cn.abcyun.cis.goods.schedule.AsyncSchedulePullSheBaoMatchCodeTask;
import cn.abcyun.cis.goods.service.b2b.*;
import cn.abcyun.cis.goods.service.exam.ExamService;
import cn.abcyun.cis.goods.service.exam.ExaminationDeviceConnectApplyService;
import cn.abcyun.cis.goods.service.exam.ExaminationSamplePipeService;
import cn.abcyun.cis.goods.service.export.GoodsAsyncExportService;
import cn.abcyun.cis.goods.service.hisversion.GoodsHisVersionService;
import cn.abcyun.cis.goods.service.order.DeliveryOrderService;
import cn.abcyun.cis.goods.service.order.InspectOrderService;
import cn.abcyun.cis.goods.service.order.ReceiveOrderService;
import cn.abcyun.cis.goods.service.order.ReceiveOrderServiceBase;
import cn.abcyun.cis.goods.service.purchase.GoodsClaimOrderService;
import cn.abcyun.cis.goods.service.purchase.GoodsPurchaseOrderService;
import cn.abcyun.cis.goods.service.query.*;
import cn.abcyun.cis.goods.service.query.batchprocessor.CategoryBatchModifyProcessor;
import cn.abcyun.cis.goods.service.query.batchprocessor.CategoryCustomTypeIdBatchModifyProcessor;
import cn.abcyun.cis.goods.service.query.batchprocessor.GoodsBatchModifyProcessor;
import cn.abcyun.cis.goods.service.query.batchprocessor.ProjectExecuteDepartmentBatchModifyProcessor;
import cn.abcyun.cis.goods.service.query.batchprocessor.ProjectIsSellBatchModifyProcessor;
import cn.abcyun.cis.goods.service.query.batchprocessor.ProjectPackageUnitBatchModifyProcessor;
import cn.abcyun.cis.goods.service.query.batchprocessor.ProjectSampleGroupBatchModifyProcessor;
import cn.abcyun.cis.goods.service.query.batchprocessor.ProjectSampleTypeBatchModifyProcessor;
import cn.abcyun.cis.goods.service.query.batchprocessor.ProjectSupplierBatchModifyProcessor;
import cn.abcyun.cis.goods.service.rpc.*;
import cn.abcyun.cis.goods.service.search.SearchGoodsService;
import cn.abcyun.cis.goods.service.settlement.GoodsSettlementOrderService;
import cn.abcyun.cis.goods.service.stock.GoodsStockBatchesService;
import cn.abcyun.cis.goods.service.stock.GoodsStockLockingService;
import cn.abcyun.cis.goods.service.stockcheck.*;
import cn.abcyun.cis.goods.service.stockin.GoodsStockInOrderService;
import cn.abcyun.cis.goods.service.stockout.GoodsStockOutOrderService;
import cn.abcyun.cis.goods.service.stockoutreason.GoodsStockOutReasonTemplateService;
import cn.abcyun.cis.goods.service.stockreception.GoodsStockReceptionOrderService;
import cn.abcyun.cis.goods.service.stocktrans.GoodsStockTransOrderService;
import cn.abcyun.cis.goods.service.todo.GoodsTodoCountService;
import cn.abcyun.cis.goods.service.update.*;
import cn.abcyun.cis.goods.service.update.modgoods.GoodsUpdateServiceBase;
import cn.abcyun.cis.goods.service.update.newgoods.GoodsCreateServiceBase;
import cn.abcyun.cis.goods.service.update.newspu.GoodsSpuCreateServiceBase;
import cn.abcyun.cis.goods.service.update.newspu.GoodsSpuUpdateServiceBase;
import cn.abcyun.cis.goods.stock.service.*;
import cn.abcyun.cis.goods.utils.*;
import cn.abcyun.cis.goods.utils.PinyinUtils;
import cn.abcyun.cis.goods.view.ExaminationDeviceConnectApplyView;
import cn.abcyun.cis.goods.view.alihealth.AliTraceCodeQueryResult;
import cn.abcyun.cis.goods.vo.GetGoodsClinicPriceReq;
import cn.abcyun.cis.goods.vo.GetGoodsClinicPriceRsp;
import cn.abcyun.cis.goods.vo.UpdateAliHealthConfigReq;
import cn.abcyun.cis.goods.vo.backend.RpcTodoCountRsp;
import cn.abcyun.cis.goods.vo.backend.ShebaoConfig;
import cn.abcyun.cis.goods.vo.frontend.*;
import cn.abcyun.cis.goods.vo.frontend.B2BWarningStocksRsp;
import cn.abcyun.cis.goods.vo.frontend.batches.*;
import cn.abcyun.cis.goods.vo.frontend.batches.GetGoodsSubClinicInfosRsp;
import cn.abcyun.cis.goods.vo.frontend.config.*;
import cn.abcyun.cis.goods.vo.frontend.customtype.UpdateClinicCustomTypeReq;
import cn.abcyun.cis.goods.vo.frontend.customtype.UpdateClinicCustomTypeRsp;
import cn.abcyun.cis.goods.vo.frontend.exam.*;
import cn.abcyun.cis.goods.vo.frontend.export.GetGoodsAsyncExportInfoReq;
import cn.abcyun.cis.goods.vo.frontend.export.GetGoodsAsyncExportInfoRsp;
import cn.abcyun.cis.goods.vo.frontend.goodsinfo.*;
import cn.abcyun.cis.goods.vo.frontend.goodsinfo.price.*;
import cn.abcyun.cis.goods.vo.frontend.goodsinfo.price.ModifyPriceTipsView;
import cn.abcyun.cis.goods.vo.frontend.goodsinfo.spu.ClientGoodsSpuCreateReq;
import cn.abcyun.cis.goods.vo.frontend.goodslist.*;
import cn.abcyun.cis.goods.vo.frontend.goodslist.GetStockGoodsListRsp;
import cn.abcyun.cis.goods.vo.frontend.goodslog.StockBatchesLogView;
import cn.abcyun.cis.goods.vo.frontend.goodsupdate.*;
import cn.abcyun.cis.goods.vo.frontend.infusion.OutpatientConfigVO;
import cn.abcyun.cis.goods.vo.frontend.infusion.OutpatientInfusionRelatedConfigVO;
import cn.abcyun.cis.goods.vo.frontend.infusion.OutpatientInfusionRelatedConfigsVO;
import cn.abcyun.cis.goods.vo.frontend.infusion.updateOutpatientInfusionRelatedConfigReq;
import cn.abcyun.cis.goods.vo.frontend.jenkins.JenkinsUpdateShortIdReq;
import cn.abcyun.cis.goods.vo.frontend.lock.GoodsLockRsp;
import cn.abcyun.cis.goods.vo.frontend.lock.ScGoodsLockingGoodsLockReq;
import cn.abcyun.cis.goods.vo.frontend.order.*;
import cn.abcyun.cis.goods.vo.frontend.pharmacy.*;
import cn.abcyun.cis.goods.vo.frontend.pharmacy.GoodsPharmacyView;
import cn.abcyun.cis.goods.vo.frontend.price.CreateModifyPriceOrderReq;
import cn.abcyun.cis.goods.vo.frontend.price.PrintGoodsPriceReq;
import cn.abcyun.cis.goods.vo.frontend.purchase.*;
import cn.abcyun.cis.goods.vo.frontend.receive.ImportStockInReceiveReq;
import cn.abcyun.cis.goods.vo.frontend.receive.ImportStockInReceiveRsp;
import cn.abcyun.cis.goods.vo.frontend.receive.UpdateFinishedReceiveOrderItemReq;
import cn.abcyun.cis.goods.vo.frontend.receive.UpdateReceiveOrderInfoReq;
import cn.abcyun.cis.goods.vo.frontend.reception.*;
import cn.abcyun.cis.goods.vo.frontend.reception.GoodsStockReceptionOrderView;
import cn.abcyun.cis.goods.vo.frontend.settlement.*;
import cn.abcyun.cis.goods.vo.frontend.settlement.SettlementOrderView;
import cn.abcyun.cis.goods.vo.frontend.shebao.ShebaoChangeOrderView;
import cn.abcyun.cis.goods.vo.frontend.stockcheck.*;
import cn.abcyun.cis.goods.vo.frontend.stockcheck.GoodsStockCheckOrderItemView;
import cn.abcyun.cis.goods.vo.frontend.stockcheck.GoodsStockCheckOrderView;
import cn.abcyun.cis.goods.vo.frontend.stockin.*;
import cn.abcyun.cis.goods.vo.frontend.stockin.GetStockInOrderByBatchIdReq;
import cn.abcyun.cis.goods.vo.frontend.stockin.GoodsStockInOrderItemView;
import cn.abcyun.cis.goods.vo.frontend.stockin.GoodsStockInOrderView;
import cn.abcyun.cis.goods.vo.frontend.stockin.GoodsStockOrderItemDiffView;
import cn.abcyun.cis.goods.vo.frontend.stockout.*;
import cn.abcyun.cis.goods.vo.frontend.stockout.GoodsStockOutOrderItemView;
import cn.abcyun.cis.goods.vo.frontend.stockout.GoodsStockOutOrderView;
import cn.abcyun.cis.goods.vo.frontend.stockoutreason.GoodsStockOutReasonTemplateRsp;
import cn.abcyun.cis.goods.vo.frontend.stockoutreason.UpdateGoodsStockOutReasonTemplateReq;
import cn.abcyun.cis.goods.vo.frontend.stocktrans.*;
import cn.abcyun.cis.goods.vo.frontend.stocktrans.GoodsStockTransOrderView;
import cn.abcyun.cis.goods.vo.frontend.supplier.*;
import cn.abcyun.cis.goods.vo.frontend.tracecode.*;
import cn.abcyun.cis.goods.vo.frontend.types.*;
import cn.abcyun.cis.goods.vo.frontend.types.GoodsFeeTypeView;
import cn.abcyun.common.log.marker.AbcLogMarker;
import cn.abcyun.common.model.AbcListPage;
import com.alibaba.excel.EasyExcel;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import reactor.util.function.Tuple3;

import javax.servlet.http.HttpServletResponse;
import javax.validation.groups.Default;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GoodsService {

    private static final Logger sLogger = LoggerFactory.getLogger(GoodsService.class);

    private static final String GOODS_PROCESSING_IMPORT_KEY_PREFIX = "sc_goods:processing:import";

    private final SearchGoodsService searchGoodsService;
    private final GoodsInfoModifyManageService goodsInfoModifyManageService;
    private final GoodsPriceService goodsPriceService;
    private final GoodsSpuService goodsSpuService;
    private final B2BGoodsPurchaseCarService b2BGoodsPurchaseCarService;
    private final B2BGoodsMorePurchaseCarService b2BGoodsMorePurchaseCarService;
    private final B2BGoodsPurchaseOrderService b2BGoodsPurchaseOrderService;
    private final GoodsStockService goodsStockService;
    private final GoodsTraceCodeService goodsTraceCodeService;
    private final B2BGoodsInOrderService b2BGoodsInOrderService;
    private final B2BGoodsRelationService b2BGoodsRelationService;
    private final GoodsStockCheckService goodsStockCheckService;
    private final GoodsListService goodsListService;
    private final GoodsListV2Service goodsListV2Service;
    private final GoodsCustomTypeRepository goodsCustomTypeRepository;
    private final GoodsExtendRepository goodsExtendRepository;
    private final QueryCisGoodsListService queryCisGoodsListService;
    private final QueryBisGoodsListService queryBisGoodsService;
    private final BisGoodsService bisGoodsService;
    private final GoodsJenkinsRpcService goodsJenkinsRpcService;
    private final GoodsStockOutOrderService goodsStockOutService;
    private final GoodsCustomUnitService goodsCustomUnitService;
    private final GoodsStockCheckOrderService goodsStockCheckOrderService;
    private final GoodsStockInOrderService goodsStockInOrderService;
    private final GoodsTodoCountService goodsTodoCountService;
    private final GoodsSettlementOrderService goodsSettlementOrderService;
    private final GoodsPharmacyService goodsPharmacyService;
    private final GoodsStockBatchesService goodsStockBatchesService;
    private final SupplierUtils supplierUtils;
    private final OrganUtils organUtils;
    private final EmployeeUtils employeeUtils;
    private final GoodsRedisUtils goodsRedisUtils;
    private final RocketMqProducer rocketMqProducer;
    private final RedisUtils redisUtils;
    private final CisShebaoService cisShebaoService;
    private final CisClinicService cisClinicService;
    private final GoodsMapper goodsMapper;
    private final GoodsModifyPriceOrderRepository goodsModifyPriceOrderRepository;
    private final GoodsModifyPriceOrderItemRepository goodsModifyPriceOrderItemRepository;
    private final CisGoodsLogService cisGoodsLogService;
    private final AsyncSchedulePullSheBaoMatchCodeTask asyncSchedulePullSheBaoMatchCodeTask;
    private final GoodsPurchasePlanService goodsPurchasePlanService;
    private final SuggestConfiguration suggestConfiguration;
    private final GoodsStatService goodsStatService;
    private final GoodsStockTransOrderService goodsStockTransOrderService;
    private final GoodsSysTypeService goodsSysTypeService;
    private final GoodsCustomTypeRedisService goodsCustomTypeRedisService;
    private final GoodsFeeTypeRedisService goodsFeeTypeRedisService;
    private final PropertyService propertyService;
    @Value("${abc.env}")
    private String abcEnv;
    private final GoodsDrugIdentificationCodeRelationRepository goodsDrugIdentificationCodeRelationRepository;

    private final ExamService examService;
    private final ExaminationSamplePipeService examinationSamplePipeService;
    private final ExaminationDeviceConnectApplyService examinationDeviceConnectApplyService;
    private final GoodsConfigRedisCache goodsConfigRedisCache;
    private final GoodsShebaoConfig goodsShebaoConfig;

    private final StringRedisTemplate stringRedisTemplate;

    private final GoodsInOutTaxRateRuleService goodsInOutTaxRateRuleService;

    private final UsageInfoAssociationService usageInfoAssociationService;

    private final GoodsStockLockingService goodsStockLockingService;
    private final AliHealthService aliHealthService;
    private final GoodsStockReceptionOrderService goodsStockReceptionOrderService;
    private final HisWardService hisWardService;
    private final GoodsAsyncExportService goodsAsyncExportService;
    private final GoodsShebaoService goodsShebaoService;
    private final GoodsMedicalStatService goodsMedicalStatService;
    private final GoodsSysFeeCategoryService goodsSysFeeCategoryService;
    private final GoodsHisVersionService goodsHisVersionService;
    private final GspService gspService;
    private final GoodsClaimOrderService goodsClaimOrderService;
    private final ReceiveOrderService receiveOrderService;
    private final InspectOrderService inspectOrderService;
    private final DeliveryOrderService deliveryOrderService;
    private final GoodsPurchaseOrderService goodsPurchaseOrderService;
    private final GoodsRepository goodsRepository;
    private final BisGoodsSupplierService bisGoodsSupplierService;
    private final ObjectProvider<GoodsModifyPriceOrderListener> goodsModifyPriceOrderListeners;
    private final BisOrderService bisOrderService;
    private final GoodsStockOutReasonTemplateService goodsStockOutReasonTemplateService;
    private final GoodsStockInRepository goodsStockInRepository;
    private final CisPromotionService cisPromotionService;
    private final CisCrmService cisCrmService;
    private final RegistrationService registrationService;

    public GoodsService(GoodsPriceService goodsPriceService,
                        SearchGoodsService searchGoodsService,
                        GoodsStockCheckOrderService goodsStockCheckOrderService,
                        GspService gspService,
                        AliHealthService aliHealthService,
                        GoodsSettlementOrderService goodsSettlementOrderService,
                        SuggestConfiguration suggestConfiguration,
                        GoodsFeeTypeRedisService goodsFeeTypeRedisService,
                        SupplierUtils supplierUtils,
                        GoodsRepository goodsRepository,
                        ExamService examService,
                        GoodsPharmacyService goodsPharmacyService,
                        GoodsCustomUnitService goodsCustomUnitService,
                        GoodsRedisUtils goodsRedisUtils,
                        CisPromotionService cisPromotionService,
                        GoodsCustomTypeRedisService goodsCustomTypeRedisService,
                        GoodsDrugIdentificationCodeRelationRepository goodsDrugIdentificationCodeRelationRepository,
                        GoodsStockInOrderService goodsStockInOrderService,
                        GoodsInfoModifyManageService goodsInfoModifyManageService,
                        GoodsListV2Service goodsListV2Service,
                        GoodsTraceCodeService goodsTraceCodeService,
                        GoodsPurchaseOrderService goodsPurchaseOrderService,
                        EmployeeUtils employeeUtils,
                        GoodsMedicalStatService goodsMedicalStatService,
                        GoodsSpuService goodsSpuService,
                        B2BGoodsPurchaseCarService b2BGoodsPurchaseCarService,
                        QueryBisGoodsListService queryBisGoodsService,
                        PropertyService propertyService,
                        ReceiveOrderService receiveOrderService,
                        InspectOrderService inspectOrderService,
                        GoodsShebaoService goodsShebaoService,
                        GoodsStockBatchesService goodsStockBatchesService,
                        GoodsStockTransOrderService goodsStockTransOrderService,
                        B2BGoodsMorePurchaseCarService b2BGoodsMorePurchaseCarService,
                        B2BGoodsPurchaseOrderService b2BGoodsPurchaseOrderService,
                        GoodsMapper goodsMapper,
                        GoodsCustomTypeRepository goodsCustomTypeRepository,
                        GoodsExtendRepository goodsExtendRepository,
                        GoodsStockService goodsStockService,
                        B2BGoodsInOrderService b2BGoodsInOrderService,
                        BisGoodsService bisGoodsService,
                        GoodsAsyncExportService goodsAsyncExportService,
                        GoodsConfigRedisCache goodsConfigRedisCache,
                        GoodsShebaoConfig goodsShebaoConfig,
                        ObjectProvider<GoodsModifyPriceOrderListener> goodsModifyPriceOrderListeners,
                        GoodsStockInRepository goodsStockInRepository,
                        QueryCisGoodsListService queryCisGoodsListService,
                        GoodsStockReceptionOrderService goodsStockReceptionOrderService,
                        GoodsHisVersionService goodsHisVersionService,
                        GoodsModifyPriceOrderItemRepository goodsModifyPriceOrderItemRepository,
                        DeliveryOrderService deliveryOrderService,
                        RedisUtils redisUtils,
                        GoodsModifyPriceOrderRepository goodsModifyPriceOrderRepository,
                        StringRedisTemplate stringRedisTemplate,
                        GoodsStockLockingService goodsStockLockingService,
                        HisWardService hisWardService,
                        GoodsStockOutReasonTemplateService goodsStockOutReasonTemplateService,
                        GoodsInOutTaxRateRuleService goodsInOutTaxRateRuleService,
                        UsageInfoAssociationService usageInfoAssociationService,
                        GoodsStatService goodsStatService,
                        CisCrmService cisCrmService,
                        GoodsClaimOrderService goodsClaimOrderService,
                        BisGoodsSupplierService bisGoodsSupplierService,
                        B2BGoodsRelationService b2BGoodsRelationService,
                        GoodsJenkinsRpcService goodsJenkinsRpcService,
                        CisGoodsLogService cisGoodsLogService,
                        GoodsSysFeeCategoryService goodsSysFeeCategoryService,
                        CisShebaoService cisShebaoService,
                        RegistrationService registrationService,
                        ExaminationDeviceConnectApplyService examinationDeviceConnectApplyService,
                        BisOrderService bisOrderService,
                        GoodsListService goodsListService,
                        GoodsStockCheckService goodsStockCheckService,
                        CisClinicService cisClinicService,
                        RocketMqProducer rocketMqProducer,
                        GoodsPurchasePlanService goodsPurchasePlanService,
                        GoodsSysTypeService goodsSysTypeService,
                        AsyncSchedulePullSheBaoMatchCodeTask asyncSchedulePullSheBaoMatchCodeTask,
                        ExaminationSamplePipeService examinationSamplePipeService,
                        GoodsStockOutOrderService goodsStockOutService,
                        OrganUtils organUtils,
                        GoodsTodoCountService goodsTodoCountService) {
        this.goodsPriceService = goodsPriceService;
        this.searchGoodsService = searchGoodsService;
        this.goodsStockCheckOrderService = goodsStockCheckOrderService;
        this.gspService = gspService;
        this.aliHealthService = aliHealthService;
        this.goodsSettlementOrderService = goodsSettlementOrderService;
        this.suggestConfiguration = suggestConfiguration;
        this.goodsFeeTypeRedisService = goodsFeeTypeRedisService;
        this.supplierUtils = supplierUtils;
        this.goodsRepository = goodsRepository;
        this.examService = examService;
        this.goodsPharmacyService = goodsPharmacyService;
        this.goodsCustomUnitService = goodsCustomUnitService;
        this.goodsRedisUtils = goodsRedisUtils;
        this.cisPromotionService = cisPromotionService;
        this.goodsCustomTypeRedisService = goodsCustomTypeRedisService;
        this.goodsDrugIdentificationCodeRelationRepository = goodsDrugIdentificationCodeRelationRepository;
        this.goodsStockInOrderService = goodsStockInOrderService;
        this.goodsInfoModifyManageService = goodsInfoModifyManageService;
        this.goodsListV2Service = goodsListV2Service;
        this.goodsTraceCodeService = goodsTraceCodeService;
        this.goodsPurchaseOrderService = goodsPurchaseOrderService;
        this.employeeUtils = employeeUtils;
        this.goodsMedicalStatService = goodsMedicalStatService;
        this.goodsSpuService = goodsSpuService;
        this.b2BGoodsPurchaseCarService = b2BGoodsPurchaseCarService;
        this.queryBisGoodsService = queryBisGoodsService;
        this.propertyService = propertyService;
        this.receiveOrderService = receiveOrderService;
        this.inspectOrderService = inspectOrderService;
        this.goodsShebaoService = goodsShebaoService;
        this.goodsStockBatchesService = goodsStockBatchesService;
        this.goodsStockTransOrderService = goodsStockTransOrderService;
        this.b2BGoodsMorePurchaseCarService = b2BGoodsMorePurchaseCarService;
        this.b2BGoodsPurchaseOrderService = b2BGoodsPurchaseOrderService;
        this.goodsMapper = goodsMapper;
        this.goodsCustomTypeRepository = goodsCustomTypeRepository;
        this.goodsExtendRepository = goodsExtendRepository;
        this.goodsStockService = goodsStockService;
        this.b2BGoodsInOrderService = b2BGoodsInOrderService;
        this.bisGoodsService = bisGoodsService;
        this.goodsAsyncExportService = goodsAsyncExportService;
        this.goodsConfigRedisCache = goodsConfigRedisCache;
        this.goodsShebaoConfig = goodsShebaoConfig;
        this.goodsModifyPriceOrderListeners = goodsModifyPriceOrderListeners;
        this.goodsStockInRepository = goodsStockInRepository;
        this.queryCisGoodsListService = queryCisGoodsListService;
        this.goodsStockReceptionOrderService = goodsStockReceptionOrderService;
        this.goodsHisVersionService = goodsHisVersionService;
        this.goodsModifyPriceOrderItemRepository = goodsModifyPriceOrderItemRepository;
        this.deliveryOrderService = deliveryOrderService;
        this.redisUtils = redisUtils;
        this.goodsModifyPriceOrderRepository = goodsModifyPriceOrderRepository;
        this.stringRedisTemplate = stringRedisTemplate;
        this.goodsStockLockingService = goodsStockLockingService;
        this.hisWardService = hisWardService;
        this.goodsStockOutReasonTemplateService = goodsStockOutReasonTemplateService;
        this.goodsInOutTaxRateRuleService = goodsInOutTaxRateRuleService;
        this.usageInfoAssociationService = usageInfoAssociationService;
        this.goodsStatService = goodsStatService;
        this.cisCrmService = cisCrmService;
        this.goodsClaimOrderService = goodsClaimOrderService;
        this.bisGoodsSupplierService = bisGoodsSupplierService;
        this.b2BGoodsRelationService = b2BGoodsRelationService;
        this.goodsJenkinsRpcService = goodsJenkinsRpcService;
        this.cisGoodsLogService = cisGoodsLogService;
        this.goodsSysFeeCategoryService = goodsSysFeeCategoryService;
        this.cisShebaoService = cisShebaoService;
        this.registrationService = registrationService;
        this.examinationDeviceConnectApplyService = examinationDeviceConnectApplyService;
        this.bisOrderService = bisOrderService;
        this.goodsListService = goodsListService;
        this.goodsStockCheckService = goodsStockCheckService;
        this.cisClinicService = cisClinicService;
        this.rocketMqProducer = rocketMqProducer;
        this.goodsPurchasePlanService = goodsPurchasePlanService;
        this.goodsSysTypeService = goodsSysTypeService;
        this.asyncSchedulePullSheBaoMatchCodeTask = asyncSchedulePullSheBaoMatchCodeTask;
        this.examinationSamplePipeService = examinationSamplePipeService;
        this.goodsStockOutService = goodsStockOutService;
        this.organUtils = organUtils;
        this.goodsTodoCountService = goodsTodoCountService;
    }

    /*
     * Goods 的搜索接口，需要加注解。因为可能会加载Redis缓存 导致加载请求走到了主节点上去
     * 合作药房唯一一个会走到goods，由goods来分发到多分区的接口
     * 1.前端要根据分区跨域搜索，鉴权是一个很大的工作量，交付压力打，先来到goods。
     * 2.从空中药房的搜索数据来看，搜索延迟还好
     * 3.本方法为了性能也做了优化，本分区内直接调用本地方法，不走[abc-cis-co-pharmacy-service]
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsRecommendRsp recommendGoods(GoodsCommonSearchReq req) throws CisGoodsServiceException {
        //合作药房搜药
        if (req.getPharmacyType() != null && req.getPharmacyType() == GoodsConst.PharmacyType.CO_PHARMACY) {
            return searchGoodsService.coPharmacySearchGoodsByGoodsRedis(req);
        }
        //外置处方推荐
        if (StringUtils.isEmpty(req.getKeyWord()) && GoodsUtils.compareStrEqual(req.getBiz(), "prescriptionExternal")) {
            GoodsRecommendReq recommendReq = new GoodsRecommendReq();
            BeanUtils.copyProperties(req, recommendReq);
            return searchGoodsService.recommendGoodsNested(recommendReq);
        }
        //其他本门店搜索
        adjustGoodsSearchReq(req);
        return searchGoodsService.commonSearchGoodsByGoodsRedis(suggestConfiguration.getClientSearchGoodsNested(), req);
    }

    /*
     * 通用搜索推荐
     * Goods 的搜索接口，需要加注解。因为可能会加载Redis缓存 导致加载请求走到了主节点上去
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsRecommendRsp commonSearchGoods(GoodsCommonSearchReq req) throws CisGoodsServiceException {
        adjustGoodsSearchReq(req);
        return searchGoodsService.commonSearchGoodsByGoodsRedis(suggestConfiguration.getClientSearchGoodsNested(), req);
    }

    private void adjustGoodsSearchReq(GoodsCommonSearchReq req) {
        List<JsonGoodsType> excludeJsonGoodsTypeList = req.getExcludeJsonType();
        if (!CollectionUtils.isEmpty(excludeJsonGoodsTypeList)) {
            Set<Integer> excludeJsonTypeSets = excludeJsonGoodsTypeList.stream().map(JsonGoodsType::getType).collect(Collectors.toSet());

            List<Integer> clientReqTypeList = req.getType();
            List<Integer> clientReqSubTypeList = req.getSubType();
            List<JsonGoodsType> clientReqJsonTypeList = req.getJsonType();
            if (CollectionUtils.isEmpty(clientReqTypeList) && CollectionUtils.isEmpty(clientReqSubTypeList) && CollectionUtils.isEmpty(clientReqJsonTypeList)) {
                // 全都为空忘jsonType上加
                List<GoodsSysType> goodsSysTypesList = goodsSysTypeService.getHisGoodsSysTypesList();
                if (CollectionUtils.isNotEmpty(goodsSysTypesList)) {
                    List<Integer> goodsTypeList = goodsSysTypesList.stream()
                            // 这里要把义齿加工的类型排除掉
                            .filter(goodsSysType -> goodsSysType.getGoodsType() != GoodsConst.GoodsType.PROCESSING && !excludeJsonTypeSets.contains((int) goodsSysType.getGoodsType()))
                            .map(e -> (int) e.getGoodsType()).distinct().collect(Collectors.toList());
                    List<JsonGoodsType> jsonGoodsTypeList = goodsTypeList.stream().map(goodsType -> {
                        JsonGoodsType jsonGoodsType = new JsonGoodsType();
                        jsonGoodsType.setType(goodsType);
                        //jsonGoodsType.setSubType(subTypeList);
                        return jsonGoodsType;
                    }).collect(Collectors.toList());
                    req.setJsonType(jsonGoodsTypeList);
                }
            } else {
                if (!CollectionUtils.isEmpty(clientReqTypeList)) {
                    clientReqTypeList.removeIf(excludeJsonTypeSets::contains);
                    req.setType(clientReqTypeList);
                }
                if (!CollectionUtils.isEmpty(clientReqJsonTypeList)) {
                    clientReqJsonTypeList.removeIf(clientReqJsonType -> excludeJsonTypeSets.contains(clientReqJsonType.getType()));
                    req.setJsonType(clientReqJsonTypeList);
                }
            }
        }
    }

    /*
     * 搜索内置系统Goods
     * Goods 的搜索接口，需要加注解。因为可能会加载Redis缓存 导致加载请求走到了主节点上去
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsRecommendRsp searchInnerSystemGoods(String clinicId, String keyWord, Integer type, Integer subType, String extendSpec, Integer combineType, List<String> deviceModelIds, Integer offset, Integer limit) throws CisGoodsServiceException {
        return searchGoodsService.searchInnerSystemGoods(clinicId, keyWord, type, subType, extendSpec, combineType, deviceModelIds, offset, limit);
    }

    /*
     * 搜索BisGoods
     *
     * @param chainId            连锁ID
     * @param clinicId           门店ID
     * @param keyWord            搜索关键字
     * @param vendorId           供应商ID
     * @param typeId             类型ID 中药饮片还是中药颗粒
     * @param withDomainMedicine 是否包含系统库存药
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<Object> bisGoodsSearch(String chainId, String clinicId, String keyWord, String vendorId, Integer typeId,
                                              Long usageScopeId, Long vendorUsageScopeId, Integer withDomainMedicine) throws CisGoodsServiceException {
        return searchGoodsService.bisGoodsSearch(chainId, clinicId, keyWord, vendorId, typeId, usageScopeId, vendorUsageScopeId, withDomainMedicine);
    }

    /*
     * 新建药品资料，搜索系统库存药模版
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public SearchDomainMedicineGoodsRsp domainMedicineSearchCreateGoods(String chainId,
                                                                        String clinicId,
                                                                        String employeeId,
                                                                        String keyword,
                                                                        List<Integer> typeList,
                                                                        List<Integer> subTypeList,
                                                                        List<Integer> typeIdList,
                                                                        String dispSpec,
                                                                        String medicineCadn,
                                                                        String manufacturerFull,
                                                                        Integer needAggs, Integer offset, Integer limit) throws CisGoodsServiceException {
        return searchGoodsService.domainMedicineSearchCreateGoods(chainId,
                clinicId,
                employeeId,
                keyword,
                typeList,
                subTypeList,
                typeIdList,
                dispSpec,
                medicineCadn,
                manufacturerFull,
                needAggs, offset, limit);
    }

    /*
     * searchForCreate
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public SearchResultRsp searchForCreateGoods(String chainId,
                                                Map<String, String> requestParam,
                                                int hisType) throws CisGoodsServiceException {
        return searchGoodsService.searchForCreateGoods(chainId,
                requestParam,
                hisType);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp disableStockGoods(ClientGoodsDisableReq clientReq) throws CisGoodsServiceException {
        return goodsInfoModifyManageService.disableGoods(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp disableJenkinsGoods(ClientGoodsDisableReq clientReq) throws CisGoodsServiceException {
        return goodsInfoModifyManageService.disableJenkinsGoods(clientReq);
    }


    /*
     * 药品停用升级
     * 如果没有库存会立即停用，否者可以标记是立即停用还是售完再停用
     * <p>
     * 参数:goodsId 要停用的商品
     * clinicId 如果指定，这指定停用某个诊所（只有主店可以停用子店，子店停用自己），不指定会被设置成http头里面的clnicId
     * disableStatus 停用状态 0 启用，10 停入库不停销售,20停入库和停销售
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp stockGoodsV2Disable(ClientGoodsDisableReq clientReq) throws CisGoodsServiceException {
        return goodsInfoModifyManageService.stockGoodsV2Disable(clientReq, false);
    }

    /*
     * 新建药品资料
     */
    public GoodsItem createGoods(String clinicId, String chainId, String employeeId, ClientGoodsCreateReq req) throws CisGoodsServiceException {
        String redisKey = "goods:create:check:" + chainId + ":" + clinicId + ":" + getGoodsCreateMd5Key(req);
        goodsRedisUtils.goodsCreateCheckAndSet(redisKey);
        //建药品资料
        GoodsCreateServiceBase createServiceBase;
        try {
            createServiceBase = goodsInfoModifyManageService.createNewGoods(clinicId, chainId, employeeId, req);
        } finally {
            goodsRedisUtils.goodsCreateClearKey(redisKey);
        }
        //异步事务处理发消息等
        goodsInfoModifyManageService.doAfterCreateGoods(createServiceBase, req);
        //异步事务 发送消息写es
        goodsStatService.sendMessageToESNestGoodsMainDb(createServiceBase.getGoods());
        return createServiceBase.createResponse();
    }

    private String getGoodsCreateMd5Key(ClientGoodsCreateReq clientReq) {
        String sb = String.valueOf(clientReq.getType()) + clientReq.getSubType() + clientReq.getTypeId() + clientReq.getFeeTypeId() +
                (clientReq.getName() != null ? clientReq.getName() : "") +
                (clientReq.getMedicineCadn() != null ? clientReq.getMedicineCadn() : "") +
                (clientReq.getCMSpec() != null ? clientReq.getCMSpec() : "");
        return MD5Utils.sign(sb);
    }


    /*
     * 批量修改进价
     * 接口
     */
    public ModifyPriceOrderView createPriceOrder(CreateModifyPriceOrderReq clientReq) throws CisGoodsServiceException {
        ModifyPriceOrderResult modifyPriceOrderResult;
        /*
         * 加一下并发控制
         * */
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_PRICE + clientReq.getClinicId(), clientReq.getMd5Key(), GoodsRedisUtils.CHECKING_MODIFY_PRICE);
        try {
            //批量改价
            sLogger.info("批量改价 开始:{}", JsonUtils.dump(clientReq));
            modifyPriceOrderResult = goodsPriceService.createOrUpdatePriceOrder(clientReq, gspService);
            //改价打长日志 方便查问题
            sLogger.info(AbcLogMarker.MARKER_LONG_TIME, "批量改价  batchModifyGoodsPrices req:{},updatePriceGoodsList={}", clientReq, JsonUtils.dump(modifyPriceOrderResult));
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_PRICE + clientReq.getClinicId(), clientReq.getMd5Key(), false);
            throw exp;
        }
        //这里要根据 入库单是否完成 来决定是否是 删除还是 设置标记位
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_PRICE + clientReq.getClinicId(), clientReq.getMd5Key(), true);

        String clinicId;
        if (GoodsPrivUtils.isSingleMode(clientReq.getHeaderClinicType(), clientReq.getHeaderViewMode())) {
            clinicId = clientReq.getHeaderChainId();
        } else {
            clinicId = clientReq.getHeaderClinicId();
        }
        //异步事务
        goodsPriceService.batchModifyGoodsPricesAfter(modifyPriceOrderResult.getGoodsModifyPriceOrder(), modifyPriceOrderResult.getGoodsList(), clinicId, clientReq.getHeaderChainId());
        long sTime = System.currentTimeMillis();
        ModifyPriceOrderView priceOrderView = GoodsPriceService.toModifyPriceOrderView(modifyPriceOrderResult.getGoodsModifyPriceOrder(), organUtils, employeeUtils);
        rocketMqProducer.sendUserOperationLogMessage(//批量调价
                clientReq.getHeaderChainId(),
                clientReq.getHeaderClinicId(),
                clientReq.getHeaderEmployeeId(),
                OpType.UPDATE,
                0,
                OperationObjectType.GOODS,
                OperationObjectType.GoodsSubObjectType.BATCH_ADJUST_PRICE,
                OpSource.PC, //TODO xian
                priceOrderView.getId().toString(),
                priceOrderView.getOrderNo(),
                null,
                JsonUtils.dumpAsJsonNode(clientReq),
                JsonUtils.dumpAsJsonNode(priceOrderView),//may be 2 big
                null
        );
        if (goodsModifyPriceOrderListeners != null) {
            goodsModifyPriceOrderListeners.forEach(goodsModifyPriceOrderListener -> goodsModifyPriceOrderListener.onGoodsModifyPriceOrderCreate(modifyPriceOrderResult.getGoodsModifyPriceOrder(), modifyPriceOrderResult.getGoodsModifyPriceOrder().getList(), clientReq.getHeaderEmployeeId()));
        }
        sLogger.info("批量调价 耗时数量统计(发消息，写操作日志)  耗时:{}", (System.currentTimeMillis() - sTime));

        return priceOrderView;
    }

    /*
     * 批量修改进价
     */
    public ModifyPriceOrderResult updatePriceOrder(CreateModifyPriceOrderReq clientReq) throws CisGoodsServiceException {
        //批量改价
        ModifyPriceOrderResult modifyPriceOrderResult;
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_PRICE + clientReq.getClinicId(), clientReq.getMd5Key(), GoodsRedisUtils.CHECKING_MODIFY_PRICE);
        try {
            //批量改价
            if (!clientReq.getIsSubmit() && (
                    clientReq.getSourceType() == GoodsModifyPriceOrder.SourceFrom.FROM_GOODS_LIST
                            || clientReq.getSourceType() == GoodsModifyPriceOrder.SourceFrom.FROM_PURCHASE_ORDER
                            || clientReq.getSourceType() == GoodsModifyPriceOrder.SourceFrom.FROM_GOODS_BATCH_LIST

            )) {
                long sTime = System.currentTimeMillis();
                Pair<ClinicConfig, String> clinicConfigPair = goodsPriceService.getClinicConfigAndModifyClinicId(clientReq);
                GoodsPriceService.GoodsLoadResult goodsLoadResult = goodsPriceService.loadGoodsAndRelatedData(clientReq, clinicConfigPair.getFirst(), sTime);
                modifyPriceOrderResult = goodsPriceService.addToDefaultDraftPriceOrder(clientReq, clinicConfigPair, goodsLoadResult, gspService, GoodsPriceService.CREATE_DRAFT_ORDER_QUICK_PRICE);
                /*
                 * 改价核心函数
                 * */
                if (modifyPriceOrderResult.getUpdatePriceNow()) {
                    goodsPriceService.updateGoodsPriceByModifyPriceOrder(modifyPriceOrderResult.getGoodsModifyPriceOrder(),
                            modifyPriceOrderResult.getCurGoodsModifyPriceOrderItems(),
                            clinicConfigPair.getFirst(),
                            goodsLoadResult.getGoodsIdToGoods(),
                            goodsLoadResult.getPriceWrapper(),
                            clientReq.getHeaderEmployeeId());
                    sLogger.info("批量调价耗时统计 实际调价 耗时:{}", (System.currentTimeMillis() - sTime));
                }
                /*
                 *  从 addToDefaultDraftPriceOrder 移动出来，因为在修改goods的时候也调用了 addToDefaultDraftPriceOrder。
                 *  如果里面也刷goods stat的itemId，会和goods修改刷的字段冲突，冲掉。
                 * */
                ModifyPriceOrderResult finalModifyPriceOrderResult = modifyPriceOrderResult;
                GoodsUtils.runAfterTransaction(() -> {
                            long sTimeIn = System.currentTimeMillis();
                            // 把待生效的itemId刷到goodsStat
                            goodsPriceService.updateGoodsStatWaitingEffectPrice(clientReq.getHeaderChainId(), clientReq.getGoodsIdList(), finalModifyPriceOrderResult.getUpdateGoodsStatClinicIdList());
                            sLogger.info("批量调价耗时统计 调价事务提交后 updateGoodsStatHasModifyPriceOrder 耗时:{}", (System.currentTimeMillis() - sTimeIn));
                        }
                );
            } else {
                modifyPriceOrderResult = goodsPriceService.createOrUpdatePriceOrder(clientReq, gspService);
            }

            //改价打长日志 方便查问题
            sLogger.info(AbcLogMarker.MARKER_LONG_TIME, "批量改价  batchModifyGoodsPrices req:{},updatePriceGoodsList={}", clientReq, modifyPriceOrderResult);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_PRICE + clientReq.getClinicId(), clientReq.getMd5Key(), false);
            throw exp;
        }
        //这里要根据 入库单是否完成 来决定是否是 删除还是 设置标记位
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_PRICE + clientReq.getClinicId(), clientReq.getMd5Key(), true);

        //改价打长日志 方便查问题
        sLogger.info(AbcLogMarker.MARKER_LONG_TIME, "批量改价  batchModifyGoodsPrices req:{},updatePriceGoodsList={}", clientReq, modifyPriceOrderResult);

        String clinicId;
        if (GoodsPrivUtils.isSingleMode(clientReq.getHeaderClinicType(), clientReq.getHeaderViewMode())) {
            clinicId = clientReq.getHeaderChainId();
        } else {
            clinicId = clientReq.getHeaderClinicId();
        }
        //异步事务
        GoodsModifyPriceOrder goodsModifyPriceOrder = modifyPriceOrderResult.getGoodsModifyPriceOrder();
        goodsPriceService.batchModifyGoodsPricesAfter(goodsModifyPriceOrder, modifyPriceOrderResult.getGoodsList(), clinicId, clientReq.getHeaderChainId());

        if (goodsModifyPriceOrder.getStatus() == GoodsModifyPriceOrder.Status.FINISHED && goodsModifyPriceOrderListeners != null) {
            goodsModifyPriceOrderListeners.forEach(goodsModifyPriceOrderListener -> goodsModifyPriceOrderListener.onGoodsModifyPriceOrderConfirm(goodsModifyPriceOrder, goodsModifyPriceOrder.getList(), clientReq.getHeaderEmployeeId()));
        }
        return modifyPriceOrderResult;
    }

    /*
     * 批量修改进价
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp passPriceOrder(ReviewPriceOrderReq clientReq) throws CisGoodsServiceException {
        //批量改价
        goodsPriceService.doReviewOrRejectPriceOrder(
                ApprovalConstant.InstStatus.PASS,
                null,
                clientReq.getHeaderChainId(),
                clientReq.getHeaderEmployeeId(),
                clientReq.getComment(),
                clientReq.getOrderId()
        );
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    /*
     * 批量修改进价
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp deletePriceOrder(String chainId, String employeeId, Long orderId) throws CisGoodsServiceException {
        //批量改价
        goodsPriceService.deletePriceOrder(chainId, employeeId, orderId);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp cancelPriceOrder(String chainId, String employeeId, Long orderId) throws CisGoodsServiceException {
        //批量改价
        goodsPriceService.cancelPriceOrder(chainId, employeeId, orderId);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ModifyPriceTipsView getModifyPriceOrderItemTips(String chainId, Long orderItemId, String clinicId) throws CisGoodsServiceException {
        CacheSupplier<Map<String, String>> memberTypeMapSupplier = new CacheSupplier<>(() -> cisCrmService.getChainMemberTypeInfo(chainId));
        return GoodsPriceService.getModifyPriceOrderItemTips(
                cisClinicService,
                goodsModifyPriceOrderItemRepository,
                goodsModifyPriceOrderRepository,
                goodsMapper,
                goodsRedisUtils,
                chainId,
                Collections.singletonList(orderItemId),
                clinicId,
                null,
                memberTypeMapSupplier).getOrDefault(orderItemId, new ModifyPriceTipsView());
    }

    /*
     * 查修改价格列表
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetModifyPriceListRsp getModifyPriceOrderList(GetModifyPriceListReq clientReq) throws CisGoodsServiceException {
        return goodsPriceService.getModifyPriceOrderList(clientReq);
    }

    /*
     * 查修改价格 单个订单
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ModifyPriceOrderView getModifyPriceOrderInfo(GetModifyPriceOrderReq clientReq) throws CisGoodsServiceException {
        return goodsPriceService.getModifyPriceOrderInfo(clientReq);
    }

    /*
     * 标记价签已经打印
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp printGoodsPrice(PrintGoodsPriceReq clientReq) throws CisGoodsServiceException {
        return goodsPriceService.printGoodsPrice(clientReq);
    }

    /*
     * 批量修改进价
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetGoodsListForModifyGoodsPricesRsp getGoodsListForBatchModifyPrices(GetGoodsListForModifyGoodsPricesReq req) throws CisGoodsServiceException {
        return goodsPriceService.getGoodsListForBatchModifyPrices(req);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetGoodsLogRsp getGoodsLogList(GetGoodsLogReq clientReq) throws CisGoodsServiceException {
        return goodsInfoModifyManageService.getGoodsLogList(clientReq);
    }


    /*
     * 修改药品资料
     */
    public GoodsItem updateGoods(String goodsId, String clinicId, String chainId, String employeeId, ClientGoodsCreateReq req) throws CisGoodsServiceException {
        /*
         * 药店管家，总部可以给指定门店改药品资料，在这里把门店替换了
         * */
        String headerClinicId = clinicId;
        if (!StringUtils.isEmpty(req.getClinicId())) {
            clinicId = req.getClinicId();
        }
        ObjectNode objectNode = JsonUtils.createObjectNode();
        Pair<Boolean, Boolean> needApprovalPari = goodsInfoModifyManageService.preCheckGoodsModifyNeedSendToApproval(chainId, headerClinicId, goodsId, req, objectNode);
        boolean needApproval = needApprovalPari.getFirst();
        String redisKey = "goods:update:check:" + chainId + ":" + clinicId + ":" + goodsId;
        goodsRedisUtils.goodsUpdateCheckAndSet(redisKey);
        //修改药品资料
        GoodsUpdateServiceBase goodsUpdateServiceBase;
        GoodsItem goodsItem;
        if (!needApproval || (req.getGspModifyStatus() != null && req.getGspModifyStatus() == ApprovalConstant.InstStatus.PASS)) {
            req.setNeedApproval(needApproval);
            try {
                goodsUpdateServiceBase = goodsInfoModifyManageService.updateOldGoods(goodsId, headerClinicId, clinicId, chainId, employeeId, req);
            } finally {
                goodsRedisUtils.goodsCreateClearKey(redisKey);
            }
            //异步事务处理发消息等
            goodsUpdateServiceBase.doAfterUpdateGoods();
            //异步事务 发送消息写es
            if (goodsUpdateServiceBase.getGoods().getEsNeedSyncFlag() == GoodsUtils.SwitchFlag.ON) {
                goodsStatService.sendMessageToESNestGoodsMainDb(goodsUpdateServiceBase.getGoods());
            }
            goodsItem = goodsUpdateServiceBase.createResponse();
        } else {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "药店修改审批, chainId = {}, clinicId= {}", chainId, clinicId);
            ClientGoodsCreateReq sourceClientReq = JsonUtils.readValue(JsonUtils.dump(req), ClientGoodsCreateReq.class);
            req.setNeedApproval(needApproval);
            try {
                goodsUpdateServiceBase = goodsInfoModifyManageService.updatePharmacyOldGoodsApproval(goodsId, headerClinicId, clinicId, chainId, employeeId, req);
            } finally {
                goodsRedisUtils.goodsCreateClearKey(redisKey);
            }
            gspService.getAndSetModifyGsp(objectNode, clinicId, employeeId, chainId);
            Goods goods = goodsUpdateServiceBase.doPharmacyApproval(sourceClientReq, objectNode);
            goodsInfoModifyManageService.updateGoodsGspModifyStatus(goods.getId(), goods.getGspModifyStatus(), goods.getGspModifyInstId());
            goodsUpdateServiceBase.doAfterUpdateGoodsForApproval();
            goodsItem = goodsUpdateServiceBase.createResponseForApproval();
        }
        //改价打长日志 方便查问题
        sLogger.info(AbcLogMarker.MARKER_LONG_TIME, "修改药品资料  updateGoods req:{},updatePriceGoodsList={}, employeeId={}", req, goodsUpdateServiceBase.getGoods(), employeeId);

        return goodsItem;
    }


    /*
     * 删除goods，事务在goodsInfoModifyManageService.deleteGoods
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteGoods(String goodsId, String clinicId, String chainId, int clinicType, int viewMode, String employeeId) throws CisGoodsServiceException {
        //goods删除
        Goods goods = goodsInfoModifyManageService.deleteGoods(goodsId, clinicId, chainId, clinicType, viewMode, employeeId, true);
        //发删除goods消息、 清除redis、移除随货单导入的映射关联关系数据
        goodsInfoModifyManageService.deleteGoodsAfter(cisClinicService.getClinicConfig(clinicId), Collections.singletonList(goods));
        goodsInfoModifyManageService.doDeleteGoodsAfter(cisClinicService.getClinicConfig(clinicId), goods, supplierUtils, employeeId);
    }

    /*
     * Goods删除的检查
     * 这里得操作是查库，但是都是用户输入的性能，并发不会太高问题不大 查的读库
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void checkDeleteGoods(CheckGoodsDeleteListReq req) throws CisGoodsServiceException {
        //发删除goods消息和 清除redis
        goodsInfoModifyManageService.checkDeleteGoods(req);
    }

    /*
     * Goods删除的检查
     * 这里得操作是查库，但是都是用户输入的性能，并发不会太高问题不大 查的读库
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void checkDisableGoods(CheckGoodsDisableListReq req) throws CisGoodsServiceException {
        //发删除goods消息和 清除redis
        goodsInfoModifyManageService.checkDisableGoods(req);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void checkGoodsShortId(CheckGoodsShortIdListReq req) throws CisGoodsServiceException {
        //发删除goods消息和 清除redis
        goodsInfoModifyManageService.checkGoodsShortId(req);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void checkGoodsBarCode(CheckGoodsBarCodeListReq req) throws CisGoodsServiceException {
        //发删除goods消息和 清除redis
        goodsInfoModifyManageService.checkGoodsBarCode(req);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp modifyGoodsSubClinicInfoList(GoodsModifySubClinicsPricesReq req) throws CisGoodsServiceException {
        //传入threadLocal参数,方便后面获取
        Goods goods = goodsPriceService.modifyGoodsSubClinicInfoList(req, goodsInfoModifyManageService, gspService);

        /*
         * 改了子店的一批 定价和禁用信息，重新刷一下
         * */
        if (GoodsUtils.isStockGoods(goods.getType())) {
            GoodsUtils.runAfterTransaction(() -> {
                //这个事务里面会清理redis
                goodsStatService.reFlushGoodsStat(Collections.singletonList(goods.getId()), goods.getOrganId(), cisClinicService.getClinicConfig(req.getHeaderClinicId()), false);
                rocketMqProducer.sendGoodsUpdateMessage(cisClinicService, cisClinicService.getClinicConfig(goods.getOrganId()), goods.getId(), goodsRedisUtils);
            });
        } else {
            GoodsUtils.runAfterTransaction(() -> {
                /*
                 * 项目这都强制重刷Stat 基于几点考虑
                 * 1.业务逻辑复杂
                 * 2.修改的量不大
                 * */
                goodsMedicalStatService.reFlushGoodsMedicalStatList(goods.getOrganId(), null, Collections.singletonList(goods.getId()), false, null);
                rocketMqProducer.sendGoodsUpdateMessage(cisClinicService, cisClinicService.getClinicConfig(goods.getOrganId()), goods.getId(), goodsRedisUtils);
            });
        }
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    /*
     * 单个goods的告警配置信息 改
     */
    @Transactional(rollbackFor = Exception.class)
    public void putGoodsWarnSetting(ClientGoodsWarningSetting req) throws CisGoodsServiceException {
        goodsInfoModifyManageService.putGoodsWarnSetting(req);
    }

    /*
     * 单个goods的告警配置信息 拉
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ClientGoodsWarningSetting getGoodsWarnSetting(ClientGoodsWarningSetting req) throws CisGoodsServiceException {
        return goodsInfoModifyManageService.getGoodsWarnSetting(req);
    }


    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public B2BWarningStocksRsp warningStocks(B2BWarningStocksReq req) throws CisGoodsServiceException {
        return b2BGoodsPurchaseCarService.warningStocks(req);
    }

    /*
     * 搜索采购计划 offset limit 没指定默认拉取0，20条
     * hisType/hisSubType指定
     */

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public B2BSingleShoppingPlanListRsp shoppingPlanList(B2BSingleShoppingPlanListReq req) throws CisGoodsServiceException {
        return b2BGoodsPurchaseCarService.shoppingPlanList(req);
    }

    /*
     * 增加或修改采购计划
     */
    @Transactional(rollbackFor = Exception.class)
    public B2BShoppingPlanOpsRsp addOrUpdateShoppingPlanList(B2BShoppingPlanUpdateReq req) throws CisGoodsServiceException {
        return b2BGoodsPurchaseCarService.addOrUpdateShoppingPlanList(req);
    }


    /*
     * 删除采购计划
     */
    @Transactional(rollbackFor = Exception.class)
    public B2BShoppingPlanOpsRsp deleteShoppingPlanList(B2BShoppingPlanDelReq req) throws CisGoodsServiceException {
        return b2BGoodsPurchaseCarService.deleteShoppingPlanList(req);
    }

    /*
     * 客户端提交采购计划
     */
    @Transactional(rollbackFor = Exception.class, noRollbackFor = CisGoodsServiceNoRollbackException.class)
    public B2BShoppingPlanPreparePurchaseRsp preparePurchaseShoppingPlan(B2BShoppingPlanPreparePurchaseReq req) throws CisGoodsServiceException, CisGoodsServiceNoRollbackException {
        return b2BGoodsPurchaseCarService.preparePurchaseShoppingPlan(req);
    }

    //这里很扯，默认可能没有采购计划，但是这里必须要给客户端返回一个采购计划，如果发现没有，要往db里面插入一个
    @Transactional(rollbackFor = Exception.class)
    public B2BShoppingPlanListRsp shoppingPlanSimpleList(String chainId, String clinicId, String employeeId) throws CisGoodsServiceException, CisGoodsServiceNoRollbackException {
        return b2BGoodsMorePurchaseCarService.shoppingPlanSimpleList(chainId, clinicId, employeeId);
    }

    @Transactional(rollbackFor = Exception.class)
    public B2BShoppingPlanListRsp.ShoppingPlanSimpleInfoView newShoppingPlanSimpleList(B2BNewShoppingPlanReq clientReq) throws CisGoodsServiceException, CisGoodsServiceNoRollbackException {
        return b2BGoodsMorePurchaseCarService.newShoppingPlanSimpleList(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp deleteShoppingPlanSimpleList(String chainId, String clinicId, String employeeId, String purchasePlanId) throws CisGoodsServiceException, CisGoodsServiceNoRollbackException {
        return b2BGoodsMorePurchaseCarService.deleteShoppingPlanSimpleList(chainId, clinicId, employeeId, purchasePlanId);
    }

    /*
     * 获取诊所订单的数量红点
     * 包括了以前老的线下集采订单
     * 用redis缓存
     * 【redis数量更新时机】，订单有变更的时候全量算一把，写入redis
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public B2BShoppingOrderCountView getClinicOrderCount(String clinicId, int clinicType) {
        try {
            B2BShoppingOrderCount count = b2BGoodsPurchaseOrderService.getClinicOrderCount(clinicId, clinicType);
            B2BShoppingOrderCountView view = new B2BShoppingOrderCountView();
            if (count != null) {
                view.setAuditingCount(count.getAuditingCount());

                //总店不要商城的 代付款 待收货 count
                if (clinicType != CisClinicType.CHAIN_HEAD_CLINIC) {
                    view.setWaitingPayCount(count.getWaitingPayCount());
                    view.setWaitingRecvGoodsCount(count.getWaitingRecvGoodsCount());
                    view.setWaitingSendGoodsCount(count.getWaitingSendGoodsCount());

                    MallOrderListToPurchaseOrder purchaseOrderListSimpleInfo = bisOrderService.getPurchaseOrderListSimpleInfo(clinicId, null);

                    if (Objects.nonNull(purchaseOrderListSimpleInfo)) {
                        view.setUnpaidCanPayCount(purchaseOrderListSimpleInfo.getUnpaidCanPayCount());
                    }
                }

                /*
                 * 商城订单的数量，表示是否买过，权限控制
                 * */
                view.setMallOrderCount(count.getWaitingPayCount() +
                        count.getWaitingPayCount() +
                        count.getWaitingSendGoodsCount() +
                        count.getFinishCount() +
                        count.getCloseCount()
                );
            }
            return view;
        } catch (Exception exp) {
            //临时保护下，避免总部redis的json对象转换失败，清理一下
            b2BGoodsPurchaseOrderService.clearReidsCacheOrderCount(clinicId);
            throw exp;
        }
    }


    /*
     * 获取诊所的订单列表
     * 包括以前老的线下集采订单
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public B2BShoppingOrderListRsp getPurchaseOrderList(B2BShoppingOrderListReq req) throws CisGoodsServiceException {
        return b2BGoodsPurchaseOrderService.getPurchaseOrderList(req);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportPurchaseOrderList(B2BShoppingOrderListReq req) throws CisGoodsServiceException {
        b2BGoodsPurchaseOrderService.exportPurchaseOrderList(req);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public B2BShoppingMergePayOrderListRsp getAllUnPayPurchaseOrderList(String chainId, String clinicId, String employeeId) throws CisGoodsServiceException {

        return b2BGoodsPurchaseOrderService.getAllUnPayPurchaseOrderList(chainId, clinicId, employeeId);
    }

    /*
     * 获取诊所的订单列表
     * 包括以前老的线下集采订单
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public B2BShoppingOrderRsp getClinicOrderDetail(B2BShoppingOrderReq clientReq) throws CisGoodsServiceException {
        B2BShoppingOrderRsp clientRsp = b2BGoodsPurchaseOrderService.getPurchaseOrder(clientReq);
        List<TableHeaderEmployeeItem> headerList = null;
        try {
            int chainMode = clientReq.getViewMode() == Organ.ViewMode.SINGLE ? GoodsUtils.SwitchFlag.OFF : GoodsUtils.SwitchFlag.ON;
            JsonNode jsonNode = propertyService.getTableHeaderEmployeeItemsByTableKey(clientReq.getEmployeeId(),
                    TableKey.GOODS_STOCK_PURCHASE_ORDER,
                    abcEnv,
                    chainMode,
                    clientReq.getClinicType(),
                    (int) GoodsUtils.SwitchFlag.ON,
                    (int) GoodsUtils.SwitchFlag.ON,
                    null,
                    null,
                    null, null, null);
            headerList = JsonUtils.readValue(jsonNode, new com.fasterxml.jackson.core.type.TypeReference<List<TableHeaderEmployeeItem>>() {
            });
            if (clientRsp.getOrderType() == GoodsPurchaseOrder.OrderType.PURCHASE_ORDER && !CollectionUtils.isEmpty(headerList)) {
                headerList = headerList.stream().filter(it -> {
                    boolean mall = GoodsUtils.compareStrEqual(it.getKey(), "goodsStockInfo.realPurchaseCount")
                            || GoodsUtils.compareStrEqual(it.getKey(), "goodsStockInfo.purchaseGoodsName")
                            || GoodsUtils.compareStrEqual(it.getKey(), "goodsStockInfo.purchaseGoodsDisplaySpec")
                            || GoodsUtils.compareStrEqual(it.getKey(), "goodsStockInfo.purchaseGoodsCostPrice");
                    return !mall;
                }).collect(Collectors.toList());
            }
        } catch (Exception exp) {
            sLogger.error("pageListStockGoods getGoodsHeader={}", exp.toString());
        }
        clientRsp.setTableHeader(headerList);
        return clientRsp;
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportClinicOrderDetail(B2BShoppingOrderReq req) throws CisGoodsServiceException {
        b2BGoodsPurchaseOrderService.exportPurchaseOrder(req);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportBatchInExport(HttpServletResponse httpServletResponse, String chainId, String clinicId, String employeeId, Integer isAll, List<Integer> typeIdList, List<Long> customTypeIdList, List<Long> otherCustomTypeTypeIdList, Integer pharmacyNo) throws CisGoodsServiceException, IOException {
        b2BGoodsPurchaseOrderService.exportBatchInExport(httpServletResponse, chainId, clinicId, employeeId, isAll, typeIdList, customTypeIdList, otherCustomTypeTypeIdList, pharmacyNo);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportClinicOrderAccompanyingBillDetail(B2BShoppingOrderReq clientReq) throws CisGoodsServiceException {
        List<TableHeaderEmployeeItem> headerList = null;
        clientReq.parameterCheck();
        B2BShoppingOrderRsp clientRsp = b2BGoodsPurchaseOrderService.getPurchaseOrder(clientReq);
        try {
            int chainMode = clientReq.getViewMode() == Organ.ViewMode.SINGLE ? GoodsUtils.SwitchFlag.OFF : GoodsUtils.SwitchFlag.ON;
            JsonNode jsonNode = propertyService.getTableHeaderEmployeeItemsByTableKey(clientReq.getEmployeeId(),
                    TableKey.GOODS_STOCK_PURCHASE_ORDER,
                    abcEnv,
                    chainMode,
                    clientReq.getClinicType(),
                    (int) GoodsUtils.SwitchFlag.ON,
                    (int) GoodsUtils.SwitchFlag.ON,
                    null,
                    null, null, null, null);
            headerList = JsonUtils.readValue(jsonNode, new com.fasterxml.jackson.core.type.TypeReference<List<TableHeaderEmployeeItem>>() {
            });
            if (clientRsp.getOrderType() == GoodsPurchaseOrder.OrderType.PURCHASE_ORDER && !CollectionUtils.isEmpty(headerList)) {
                headerList = headerList.stream().filter(it -> {
                    boolean mall = GoodsUtils.compareStrEqual(it.getKey(), "goodsStockInfo.realPurchaseCount")
                            || GoodsUtils.compareStrEqual(it.getKey(), "goodsStockInfo.purchaseGoodsName")
                            || GoodsUtils.compareStrEqual(it.getKey(), "goodsStockInfo.purchaseGoodsDisplaySpec")
                            || GoodsUtils.compareStrEqual(it.getKey(), "goodsStockInfo.purchaseGoodsCostPrice");
                    return !mall;
                }).collect(Collectors.toList());
            }
        } catch (Exception exp) {
            sLogger.error("pageListStockGoods getGoodsHeader={}", exp.toString());
        }
        b2BGoodsPurchaseOrderService.exportClinicOrderAccompanyingBillDetail(clientReq, clientRsp, headerList);
    }

    /*
     * 获取诊所的订单列表
     * 包括以前老的线下集采订单
     */
    @Transactional(rollbackFor = Exception.class)
    public B2BMallInOrderRsp signOrderRecvAndInOrder(B2BShoppingOrderReq req) throws CisGoodsServiceException {
        return b2BGoodsInOrderService.signOrderRecvAndInOrder(req);
    }

    /*
     * 重新再买一单
     */
    @Transactional(rollbackFor = Exception.class)
    public B2BShoppingPlanPreparePurchaseRsp rebuyPurchaseOrder(String clinicId,
                                                                String chainId,
                                                                String employeeId,
                                                                String orderId) throws CisGoodsServiceException {
        return b2BGoodsPurchaseOrderService.rebuyPurchaseOrder(clinicId, chainId, employeeId, orderId);
    }

    /*
     * 按商城订单号，拉取入库单详情
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public B2BMallInOrderRsp getMallOrderInOrderDetail(B2BMallInOrderReq req) throws CisGoodsServiceException {
        return b2BGoodsInOrderService.getMallOrderInOrderDetail(req);
    }

    /*
     * 拉取所有可以入库的 入库单列表
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<B2BMallInOrderView> getMallOrderInOrderList(B2BMallInOrderListReq req) throws CisGoodsServiceException {
        return b2BGoodsInOrderService.getMallOrderInOrderList(req);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetGoodsStockJsonForStockInRsp importGoodsStockForGoodsStockIn(GetGoodsStockJsonForStockInReq clientReq) throws IOException {
        return b2BGoodsInOrderService.importGoodsStockForGoodsStockIn(clientReq);
    }

    /*********************************************************************************************
     * 计算：某个供应商历史销量数据 用于入库
     * 方案选择：
     *      1.业务库自己 goodsStockLog计算。(统计可以快速出，但是不能用于业务数据)
     *      2.计算过程很耗时 所以做成异步任务的方式.提交过后前端不停来轮结果
     *      3.因为是异步任务，可能执行任务的机器被重启？如何重启没执行完的任务？
     *       a.通过前端的轮巡  执行销量的任务进程是否还在，不在就立即重启一个任务继续执行
     *           缺点是：前端不来轮，这个任务就起不起来
     *       b.通过定时任务，轮训是否有需要计算销量的任务
     *          缺点：重，有个定时器
     *     4.                           任务key                       redisKey        dbOrder         备注
     *           chainId-clinicId-supplierId-beginDate-endDate          N               N             新建计算缓存任务
     *                                                                  Y               N             任务新建中
     *                                                                  N               Y             1.建任务的进程已经挂掉；但是任务已经建出来了/跑完了 看order状态
     *                                                                  Y               Y             1.建任务的进程在，任务还在跑/或跑完了，看order状态
     *********************************************************************************************/
    public GetGoodsStockJsonForStockInRsp autoInOrderSupplierGoodsStock(SupplierGoodsStockInReq clientReq) throws IOException {
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(clientReq.getClinicId());
        if (clinicConfig == null || CollectionUtils.isEmpty(clinicConfig.getPharmacyList())) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "加载配置失败");
        }

        GoodsPharmacyView pharmacyView = clinicConfig.getPharmacyList().stream().filter(it -> it.getType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY).findFirst().orElse(null);
        if (pharmacyView == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "你还未开通代价代配药房");
        }
        GetGoodsStockJsonForStockInRsp clientRsp = b2BGoodsInOrderService.createInOrderSupplierGoodsStock(clientReq);
        if (clientRsp.getOrderId() != null) { //通过orderId来进行是否需要启动任务的标识，新任务或者老任务进程被杀了
            startAsyncInOrderSupplierGoodsStock(clientRsp.getOrderId());
        }

        clientRsp.setPharmacyNo(pharmacyView.getNo());
        clientRsp.setPharmacyType(GoodsConst.PharmacyType.VIRTUAL_PHARMACY);
        return clientRsp;
    }


    public void startAsyncInOrderSupplierGoodsStock(Long orderId) throws IOException {
        while (b2BGoodsInOrderService.startCalSupplierGoodsStock(orderId)) {
            sLogger.info("startCalSupplierGoodsStock orderId={}", orderId);
        }
    }

    /*
     * 获取映射关系
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public B2BGoodsRelationRsp getRelation(B2BGoodsRelationReq req) throws CisGoodsServiceException {

        return b2BGoodsRelationService.getRelation(req);
    }


    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp setRelation(B2BGoodsRelationUpdateReq req) throws CisGoodsServiceException {
        return b2BGoodsRelationService.bindRelationV2(req);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public B2BShoppingRecommendGoodsRsp recommendGoodsOnShoppingPlan(B2BShoppingRecommendGoodsReq req) throws CisGoodsServiceException {
        return b2BGoodsPurchaseCarService.recommendGoodsOnShoppingPlan(req);
    }

    /*********************B2B商城入库相关********************************/
    public GoodsStockInOrderView createOrUpdateInProgressStockInOrder(CreateStockInOrderReq clientReq) throws CisGoodsServiceException {
        GoodsStockInOrderView returnView;
        ClinicConfig inOrderClinicConfig = cisClinicService.getClinicConfig(clientReq.getInOrderToClinicId());
        if (!clientReq.isNotCheckParam()) {
            GoodsStockRowErrorDetail errorDetail = new GoodsStockRowErrorDetail();
            errorDetail.setErrorTitle(StockInOrderType.RETURN_OUT_TYPES.contains(clientReq.getType()) ? "退货出库参数错误" : "入库参数错误");
            clientReq.reRowIndex();
            clientReq.parameterCheck(errorDetail, inOrderClinicConfig, true);
        }
        GoodsStockInOrderServiceBase inOrderServiceBase;
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getInOrderToClinicId(), clientReq.getReqMd5Key(), GoodsRedisUtils.CHECKING_IN);
        try {
            Pair<GoodsStockInOrderServiceBase, GoodsStockInOrderView> returnPairResult = goodsStockInOrderService.createOrUpdateInProgressStockInOrder(clientReq,deliveryOrderService);
            inOrderServiceBase = returnPairResult.getFirst();
            returnView = returnPairResult.getSecond();
            goodsStockInOrderService.asyncTrans(inOrderServiceBase);
            // 创建配送单
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getInOrderToClinicId(), clientReq.getReqMd5Key(), false);
            throw exp;
        }

        //这里要根据 入库单是否完成 来决定是否是 删除还是 设置标记位
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getInOrderToClinicId(),
                clientReq.getReqMd5Key(),
                inOrderServiceBase.getGoodsStockInOrder().isOrderStable());

        return returnView;
    }

    @Transactional(rollbackFor = Exception.class) //修改入库单
    public OpsCommonRsp inOrderUpdateFinishedOrderInfo(UpdateStockInOrderInfoReq clientReq) throws CisGoodsServiceException {
        goodsStockInOrderService.updateFinishedOrderInfo(clientReq);
        //这里要根据 入库单是否完成 来决定是否是 删除还是 设置标记位
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "入库单修改成功");
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsStockInOrderRsp> queryGoodsStockInItem(GoodsInspectOrderQueryReq inspectOrderQueryReq) {
        return goodsStockInOrderService.queryGoodsStockInSpecialGoodsGspInItem(inspectOrderQueryReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsStockInOrderRsp> queryGoodsStockLossOutGspItem(GoodsInspectOrderQueryReq inspectOrderQueryReq) {
        return goodsStockOutService.queryGoodsStockLossOutGspItem(inspectOrderQueryReq);
    }

    /**
     * 修改入库单
     * 20204.11 支持修改入库单为一个独立单据，并支持审批（抽象）
     */
    public void inOrderUpdateFinishedOrderItem(CreateStockInItemViewReq clientReq) throws CisGoodsServiceException {
        GoodsStockInOrderServiceBase serviceBase;
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getClinicId(), clientReq.reqMd5Key(), GoodsRedisUtils.CHECKING_UPDATE_STOCKIN);
        try {
            serviceBase = goodsStockInOrderService.updateFinishedOrderItem(clientReq);
            goodsStockInOrderService.asyncTrans(serviceBase);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getClinicId(), clientReq.reqMd5Key(), false);
            throw exp;
        }

        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getClinicId(),
                clientReq.reqMd5Key(),
                serviceBase.getGoodsStockInOrder().isOrderStable());
    }

    public GoodsStockInOrderView reviewOrConfirmOrRevokeStockOutOrder(CreateStockInOrderReviewReq clientReq) throws CisGoodsServiceException {
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING, clientReq.getReqMd5Key(), GoodsRedisUtils.CHECKING_OUT);
        Pair<GoodsStockInOrderServiceBase, GoodsStockInOrderView> returnPairResult;
        try {
            returnPairResult = goodsStockInOrderService.reviewOrConfirmOrRevokeStockOutOrder(clientReq, deliveryOrderService);
            //可能有入库，需要写goodsStat
            goodsStockInOrderService.asyncTrans(returnPairResult.getFirst());
            /*
             * 审核完成，药店要去刷采购单
             * TODO 如果这个单子被拒绝了，还要删关联单据
             * */
            if (clientReq.getGspApprovalFlag() == null && returnPairResult.getFirst().getGoodsStockInOrder().getPurchaseOrderId() != null) {
                goodsStockInOrderService.anotherAsynTransToReflushPurchaseOrder(returnPairResult.getFirst().getGoodsStockInOrder().getChainId(), returnPairResult.getFirst().getGoodsStockInOrder().getPurchaseOrderId());
            }
//            // 创建配送单
//            if (returnPairResult.getFirst().isNeedCreateDeliveryOrder()) {
//                deliveryOrderService.createDeliveryOrderOnReturnIn(returnPairResult.getFirst().getGoodsStockInOrder(), clientReq.getHeaderEmployeeId());
//            }
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING, clientReq.getReqMd5Key(), false);
            throw exp;
        }
        //审核这里，不管怎么样都 结束了 设置标记位 为完成
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING, clientReq.getReqMd5Key(), true);
        return returnPairResult.getSecond();
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsStockInOrderItemView> findStockInListByBatchId(OpenApiGoodsStockInByBatchIdReq clientReq) throws CisGoodsServiceException {
        return goodsStockInOrderService.findStockInListByBatchId(clientReq);
    }

    /*
     * 拉取入库单列表
     * 拉退货出库的入库单列表
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetStockInOrderListRsp getInOrdersList(GetStockInOrderListReq clientReq) throws CisGoodsServiceException {
        return goodsStockInOrderService.getInOrdersList(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportInOrdersList(GetStockInOrderListReq clientReq) throws CisGoodsServiceException {
        sLogger.info("exportInOrdersList getReqMd5Key={}", clientReq.getReqMd5Key());
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(), clientReq.getReqMd5Key(), GoodsRedisUtils.CHECKING_IN_EXPORT);
        try {
            goodsStockInOrderService.exportInOrdersList(clientReq);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(), clientReq.getReqMd5Key(), false);
            sLogger.info("exportInOrdersList getReqMd5Key={} deleted", clientReq.getReqMd5Key());
            throw exp;
        }
        //false 马上把key删掉
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(), clientReq.getReqMd5Key(), true);
        sLogger.info("exportInOrdersList getReqMd5Key={} deleted", clientReq.getReqMd5Key());
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockInOrderView getGoodsStockInOrderByBatchId(GetStockInOrderByBatchIdReq clientReq) throws CisGoodsServiceException {
        return goodsStockInOrderService.getGoodsStockInOrderByBatchId(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockInOrderView getGoodsStockInOrder(GetStockInOrderReq clientReq) throws CisGoodsServiceException {
        return goodsStockInOrderService.getGoodsStockInOrderView(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportGoodsStockInOrder(GetStockInOrderReq clientReq) throws CisGoodsServiceException {
        goodsStockInOrderService.exportGoodsStockInOrder(clientReq);
    }

    /*--------------------多人盘点任务------------------------*/
    @Transactional(rollbackFor = Exception.class)
    public CoworkGoodsStockCheckTaskRsp createCoworkGoodsStockCheckTask(CreateCoworkGoodsStockCheckTaskReq req) throws CisGoodsServiceException {
        return goodsStockCheckService.createCoworkGoodsStockCheckTask(req);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public CoworkGoodsStockCheckTaskRsp getCoworkGoodsStockCheckTask(GetGoodsCoworkTaskReq req) throws CisGoodsServiceException {
        return goodsStockCheckService.getCoworkGoodsStockCheckTask(req);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public CoworkGoodsStockCheckTaskListRsp getMyCoworkGoodsStockCheckTask(String clinicId, String employeeId, Integer pharmacyNo) throws CisGoodsServiceException {
        return goodsStockCheckService.getMyCoworkGoodsStockCheckTask(clinicId, employeeId, pharmacyNo);
    }

    /*
     * 事务放到checkService上
     */
    public OpsCommonRsp deleteCoworkGoodsStockCheckTask(GetGoodsCoworkTaskReq req) throws CisGoodsServiceException {
        goodsStockCheckService.deleteCoworkGoodsStockCheckTask(req);
        //异步，另起一个事务来干这个事，主要是要重新算一下主任务的md5，如果不算md5，删除后直接合并就永远合并不了了
        goodsStockCheckService.refreshGoodsStockCheckJobForDelete(req.getChainId(), req.getClinicId(), req.getHeaderClinicType(), req.getHeaderViewMode(), req.getLongParentTaskId());
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    /*--------------------多人盘点任务的具体盘点工作 药品盘点------------------------*/
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public CoworkGoodsStockCheckJobRsp getGoodsStockCheckJob(GetGoodsCoworkTaskReq req) throws CisGoodsServiceException {
        CoworkGoodsStockCheckJobRsp rsp = new CoworkGoodsStockCheckJobRsp();
        goodsStockCheckService.getGoodsStockCheckJob(req, rsp);
        return rsp;
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public CoworkGoodsStockCheckJobForBatchEditRsp getGoodsStockCheckForBatchEditJob(GetGoodsCoworkTaskReq req) throws CisGoodsServiceException {
        return goodsStockCheckService.getGoodsStockCheckForBatchEditJob(req);
    }

    /**
     * 多人盘点任务 不能同时并发操作，redis枷锁提示下
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp modifyGoodsStockCheckJob(ModifyCoworkGoodsStockCheckJobReq req) throws CisGoodsServiceException {
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + "cocheck:", req.getLongParentTaskId().toString(), GoodsRedisUtils.CHECKING_CHECK);
        try {
            goodsStockCheckService.modifyGoodsStockCheckJob(req);
        } catch (Exception exp) {
            //这里强制删除
            goodsRedisUtils.orderingFlagDelete(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + "cocheck:", req.getLongParentTaskId().toString());
            throw exp;
        }

        //这里强制删除,放事务后删除锁，同时提交，一人释放锁，但事务还没完成提交，另一人拿到锁进行合并，就会出现重复
        GoodsUtils.runAfterTransaction(() -> goodsRedisUtils.orderingFlagDelete(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + "cocheck:", req.getLongParentTaskId().toString()));
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp accomplishGoodsStockCheckJob(ModifyCoworkGoodsStockCheckJobReq req) throws CisGoodsServiceException {
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + "cocheck:", req.getLongParentTaskId().toString(), GoodsRedisUtils.CHECKING_CHECK);
        try {
            goodsStockCheckService.accomplishGoodsStockCheckJob(req);
        } catch (Exception exp) {
            //这里强制删除
            goodsRedisUtils.orderingFlagDelete(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + "cocheck:", req.getLongParentTaskId().toString());
            throw exp;
        }

        //这里强制删除,放事务后删除锁，同时提交，一人释放锁，但事务还没完成提交，另一人拿到锁进行合并，就会出现重复
        GoodsUtils.runAfterTransaction(() -> goodsRedisUtils.orderingFlagDelete(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + "cocheck:", req.getLongParentTaskId().toString()));
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    /*
     * 多人盘点拆成三个事务
     * 1一个事务做盘点
     * 2.盘点完后一异步线程两个串行事务 先更新goodsstat，再检查药品禁用
     */
    public OpsCommonRsp submitGoodsStockCheckJob(SubmitCoworkGoodsStockCheckTaskReq req) throws CisGoodsServiceException {
        OpsCommonRsp clientRsp;
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + "cocheck:", req.getLongParentTaskId().toString(), GoodsRedisUtils.CHECKING_CHECK);
        try {
            GoodsStockCheckCreateOrderByCoWork checkCreateOrderByCoWork = new GoodsStockCheckCreateOrderByCoWork();
            clientRsp = goodsStockCheckService.submitGoodsStockCheckJobV2(req, checkCreateOrderByCoWork);
            goodsStockCheckService.asyncTrans(checkCreateOrderByCoWork);
        } catch (Exception exp) {
            //这里强制删除
            goodsRedisUtils.orderingFlagDelete(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + "cocheck:", req.getLongParentTaskId().toString());
            throw exp;
        }

        //这里强制删除
        goodsRedisUtils.orderingFlagDelete(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + "cocheck:", req.getLongParentTaskId().toString());
        return clientRsp;
    }

    /*------------------------获取库存信息---------------------------------*/
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetGoodsBatchesForStockCheckRsp getStockBatchesForStockCheck(GetGoodsBatchesForStockCheckReq req) throws CisGoodsServiceException {
        return goodsStockBatchesService.getStockBatchesForStockCheck(req);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsLockingListRsp getGoodsLockingList(QueryGoodsLockingListReq clientReq) throws CisGoodsServiceException {
        return goodsStockLockingService.getGoodsLockingList(clientReq);
    }

    /*
     * 获取 拉取有效期预警的商品
     * <p>
     * 智能出库
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetExpiredWarnGoodsStockBatchesForStockOutRsp getExpiredWarnGoodsForStockOut(GetExpiredWarnGoodsBatchesForStockOutReq clientReq) throws CisGoodsServiceException {
        return goodsStockBatchesService.getExpiredWarnGoodsForStockOut(clientReq);
    }

    /*
     * 查一批药品的批次信息
     *
     * @GetExpiredWarnGoodsStockBatchesForStockOutRsp 复用一个协议
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetExpiredWarnGoodsStockBatchesForStockOutRsp getGoodsStockBatches(GetGoodsListStockBatches clientReq) throws CisGoodsServiceException {
        return goodsStockBatchesService.getGoodsStockBatches(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportStockBatchesInGoodsInfo(GetGoodsBatchesForGoodsInfoReq clientReq) throws CisGoodsServiceException, IOException {
        goodsStockBatchesService.exportStockBatchesInGoodsInfo(clientReq);
    }

    /*
     * 查询Goods的批次信息
     * 1.如果没传ClinicID会进行全连锁的汇总
     * 2.如果没传pharmacyNo会进行全药房的汇总
     * 3.TODO 是否要根据不通成本价汇总？？？
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public Tuple3<GetGoodsBatchesForGoodsInfoRsp, ClinicConfig, Set<Long>> getStockBatchesInGoodsInfo(GetGoodsBatchesForGoodsInfoReq clientReq) throws CisGoodsServiceException {
        return goodsStockBatchesService.getStockBatchesInGoodsInfo(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public void reflushGoodsStock(ClinicConfig clinicConfig, Set<Long> batchIdSet) throws CisGoodsServiceException {
        goodsStockBatchesService.reflushGoodsStock(clinicConfig, batchIdSet);
    }

    /*
     * 取某个药品的某个批次信息
     * 1.如果没传ClinicID会进行全连锁的汇总
     * 2.如果没传pharmacyNo会进行全药房的汇总
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetGoodsBatchesForGoodsInfoRsp getSingleStockBatchesInfo(GetGoodsBatchInfoReq clientReq) throws CisGoodsServiceException {
        return goodsStockBatchesService.getSingleStockBatchesInfo(clientReq);
    }

    /*
     * 返回批次，模拟扣库
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsBatchesPretendCutRsp pretendCutGoodsStockBatches(GoodsBatchesPretendCutReq clientReq) throws CisGoodsServiceException {

        if (!CollectionUtils.isEmpty(clientReq.getGoodsList())) {
            GetTraceableCodeListForBatchSummeryReq rpcReq = new GetTraceableCodeListForBatchSummeryReq();
            rpcReq.setChainId(clientReq.getChainId());
            rpcReq.setClinicId(clientReq.getClinicId());
            rpcReq.setQueryTraceCodeList(new ArrayList<>());
            for (GoodsBatchesPretendCutReq.CutGoodsItem queryGoodsListStruct : clientReq.getGoodsList()) {
                if (CollectionUtils.isEmpty(queryGoodsListStruct.getNoList())) {
                    continue;
                }
                if (StringUtils.isEmpty(queryGoodsListStruct.getKeyId())) {
                    queryGoodsListStruct.setKeyId(queryGoodsListStruct.getGoodsId());
                }
                GetTraceableCodeListForBatchSummeryReq.QueryTraceableCodeBatchAndRefId item = new GetTraceableCodeListForBatchSummeryReq.QueryTraceableCodeBatchAndRefId();
                item.setGoodsId(queryGoodsListStruct.getGoodsId());
                item.setKeyId(queryGoodsListStruct.getGoodsId());
                item.setNoList(new ArrayList<>());
                for (String s : queryGoodsListStruct.getNoList()) {
                    item.getNoList().add(s);
                }
                rpcReq.getQueryTraceCodeList().add(item);
            }
            if (!CollectionUtils.isEmpty(rpcReq.getQueryTraceCodeList())) {
                GetTraceableCodeListForBatchSummeryRsp rsp = queryTraceableCodeListForBatchSummery(rpcReq);
                if (!CollectionUtils.isEmpty(rsp.getList())) {
                    Map<String, GetTraceableCodeListForBatchSummeryRsp.GetTraceableCodeListForBatchSummery> keyIdToSummery = rsp.getList().stream().collect(Collectors.toMap(GetTraceableCodeListForBatchSummeryRsp.GetTraceableCodeListForBatchSummery::getKeyId, Function.identity(), (a, b) -> a));
                    for (GoodsBatchesPretendCutReq.CutGoodsItem queryGoodsListStruct : clientReq.getGoodsList()) {
                        if (CollectionUtils.isEmpty(queryGoodsListStruct.getNoList()) || StringUtils.isEmpty(queryGoodsListStruct.getKeyId())) {
                            continue;
                        }
                        GetTraceableCodeListForBatchSummeryRsp.GetTraceableCodeListForBatchSummery summery = keyIdToSummery.get(queryGoodsListStruct.getKeyId());
                        if (summery != null) {
                            queryGoodsListStruct.setBatchTraceCodeCountList(summery.getBatchTraceCodeCountList());
                        }

                    }
                }
            }
        }

        return goodsStockBatchesService.pretendCutGoodsStockBatches(clientReq);
    }

    /*
     * 返回Goods 某个进销存影响的批次
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<BatchInfo> getSingleGoodsStockAction(String chainId, String clinicId, String goodsId, Integer actionType, String orderId, String orderDetailId) throws CisGoodsServiceException {
        return goodsStockBatchesService.getSingleGoodsStockAction(chainId, clinicId, goodsId, actionType, orderId, orderDetailId);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockDetailRsp getGoodsStockDetail(String goodsId, String chainId, String clinicId, String queryClinicId, int pharmacyType, Integer pharmacyNo) throws CisGoodsServiceException {
        return goodsStockBatchesService.getGoodsStockDetail(goodsId, chainId, clinicId, queryClinicId, pharmacyType, pharmacyNo);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp enableGoodsStockList(EnableGoodsStocksReq req) throws CisGoodsServiceException {
        return goodsInfoModifyManageService.enableGoodsStockList(req);
    }

    /**
     * @param chainId    连锁ID
     * @param pharmacyNo 药房NO
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsGoodsMaxCostPriceRsp getGoodsMaxCostPrice(String goodsId, String chainId, String clinicId, Integer pharmacyNo) throws CisGoodsServiceException {
        return goodsStockBatchesService.getGoodsMaxCostPrice(goodsId, chainId, clinicId, pharmacyNo);
    }

    /*------------------------进销存---------------------------------*/


    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetImplNationCodeStatusRsp getImplNationalStatusSummary(String chainId, String clinicId, int clinicType, int headerViewMode, String queryClinicId, Integer stockGoods, Integer onlyStock, Integer pharmacyNo) throws CisGoodsServiceException {
        return goodsListService.getImplNationalStatusSummary(chainId, clinicId, clinicType, headerViewMode, queryClinicId, stockGoods, onlyStock, pharmacyNo);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetImplNationalGoodsWarningCountRsp getRpcImplNationalCountSummary(String chainId, String clinicId) throws CisGoodsServiceException {
        return goodsListService.getRpcImplNationalCountSummary(chainId, clinicId);
    }

    /*------------------------获取库存信息---------------------------------*/
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsInfoPurchaseInfoRsp getGoodsPurchaseInfo(String goodsId, String chainId, String queryClinicId, int pharmacyNo) throws CisGoodsServiceException {
        return queryCisGoodsListService.getGoodsPurchaseInfo(goodsId, chainId, queryClinicId, pharmacyNo); //TODO 先放这个service
    }

    //----查询中药别名
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<String> getChineseMedicineStandardAliasNames(String medicineName) {
        return queryBisGoodsService.getChineseMedicineStandardAliasNames(medicineName);
    }

    /*
     * Goods分页列表查询
     *
     * @return 结果
     * @throws CisGoodsServiceException 异常
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetNonStockGoodsListRsp pageListNonStockGoods(GetNonStockGoodsListReq clientReq) throws CisGoodsServiceException {
        GetNonStockGoodsListRsp goodsListRsp = new GetNonStockGoodsListRsp();
        if (!clientReq.isProcessReq()) {
            if (Objects.isNull(clientReq.getOffset()) || Objects.isNull(clientReq.getLimit())) {
                throw new ParamRequiredException("offset、limit");
            }
            //非义齿加工 supplierId null
            clientReq.setSupplierId(null);
        }
        List<NonStockGoodsListGoodsDto> list = goodsListService.pageListNonStockGoods(clientReq, goodsListRsp);
        if (!clientReq.isProcessReq()) {
            goodsListService.updateListNonStockGoodsMatchStatus(clientReq.getChainId(), clientReq.getHeaderClinicId(), list);
        }
        return goodsListRsp;
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportPageListNonStockGoods(GetNonStockGoodsListReq clientReq) throws CisGoodsServiceException {
        goodsListService.exportPageListNonStockGoods(clientReq);
    }


    public BatchModifyGoodsRsp batchModifyProjects(GetNonStockGoodsListReq clientReq) throws CisGoodsServiceException {
        if (clientReq.getModifyField() == null || clientReq.getModifyField() == 0L) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "未指定批量修改字段");
        }
        String clinicId = clientReq.getQueryClinicId();
        if (StringUtils.isEmpty(clinicId)) {
            clinicId = clientReq.getHeaderClinicId();
        }
        ClinicConfig clinicConfig = ServiceUtils.getClinicConfig(cisClinicService, clinicId);
        if (clinicConfig == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.GET_CLINIC_CONFIG_ERROR);
        }
        ClinicEmployeeDataPermission.Inventory inventory = cisClinicService.getEmployeePermission(clientReq.getHeaderEmployeeId(), clientReq.getChainId(), clientReq.getHeaderClinicId());
        if (inventory == null || inventory.getIsCanModifyGoodsArchives() != YesOrNo.YES) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "无编辑档案权限");
        }

        String taskId = clientReq.goodsCountSubKey(YesOrNo.NO);
        BatchModifyGoodsRsp redisRsp = (BatchModifyGoodsRsp) redisUtils.get(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_PROJECT + taskId);
        if (redisRsp != null) {
            redisRsp.setSuccess(redisRsp.getSuccessGoodsIds().size());
            redisRsp.setFailed(redisRsp.getFailedGoodsIds().size());
            redisRsp.setSuccessGoodsIds(null);
            redisRsp.setFailedGoodsIds(null);
            return redisRsp;
        }

        redisRsp = new BatchModifyGoodsRsp();
        redisRsp.setModifyTaskId(taskId);

        ShebaoConfig shebaoConfig = cisShebaoService.queryShebaoConfig(clinicId);
        if (clientReq.isFullSelectMode() || CollectionUtils.isEmpty(clientReq.getBatchGoodsIdList())) {
            String dbOrderByString = buildNonStockOrderBy(clientReq.getOrderType(), clientReq.getOrderBy());
            QueryStockGoodsListGoodsReqDto queryParam = GoodsListService.createNonStockQueryParam(
                    shebaoConfig,
                    clinicConfig,
                    goodsShebaoConfig,
                    GoodsUtils.SwitchFlag.OFF,
                    dbOrderByString,
                    clientReq);
            StockGoodsListGoodsDtoCount count = goodsListService.getNonStockGoodsCountFromRepoAndPutToRedis(clientReq, queryParam);
            redisRsp.setTotal(count != null ? count.getTotalGoodsCount() : 0);
        } else {
            redisRsp.setTotal(clientReq.getBatchGoodsIdList().size());
        }

        redisUtils.set(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_PROJECT + taskId, redisRsp, RedisUtils.EXPIRE_ONE_HOUR_SECONDS);
        GoodsService bean = SpringUtils.getBean(GoodsService.class);
        bean.doAsyncBatchModifyProjects(clientReq, taskId, clinicConfig, redisRsp);
        return redisRsp;
    }

    @Async
    public void doAsyncBatchModifyProjects(GetNonStockGoodsListReq clientReq,
                                           String taskId,
                                           ClinicConfig clinicConfig,
                                           BatchModifyGoodsRsp redisRsp) {
        String redisKey = RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_PROJECT + taskId;
        redisRsp.setStatus(10);
        redisUtils.set(redisKey, redisRsp, RedisUtils.EXPIRE_ONE_HOUR_SECONDS);
        Set<String> updatedGoodsIds = new HashSet<>();
        Integer originalLimit = clientReq.getLimit();
        Integer originalOffset = clientReq.getOffset();
        try {
            Long modifyFieldObj = clientReq.getModifyField();
            int modifyField = modifyFieldObj != null ? modifyFieldObj.intValue() : 0;
            Map<String, GoodsCustomType> keyToCustomType = new HashMap<>();
            if (GoodsUtils.checkFlagOn(modifyField, Constant.BatchModifyFlag.CATEGORY)) {
                goodsCustomTypeRepository.findAllByChainIdAndIsDeleted(clinicConfig.getChainId(), GoodsUtils.DeleteFlag.OK)
                        .forEach(it -> keyToCustomType.put(it.getTypeId() + "_" + it.getName(), it));
            }

            clientReq.setLimit(100);
            clientReq.setOffset(0);

            List<String> manualIds = null;
            if (!clientReq.isFullSelectMode() && !CollectionUtils.isEmpty(clientReq.getBatchGoodsIdList())) {
                manualIds = new ArrayList<>(clientReq.getBatchGoodsIdList());
            }

            boolean hasNext;
            do {
                List<String> goodsIdList;
                if (manualIds != null) {
                    if (manualIds.isEmpty()) {
                        break;
                    }
                    int end = Math.min(clientReq.getLimit(), manualIds.size());
                    goodsIdList = new ArrayList<>(manualIds.subList(0, end));
                    manualIds.subList(0, end).clear();
                    hasNext = !manualIds.isEmpty();
                } else {
                    GetNonStockGoodsListRsp pageRsp = new GetNonStockGoodsListRsp();
                    List<NonStockGoodsListGoodsDto> dtoList = goodsListService.pageListNonStockGoods(clientReq, pageRsp);
                    goodsIdList = dtoList.stream().map(NonStockGoodsListGoodsDto::getId).collect(Collectors.toList());
                    hasNext = dtoList.size() == clientReq.getLimit();
                    clientReq.setOffset(clientReq.getOffset() + clientReq.getLimit());
                    if (CollectionUtils.isEmpty(goodsIdList)) {
                        continue;
                    }
                }
                batchModifyProjectsOnce(goodsIdList,
                        clientReq,
                        clinicConfig,
                        modifyField,
                        redisRsp,
                        clientReq.getHeaderEmployeeId(),
                        keyToCustomType,
                        updatedGoodsIds);

                redisRsp.setSuccess(redisRsp.getSuccessGoodsIds().size());
                redisRsp.setFailed(redisRsp.getFailedGoodsIds().size());
                redisUtils.set(redisKey, redisRsp, RedisUtils.EXPIRE_ONE_HOUR_SECONDS);
            } while (hasNext);

            if (originalLimit != null) {
                clientReq.setLimit(originalLimit);
            }
            if (originalOffset != null) {
                clientReq.setOffset(originalOffset);
            }

            if (!CollectionUtils.isEmpty(updatedGoodsIds)) {
                goodsMedicalStatService.reFlushGoodsMedicalStatList(clinicConfig.getChainId(), null, new ArrayList<>(updatedGoodsIds), true, null);
                rocketMqProducer.notifyOfflineTaskDumpJson(clinicConfig.getChainId());
            }
            redisRsp.setStatus(20);
            redisRsp.setSuccess(redisRsp.getSuccessGoodsIds().size());
            redisRsp.setFailed(redisRsp.getFailedGoodsIds().size());
            redisUtils.set(redisKey, redisRsp, RedisUtils.EXPIRE_ONE_MIN_SECONDS);
        } catch (Exception exp) {
            sLogger.error("批量设置项目异常", exp);
            redisRsp.setStatus(20);
            redisRsp.setSuccess(redisRsp.getSuccessGoodsIds().size());
            redisRsp.setFailed(redisRsp.getFailedGoodsIds().size());
            redisUtils.set(redisKey, redisRsp, RedisUtils.EXPIRE_ONE_MIN_SECONDS);
        }
    }

    public BatchModifyGoodsRsp batchModifyProjectsProgress(String taskId) {
        BatchModifyGoodsRsp rsp = (BatchModifyGoodsRsp) redisUtils.get(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_PROJECT + taskId);
        if (rsp == null) {
            BatchModifyGoodsRsp empty = new BatchModifyGoodsRsp();
            empty.setStatus(20);
            empty.setFailed(0);
            empty.setSuccess(0);
            empty.setTotal(0);
            return empty;
        }
        rsp.setSuccess(rsp.getSuccessGoodsIds().size());
        rsp.setFailed(rsp.getFailedGoodsIds().size());
        rsp.setSuccessGoodsIds(null);
        rsp.setFailedGoodsIds(null);
        return rsp;
    }

    private void batchModifyProjectsOnce(List<String> goodsIdList,
                                         GetNonStockGoodsListReq clientReq,
                                         ClinicConfig clinicConfig,
                                         int modifyField,
                                         BatchModifyGoodsRsp redisRsp,
                                         String employeeId,
                                         Map<String, GoodsCustomType> keyToCustomType,
                                         Set<String> updatedGoodsIds) throws CisGoodsServiceException {
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return;
        }

        List<Goods> goodsList = goodsRepository.findAllByIdIn(goodsIdList);
        if (CollectionUtils.isEmpty(goodsList)) {
            redisRsp.getFailedGoodsIds().addAll(goodsIdList);
            return;
        }

        Map<String, GoodsExtend> goodsIdToExtend = goodsExtendRepository.findAllByChainIdAndOrganIdAndGoodsIdIn(
                clinicConfig.getChainId(), clinicConfig.getClinicId(), goodsIdList)
                .stream().collect(Collectors.toMap(GoodsExtend::getGoodsId, Function.identity(), (a, b) -> a));

        Set<String> missingIds = new HashSet<>(goodsIdList);
        goodsList.forEach(goods -> missingIds.remove(goods.getId()));
        if (!CollectionUtils.isEmpty(missingIds)) {
            redisRsp.getFailedGoodsIds().addAll(missingIds);
        }

        List<GoodsCustomType> newCustomTypeList = new ArrayList<>();
        Map<String, GoodsExtend> extendToUpdateMap = new HashMap<>();

        if (GoodsUtils.checkFlagOn(modifyField, Constant.BatchModifyFlag.CATEGORY)) {
            CategoryBatchModifyProcessor processor = new CategoryBatchModifyProcessor(clinicConfig.getClinicId(), goodsList, clientReq.getModifyCustomName(), employeeId, keyToCustomType);
            handleBatchModifyProcessor(processor, null, redisRsp, updatedGoodsIds);
            newCustomTypeList.addAll(processor.getGoodsCustomTypeList());
        }

        if (GoodsUtils.checkFlagOn(modifyField, Constant.BatchModifyFlag.CATEGORY_BY_ID) && clientReq.getModifyCustomTypeId() != null) {
            GoodsCustomType customType = goodsCustomTypeRepository.findByChainIdAndId(clinicConfig.getChainId(), clientReq.getModifyCustomTypeId());
            if (customType != null) {
                CategoryCustomTypeIdBatchModifyProcessor processor = new CategoryCustomTypeIdBatchModifyProcessor(clinicConfig.getClinicId(), goodsList, customType, employeeId);
                handleBatchModifyProcessor(processor, null, redisRsp, updatedGoodsIds);
            }
        }

        if (GoodsUtils.checkFlagOn(modifyField, Constant.BatchModifyFlag.PROJECT_UNIT)) {
            ProjectPackageUnitBatchModifyProcessor processor = new ProjectPackageUnitBatchModifyProcessor(clinicConfig.getClinicId(), goodsList, clientReq.getModifyPackageUnit(), employeeId);
            handleBatchModifyProcessor(processor, null, redisRsp, updatedGoodsIds);
        }

        if (GoodsUtils.checkFlagOn(modifyField, Constant.BatchModifyFlag.PROJECT_SAMPLE_TYPE)) {
            ProjectSampleTypeBatchModifyProcessor processor = new ProjectSampleTypeBatchModifyProcessor(clinicConfig.getClinicId(), goodsList, clientReq.getModifySampleType(), employeeId);
            handleBatchModifyProcessor(processor, null, redisRsp, updatedGoodsIds);
        }

        if (GoodsUtils.checkFlagOn(modifyField, Constant.BatchModifyFlag.PROJECT_SAMPLE_GROUP)) {
            ProjectSampleGroupBatchModifyProcessor processor = new ProjectSampleGroupBatchModifyProcessor(clinicConfig.getClinicId(), goodsList, clientReq.getModifySampleId(), employeeId);
            handleBatchModifyProcessor(processor, null, redisRsp, updatedGoodsIds);
        }

        if (GoodsUtils.checkFlagOn(modifyField, Constant.BatchModifyFlag.PROJECT_EXECUTE_DEPARTMENT)) {
            ProjectExecuteDepartmentBatchModifyProcessor processor = new ProjectExecuteDepartmentBatchModifyProcessor(
                    clinicConfig.getClinicId(), goodsList, clientReq.getModifyExecuteDepartmentId(), employeeId, goodsIdToExtend,
                    clinicConfig.getChainId(), clinicConfig.getClinicId());
            handleBatchModifyProcessor(processor, null, redisRsp, updatedGoodsIds);
            processor.getUpdateList().forEach(extend -> extendToUpdateMap.put(extend.getGoodsId(), extend));
        }

        if (GoodsUtils.checkFlagOn(modifyField, Constant.BatchModifyFlag.PROJECT_SUPPLIER)) {
            ProjectSupplierBatchModifyProcessor processor = new ProjectSupplierBatchModifyProcessor(
                    clinicConfig.getClinicId(), goodsList, clientReq.getModifySupplierId(), employeeId, goodsIdToExtend,
                    clinicConfig.getChainId(), clinicConfig.getClinicId());
            handleBatchModifyProcessor(processor, null, redisRsp, updatedGoodsIds);
            processor.getUpdateList().forEach(extend -> extendToUpdateMap.put(extend.getGoodsId(), extend));
        }

        if (GoodsUtils.checkFlagOn(modifyField, Constant.BatchModifyFlag.PROJECT_IS_SELL) && clientReq.getModifyIsSell() != null) {
            ProjectIsSellBatchModifyProcessor processor = new ProjectIsSellBatchModifyProcessor(clinicConfig.getClinicId(), goodsList, clientReq.getModifyIsSell(), employeeId);
            handleBatchModifyProcessor(processor, null, redisRsp, updatedGoodsIds);
        }

        if (!CollectionUtils.isEmpty(newCustomTypeList)) {
            goodsCustomTypeRepository.saveAll(newCustomTypeList);
            newCustomTypeList.forEach(it -> keyToCustomType.put(it.getTypeId() + "_" + it.getName(), it));
        }

        goodsRepository.saveAll(goodsList);
        if (!extendToUpdateMap.isEmpty()) {
            goodsExtendRepository.saveAll(extendToUpdateMap.values());
        }

        goodsRedisUtils.clearGoodsRedisCache(clinicConfig.getChainId(), goodsIdList);
    }

    private void handleBatchModifyProcessor(GoodsBatchModifyProcessor<?> processor,
                                            List<GoodsLog> goodsLogList,
                                            BatchModifyGoodsRsp redisRsp,
                                            Set<String> updatedGoodsIds) {
        processor.setGoodsLogList(goodsLogList);
        processor.doProcess(cisClinicService);
        Set<String> successGoodsIds = redisRsp.getSuccessGoodsIds();
        Set<String> failedGoodsIds = redisRsp.getFailedGoodsIds();

        successGoodsIds.addAll(processor.getSuccessGoodsIds());
        failedGoodsIds.addAll(processor.getFailedGoodsIds());
        failedGoodsIds.removeAll(successGoodsIds);
        if (updatedGoodsIds != null) {
            updatedGoodsIds.addAll(processor.getSuccessGoodsIds());
        }
    }

    private String buildNonStockOrderBy(String orderDescType, String orderBy) {
        if (StringUtils.isEmpty(orderBy)) {
            return null;
        }
        if (orderDescType == null || orderDescType.compareTo("desc") != 0) {
            orderDescType = "asc";
        }
        if ("shebaoNationalNotMatchFirst".equals(orderBy)) {
            return " shebaoCodeNationalMatchedStatus asc ";
        }
        if ("name".equals(orderBy)) {
            return String.format(" stat.py %s ", orderDescType);
        }
        if ("packagePrice".equals(orderBy)) {
            return String.format(" stat.package_price %s ", orderDescType);
        }
        if ("packageCostPrice".equals(orderBy)) {
            return String.format(" stat.package_cost_price %s ", orderDescType);
        }
        if ("customTypeName".equals(orderBy)) {
            return String.format(" v2gct.name %s ", orderDescType);
        }
        return null;
    }


    public BatchModifyGoodsRsp batchModifyGoods(GetStockGoodsListReq clientReq) throws CisGoodsServiceException {
        /*
         * 拉取诊所配置和社保配置
         * 查门店的诊所配置 如果查的是全部门店 用总部的诊所配置
         * 以前把全部门店设计成  空格埋下的坑
         * */
        String clinicId = clientReq.getQueryClinicId();
        if (StringUtils.isEmpty(clinicId)) { //为空一定是总部门店
            clinicId = clientReq.getHeaderClinicId();
        }
        ClinicConfig clinicConfig = ServiceUtils.getClinicConfig(cisClinicService, clinicId);
        boolean noCheck = clientReq.getModifyField() != null && GoodsUtils.checkFlagOn(clientReq.getModifyField().intValue(), Constant.BatchModifyFlag.NO_CODE);

        if (!noCheck && clinicConfig.isSubClinic()) {
            if (!clinicConfig.enableClinicUpdateArchive(clinicId)) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "门店不能批量修改药品资料");
            }
        }
        ClinicEmployeeDataPermission.Inventory inventory = cisClinicService.getEmployeePermission(clientReq.getHeaderEmployeeId(), clientReq.getChainId(), clientReq.getHeaderClinicId());
        if (!noCheck && inventory.getIsCanModifyGoodsArchives() != YesOrNo.YES) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "无编辑档案权限");
        }

        BatchModifyGoodsRsp redisRsp = (BatchModifyGoodsRsp) redisUtils.get(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_GOODS + clientReq.goodsCountSubKey(YesOrNo.NO));
        if (redisRsp != null) {
            sLogger.info("批量改goods进行中，直接从redis拿到执行结果:{}", redisRsp);
            redisRsp.setSuccess(redisRsp.getSuccessGoodsIds().size());
            redisRsp.setFailed(redisRsp.getFailedGoodsIds().size());
            redisRsp.setSuccessGoodsIds(null);
            redisRsp.setFailedGoodsIds(null);
            return redisRsp;
        }
        redisRsp = new BatchModifyGoodsRsp();
        String queryStatClinicId = clientReq.getQueryClinicId();

        cn.abcyun.cis.goods.vo.backend.ShebaoConfig shebaoConfig = cisShebaoService.queryShebaoConfig(clinicId);

        if (clientReq.isFullSelectMode() || CollectionUtils.isEmpty(clientReq.getBatchGoodsIdList())) {
            /*
             * 库存列表这里修正一下查goodsStat药查的药房号
             * */
            clientReq.fixPharmacyNo(clinicConfig);
            /*
             * 生成排序字符串
             */
            String dbOrderByString = goodsListV2Service.generateStockGoodsListSortString(
                    clientReq.getOrderType(),
                    clientReq.getOrderBy(),
                    clinicConfig.getHisType(), clientReq.getIsSpu(), clientReq);
            /*
             * 生成查询请求体
             * */
            QueryStockGoodsListGoodsReqDto queryParam = GoodsListV2Service.createStockQueryParam(queryStatClinicId,
                    shebaoConfig,
                    clinicConfig,
                    dbOrderByString,
                    clientReq);


            /*
             * 统计列表数量，这里是很容易出性能问题的地方。
             * */

            StockGoodsListGoodsDtoCount cacheCount = goodsListV2Service.stockGoodsListGoodsDtoCount(clientReq, queryParam);
            redisRsp.setTotal(cacheCount.getTotalGoodsCount());
        } else {
            redisRsp.setTotal(clientReq.getBatchGoodsIdList().size());
        }
        String taskId = clientReq.goodsCountSubKey(YesOrNo.NO);
        redisRsp.setModifyTaskId(taskId);
        redisUtils.set(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_GOODS + taskId, redisRsp, RedisUtils.EXPIRE_ONE_HOUR_SECONDS);
        GoodsService beanGoodsService = SpringUtils.getBean(GoodsService.class);
        beanGoodsService.doAsyncBatchModifyGoods(clientReq, taskId, clinicConfig, shebaoConfig, redisRsp);
        return redisRsp;
    }

    @Async
    public void doAsyncBatchModifyGoods(GetStockGoodsListReq clientReq, String taskId, ClinicConfig clinicConfig, cn.abcyun.cis.goods.vo.backend.ShebaoConfig shebaoConfig, BatchModifyGoodsRsp redisRsp) {
        boolean isBatch;
        /*
         * 药房这里目前简单根据药房号进行处理
         * 目前只支持虚拟药房，虚拟药房只支持颗粒，直接代码里面写死。药房数据表里面有这个字段。目前简单处理少读一次表
         * */
        if (clientReq.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY && !clientReq.typeFilter()) {
            clientReq.setTypeId(Arrays.asList(GoodsConst.GoodsTypeId.MEDICINE_CHINESE_PIECES_TYPEID, GoodsConst.GoodsTypeId.MEDICINE_CHINESE_GRANULE_TYPEID));
        }
        String employeeId = clientReq.getEmployeeId();
        Map<String, GoodsCustomType> keyToCustomType = new HashMap<>();
        if (GoodsUtils.checkFlagOn(clientReq.getModifyField().intValue(), 0x01)) {
            keyToCustomType.putAll(goodsCustomTypeRepository.findAllByChainIdAndIsDeleted(clientReq.getChainId(), (short) YesOrNo.NO).stream().collect(Collectors.toMap(it -> it.getTypeId() + "_" + it.getName(), Function.identity(), (a, b) -> a)));
        }
        clientReq.setLimit(100);
        clientReq.setOffset(0);
        redisRsp.setStatus(10);
        do {
            try {
                isBatch = goodsListV2Service.batchModifyGoods(clientReq, clinicConfig, shebaoConfig, redisRsp, employeeId, keyToCustomType);
                sLogger.info("异步批量修改goods，是否还有下一页数据：{},clientReq:{}", isBatch, clientReq);
                clientReq.setOffset(clientReq.getOffset() + 100);
                redisUtils.set(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_GOODS + taskId, redisRsp, RedisUtils.EXPIRE_ONE_HOUR_SECONDS);
            } catch (Exception exp) {
                sLogger.error("异步批量修改goods异常，clientReq:{}", clientReq, exp);
                redisRsp.setStatus(20);
                redisUtils.del(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_GOODS + taskId);
                return;
            }
        } while (isBatch);

        redisRsp.setStatus(20);
        redisUtils.set(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_GOODS + taskId, redisRsp, RedisUtils.EXPIRE_ONE_MIN_SECONDS);
        goodsStatService.cleanJenkinsChainRedisCache(clinicConfig.getChainId());
        rocketMqProducer.notifyOfflineTaskDumpJson(clinicConfig.getChainId());
    }

    public BatchModifyGoodsRsp batchModifyGoodsProgress(String taskId) {
        BatchModifyGoodsRsp rsp = (BatchModifyGoodsRsp) redisUtils.get(RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_GOODS + taskId);
        if (rsp == null) {
            rsp = new BatchModifyGoodsRsp();
            rsp.setStatus(20);
            rsp.setFailed(0);
            rsp.setTotal(0);
            rsp.setSuccess(0);
            sLogger.error("从redis里面去修改数据结果失败:{}", RedisUtils.SCGOODS_REDISCACHEKEY_BATCH_MODIFY_GOODS + taskId);
            return rsp;
        }
        rsp.setSuccess(rsp.getSuccessGoodsIds().size());
        rsp.setFailed(rsp.getFailedGoodsIds().size());
        rsp.setSuccessGoodsIds(null);
        rsp.setFailedGoodsIds(null);
        return rsp;
    }

    /*
     * Goods分页列表查询
     *
     * @return 结果
     * @throws CisGoodsServiceException 异常
     */
    public GetStockGoodsListRsp pageListStockGoods(GetStockGoodsListReq clientReq) throws CisGoodsServiceException {
        GetStockGoodsListRsp goodsListRsp = new GetStockGoodsListRsp();
        List<StockGoodsListGoodsDtoV2> list = goodsListV2Service.pageListStockGoods(clientReq, goodsListRsp);
        String clinicId = clientReq.getQueryClinicId();
        if (StringUtils.isEmpty(clinicId)) { //为空一定是总部门店
            clinicId = clientReq.getHeaderClinicId();
        }
        ClinicConfig clinicConfig = ServiceUtils.getClinicConfig(cisClinicService, clinicId);
        if (clinicConfig == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.GET_CLINIC_CONFIG_ERROR);
        }

        /*
         * 没有获取到数据页把数据送回去
         * pc没header页应该要能展示出defalut列
         * */
        int chainMode = clinicConfig.getViewMode();
        Organ organ = cisClinicService.getOrgan(clinicId);
        try {

            List<TableHeaderEmployeeItem> headerList;
            JsonNode jsonNode;
            if (clientReq.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY) {
                if (clinicConfig.isAbcPharmacy()) {
                    jsonNode = propertyService.getTableHeaderEmployeeItemsByTableKey(clientReq.getEmployeeId(),
                            "goods.pharmacy.goodsBasicInfo", abcEnv, chainMode, clinicConfig.getClinicType(),
                            clinicConfig.getHisType(), (int) GoodsUtils.SwitchFlag.ON, null, null,
                            organ != null ? organ.getAddressProvinceId() : null, organ != null ? organ.getAddressCityId() :
                                    null, organ != null ? organ.getAddressDistrictId() : null);
                } else {
                    jsonNode = propertyService.getTableHeaderEmployeeItemsByTableKey(clientReq.getEmployeeId(),
                            TableKey.GOODS_STOCK_GOODS_BASIC_LIST, abcEnv, chainMode, clinicConfig.getClinicType(),
                            clinicConfig.getHisType(), (int) GoodsUtils.SwitchFlag.ON, null, null, organ != null ? organ.getAddressProvinceId() : null, organ != null ? organ.getAddressCityId() :
                                    null, organ != null ? organ.getAddressDistrictId() : null);
                }
            } else {
                jsonNode = propertyService.getTableHeaderEmployeeItemsByTableKey(clientReq.getEmployeeId(), "goods" +
                                ".stocks.goodsBasicInfo.virtual", abcEnv, chainMode, clinicConfig.getClinicType(),
                        clinicConfig.getHisType(), (int) GoodsUtils.SwitchFlag.ON, null, null,
                        organ != null ? organ.getAddressProvinceId() : null, organ != null ? organ.getAddressCityId() :
                                null, organ != null ? organ.getAddressDistrictId() : null);
            }
            headerList = JsonUtils.readValue(jsonNode, new com.fasterxml.jackson.core.type.TypeReference<List<TableHeaderEmployeeItem>>() {
            });
            headerList = headerList.stream().filter(child -> {
                if (GoodsUtils.compareStrEqual(child.getKey(), "goodsPriceInfo.chainPackagePrice") || GoodsUtils.compareStrEqual(child.getKey(), "goodsPriceInfo.chainPiecePrice")) {
                    if (clinicConfig.isSingleMode()) {
                        return false;
                    }
                }
                if (!clinicConfig.isAbcPharmacy()) {
                    if (GoodsUtils.compareStrEqual(child.getKey(), "goodsPriceInfo.packagePrice") || GoodsUtils.compareStrEqual(child.getKey(), "goodsPriceInfo.piecePrice")) {
                        if (clinicConfig.isHeadClinic() && !clinicConfig.isSingleMode()) {
                            return false;
                        }
                    }
                }
                if (GoodsUtils.compareStrEqual(child.getKey(), "goodsShebaoInfo.shebaoCodePayMode")) {
                    if (clinicConfig.isHeadClinic() && !clinicConfig.isSingleMode()) {
                        // 总部不返回医保支付方式
                        return false;
                    }
                }
                if (GoodsUtils.compareStrEqual(child.getKey(), "goodsBasicInfo.position")) {
                    // 口腔不返回柜号
                    return !clinicConfig.isDentistry();
                }
                if (Objects.equals(clientReq.getQueryType(), 1)) {
                    if (GoodsUtils.compareStrEqual(child.getKey(), "goodsBasicInfo.clinicName")) {
                        return false;
                    }
                }
                return true;
            }).collect(Collectors.toList());
            goodsListRsp.setGoodsHeader(headerList);
        } catch (Exception exp) {
            sLogger.error("pageListStockGoods getGoodsHeader={}", exp.toString());
        }
        goodsListV2Service.updateListStockGoodsMatchStatus(clientReq.getChainId(), clinicId, list);
        return goodsListRsp;
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportPageListStockGoods(GetStockGoodsListReq clientReq, boolean isJenkinsExport) throws CisGoodsServiceException {
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(), clientReq.getEmployeeId(), GoodsRedisUtils.CHECKING_EXPORT_GOODSLIST);
        try {
            goodsListV2Service.exportPageListStockGoods(clientReq, isJenkinsExport);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(), clientReq.getEmployeeId(), false);
            throw exp;
        }
        //false 马上把key删掉
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(), clientReq.getEmployeeId(), true);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<ToClientGoodsPharmacyView> findPharmacy(String chainId, String clinicId) {
        return goodsPharmacyService.findPharmacy(chainId, clinicId);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ClinicPharmacyDistributionRsp findPharmacyClinics(String chainId, String clinicId, int clinicType, Integer pharmacyType) {
        return goodsPharmacyService.findPharmacyClinics(chainId, clinicId, clinicType, pharmacyType);
    }

    /*
     * 检查代煎代配药房是否开通
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsPharmacyOpenRsp checkPharmacyOpen(String chainId, String clinicId) {
        return goodsPharmacyService.checkPharmacyOpen(chainId, clinicId);
    }

    /*
     * Goods分页列表查询
     *
     * @return 结果
     * @throws CisGoodsServiceException 异常
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public List<GoodsItem> loadGoodsFromRedisCacheByGoodsId(GetGoodsByGoodsIdListReq clientReq) throws CisGoodsServiceException {
        if (!CollectionUtils.isEmpty(clientReq.getQueryGoodsList())) {
            GetTraceableCodeListForBatchSummeryReq rpcReq = new GetTraceableCodeListForBatchSummeryReq();
            rpcReq.setChainId(clientReq.getChainId());
            rpcReq.setClinicId(clientReq.getClinicId());
            rpcReq.setQueryTraceCodeList(new ArrayList<>());
            for (GetGoodsByGoodsIdListReq.QueryGoodsListStruct queryGoodsListStruct : clientReq.getQueryGoodsList()) {
                if (CollectionUtils.isEmpty(queryGoodsListStruct.getGoodsIds())) {
                    continue;
                }
                for (GetGoodsByGoodsIdListReq.QueryGoodsIdAndRefId goodsId : queryGoodsListStruct.getGoodsIds()) {
                    if (CollectionUtils.isEmpty(goodsId.getNoList()) || StringUtils.isEmpty(goodsId.getKeyId())) {
                        continue;
                    }
                    GetTraceableCodeListForBatchSummeryReq.QueryTraceableCodeBatchAndRefId item = new GetTraceableCodeListForBatchSummeryReq.QueryTraceableCodeBatchAndRefId();
                    item.setGoodsId(goodsId.getGoodsId());
                    item.setKeyId(goodsId.getKeyId());
                    item.setNoList(new ArrayList<>());
                    for (String s : goodsId.getNoList()) {
                        item.getNoList().add(s);
                    }
                    rpcReq.getQueryTraceCodeList().add(item);
                }
            }
            if (!CollectionUtils.isEmpty(rpcReq.getQueryTraceCodeList())) {
                GetTraceableCodeListForBatchSummeryRsp rsp = queryTraceableCodeListForBatchSummery(rpcReq);
                if (!CollectionUtils.isEmpty(rsp.getList())) {
                    Map<String, GetTraceableCodeListForBatchSummeryRsp.GetTraceableCodeListForBatchSummery> keyIdToSummery = rsp.getList().stream().collect(Collectors.toMap(GetTraceableCodeListForBatchSummeryRsp.GetTraceableCodeListForBatchSummery::getKeyId, Function.identity(), (a, b) -> a));
                    for (GetGoodsByGoodsIdListReq.QueryGoodsListStruct queryGoodsListStruct : clientReq.getQueryGoodsList()) {
                        if (CollectionUtils.isEmpty(queryGoodsListStruct.getGoodsIds())) {
                            continue;
                        }
                        for (GetGoodsByGoodsIdListReq.QueryGoodsIdAndRefId goodsId : queryGoodsListStruct.getGoodsIds()) {
                            if (CollectionUtils.isEmpty(goodsId.getNoList()) || StringUtils.isEmpty(goodsId.getKeyId())) {
                                continue;
                            }
                            GetTraceableCodeListForBatchSummeryRsp.GetTraceableCodeListForBatchSummery summery = keyIdToSummery.get(goodsId.getKeyId());
                            if (summery != null) {
                                goodsId.setBatchTraceCodeCountList(summery.getBatchTraceCodeCountList());
                            }
                        }
                    }
                }
            }
        }
        return queryCisGoodsListService.loadGoodsFromRedisCacheByGoodsId(clientReq);
    }

    /*
     * 把Goods列表里面的费用类型换成项目类型
     *
     * @param req 如果要加载goods要服用req里面的参数
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public List<GoodsItem> hospitalChangeFeeToMedicalGoods(GetGoodsByGoodsIdListReq req, List<GoodsItem> list) throws CisGoodsServiceException {
        return queryCisGoodsListService.hospitalChangeFeeToMedicalGoods(req, list);

    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetGoodsCostRangeRsp getGoodsCostRange(GetGoodsCostRangeReq clientReq) throws CisGoodsServiceException {
        return queryCisGoodsListService.getGoodsCostRange(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsItem loadOneGoodsFromDbByGoodsId(GetGoodsByByGoodsIdReq clientReq) throws CisGoodsServiceException {
        return queryCisGoodsListService.loadOneGoodsFromDbByGoodsId(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetGoodsSubClinicInfosRsp getGoodsSubClinicInfoList(String chainId, String goodsId) throws CisGoodsServiceException {
        return queryCisGoodsListService.getGoodsSubClinicInfoList(chainId, goodsId);
    }


    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public QueryGoodsByGoodsIdListRsp queryChineseMedicine(QueryChineseGoodsByIdCmSpecReq clientReq) throws CisGoodsServiceException {
        return queryCisGoodsListService.queryChineseMedicine(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public QueryChineseMedicinesInPharmacyByCadnsRsp findChineseMedicines(QueryChineseMedicinesInPharmacyByCadnsReq clientReq) throws CisGoodsServiceException {
        return queryCisGoodsListService.findChineseMedicines(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public QueryChineseMedicinesInPharmacyByCadnsRsp findMedicinesByName(FindGoodsByNameReq clientReq) throws CisGoodsServiceException {
        return queryCisGoodsListService.findMedicinesByName(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public QueryChineseMedicinesInPharmacyByCadnsRsp findMedicinesByNameV2(FindGoodsByNameReq clientReq) throws CisGoodsServiceException {
        return queryCisGoodsListService.findMedicinesByNameV2(clientReq);
    }

    /*
     * Goods分页列表查询
     *
     * @return 结果
     * @throws CisGoodsServiceException 异常
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public QueryGoodsByGoodsIdListRsp getParentComposeGoodsList(String chainId, String clinicId, int clinicType, int viewMode, String goodsId, Integer disable, int pharmacyNo)
            throws CisGoodsServiceException {
        QueryGoodsByGoodsIdListRsp goodsItems = queryCisGoodsListService.getParentComposeGoodsList(chainId, clinicId, goodsId, disable, pharmacyNo);
        goodsItems.setRegistrationFeeRelatedGoodsList(registrationService.checkChainEmployeeRegistrationFeeRelatedGoodsId(chainId, clinicId, clinicType, viewMode, goodsId));
        return goodsItems;
    }

    @Transactional(rollbackFor = Exception.class)
    public UpdateClinicCustomTypeRsp updateCustomTypes(UpdateClinicCustomTypeReq req) throws CisGoodsServiceException {
        return goodsInfoModifyManageService.updateCustomTypes(req);
    }

    /*
     * [内置GOods]增加使用次数
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp notifySystemGoodsUsed(String abcGoodsId) throws CisGoodsServiceException {
        int ret = goodsInfoModifyManageService.notifySystemGoodsUsed(abcGoodsId);
        if (ret == 0) {
            return new OpsCommonRsp(OpsCommonRsp.SUCC, "Success");
        } else {
            return new OpsCommonRsp(OpsCommonRsp.FAIL, "Failed code=" + ret);
        }
    }

    /**
     * 拉取系统内置药品的列表
     *
     * @param headerClinicId http头里面的门店id
     * @param goodsType      拉取什么类型goods的列表  {@link GoodsConst.GoodsType }
     * @param supplementType 拉取什么扩展类型的goods列表 为空表示不限定类型 {@link ExaminationGoodsExtension#getExaminationMethod}
     * @param offset         分页参数
     * @param limit          分页参数
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsItem> listInnerSystemGoodsList(String headerClinicId, Integer goodsType, Integer goodsSubType, String extendSpec,
                                                           Integer supplementType, Integer combineType, List<String> deviceModelIds, Integer offset, Integer limit) throws CisGoodsServiceException {
        return goodsListService.listInnerSystemGoodsList(headerClinicId, goodsType, goodsSubType, extendSpec,
                supplementType, combineType, deviceModelIds, offset, limit);
    }

    /*
     * 拉取药品在门店的分布信息
     *
     * @param orderBy   按什么字段来排序[asc,desc]
     * @param orderType 排序类型[packagePrice,lastPackageCostPrice,profitRat,currentCount,recentAvgSell,turnoverDays]
     * @param disable   过滤disable的
     * @param limit     分页
     * @param offset
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetGoodsDistributionRsp getGoodsDistributionList(GetGoodsDistributionReq req) throws CisGoodsServiceException {
        return queryCisGoodsListService.getGoodsDistributionList(req);
    }


    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ClientCustomUnitRsp getCustomUnit(String chainId, String clinicId, Integer type, Integer innerFlag, Integer subType, Integer unitType) throws CisGoodsServiceException {
        return goodsCustomUnitService.getCustomUnit(chainId, clinicId, type, innerFlag, subType, unitType);
    }

    @Transactional(rollbackFor = Exception.class)
    public ClientCustomUnitRsp updateCustomUnit(String chainId, String clinicId, int clinicType, int viewMode, Integer type, String employeeId, ClientCustomUnitReq req) throws CisGoodsServiceException {
        return goodsCustomUnitService.updateCustomUnit(chainId, clinicId, clinicType, viewMode, type, employeeId, req);
    }

    //--------------------------------------------出库-----------------------
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockOutOrderListRsp getOutOrderList(GoodsStockOutOrderListReq clientReq) {
        return goodsStockOutService.getOutOrderList(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void getExportOutOrderList(GoodsStockOutOrderListReq clientReq) {

        sLogger.info("exportOutOrdersList getReqMd5Key={}", clientReq.getReqMd5Key());
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getChainId(), clientReq.getReqMd5Key(), GoodsRedisUtils.CHECKING_IN_EXPORT);
        try {
            goodsStockOutService.getExportOutOrderList(clientReq);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getChainId(), clientReq.getReqMd5Key(), false);
            sLogger.info("exportOutOrdersList getReqMd5Key={} deleted", clientReq.getReqMd5Key());
            throw exp;
        }
        //false 马上把key删掉
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getChainId(), clientReq.getReqMd5Key(), true);
        sLogger.info("exportOutOrdersList getReqMd5Key={} deleted", clientReq.getReqMd5Key());

    }

    /*
     * 出库单的新建和修改流程，事务注解放到goodsStockOutService 上面
     * 分成两个事务
     */
    public OpsCommonRsp createOrUpdateStockOutOrder(CreateStockOutOrderReq clientReq) throws CisGoodsServiceException {
        GoodsOrderOutOrderOpsBase inOrderServiceBase;
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getOutOrderClinicId(),
                clientReq.getReqMd5Key(),
                GoodsRedisUtils.CHECKING_OUT);
        try {
            inOrderServiceBase = goodsStockOutService.createOrUpdateOutOrder(clientReq, gspService);
            goodsStockOutService.asyncTrans(inOrderServiceBase);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getOutOrderClinicId(),
                    clientReq.getReqMd5Key(),
                    false);
            throw exp;
        }

        //这里要根据 入库单是否完成 来决定是否是 删除还是 设置标记位
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getOutOrderClinicId(),
                clientReq.getReqMd5Key(),
                inOrderServiceBase.getGoodsStockOutOrder().isOrderStable());

        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    /*
     * 出库单的流程管理 都收到这个函数里面
     * 却别于入库单的是审核出库单 还可以修改出库数量(不能删除)
     */
    public OpsCommonRsp reviewOrConfirmOrRevokeStockOutOrder(CreateStockOutOrderReviewReq clientReq) throws CisGoodsServiceException {
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING,
                clientReq.getReqMd5Key(),
                GoodsRedisUtils.CHECKING_OUT);
        GoodsOrderOutOrderOpsBase inOrderServiceBase;
        try {
            inOrderServiceBase = goodsStockOutService.reviewOrConfirmOrRevokeStockOutOrder(clientReq, gspService);
            //可能有入库，需要写goodsStat
            goodsStockOutService.asyncTrans(inOrderServiceBase);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING,
                    clientReq.getReqMd5Key(),
                    false);
            throw exp;
        }
        //审核这里，不管怎么样都 结束了 设置标记位 为完成
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING,
                clientReq.getReqMd5Key(),
                inOrderServiceBase.getGoodsStockOutOrder().isOrderStable());

        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockOutOrderView getGoodsStockOutOrder(GetStockOutOrderReq clientReq) throws CisGoodsServiceException {
        return goodsStockOutService.getGoodsStockOutOrder(clientReq, gspService);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockOutOrderView getGoodsStockOutOrderForOpenApi(GetStockOutOrderReq clientReq) throws CisGoodsServiceException {
        GoodsStockOutOrderView outOrder = goodsStockOutService.getGoodsStockOutOrder(clientReq, gspService);
        List<GoodsStockLogItem> allList = cisGoodsLogService.getOrderGoodsStockLogList(
                clientReq.getHeaderChainId(),
                outOrder.getOutClinicId(),
                outOrder.getId().toString(),
                null,
                outOrder.getList().stream().map(GoodsStockOutOrderItemView::getId).map(Object::toString).collect(Collectors.toList()),
                Arrays.asList(GoodsConst.StockLogAction.ACTION_OUTORDER_DEPARMENT_CONSUME,
                        GoodsConst.StockLogAction.ACTION_OUTORDER_LOSSOUT,
                        GoodsConst.StockLogAction.ACTION_OUTORDER_RETURNOUT_V2,
                        GoodsConst.StockLogAction.ACTION_OUTORDER_OTHEROUT,
                        GoodsConst.StockLogAction.ACTION_OUTORDER_PRODUCTION_OUT,
                        GoodsConst.StockLogAction.ACTION_OUTORDER_RECEPTION_OUT
                ));
        if (CollectionUtils.isEmpty(allList)) {
            return outOrder;
        }
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(outOrder.getOutClinicId());
        Map<Long, GoodsStock> stockIdToGoodsStock = goodsStockService.getGoodsStockByStockIdList(
                allList.stream().map(GoodsStockLogItem::getStockId).collect(Collectors.toList())
        );

        //先从v2_goods_stock_log表里面找到本次盘点单盘点的日志记录
        Map<String, List<GoodsStockLogItem>> orderDetailToList = allList
                .stream().collect(Collectors.groupingBy(GoodsStockLogItem::getOrderDetailId));
        for (GoodsStockOutOrderItemView goodsStockOutOrderItemView : outOrder.getList()) {
            goodsStockOutOrderItemView.setStockBatchesLogViewList(new ArrayList<>());
            List<GoodsStockLogItem> logItems = orderDetailToList.get(goodsStockOutOrderItemView.getId().toString());
            if (CollectionUtils.isEmpty(logItems)) {
                continue;
            }

            //进销存
            for (GoodsStockLogItem logItem : logItems) {
                cn.abcyun.cis.goods.vo.frontend.goodslog.StockBatchesLogView stockBatchesLogView = new StockBatchesLogView();
                stockBatchesLogView.setAction(logItem.getAction());
                stockBatchesLogView.setStockId(logItem.getStockId());
                stockBatchesLogView.setPackageCount(logItem.getChangePackageCount());
                stockBatchesLogView.setPieceCount(logItem.getChangePieceCount());
                stockBatchesLogView.setPackageCostPrice(logItem.getPackageCostPrice());
                GoodsStock goodsStock = stockIdToGoodsStock.get(logItem.getStockId());
                if (goodsStock != null) {
                    stockBatchesLogView.setExpiryDate(goodsStock.getExpiryDate());
                    stockBatchesLogView.setProductionDate(goodsStock.getProductionDate());
                    stockBatchesLogView.setBatchNo(goodsStock.getBatchNo());
                    stockBatchesLogView.setStockInId(goodsStock.getStockInId());
                    if (!StringUtils.isEmpty(logItem.getSupplierId())) {
                        GoodsSupplierView goodsSupplierView = supplierUtils.getSupplierCachedById(clinicConfig, goodsStock.getChainId(), goodsStock.getSupplierId());
                        if (goodsSupplierView != null) {
                            stockBatchesLogView.setSupplier(new GoodsSupplierVo());
                            stockBatchesLogView.getSupplier().setId(goodsSupplierView.getId());
                            stockBatchesLogView.getSupplier().setName(goodsSupplierView.getName());
                        }
                    }
                }
                goodsStockOutOrderItemView.getStockBatchesLogViewList().add(stockBatchesLogView);
            }
        }

        if (clientReq.getWithStockInInfo() == 1 && !CollectionUtils.isEmpty(outOrder.getList())) {
            bindStockInInfoForOutOrderItems(outOrder.getList());
        }

        return outOrder;
    }

    private void bindStockInInfoForOutOrderItems(List<GoodsStockOutOrderItemView> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Long> stockInIds = list.stream().map(GoodsStockOutOrderItemView::getStockBatchesLogViewList).filter(Objects::nonNull).flatMap(Collection::stream)
                .map(StockBatchesLogView::getStockInId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockInIds)) {
            return;
        }

        List<GoodsStockIn> goodStockInList = goodsStockInRepository.findAllByIdIn(stockInIds);
        if (CollectionUtils.isEmpty(goodStockInList)) {
            return;
        }

        Map<Long, GoodsStockIn> stockInIdToGoodsStockIn = ListUtils.toMap(goodStockInList, GoodsStockIn::getId);
        for (GoodsStockOutOrderItemView goodsStockOutOrderItemView : list) {
            if (CollectionUtils.isEmpty(goodsStockOutOrderItemView.getStockBatchesLogViewList())) {
                continue;
            }
            for (StockBatchesLogView stockBatchesLogView : goodsStockOutOrderItemView.getStockBatchesLogViewList()) {
                if (stockBatchesLogView.getStockInId() == null) {
                    continue;
                }
                GoodsStockIn goodsStockIn = stockInIdToGoodsStockIn.get(stockBatchesLogView.getStockInId());
                if (goodsStockIn != null && goodsStockIn.getExtendData() != null) {
                    stockBatchesLogView.setStockInOutDetailId(goodsStockIn.getExtendData().getOutDetailId());
                }
            }
        }
    }


    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportGoodsStockOutOrder(GetStockOutOrderReq clientReq) throws CisGoodsServiceException {
        goodsStockOutService.exportGoodsStockOutOrder(clientReq, gspService);
    }
    //--------------------------------------------调拨-----------------------

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockTransOrderListRsp getTransOrderList(GoodsStockTransOrderListReq clientReq) {
        return goodsStockTransOrderService.getTransOrderList(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsCheckStockLockItemRsp> checkGoodsInStockTrans(GoodsStockTransOrderListReq clientReq) {
        AbcListPage<GoodsCheckStockLockItemRsp> rsp = new AbcListPage<>();
        rsp.setRows(goodsStockTransOrderService.getGoodsInTransingGoodsList(clientReq));
        return rsp;
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportTransOrderList(GoodsStockTransOrderListReq clientReq) throws IOException {
        sLogger.info("exportTransOrdersList getReqMd5Key={}", clientReq.getReqMd5Key());
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderChainId(), clientReq.getReqMd5Key(), GoodsRedisUtils.CHECKING_IN_EXPORT);
        try {
            goodsStockTransOrderService.exportTransOrderList(clientReq);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderChainId(), clientReq.getReqMd5Key(), false);
            sLogger.info("exportTransOrdersList getReqMd5Key={} deleted", clientReq.getReqMd5Key());
            throw exp;
        }
        //false 马上把key删掉
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderChainId(), clientReq.getReqMd5Key(), true);
        sLogger.info("exportTransOrdersList getReqMd5Key={} deleted", clientReq.getReqMd5Key());
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockTransOrderView getGoodsStockTransOrder(GetStockTransOrderReq clientReq) {
        return goodsStockTransOrderService.getGoodsStockTransOrder(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportGoodsStockTransOrder(GetStockTransOrderReq clientReq) {
        goodsStockTransOrderService.exportGoodsStockTransOrder(clientReq);
    }

    /*
     * 调拨单单的新建和修改流程，事务注解放到goodsStockTransOrderService 上面
     * 分成两个事务
     */
    public OpsCommonRsp createOrUpdateStockTransOrder(CreateStockTransOrderReq clientReq) throws CisGoodsServiceException {
        GoodsOrderTransOrderOpsBase transOrderServiceBase;
        clientReq.parameterCheck(cisClinicService);
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getTransOutClinicId(),
                clientReq.getReqMd5Key(),
                GoodsRedisUtils.CHECKING_TRANS);
        try {
            transOrderServiceBase = goodsStockTransOrderService.createOrUpdateStockTransOrder(clientReq);
            goodsStockTransOrderService.asyncTrans(transOrderServiceBase);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getTransOutClinicId(),
                    clientReq.getReqMd5Key(),
                    false);
            throw exp;
        }

        //这里要根据 入库单是否完成 来决定是否是 删除还是 设置标记位
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getTransOutClinicId(),
                clientReq.getReqMd5Key(),
                transOrderServiceBase.getGoodsStockTransOrder().isOrderStable());

        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    /*
     * 调拨单单的新建和修改流程，事务注解放到goodsStockTransOrderService 上面
     * 分成两个事务
     */
    public OpsCommonRsp reviewOrRevokeStockTransOrder(CreateStockTransOrderReviewReq clientReq) throws CisGoodsServiceException {
        GoodsOrderTransOrderOpsBase transOrderServiceBase;
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderChainId(),
                clientReq.getReqMd5Key(),
                GoodsRedisUtils.CHECKING_TRANS);
        try {
            transOrderServiceBase = goodsStockTransOrderService.reviewOrRevokeStockTransOrder(clientReq);
            goodsStockTransOrderService.asyncTrans(transOrderServiceBase);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderChainId(), clientReq.getReqMd5Key(), false);
            throw exp;
        }

        //这里要根据 入库单是否完成 来决定是否是 删除还是 设置标记位
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderChainId(),
                clientReq.getReqMd5Key(),
                transOrderServiceBase.getGoodsStockTransOrder().isOrderStable());

        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }


    //----------------------------------------盘点---------------
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockCheckOrderView getGoodsCheckOrder(String chainId, String clinicId, String employeeId, int clinicType, Integer needMergedOrder, Long stockCheckOrderId, Integer queryBatches, Integer offset, Integer limit) {
        return goodsStockCheckOrderService.getGoodsCheckOrder(chainId,
                clinicId,
                employeeId,
                clinicType,
                needMergedOrder,
                stockCheckOrderId,
                queryBatches,
                offset,
                limit);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockCheckOrderView getGoodsCheckOrderForOpenApi(String chainId, String clinicId, String employeeId, int clinicType, Long stockCheckOrderId, Integer queryBatches, int withStockInInfo) {
        GoodsStockCheckOrderView checkOrderView = goodsStockCheckOrderService.getGoodsCheckOrder(chainId, clinicId, employeeId, clinicType, null, stockCheckOrderId, queryBatches, null, null);
        List<GoodsStockLogItem> allList = cisGoodsLogService.getOrderGoodsStockLogList(
                chainId,
                checkOrderView.getApplyClinicId(),
                checkOrderView.getId().toString(),
                null,
                checkOrderView.getRows().stream().map(GoodsStockCheckOrderItemView::getId).map(String::toString).collect(Collectors.toList()),
                Arrays.asList(GoodsConst.StockLogAction.ACTION_STOCK_CHECK_IN,
                        GoodsConst.StockLogAction.ACTION_STOCK_CHECK_OUT));
        if (CollectionUtils.isEmpty(allList)) {
            return checkOrderView;
        }
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(checkOrderView.getApplyClinicId());

        Map<Long, GoodsStock> stockIdToGoodsStock = goodsStockService.getGoodsStockByStockIdList(
                allList.stream().map(GoodsStockLogItem::getStockId).collect(Collectors.toList())
        );

        //先从v2_goods_stock_log表里面找到本次盘点单盘点的日志记录
        Map<String, List<GoodsStockLogItem>> orderDetailToList = allList
                .stream().collect(Collectors.groupingBy(GoodsStockLogItem::getOrderDetailId));
        for (GoodsStockCheckOrderItemView goodsStockOutOrderItemView : checkOrderView.getRows()) {
            List<GoodsStockLogItem> logItems = orderDetailToList.get(goodsStockOutOrderItemView.getId());
            if (CollectionUtils.isEmpty(logItems)) {
                continue;
            }
            goodsStockOutOrderItemView.setStockBatchesLogViewList(new ArrayList<>());

            //进销存
            for (GoodsStockLogItem logItem : logItems) {
                cn.abcyun.cis.goods.vo.frontend.goodslog.StockBatchesLogView stockBatchesLogView = new StockBatchesLogView();
                stockBatchesLogView.setStockId(logItem.getStockId());
                stockBatchesLogView.setAction(logItem.getAction());
                stockBatchesLogView.setPackageCount(logItem.getChangePackageCount());
                stockBatchesLogView.setPieceCount(logItem.getChangePieceCount());
                stockBatchesLogView.setPackageCostPrice(logItem.getPackageCostPrice());
                stockBatchesLogView.setPiecePrice(logItem.getPiecePrice());
                stockBatchesLogView.setPackagePrice(logItem.getPackagePrice());
                stockBatchesLogView.setPieceNum(logItem.getPieceNum());
                stockBatchesLogView.setStockChangeCost(logItem.getStockChangeCost());
                GoodsStock goodsStock = stockIdToGoodsStock.get(logItem.getStockId());
                if (goodsStock != null) {
                    stockBatchesLogView.setExpiryDate(goodsStock.getExpiryDate());
                    stockBatchesLogView.setProductionDate(goodsStock.getProductionDate());
                    stockBatchesLogView.setBatchNo(goodsStock.getBatchNo());
                    stockBatchesLogView.setBatchId(goodsStock.getBatchId());
                    stockBatchesLogView.setStockInId(goodsStock.getStockInId());
                    if (!StringUtils.isEmpty(logItem.getSupplierId())) {
                        GoodsSupplierView goodsSupplierView = supplierUtils.getSupplierCachedById(clinicConfig, goodsStock.getChainId(), goodsStock.getSupplierId());
                        if (goodsSupplierView != null) {
                            stockBatchesLogView.setSupplier(new GoodsSupplierVo());
                            stockBatchesLogView.getSupplier().setId(goodsSupplierView.getId());
                            stockBatchesLogView.getSupplier().setName(goodsSupplierView.getName());
                        }
                    }
                }
                goodsStockOutOrderItemView.getStockBatchesLogViewList().add(stockBatchesLogView);
            }
        }

        if (withStockInInfo == 1 && !CollectionUtils.isEmpty(checkOrderView.getRows())) {
            bindStockInInfoCheckOrderItem(checkOrderView.getRows());
        }

        return checkOrderView;
    }

    private void bindStockInInfoCheckOrderItem(List<GoodsStockCheckOrderItemView> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Long> stockInIds = list.stream().map(GoodsStockCheckOrderItemView::getStockBatchesLogViewList).filter(Objects::nonNull).flatMap(Collection::stream)
                .map(StockBatchesLogView::getStockInId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockInIds)) {
            return;
        }

        List<GoodsStockIn> goodStockInList = goodsStockInRepository.findAllByIdIn(stockInIds);
        if (CollectionUtils.isEmpty(goodStockInList)) {
            return;
        }

        Map<Long, GoodsStockIn> stockInIdToGoodsStockIn = ListUtils.toMap(goodStockInList, GoodsStockIn::getId);
        for (GoodsStockCheckOrderItemView goodsStockOutOrderItemView : list) {
            if (CollectionUtils.isEmpty(goodsStockOutOrderItemView.getStockBatchesLogViewList())) {
                continue;
            }
            for (StockBatchesLogView stockBatchesLogView : goodsStockOutOrderItemView.getStockBatchesLogViewList()) {
                if (stockBatchesLogView.getStockInId() == null) {
                    continue;
                }
                GoodsStockIn goodsStockIn = stockInIdToGoodsStockIn.get(stockBatchesLogView.getStockInId());
                if (goodsStockIn != null && goodsStockIn.getExtendData() != null) {
                    stockBatchesLogView.setStockInOutDetailId(goodsStockIn.getExtendData().getOutDetailId());
                }
            }
        }
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportGoodsCheckOrderExcel(HttpServletResponse httpServletResponse, String chainId, String clinicId, String employeeId, int clinicType, Long stockCheckOrderId) throws IOException {
        goodsStockCheckOrderService.exportGoodsCheckOrderExcel(httpServletResponse, chainId, clinicId, employeeId, clinicType, stockCheckOrderId);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockCheckOrderBatchEditView getGoodsCheckOrderEditBatches(String chainId, String clinicId, int clinicType, Long stockCheckOrderId) {
        return goodsStockCheckOrderService.getGoodsCheckOrderEditBatches(chainId, clinicId, clinicType, stockCheckOrderId);
    }


    /*
     * 新增盘点单拆成三个事务
     * 1一个事务做盘点
     * 2.盘点完后一异步线程两个串行事务 先更新goodsstat，再检查药品禁用
     */
    public GoodsStockCheckOrderView createGoodsCheckOrder(CreateGoodsStockCheckOrderReq clientReq) {
        GoodsStockCheckCreateOrderBase goodsStockCheckCreateOrderBase = null;
        if (clientReq.getOpType() == 0) {
            goodsStockCheckCreateOrderBase = new GoodsStockCheckCreateOrderByUser();
        } else {
            goodsStockCheckCreateOrderBase = new GoodsStockCheckCreateOrderDirectByUser();
        }
        GoodsStockCheckOrderView rspView = goodsStockCheckOrderService.createGoodsCheckOrder(clientReq, goodsStockCheckCreateOrderBase);
        if (rspView != null) {
            List<GoodsStockCheckOrderItemView> rows = null;
            if (!CollectionUtils.isEmpty(rspView.getRows()) && rspView.getRows().size() > 20) {
                rows = rspView.getRows();
                rspView.setRows(new ArrayList<>());
            }
            try {
                rocketMqProducer.sendUserOperationLogMessage( //新增盘点单
                        clientReq.getChainId(),
                        clientReq.getClinicId(),
                        clientReq.getEmployeeId(),
                        goodsStockCheckCreateOrderBase.opType,
                        goodsStockCheckCreateOrderBase.subOpType,
                        OperationObjectType.STOCK_ORDER,
                        OperationObjectType.GoodsOrderSubObjectType.CHECK,
                        OpSource.PC,
                        rspView.getId().toString(),
                        rspView.getOrderNo(),
                        null,
                        null,
                        JsonUtils.dumpAsJsonNode(rspView)
                );
            } catch (Exception exp) {
                sLogger.error("sendUserOperationLogMessage error", exp);
            }
            if (rows != null) {
                rspView.setRows(rows);
            }
        }
        goodsStockCheckOrderService.asyncTrans(goodsStockCheckCreateOrderBase);
        return rspView;
    }

    @Transactional(rollbackFor = Exception.class)
    public GoodsStockCheckOrderView updateGoodsCheckOrder(CreateGoodsStockCheckOrderReq clientReq) {
        return goodsStockCheckOrderService.updateGoodsCheckOrder(clientReq);
    }

    /*
     * 审核盘点单拆成三个事务
     * 1一个事务做盘点
     * 2.盘点完后一异步线程两个串行事务 先更新goods stat，再检查药品禁用
     */
    public OpsCommonRsp reviewGoodsCheckOrder(GoodsOrderReviewReq clientReq) {
        GoodsStockCheckReviewOrderByUser reviewOrder = new GoodsStockCheckReviewOrderByUser();
        OpsCommonRsp clientRsp = goodsStockCheckOrderService.reviewGoodsCheckOrder(clientReq, reviewOrder);
        goodsStockCheckOrderService.asyncTrans(reviewOrder);
        return clientRsp;
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp revokeGoodsCheckOrder(GoodsOrderRevokeReq clientReq) {
        return goodsStockCheckOrderService.revokeGoodsCheckOrder(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockCheckOrderListRsp getGoodsStockCheckOrderList(GoodsStockCheckOrderListReq clientReq) {
        return goodsStockCheckOrderService.getGoodsStockCheckOrderList(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportGoodsStockCheckOrderListExcel(GoodsStockCheckOrderListReq clientReq) throws IOException {

        sLogger.info("exportCheckOrdersList getReqMd5Key={}", clientReq.getReqMd5Key());
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getChainId(), clientReq.getReqMd5Key(), GoodsRedisUtils.CHECKING_IN_EXPORT);
        try {
            goodsStockCheckOrderService.exportGoodsStockCheckOrderListExcel(clientReq);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getChainId(), clientReq.getReqMd5Key(), false);
            sLogger.info("exportCheckOrdersList getReqMd5Key={} deleted", clientReq.getReqMd5Key());
            throw exp;
        }
        //false 马上把key删掉
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getChainId(), clientReq.getReqMd5Key(), true);
        sLogger.info("exportCheckOrdersList getReqMd5Key={} deleted", clientReq.getReqMd5Key());
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportGoodsStockForGoodsStockCheck(GetGoodsStockExcelForStockCheckReq clientReq) throws IOException {
        goodsStockCheckOrderService.exportGoodsStockForGoodsStockCheck(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockCheckImportParseGoodsRsp importGoodsStockForGoodsStockCheck(GetGoodsStockJsonForStockCheckReq clientReq) throws IOException {
        return goodsStockCheckOrderService.importGoodsStockForGoodsStockCheck(clientReq);
    }

    /*
     * 取Goods的代办数量
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public RpcTodoCountRsp getGoodsTodoCount(String clinicId, String queryClinicId, String employeeId, Integer pharmacyNo) throws CisGoodsServiceException {
        RpcTodoCountRsp clientRsp = new RpcTodoCountRsp();
        ClinicConfig clinicConfig = ServiceUtils.getClinicConfig(cisClinicService, clinicId);
        if (clinicConfig == null) {
            return clientRsp;
        }
        if (clinicConfig.isSubClinic() || clinicConfig.isSingleMode()) {
            queryClinicId = clinicId;
        }
        return goodsTodoCountService.getClinicGoodsToDoCount(clinicConfig,
                queryClinicId,
                employeeId, pharmacyNo, 0);
    }


    /*--------------------------------供应商-------------------------------------*/
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsSupplierView getSupplierCachedById(String chainId, String supplierId, int isQueryDisabled, Integer status)
            throws CisGoodsServiceException {
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(chainId);
        GoodsSupplierView supplierView = supplierUtils.getSupplierCachedById(clinicConfig, chainId, supplierId, isQueryDisabled);
        if (status != null && supplierView != null && supplierView.getStatus() != status) {
            return null;
        }
        return supplierView;
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsSupplierView getChainSupplierInfo(int hisType, String supplierId, String clinicId)
            throws CisGoodsServiceException {
        return supplierUtils.getSupplierRepoById(hisType, supplierId, gspService, clinicId);
    }

    @Transactional(rollbackFor = Exception.class)
    public GoodsSupplierView createChainSupplierInfo(UpdateGoodsSupplierReq clientReq) throws CisGoodsServiceException {
        return supplierUtils.createChainSupplierInfo(clientReq, gspService,aliHealthService);
    }

    /*
     * 义齿加工排序供应商
     */
    @Transactional(rollbackFor = Exception.class)
    public SearchGoodsSupplierRsp sortGoodsSupplierReq(SortGoodsSupplierReq clientReq) throws CisGoodsServiceException {
        return supplierUtils.sortGoodsSupplierReq(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp updateSupplierAliConfigInfo(UpdateGoodsSupplierAliConfigReq clientReq) throws CisGoodsServiceException {
        return supplierUtils.updateSupplierAliConfigInfo(aliHealthService,clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public GoodsSupplierView updateChainSupplierInfo(UpdateGoodsSupplierReq clientReq) throws CisGoodsServiceException {
        return supplierUtils.updateChainSupplierInfo(clientReq, gspService,aliHealthService);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp deleteChainSupplierInfo(String supplierId, String chainId, String employeeId) throws CisGoodsServiceException {
        return supplierUtils.deleteChainSupplierInfo(supplierId, chainId, employeeId);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public SearchGoodsSupplierRsp getSupplierByName(SearchGoodsSupplierReq clientReq) throws CisGoodsServiceException {
        return supplierUtils.getSupplierByName(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public SearchGoodsSupplierRsp getSupplierByLicense(SearchGoodsSupplierReq clientReq)
            throws CisGoodsServiceException {
        return supplierUtils.getSupplierByLicense(clientReq);
    }

    //从es里搜索供应商
    public SearchGoodsSupplierRsp searchChainSupplierList(SearchGoodsSupplierReq clientReq)
            throws CisGoodsServiceException {
        return searchGoodsService.searchChainSupplierInfo(clientReq);
    }

    //从本地db读取供应商列表
    public SearchGoodsSupplierRsp getChainSupplierListFromRepo(SearchGoodsSupplierReq clientReq) throws CisGoodsServiceException {
        return supplierUtils.getChainSupplierListFromRepo(clientReq);
    }

    //事务再里面
    public GoodExtendShebaoService.SyncSheBaoMatchStatusStat syncSheBaoMatchStatus(String chainId, String clinicId, boolean needForceFlushGoodsStat) throws CisGoodsServiceException {
        return asyncSchedulePullSheBaoMatchCodeTask.syncOneClinicGoodsSheBaoMatchInfo(chainId, clinicId, needForceFlushGoodsStat);
    }

    //事务再里面
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp syncGoodsStat(String chainId) throws CisGoodsServiceException {
        goodsStatService.reFlushWholeChainGoodsStat(chainId);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "Success");
    }


    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp updateShortId(JenkinsUpdateShortIdReq reqBody) throws CisGoodsServiceException {
        goodsJenkinsRpcService.updateShortId(reqBody);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "Success");
    }

    /*
     * 清理连锁的goodsRedisCache
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public OpsCommonRsp cleanJenkinsChainRedisCache(String chainId) throws CisGoodsServiceException {
        goodsStatService.cleanJenkinsChainRedisCache(chainId);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "Success");
    }

    //---- goods配置相关
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsConfigRsp getChainGoodsConfig(String clinicId, String employeeId) throws CisGoodsServiceException {
        GoodsConfigHelper helper = goodsRedisUtils.getGoodsConfig(clinicId);
        return GoodsConfigRedisCache.pathReturnView(helper, employeeId, suggestConfiguration, cisClinicService, hisWardService, goodsSysTypeService, goodsRedisUtils);
    }

    /*
     * 更新门店配置
     */
    public GoodsConfigRsp updateChainGoodsConfig(UpdateGoodsConfigReq req) throws CisGoodsServiceException {
        //获取修改之前的
        GoodsConfigHelper beforeHelperReturn = goodsRedisUtils.reloadChainGoodsConfig(req.getChainId(), req.getClinicId());

        GoodsConfigRsp before = GoodsConfigRedisCache.pathReturnView(beforeHelperReturn, req.getEmployeeId(), suggestConfiguration, cisClinicService, hisWardService, goodsSysTypeService, goodsRedisUtils);
        UpdateGoodsConfigHelperRsp helper = goodsInfoModifyManageService.updateChainGoodsConfig(req);
        GoodsConfigHelper helperReturn = goodsRedisUtils.reloadChainGoodsConfig(req.getChainId(), req.getClinicId());
        goodsInfoModifyManageService.asyncUpdateGoodsInTaxRateByModifyChainConfig(req.getChainId(), req.getClinicId(), helper);
        GoodsConfigRsp after = GoodsConfigRedisCache.pathReturnView(helperReturn, req.getEmployeeId(), suggestConfiguration, cisClinicService, hisWardService, goodsSysTypeService, goodsRedisUtils);
        //此处添加消息
        GoodsUtils.runAfterTransaction(()->{
            rocketMqProducer.sendUserOperationLogMessage(//发送消息
                    req.getChainId(),
                    req.getClinicId(),
                    req.getEmployeeId(),
                    OpType.UPDATE,
                    0,
                    OperationObjectType.CONFIG,
                    OperationObjectType.ConfigSubObjectType.GOODS,
                    OperationObjectType.GoodsConfigThirdObjectType.GOODS,
                    OpSource.PC, //TODO xian
                    "",
                    "",
                    null,
                    JsonUtils.dumpAsJsonNode(before),
                    JsonUtils.dumpAsJsonNode(after)
            );
        });
        return after;
    }

    /*
     * 2025 总部修改单个门店的自主定价
     */
    public GoodsConfigRsp updateGoodsSinglePriceConfig(UpdateClinicSubPriceConfigReq req) throws CisGoodsServiceException {
        goodsInfoModifyManageService.updateGoodsSinglePriceConfig(goodsPriceService,req);
        GoodsConfigHelper helperReturn = goodsRedisUtils.reloadChainGoodsConfig(req.getHeaderChainId(), req.getClinicId());
        return GoodsConfigRedisCache.pathReturnView(helperReturn, req.getHeaderEmployeeId(), suggestConfiguration, cisClinicService, hisWardService, goodsSysTypeService, goodsRedisUtils);
    }

    /*
     * 2025 总部修改单个门店的自主定价
     */
    public GoodsConfigRsp updateAliHealthConfig(UpdateAliHealthConfigReq req) throws CisGoodsServiceException {
        goodsInfoModifyManageService.updateAliHealthConfig(req);
        GoodsConfigHelper helperReturn = goodsRedisUtils.reloadChainGoodsConfig(req.getHeaderChainId(), req.getHeaderClinicId());
        return GoodsConfigRedisCache.pathReturnView(helperReturn, req.getHeaderEmployeeId(), suggestConfiguration, cisClinicService, hisWardService, goodsSysTypeService, goodsRedisUtils);
    }

    /**
     * 上报马上放心配置
     * */
    public GoodsConfigRsp updateAliMaShangFangXinReportConfig(UpdateAliHealthConfigReq req) throws CisGoodsServiceException {
        goodsInfoModifyManageService.updateAliMaShangFangXinReportConfig(req);
        GoodsConfigHelper helperReturn = goodsRedisUtils.reloadChainGoodsConfig(req.getHeaderChainId(), req.getHeaderClinicId());
        return GoodsConfigRedisCache.pathReturnView(helperReturn, req.getHeaderEmployeeId(), suggestConfiguration, cisClinicService, hisWardService, goodsSysTypeService, goodsRedisUtils);
    }

    /**
     * 改门店药房配置
     */
    public GoodsConfigRsp updateGoodsLockConfig(UpdateLockConfigReq req) throws CisGoodsServiceException {

        //现在事务里面提交
        goodsConfigRedisCache.updateGoodsLockConfig(req);
        //这个时候再从DB加载数据到Redis，最新
        GoodsConfigHelper helper = goodsRedisUtils.reloadChainGoodsConfig(req.getChainId(), req.getClinicId());
        //返回
        return GoodsConfigRedisCache.pathReturnView(helper, req.getEmployeeId(), suggestConfiguration, cisClinicService, hisWardService, goodsSysTypeService, goodsRedisUtils);
    }

    /*
     * 改进价加成开关
     */
    @Transactional(rollbackFor = Exception.class)
    public GoodsConfigRsp updateClinicPriceUpConfig(UpdatePriceUpConfigReq req) throws CisGoodsServiceException {
        //现在事务里面提交
        goodsInfoModifyManageService.updateClinicPriceUpConfig(req);
        //这个时候再从DB加载数据到Redis，最新
        GoodsConfigHelper helper = goodsRedisUtils.reloadChainGoodsConfig(req.getChainId(), req.getClinicId());
        //返回
        return GoodsConfigRedisCache.pathReturnView(helper, req.getEmployeeId(), suggestConfiguration, cisClinicService, hisWardService, goodsSysTypeService, goodsRedisUtils);
    }

    /*
     * RPC 提供给前端，强制打开关闭锁库的
     */
//    public GoodsConfigRsp openClinicBatchLock(String chainId, String clinicId, int sceneType, int LockBatch) throws CisGoodsServiceException {
//
//        //现在事务里面提交
//        goodsConfigRedisCache.openClinicBatchLock(chainId, clinicId, sceneType, LockBatch);
//        //这个时候再从DB加载数据到Redis，最新
//        GoodsConfigHelper helper = goodsRedisUtils.reloadChainGoodsConfig(chainId, clinicId);
//        //返回
//        return GoodsConfigRedisCache.pathReturnView(helper, null, suggestConfiguration, cisClinicService, hisWardService, goodsSysTypeService, goodsRedisUtils);
//    }
    /*
     * RPC 提供给收费药店总部是否开启异常禁止销售
     */
    public void disableSellOnExceptionStatus(String chainId, String employeeId, Integer disableSellOutOfStock, Integer disableSellOnExceptionPrice) throws CisGoodsServiceException {

        //现在事务里面提交
        goodsConfigRedisCache.disableSellOnExceptionStatus(chainId, employeeId, disableSellOutOfStock, disableSellOnExceptionPrice);
    }

    /**
     * 改门店药房配置
     */
    public GoodsConfigRsp updateGoodsPharmacyConfig(UpdateGoodsPharmacyConfigReq req) throws CisGoodsServiceException {
        ClinicPurchaseItemView multiPharmacyFunction = cisClinicService.getEditionFunction(req.getClinicId(), ClinicPurchaseItemKey.MULTI_PHARMACY);
        if (multiPharmacyFunction == null
                || multiPharmacyFunction.getIsPurchased() == GoodsUtils.SwitchFlag.OFF
                || multiPharmacyFunction.getIsValid() == GoodsUtils.SwitchFlag.OFF) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "你需要升级旗舰版以上版本才能使用多药房功能");
        }
        int maxPharmacyCount = 3;
        if (multiPharmacyFunction.getEditionConfig().hasNonNull("pharmacyCount")) {
            if (multiPharmacyFunction.getEditionConfig().hasNonNull(req.getClinicId())) {
                maxPharmacyCount = multiPharmacyFunction.getEditionConfig().get(req.getClinicId()).asInt();
            } else {
                maxPharmacyCount = multiPharmacyFunction.getEditionConfig().get("pharmacyCount").asInt();
            }
        }
        req.setMaxPharmacyCount(maxPharmacyCount);

        int needSumGoodsStat = goodsConfigRedisCache.updateGoodsPharmacyConfig(req);

        /*
         * 保证本次返回给前端是是最新修改的
         * */
        GoodsConfigHelper helper = goodsRedisUtils.reloadChainGoodsConfig(req.getChainId(), req.getClinicId());
        if (needSumGoodsStat == GoodsUtils.SwitchFlag.ON) {
            goodsStatService.enableMultiPharmacyToFlushGoodsStat(req.getChainId(), req.getClinicId(), GoodsPrivUtils.isSingleMode(req.getClinicType(), req.getViewMode()));
        }
        return GoodsConfigRedisCache.pathReturnView(helper, req.getEmployeeId(), suggestConfiguration, cisClinicService, hisWardService, goodsSysTypeService, goodsRedisUtils);
    }

    /**
     * 改门店药房配置
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public CheckPharmacyRsp checkGoodsPharmacyCanDeleted(String chainId, String clinicId, Integer pharmacyNo) throws CisGoodsServiceException {
        return goodsConfigRedisCache.checkGoodsPharmacyCanDeleted(chainId, clinicId, pharmacyNo);
    }

    /*
     * 查药房里面每种Goods类型的数量汇总
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public CheckPharmacyGoodsCountRsp checkGoodsPharmacyGoodsCount(String chainId, String clinicId) throws CisGoodsServiceException {
        return goodsPharmacyService.checkGoodsPharmacyGoodsCount(chainId, clinicId);
    }

    //----------结算单-------------
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetSettlementRefOrderListRsp getRefOrders(GetSettlementRefOrderListReq clientReq) {
        return goodsSettlementOrderService.getRefOrders(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetSettlementOrderListRsp getSettlementsOrderList(String chainId, String dateField, Instant beginInstant, Instant endInstant, String supplierId, Integer status, List<String> clinicIds, Integer offeset, Integer limit) {
        return goodsSettlementOrderService.getSettlementsOrderList(chainId, dateField, beginInstant, endInstant, supplierId, status, clinicIds, offeset, limit);
    }

    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'scGoods.settlement-order.supplier-id:' + #clientReq.supplierId")
    public OpsCommonRsp createSettlementsOrder(CreateSettlementOrderReq clientReq) {
        return goodsSettlementOrderService.createSettlementsOrder(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public SettlementOrderView getSettlementsOrder(Long orderId) {
        return goodsSettlementOrderService.getSettlementsOrder(orderId);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public List<SettlementRefOrderItem> getExistedSettlementOrderRefOrders(String chainId, Long orderId) {
        return goodsSettlementOrderService.getExistedSettlementOrderRefOrders(chainId, orderId);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<SettlementRefOrderItemListView> pageListExistedSettlementOrderRefOrdersItems(String chainId, Long orderId, int offset, int limit) {
        return goodsSettlementOrderService.pageListExistedSettlementOrderRefOrdersItems(chainId, orderId, offset, limit);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp reviewSettlementsOrder(ReviewStockSettlementOrderReq clientReq) {
        return goodsSettlementOrderService.reviewSettlementsOrder(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'scGoods.settlement-order.supplier-id:'+ #clientReq.supplierId")
    public OpsCommonRsp pharmacyUpdateSettlementsOrderV1(PharmacyUpdateSettlementOrderV1Req clientReq) {
        return goodsSettlementOrderService.pharmacyUpdateSettlementsOrderV1(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp deleteDraftOrder(String headerChainId, String headerEmployeeId, Long orderId) {
        return goodsSettlementOrderService.deleteDraftOrder(headerChainId, headerEmployeeId, orderId);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public BigDecimal summaryInvoiceUsedAmount(String chainId, String invoiceNo) {
        return goodsSettlementOrderService.summaryInvoiceUsedAmount(chainId, invoiceNo);
    }


    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public List<StockInOrderItemsConflictWithBatchIdsRsp> getStockInOrdersEnableSettleByOrderItemsConflictWithBatchIds(String chainId,
                                                                                                                       StockInOrdersItemsConflictWithBatchIdsReq clientReq) {
        return goodsSettlementOrderService.getStockInOrdersEnableSettleByOrderItemsConflictWithBatchIds(chainId, clientReq.getStockInOrderIds(), clientReq.getBatchIds(), clientReq.getExcludeSettleOrderId());
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public List<StockInOrderEnableSettleByOrderSummaryInfo> getSettlementRefOrderSummaryInfoEnableSettleByOrder(String chainId,
                                                                                                                SettlementRefOrdersExcludeBatchIdsReq clientReq) {
        return goodsSettlementOrderService.getSettlementRefOrderSummaryInfoEnableSettleByOrder(chainId, clientReq.getRefOrderIds(), clientReq.getExcludeBatchIds(), clientReq.getExcludeSettleOrderId());
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public SettlementRefOrdersItemsEnableSettleByOrderInfoRsp pageListSettlementRefOrdersItemsEnableSettleByOrder(String chainId, PageListSettlementRefOrdersItemsEnableSettleByOrderReq clientReq) {
        return goodsSettlementOrderService.pageListSettlementRefOrdersItemsEnableSettleByOrder(chainId, clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public List<SettlementGoodsBatchEnableByUseCountView> getGoodsBatchSalesStatEnableSettleInfo(String chainId, String goodsId, String supplierId, Long supplierSellerId, String beginDate, String endDate, Long excludeSettleOrderId) {
        return goodsSettlementOrderService.getGoodsBatchSalesStatEnableSettleInfo(chainId, goodsId, supplierId, supplierSellerId, beginDate, endDate, excludeSettleOrderId);
    }

    //----------采购计划-------------
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetPurchasePlanListRsp getPurchasePlanList(GetPurchasePlanListReq clientReq) {
        return goodsPurchasePlanService.getPurchasePlanList(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetPurchasePlanListRsp getPurchasePlanListSimple(GetPurchasePlanListReq clientReq) {
        return goodsPurchasePlanService.getPurchasePlanListSimple(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public CreatePurchasePlanReq getCreatePurchasePlanName(CreatePurchasePlanReq clientReq) {
        return goodsPurchasePlanService.getCreatePurchasePlanName(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public GoodsPurchasePlanView createPurchasePlan(CreatePurchasePlanReq clientReq) {
        return goodsPurchasePlanService.createPurchasePlan(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp renamePurchasePlan(CreatePurchasePlanReq clientReq) {
        return goodsPurchasePlanService.renamePurchasePlan(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp completePurchasePlan(CreatePurchasePlanReq clientReq) {
        return goodsPurchasePlanService.completePurchasePlan(clientReq);
    }

    public OpsCommonRsp addPurchaseOrderToPurchasePlan(AddPurchaseOrderToPlanReq clientReq) {
        String oldPlanId = goodsPurchasePlanService.addPurchaseOrderToPurchasePlan(clientReq.getPurchasePlanId(), clientReq.getPurchaseOrderId(), clientReq.getHeaderEmployeeId(), clientReq.getHeaderChainId(), clientReq.getHeaderClinicType(), clientReq.getHeaderViewMode());
        goodsPurchasePlanService.refreshPurchasePlan(clientReq.getHeaderChainId(), clientReq.getHeaderEmployeeId(), clientReq.getPurchasePlanId(), oldPlanId);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsPurchasePlanView getPurchasePlan(GetPurchasePlanReq clientReq) {
        return goodsPurchasePlanService.getPurchasePlan(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportPurchasePlan(GetPurchasePlanReq clientReq) {
        goodsPurchasePlanService.exportPurchasePlan(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportPurchasePlanZip(GetPurchasePlanReq clientReq) {
        goodsPurchasePlanService.exportPurchasePlanZip(clientReq);
    }

    //----------采购单-------------
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsListPage<RecommendView> recommendPurchaseGoods(QueryGoodsByIdsReq clientReq, String headerChainId) {
        return b2BGoodsPurchaseOrderService.recommendPurchaseGoods(clientReq, headerChainId);
    }

    /*
     * 诊所管家的采购单
     * 修改单子状态
     * 而且同时可以改单子内容
     */
    public OpsCommonRsp reviewAndModifyPurchaseOrder(ReviewStockPurchaseOrderReq clientReq) {
        CreatePurchaseOrderReq updateOrderReq = new CreatePurchaseOrderReq();
        updateOrderReq.setHeaderChainId(clientReq.getHeaderChainId());
        updateOrderReq.setHeaderClinicId(clientReq.getHeaderClinicId());
        updateOrderReq.setHeaderEmployeeId(clientReq.getHeaderEmployeeId());
        updateOrderReq.setHeaderViewMode(clientReq.getHeaderViewMode());
        updateOrderReq.setHeaderClinicType(clientReq.getHeaderClinicType());
        updateOrderReq.setHeaderHisType(clientReq.getHeaderHisType());
        updateOrderReq.setOrderId(clientReq.getOrderId());
        updateOrderReq.setComment(clientReq.getComment());
        updateOrderReq.setPlanId(clientReq.getPlanId());
        updateOrderReq.setLastModified(clientReq.getLastModified());
        updateOrderReq.setList(clientReq.getList());
        if (!clientReq.isFromApproval()) {
            updateOrderReq.setWithConfirm(true);
        } else {
            updateOrderReq.setPurchaseType(clientReq.getPurchaseType());
            updateOrderReq.setSupplierId(clientReq.getSupplierId());
        }
        updateOrderReq.setFromApproval(clientReq.isFromApproval());
        updateOrderReq.setGspStatus(clientReq.getGspStatus());
        updateOrderReq.setClaimOrderId(clientReq.getClaimOrderId());
        updateOrderReq.setClaimClinicId(clientReq.getClaimClinicId());
        updateOrderReq.setPurchaseClinicId(clientReq.getPurchaseClinicId());
        updateOrderReq.setOutPurchaseClinicType(clientReq.getOutPurchaseClinicType());
        updateOrderReq.setDelegateThirdPartySystemId(clientReq.getDelegateThirdPartySystemId());
        Pair<String, GoodsPurchaseOrder> oldPlanIdToPurchaseOrder = goodsPurchaseOrderService.updateOfflinePurchaseOrder(updateOrderReq);
        goodsPurchasePlanService.refreshPurchasePlan(clientReq.getHeaderChainId(), clientReq.getHeaderEmployeeId(), clientReq.getPlanId(), oldPlanIdToPurchaseOrder.getFirst());
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    /*
     * 审核撤回采购单
     * 不能修改单子内容，只能改单子状态
     */
    public OpsCommonRsp reviewOrderOnly(ReviewStockPurchaseOrderReq clientReq) {
        goodsPurchaseOrderService.reviewOrConfirmOrRevokeStockPurchaseOrder(clientReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    public OpsCommonRsp reviewClaimOrderOnly(ReviewStockPurchaseOrderReq clientReq) {
        goodsClaimOrderService.reviewOrConfirmOrRevokeClaimOrder(clientReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    /*
     * 诊所管家的采购单
     * 修改单子状态
     * 而且同时可以改单子内容
     */
    public OpsCommonRsp reviewOrRvokeAndModifyClaimOrder(ReviewStockPurchaseOrderReq clientReq) {
        CreatePurchaseOrderReq updateOrderReq = new CreatePurchaseOrderReq();
        updateOrderReq.setHeaderChainId(clientReq.getHeaderChainId());
        updateOrderReq.setHeaderClinicId(clientReq.getHeaderClinicId());
        updateOrderReq.setHeaderEmployeeId(clientReq.getHeaderEmployeeId());
        updateOrderReq.setHeaderViewMode(clientReq.getHeaderViewMode());
        updateOrderReq.setHeaderHisType(clientReq.getHeaderHisType());
        updateOrderReq.setHeaderClinicType(clientReq.getHeaderClinicType());
        updateOrderReq.setOrderId(clientReq.getOrderId());
        updateOrderReq.setComment(clientReq.getComment());
        updateOrderReq.setPlanId(clientReq.getPlanId());
        updateOrderReq.setLastModified(clientReq.getLastModified());
        updateOrderReq.setList(clientReq.getList());
        updateOrderReq.setOrderType(GoodsPurchaseOrder.OrderType.CLAIM_ORDER);
        updateOrderReq.setCommandType(clientReq.getCommandType());
        updateOrderReq.setPass(clientReq.getPass());
        updateOrderReq.setPurchaseOrderDate(clientReq.getPurchaseOrderDate());
        updateOrderReq.setWithConfirm(true);
        String oldPlanId = goodsClaimOrderService.updateClaimOrder(updateOrderReq);
        goodsPurchasePlanService.refreshPurchasePlan(clientReq.getHeaderChainId(), clientReq.getHeaderEmployeeId(), clientReq.getPlanId(), oldPlanId);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }


    /*
     * ¬
     * 以供应商进行拆单（不保存）
     */

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<GoodsPurchaseOrderRsp> preCreatePurchaseOrder(CreatePurchaseOrderReq clientReq) {
        return goodsPurchaseOrderService.preCreatePurchaseOrder(clientReq);
    }

    /*
     * 发送供应商
     *
     * @param clientReq
     * @return
     */
    public OpsCommonRsp sendPurchaseOrderToSupplier(CreatePurchaseOrderReq clientReq) {

        // 1 根据创建单据的参数，创建采购单
        goodsPurchaseOrderService.sendPurchaseOrderToSupplier(clientReq);

        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    /*
     * 校验采购单是否有效
     *
     * @param clientReq
     * @return
     */
    public List<CheckValidSupplierGoodRsp> checkValidToSupplierGoods(CheckValidSupplierGoodsReq clientReq) {
        return goodsPurchaseOrderService.checkValidToSupplierGoods(clientReq);
    }


    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsSystemType> getSystemTypes(Integer isLeaf) throws CisGoodsServiceException {
        return goodsSysTypeService.getClientRspViewList(isLeaf);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsSystemTypeTreeItem> getSystemTypesTree() throws CisGoodsServiceException {
        return goodsSysTypeService.getSystemTypesTree();
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsItem> getSystemGoodsList() throws CisGoodsServiceException {
        return goodsSysTypeService.getSystemGoodsList();
    }

    /*
     * 优惠卷拉取类型树
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetPromotionTypeViewRsp getPromotionTypeList(String headerChainId,
                                                        Integer needGoodsCount,
                                                        int busFlag,
                                                        int hisType,
                                                        Integer needCustomType,
                                                        Integer returnList) throws CisGoodsServiceException {
        return goodsSysTypeService.getPromotionTypeList(headerChainId,
                needGoodsCount,
                needCustomType,
                busFlag,
                hisType,
                goodsPharmacyService,
                returnList);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsListPage<PromotionTypeView> getSystemTypes(String headerChainId,
                                                           Integer queryType,
                                                           int hisType,
                                                           Integer needCustomType,
                                                           int businessType) throws CisGoodsServiceException {
        return goodsSysTypeService.getSystemTypes(headerChainId,
                queryType,
                hisType,
                needCustomType,
                businessType);
    }
    // ---- lis ----

    /*
     * 拉取ABC系统的 设备型号列表
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<ExamAssayDeviceView> getExamDeviceModelList(Integer goodsType, Integer goodsSubType, String goodsExtendSpec, Integer deviceType, Integer usageType, Integer cloudSupplierFlag, Integer status, Integer connectStatus, String keyword, int offset, int limit) {
        return examService.getExamDeviceModelList(goodsType, goodsSubType, goodsExtendSpec, deviceType, usageType, cloudSupplierFlag, status, connectStatus, keyword, offset, limit);
    }

    /*
     * 保存或更新检查设备参数
     *
     * @param deviceId         设备id
     * @param chainId          连锁id
     * @param clinicId         诊所id
     * @param operatorId       操作符id
     * @param deviceParameters 设备参数
     * @return {@link ExamAssayDeviceView}
     */
    @Transactional(rollbackFor = Exception.class)
    public ExamAssayDeviceView updateExamineDeviceParameters(Long deviceId, String chainId, String clinicId, String operatorId, JsonNode deviceParameters) {
        return examService.updateExamineDeviceParameters(deviceId, chainId, clinicId, operatorId, deviceParameters);
    }

    /*
     * 检查设备列表
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    @Deprecated
    public AbcListPage<ExamInspectDeviceView> getExamInspectDeviceList(GetDeviceListReq clientReq) {
        return examService.getExamInspectDeviceList(clientReq);
    }

    /*
     * 拉取检查设备
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ExamInspectDeviceView getExamInspectionDevice(GetDeviceListReq clientReq) {
        return examService.getExamInspectionDevice(clientReq);
    }

    /*
     * 修改检查设备
     */
    @Transactional(rollbackFor = Exception.class)
    public ExamInspectDeviceView newOrUpdateExamInspectionDevice(NewOrUpdateExamInspectDeviceReq clientReq) {
        return examService.newOrUpdateExamInspectionDevice(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp disableExamInspectionDevice(String headerChainId, String headerClinicId, String headerEmployeeId, int headerClinicType, int headerViewMode, String deviceId, int disable) {
        return examService.disableExamInspectionDevice(headerChainId, headerClinicId, headerEmployeeId, headerClinicType, headerViewMode, deviceId, disable);
    }

    /*
     * 检验设备列表
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<ExamAssayDeviceView> getExamAssayMyDeviceModelList(GetDeviceListReq clientReq) {
        return examService.getExamAssayMyDeviceModelList(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<MallGoodsSkuAbstractVO> getMallRecommendDeviceModels(String clinicId, String categoryName) {
        return bisGoodsService.getConfigMallGoodsRecommend(ServiceUtils.getClinicShortId(clinicId, cisClinicService), categoryName);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<ExamAssayDeviceView> getExamAssayDeviceList(GetDeviceListReq clientReq) {
        return examService.getExamAssayDeviceList(clientReq);
    }

    /*
     * 按设备id 拉取检验设备
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ExamAssayDeviceView getExamAssayDevice(GetDeviceListReq clientReq) {
        return examService.getExamAssayDevice(clientReq);
    }

    /*
     * 获取二级分类 走redis缓存
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsCustomTypeView> getGoodsCustomTypes(String chainId,
                                                                List<Integer> typeIdList,
                                                                int hisType,
                                                                String manufactureId) throws CisGoodsServiceException {
        AbcListPage<GoodsCustomTypeView> clientRsp = new AbcListPage<>();
        clientRsp.setRows(goodsRedisUtils.getCustomTypeByTypeId(chainId,
                typeIdList,
                hisType,
                manufactureId).values().stream().filter(it -> {
            // 护理二级分类和床位二级分类不返回
            return it.getId() != null && !it.getId().equals(GoodsConst.GoodsCustomTypeId.CUSTOMTYPEID_NURSE_NURSE_LEVEL)
                    && !it.getId().equals(GoodsConst.GoodsCustomTypeId.CUSTOMTYPEID_OTHER_BED_FEE);
        }).sorted(Comparator.comparingInt(GoodsCustomTypeView::getSort)).collect(Collectors.toList()));
        return clientRsp;
    }

    /*
     * 获取二级分类goods的数量
     * 从goods服务迁移过来，一周就几次
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public CustomTypeGoodsCountRsp getOneGoodsCustomTypeGoodsCount(String chainId, Long customTypeId) throws CisGoodsServiceException {
        return goodsCustomTypeRedisService.getOneGoodsCustomTypeGoodsCount(chainId, customTypeId);
    }

    /*
     * 修改二级分类
     */
    @Transactional(rollbackFor = Exception.class)
    public AbcListPage<GoodsCustomTypeView> updateGoodsCustomTypes(UpdateCustomViewReq clientReq) throws CisGoodsServiceException {
        AbcListPage<GoodsCustomTypeView> clientRsp = new AbcListPage<>();
        clientRsp.setRows(goodsSysTypeService.updateCustomType(clientReq));
        return clientRsp;
    }

    /*
     * 获取供应商信息
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsSupplierView> getGoodsSuppliersFromRedis(String chainId, String clinicId, Integer pharmacyType, int isQueryDisabled, Integer status) throws CisGoodsServiceException {
        AbcListPage<GoodsSupplierView> clientRsp = new AbcListPage<>();
        List<GoodsSupplierView> rows = supplierUtils.getGoodsSuppliersFromRedis(chainId,null, pharmacyType, isQueryDisabled, status);

        //云煎药供应商是clinicId维度
        if (!StringUtils.isEmpty(clinicId)) {
            rows = rows.stream().filter(item -> item.getSubType() == GoodsSupplier.SupplierSubType.NORMAL ||
                            (item.getSubType() == GoodsSupplier.SupplierSubType.CLOUD_SUPPLIER && TextUtils.equals(item.getClinicId(), clinicId)))
                    .collect(Collectors.toList());
        }

        //如果是虚拟药房，需要设置虚拟药房编号
        if (pharmacyType == GoodsConst.PharmacyType.VIRTUAL_PHARMACY && !StringUtils.isEmpty(clinicId)) {
            ClinicConfig clinicConfig = cisClinicService.getClinicConfig(clinicId);
            GoodsPharmacyView goodsPharmacy = clinicConfig.getPharmacyList().stream().filter(pharmacy -> pharmacy.getType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY).findFirst().orElse(null);
            if (goodsPharmacy != null) {
                for (GoodsSupplierView row : rows) {
                    row.setPharmacyNo(goodsPharmacy.getNo());
                    row.setPharmacyType(goodsPharmacy.getType());
                }
            }
        }
        clientRsp.setRows(rows);
        return clientRsp;
    }


    /*
     * 保存检验设备型号
     *
     * @param req        要求事情
     * @param operatorId 操作符id
     * @return {@link ExamAssayDeviceView}
     */
    @Transactional(rollbackFor = Exception.class)
    public ExamAssayDeviceView saveExamDeviceAssayModel(ExamAssayDeviceView req, String operatorId) {
        return examService.saveExamDeviceAssayModel(req, operatorId);
    }

    /*
     * 通过id删除检验设备型号
     *
     * @param deviceModelId 设备模型id
     * @param operatorId    操作符id
     * @return {@link ExamAssayDeviceView}
     */
    @Transactional(rollbackFor = Exception.class)
    public ExamAssayDeviceView deleteExamDeviceAssayModelById(Long deviceModelId, String operatorId) {
        return examService.deleteExamDeviceAssayModelById(deviceModelId, operatorId);
    }

    /*
     * 通过id修改检验设备型号
     *
     * @param deviceModelId 设备模型id
     * @param req           要求事情
     * @param operatorId    操作符id
     * @return {@link ExamAssayDeviceView}
     */
    @Transactional(rollbackFor = Exception.class)
    public ExamAssayDeviceView updateExamDeviceAssayModelById(Long deviceModelId, ExamAssayDeviceView req, String operatorId) {
        return examService.updateExamDeviceAssayModelById(deviceModelId, req, operatorId);
    }

    /*
     * 通过id查询检验设备型号
     *
     * @param deviceModelId 设备模型id
     * @return {@link ExamAssayDeviceView}
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ExamAssayDeviceView getExamDeviceAssayModelById(Long deviceModelId) {
        return examService.getExamDeviceAssayModelById(deviceModelId);
    }

    /*
     * 通过id查询检验设备型号
     *
     * @param clientReq 客户要求
     * @return {@link ExamAssayDeviceView}
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ExamAssayDeviceView getExamDeviceAssayModel(GetDeviceListReq clientReq) {
        return examService.getExamDeviceAssayModel(clientReq);
    }

    /**
     * RPC批量获取检验设备型号列表
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<ExamAssayDeviceView> getExaminationAssayDeviceModelList(GetExaminationAssayDeviceModelsReq clientReq
    ) throws CisGoodsServiceException {
        return examService.getExaminationAssayDeviceModelList(clientReq);
    }

    /**
     * RPC批量查询获取检查设备
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    @Deprecated
    public AbcListPage<ExamInspectDeviceView> getExaminationInspectionDeviceList(GetExaminationInspectionDevicesReq clientReq) throws CisGoodsServiceException {
        return examService.getExaminationInspectionDeviceList(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<ExamAssayDeviceView> getExaminationAssayDeviceList(GetExaminationAssayDevicesReq clientReq) throws CisGoodsServiceException {
        return examService.getExaminationAssayDeviceList(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<ExaminationDeviceConnectApplyView> getExaminationDeviceConnectApplies(String chainId, String clinicId) {
        return examinationDeviceConnectApplyService.getExaminationDeviceConnectApplys(chainId, clinicId);
    }

    /*
     * 通知联机成功 检验设备
     * 如果单店 直接插入记录即可
     * 如果是总部 /门店 除了插入记录 还要 把禁用的goods 全启用起来。启用goods 放到另外一个异步事务里面
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public OpsCommonRsp checkCanLink(String chainId, String clinicId, Long deviceModelId) {
        examService.checkCanLink(chainId, clinicId, deviceModelId);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public OpsCommonRsp checkLinkExamDevice(String chainId, String clinicId, ExamDeviceCheckLinkReq checkLinkReq) {
        checkLinkReq.validate();
        examService.doCheckCanLink(chainId, clinicId, YesOrNo.NO, checkLinkReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @Transactional(rollbackFor = Exception.class)
    public ExamAssayDeviceView saveExamAssayDevice(InsertExamAssayDeviceReq clientReq) {
        return examService.saveExamAssayDevice(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public ExamAssayDeviceView deleteExamAssayDeviceById(Long deviceId, DeleteExamAssayDeviceReq deleteDeviceReq) {
        return examService.deleteExamAssayDeviceById(deviceId, deleteDeviceReq);
    }


    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp updateExamAssayDeviceById(Long deviceId, String chainId, String clinicId, String operatorId, UpdateExamAssayDeviceReq updateExamAssayDeviceReq) {
        examService.updateExamAssayDeviceById(deviceId, chainId, clinicId, operatorId, updateExamAssayDeviceReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @Transactional(rollbackFor = Exception.class)
    public ExamAssayDeviceView updateExamAssayDeviceStatus(UpdateExamAssayDeviceStatusReq clientReq) {
        return examService.updateExamAssayDeviceStatus(clientReq);
        //return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp batchUpdateExamAssayDevice(BatchUpdateExamAssayDeviceReq batchUpdateReq) {
        examService.batchUpdateExamAssayDevice(batchUpdateReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @Transactional(readOnly = true)
    public BaseSuccessRsp deleteExamDeviceRoomById(String deviceRoomId, String chainId, String clinicId, String operatorId) {
        return examService.deleteExamDeviceRoomById(deviceRoomId, chainId, clinicId, operatorId);
    }

    //----exam pipe
    @Transactional(readOnly = true)
    public AbcListPage<ExaminationSamplePipeView> getExaminationSamplePipes(String chainId, String clinicId, Integer withDeleted, ExaminationSamplePipeView examinationSamplePipeReq) {
        return examinationSamplePipeService.getExaminationSamplePipes(chainId, clinicId, withDeleted, examinationSamplePipeReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public ExaminationSamplePipeView saveExaminationSamplePipe(String chainId, String clinicId, String employeeId, ExaminationSamplePipeView examinationSamplePipeReq) {
        return examinationSamplePipeService.saveExaminationSamplePipe(chainId, clinicId, employeeId, examinationSamplePipeReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public ExaminationSamplePipeView deleteExaminationSamplePipeById(String id, String chainId, String clinicId, String employeeId) {
        return examinationSamplePipeService.deleteExaminationSamplePipeById(id, chainId, clinicId, employeeId);
    }

    @Transactional(rollbackFor = Exception.class)
    public ExaminationSamplePipeView updateExaminationSamplePipeById(String id, String chainId, String clinicId, String employeeId, ExaminationSamplePipeView examinationSamplePipeReq) {
        return examinationSamplePipeService.updateExaminationSamplePipeById(id, chainId, clinicId, employeeId, examinationSamplePipeReq);
    }

    @Transactional(readOnly = true)
    public ExaminationSamplePipeView getExaminationSamplePipeById(String id, String chainId, String clinicId) {
        return examinationSamplePipeService.getExaminationSamplePipeById(id, chainId, clinicId);
    }

    @Transactional(readOnly = true)
    public AbcListPage<ExaminationSamplePipeView> getExaminationSamplePipesByIds(QueryExamSamplePipeByIdsReq req) {
        return examinationSamplePipeService.getExaminationSamplePipesByIds(req);
    }

    @Transactional(readOnly = true)
    public List<ExaminationSampleSimpleView> getExaminationSampleSample(String chainId, String clinicId) {
        return examinationSamplePipeService.getExaminationSampleSample(chainId, clinicId);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp disableExaminationSample(ClientExamDisableReq clientReq) {
        examinationSamplePipeService.disableExaminationSample(clientReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }


    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsInOutTaxRatLog> getInOutTaxRateLogList(String headerChainId, String goodsId, List<Integer> goodsTypeIdList, Integer waitingEffect, Integer offset, Integer limit) {
        return goodsInOutTaxRateRuleService.getInOutTaxRateLogList(headerChainId, goodsId, goodsTypeIdList, waitingEffect, offset, limit);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ResponseEntity<byte[]> exportModifyPriceOrderList(GetModifyPriceListReq clientReq) {
        return goodsPriceService.exportModifyPriceOrderList(clientReq);
    }

    /*
     * goods 信息的批量维护
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp createOrUpdateProcessingGoods(CreateOrUpdateProcessGoodsReq clientReq) {
        goodsInfoModifyManageService.createOrUpdateProcessingGoods(clientReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    public GoodsProcessingImportResp importProcessingGoods(String chainId, String clinicId, String employeeId, String keyId, MultipartFile file) {
        GoodsProcessingImportResp resp = new GoodsProcessingImportResp();
        String key = String.format("%s:%s:%s:%s:%s", GOODS_PROCESSING_IMPORT_KEY_PREFIX, chainId, clinicId, employeeId, keyId);
        try (InputStream is = file.getInputStream()) {
            //第二行才是头
            int headRowNumber = 2;
            GoodsProcessingImportListener listener = new GoodsProcessingImportListener(key, headRowNumber, stringRedisTemplate);
            ClientCustomUnitRsp clientCustomUnitRsp = goodsCustomUnitService.getCustomUnit(chainId, clinicId, (int) GoodsConst.GoodsType.PROCESSING, null, null, null);
            List<ClientCustomUnitRsp.ClientCustomUnitItem> customUnitList = Optional.ofNullable(clientCustomUnitRsp.getCustomUnitList()).orElse(Lists.newArrayList());
            List<ClientCustomUnitRsp.ClientCustomUnitItem> sysUnitList = Optional.ofNullable(clientCustomUnitRsp.getSysUnitList()).orElse(Lists.newArrayList());
            Set<String> customUnitNameSet = customUnitList.stream()
                    .map(ClientCustomUnitRsp.ClientCustomUnitItem::getName).collect(Collectors.toSet());
            Set<String> sysUnitNameSet = sysUnitList.stream()
                    .map(ClientCustomUnitRsp.ClientCustomUnitItem::getName).collect(Collectors.toSet());
            Integer maxCustomUnitCount = clientCustomUnitRsp.getMaxCustomUnitCount();

            EasyExcel.read(
                            is,
                            CreateOrUpdateProcessGoodsReq.CreateOrUpdateProcessGoodsItem.class,
                            listener
                    ).sheet()
                    .headRowNumber(headRowNumber)
                    .doRead();
            List<CreateOrUpdateProcessGoodsReq.CreateOrUpdateProcessGoodsItem> goodsItems = listener.getGoodsItems();
            if (listener.isFormatIncorrect()) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_PROCESSING_EXCEL_FORMAT_INCORRECT);
            }
            if (CollectionUtils.isEmpty(goodsItems)) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_PROCESSING_EXCEL_IS_EMPTY);
            }
            resp.setRows(goodsItems)
                    .setGoodsItemCount(goodsItems.size())
                    .setCustomTypeCount((int) goodsItems.stream().map(CreateOrUpdateProcessGoodsReq.CreateOrUpdateProcessGoodsItem::getCustomTypeName).distinct().count());
            GoodsStockRowErrorDetail errorDetail = new GoodsStockRowErrorDetail();
            errorDetail.setErrorTitle(CisGoodsServiceError.CIS_GOODS_PROCESSING_EXCEL_IMPORT_ERROR.getMessage());
            Set<String> nameSet = new HashSet<>();
            for (int i = 0; i < goodsItems.size(); i++) {
                int rowIndex = headRowNumber + i + 1;
                CreateOrUpdateProcessGoodsReq.CreateOrUpdateProcessGoodsItem goodsItem = goodsItems.get(i);
                errorDetail.putGoodsStockRowErrorDetailItem(rowIndex, goodsItem.getName(), "");
                List<String> messages = Validators.validateString(goodsItem, Default.class);
                if (CollectionUtils.isNotEmpty(messages)) {
                    for (String message : messages) {
                        errorDetail.addErrMsg(rowIndex, message);
                    }
                }
                if (nameSet.contains(goodsItem.getName())) {
                    errorDetail.addErrMsg(rowIndex, "项目名不可重复");
                }
                if (customUnitNameSet.size() > maxCustomUnitCount) {
                    errorDetail.addErrMsg(rowIndex, "单位种类总数不能超过" + maxCustomUnitCount + "种");
                }
                List<String> excelParseErrorMessages = goodsItem.getExcelParseErrorMessages();
                if (CollectionUtils.isNotEmpty(excelParseErrorMessages)) {
                    for (String excelParseErrorMessage : excelParseErrorMessages) {
                        errorDetail.addErrMsg(rowIndex, excelParseErrorMessage);
                    }
                }
                if (!StringUtils.isEmpty(goodsItem.getName())) {
                    nameSet.add(goodsItem.getName());
                }
                if (!StringUtils.isEmpty(goodsItem.getPackageUnit()) && !sysUnitNameSet.contains(goodsItem.getPackageUnit())) {
                    customUnitNameSet.add(goodsItem.getPackageUnit());
                }
            }
            if (errorDetail.isErrorHappen()) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_PROCESSING_EXCEL_IMPORT_ERROR, errorDetail);
            }
            stringRedisTemplate.boundHashOps(key).put("status", GoodsProcessingProgress.Status.DONE + "");
            return resp;
        } catch (Exception e) {
            log.error("parse excel error", e);
            stringRedisTemplate.boundHashOps(key).put("status", GoodsProcessingProgress.Status.ERROR + "");
            CisGoodsServiceException customException;
            if (e instanceof CisGoodsServiceException) {
                customException = (CisGoodsServiceException) e;
            } else {
                customException = new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "解析Excel异常" + e.getMessage());
            }
            throw customException;
        }
    }

    public GoodsProcessingProgress getProcessingProgress(String chainId, String clinicId, String employeeId, String keyId) {
        Map<Object, Object> objectMap = stringRedisTemplate.opsForHash().entries(String.format("%s:%s:%s:%s:%s", GOODS_PROCESSING_IMPORT_KEY_PREFIX, chainId, clinicId, employeeId, keyId));
        GoodsProcessingProgress progress = new GoodsProcessingProgress();
        if (MapUtils.isEmpty(objectMap)) {
            return progress.setCount(0).setStatus(GoodsProcessingProgress.Status.PARSING);
        }
        return progress
                .setCount(StringUtils.isEmpty(objectMap.get("count")) ? null : Integer.parseInt(String.valueOf(objectMap.get("count"))))
                .setStatus(StringUtils.isEmpty(objectMap.get("status")) ? null : Integer.parseInt(String.valueOf(objectMap.get("status"))));
    }


    /*
     * 销售直接导入Excel
     */
    public GoodsSellerImportRsp sellerImportExcelGoods(String chainId,
                                                       String clinicId,
                                                       String employeeId,
                                                       String excelId,
                                                       int opType) {
        ClinicConfig clinicConfig = ServiceUtils.getClinicConfig(cisClinicService, clinicId);
        if (clinicConfig == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "加载诊所配置失败，请扫后再试");
        }
        List<GoodsCreateExcelRecord> resultPage = new ArrayList<>();
        GoodsSellerImportRsp clientRsp = goodsJenkinsRpcService.submitSellerImportTask(
                resultPage,
                chainId,
                clinicId,
                employeeId,
                excelId,
                opType == 1,
                false);
        if (clientRsp.getNewSubmit() == 1) {
            goodsJenkinsRpcService.asyncJenkinsGoodsSellerImportGoods(clinicConfig, resultPage, chainId, clinicId, employeeId, excelId, opType);
        }
        return clientRsp;
    }

    public GoodsSellerImportRsp checkSellerImportExcelGoods(String chainId,
                                                            String clinicId,
                                                            String employeeId,
                                                            String excelId,
                                                            Integer opType) {
        ClinicConfig clinicConfig = ServiceUtils.getClinicConfig(cisClinicService, clinicId);
        if (clinicConfig == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "加载诊所配置失败，请扫后再试");
        }
        List<GoodsCreateExcelRecord> resultPage = new ArrayList<>();
        return goodsJenkinsRpcService.submitSellerImportTask(
                resultPage,
                chainId,
                clinicId,
                employeeId,
                excelId,
                opType == 1,
                true);
    }

    /*
     * 计算套餐价格
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public CalGoodsComposePriceReq calculateComposePrice(CalGoodsComposePriceReq clientReq, String clinicId) {
        return GoodsInfoModifyManageService.calculateComposePrice(clientReq, cisClinicService.getClinicConfig(clinicId), goodsRedisUtils);
    }


    /**
     * 用法关联项目
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public OutpatientConfigVO getOutpatientConfig(String chainId, String clinicId, int sourceType) {
        return usageInfoAssociationService.getOutpatientConfig(chainId, clinicId, sourceType);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public OutpatientInfusionRelatedConfigsVO getOutpatientInfusionRelatedConfigs(String chainId, String clinicId, int sourceType) {
        return usageInfoAssociationService.getOutpatientInfusionRelatedConfigs(chainId, clinicId, sourceType);
    }

    /**
     * 拉取单个用法关联项目
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public OutpatientInfusionRelatedConfigVO getOutpatientInfusionRelatedConfig(String chainId, String clinicId, int usageType, int sourceType) {
        return usageInfoAssociationService.getOutpatientInfusionRelatedConfig(chainId, clinicId, usageType, sourceType);
    }

    /*
     * 新增或修改用法关联项目
     * 项目的用法关联项目放到 goods的修改里面
     */
    @Transactional(rollbackFor = Exception.class)
    public OutpatientInfusionRelatedConfigVO updateOutpatientInfusionRelatedConfig(updateOutpatientInfusionRelatedConfigReq req, String chainId, String clinicId, String employeeId) {
        return usageInfoAssociationService.updateOutpatientInfusionRelatedConfig(req, chainId, clinicId, employeeId);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public OutpatientInfusionRelatedConfigVO getOutpatientInfusionRelatedConfigById(String chainId, String clinicId, Long id) {
        return usageInfoAssociationService.getOutpatientInfusionRelatedConfigById(chainId, clinicId, id);
    }

    @Transactional(rollbackFor = Exception.class)
    public OutpatientInfusionRelatedConfigVO createOutpatientInfusionRelatedConfig(updateOutpatientInfusionRelatedConfigReq req, String chainId, String clinicId, String employeeId) {
        return usageInfoAssociationService.createOutpatientInfusionRelatedConfig(req, chainId, clinicId, employeeId);
    }

    @Transactional(rollbackFor = Exception.class)
    public OutpatientInfusionRelatedConfigVO updateOutpatientInfusionRelatedConfigById(updateOutpatientInfusionRelatedConfigReq req, String chainId, String clinicId, String employeeId, Long id) {
        return usageInfoAssociationService.updateOutpatientInfusionRelatedConfigById(req, chainId, clinicId, employeeId, id);
    }

    public void deleteOutpatientInfusionRelatedConfig(String chainId, String clinicId, String employeeId, Long id) {
        usageInfoAssociationService.deleteOutpatientInfusionRelatedConfig(chainId, clinicId, employeeId, id);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp batchUpdateBizExtensions(UpdateBizExtensionReq req) {
        return goodsInfoModifyManageService.batchUpdateBizExtensions(req);
    }


    @Transactional(rollbackFor = Exception.class)
    public GoodsLockRsp lockingGoodsStock(ScGoodsLockingGoodsLockReq scGoodsGoodsLockReq) {
        return goodsStockLockingService.lockingGoodsStock(scGoodsGoodsLockReq).getT1();
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    @LogReqAndRsp
    public GoodsLockRsp getLockingGoodsStock(GetGoodsGoodsLockReq scGoodsGoodsLockReq) {
        return goodsStockLockingService.getLockingGoodsStock(scGoodsGoodsLockReq);
    }

    /*
     * 新建SPUGoods
     * 产品上不能接受不了异步的方案，所以这里还是尽量优化创建的速度
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public GoodsSpuView createGoodsSpu(ClientGoodsSpuCreateReq clientReq) {
        //Redis锁不能重复创建
        String createToken = GoodsRedisUtils.redisGoodsSpuCreateCheckKey(clientReq.getHeaderChainId(), clientReq.getHeaderClinicId(), clientReq.getMd5Key());
        Long createTokenValue = (Long) redisUtils.get(createToken);
        if (createTokenValue != null) {
            sLogger.error("Spu已经在创建中了:clientReq={}", JsonUtils.dump(clientReq));
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "创建中");
        }
        redisUtils.set(createToken, clientReq.getId(), RedisUtils.EXPIRE_5MIN_SECONDS);


        GoodsSpuView view = new GoodsSpuView();
        try {
            //新建快速落地SPU 存spu表和规格
            GoodsSpuCreateServiceBase createServiceBase = goodsSpuService.createGoodsSpu(clientReq);
            GoodsSpuCreateRedisTask createSkuRedisTask = createServiceBase.getCreateSkuRedisTask();
            createSkuRedisTask.setGoodsIdList(clientReq.getGoodsIdList());
            //异步 慢慢建sku
            long s = System.currentTimeMillis();
            goodsSpuService.doStartCreateSkuGoodsTask(createServiceBase.getCreateReq(), createSkuRedisTask, false,
                    false, null, SpuStatusChangeMessage.MessageType.MSG_TYPE_SPU_CREATED, false);
            long e = System.currentTimeMillis();
            sLogger.info(" SPU耗时分析 创建sku总耗时：{}ms", e - s);
            view.setId(createSkuRedisTask.getId());
            GoodsUtils.runAfterTransaction(() -> {
                if (GoodsPrivUtils.isSingleMode(clientReq.getHeaderClinicType(), clientReq.getHeaderViewMode())) {
                    goodsRedisUtils.clearClinicGoodsStockCountCache(clientReq.getHeaderChainId(), clientReq.getHeaderClinicId());
                } else {
                    goodsRedisUtils.clearClinicGoodsStockCountCache(clientReq.getHeaderChainId(), GoodsUtils.WHOLE_CHAIN_ID);
                }
            });
        } catch (Exception exp) {
            sLogger.error("createGoodsSpu error Happened:", exp);
            throw exp;
        } finally {
            redisUtils.del(createToken);
        }
        return view;
    }

    /*
     * 修改SPU ------------------产品上会在一个接口里面修改如下内容
     * 修改spu的内容分几部分
     * 1.spu基本信息的修改
     * 2.spu规格组的修改
     * 3.spu下goods的修改
     * 3.1 新增sku
     * 3.2 修改sku
     * 3.2.1 修改sku基本资料
     * 3.2.2 修改sku的停用启用状态
     * 3.2.3 子店和总部修改分别修改
     * 3.3 删除sku
     * 3.3.1 套餐的检查
     */
    @Transactional(rollbackFor = Exception.class)
    public GoodsSpuView updateGoodsSpu(ClientGoodsSpuCreateReq clientReq) {
        //一个spu只能有一个在进行中的修改
        String token = GoodsRedisUtils.redisGoodsSpuCreateCheckKey(clientReq.getHeaderChainId(), clientReq.getHeaderClinicId(), clientReq.getMd5Key());
        Long updateTokenValue = (Long) redisUtils.get(token);
        if (updateTokenValue != null) {
            sLogger.error("Spu已经有在修改中的任务:clientReq={}", JsonUtils.dump(clientReq));
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "存在修改中的任务");
        }

        //写入redis 表示已经有修改了
        redisUtils.set(token, clientReq.getId(), RedisUtils.EXPIRE_5MIN_SECONDS); //token 有效 5分钟
        GoodsSpuView view = new GoodsSpuView();
        try {
            //新建快速落地SPU 存spu表和规格
            long startTime = System.currentTimeMillis();
            GoodsSpuUpdateServiceBase createServiceBase = goodsSpuService.updateGoodsSpu(clientReq);
            long endTime = System.currentTimeMillis();
            sLogger.info("修改spu及规格组耗时：{}ms", endTime - startTime);
            GoodsSpuCreateRedisTask createSkuRedisTask = createServiceBase.getCreateSkuRedisTask();
            createSkuRedisTask.setGoodsIdList(clientReq.getGoodsIdList());

            goodsSpuService.doStartCreateSkuGoodsTask(createServiceBase.getCreateReq(), createSkuRedisTask,
                    createServiceBase.isModifySpuInfo(), createServiceBase.isManufacturerFullModify(),
                    createServiceBase.getGoodsList(), SpuStatusChangeMessage.MessageType.MSG_TYPE_SPU_UPDATED,
                    createServiceBase.isRegistrationNumberModified());
            sLogger.info("修改sku耗时：{}ms", System.currentTimeMillis() - endTime);
            if (!CollectionUtils.isEmpty(createSkuRedisTask.getGoodsIdList()) || createServiceBase.isNeedClearRedis()) {
                GoodsUtils.runAfterTransaction(() -> {
                            goodsSpuService.updateSpuPrice(clientReq.getHeaderChainId(), createSkuRedisTask.getId());
                            goodsRedisUtils.clearSpuRedisCache(clientReq.getHeaderChainId(), Collections.singletonList(createServiceBase.getGoodsSpu().getId()));
                            //刷小于十个Goods同步的刷，改很多，用户也有预期修改很慢.10个应该不会超时
                            if (!CollectionUtils.isEmpty(createSkuRedisTask.getGoodsIdList())) {
                                if (createSkuRedisTask.getGoodsIdList().size() < 10) {
                                    goodsStatService.refreshGoodsStatSync(
                                            createSkuRedisTask.getGoodsIdList(),
                                            clientReq.getHeaderChainId(),
                                            cisClinicService.getClinicConfig(clientReq.getHeaderClinicId())
                                    );
                                } else {
                                    goodsStatService.refreshGoodsStatAsync(
                                            createSkuRedisTask.getGoodsIdList(),
                                            clientReq.getHeaderChainId(),
                                            cisClinicService.getClinicConfig(clientReq.getHeaderClinicId())
                                    );
                                }
                            }
                        }
                );
            }
            view.setId(clientReq.getId());
        } catch (Exception exp) {
            sLogger.error("createGoodsSpu error Happened:", exp);
            throw exp;
        } finally {
            redisUtils.del(token);
        }
        return view;
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp deleteGoodsSpu(Long spuGoodsId, String chainId, String clinicId, int clinicType, int viewMode, String employeeId) {
        if (TextUtils.isEmpty(employeeId)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER);
        }
        if (GoodsPrivUtils.isSubClinic(clinicType, viewMode)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_UPSET_FORBIDDEN);
        }
        // 检查并上redis锁 避免并发
        String deleteSpuRedisKey = GoodsRedisUtils.redisGoodsSpuDeleteCheckKey(chainId, clinicId, spuGoodsId);
        Long deleteSpuRedisValue = (Long) redisUtils.get(deleteSpuRedisKey);
        if (deleteSpuRedisValue != null) {
            sLogger.error("SPU正在删除，spuId = {}", spuGoodsId);
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "SPU正在删除");
        }
        redisUtils.set(deleteSpuRedisKey, spuGoodsId, RedisUtils.EXPIRE_5MIN_SECONDS);
        try {
            goodsSpuService.deleteGoodsSpu(spuGoodsId, chainId, employeeId);
            if (GoodsPrivUtils.isSingleMode(clinicType, viewMode)) {
                goodsRedisUtils.clearClinicGoodsStockCountCache(chainId, clinicId);
            } else {
                goodsRedisUtils.clearClinicGoodsStockCountCache(chainId, GoodsUtils.WHOLE_CHAIN_ID);
            }
        } finally {
            redisUtils.del(deleteSpuRedisKey);
        }
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public GoodsSpuView getGoodsSpuBySpuGoodsIdWithClinicId(GetGoodsSpuBySpuGoodsIdReq clientReq) {
        return goodsSpuService.getGoodsSpuBySpuGoodsIdWithClinicId(clientReq);
    }

    /*
     * 获取spu告警配置信息
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ClientGoodsWarningSetting getSpuGoodsWarnSetting(ClientGoodsWarningSetting clientReq, Long spuGoodsId) {
        return goodsSpuService.getSpuGoodsWarnSetting(clientReq, spuGoodsId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void putSpuGoodsWarnSetting(ClientGoodsWarningSetting clientReq, Long spuGoodsId) {
        if (TextUtils.isEmpty(clientReq.getEmployeeId())) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER);
        }
        //if (GoodsPrivUtils.isSubClinic(clientReq.getClinicType(), clientReq.getViewMode())) {
        //    throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_UPSET_FORBIDDEN);
        //}
        // 增加redis保护
        String redisKey = GoodsRedisUtils.redisGoodsSpuWarnSettingCheckKey(clientReq.getChainId(), clientReq.getClinicId(), spuGoodsId);
        Long redisValue = (Long) redisUtils.get(redisKey);
        if (redisValue != null) {
            sLogger.error("SPU正在修改告警配置，spuId = {}", spuGoodsId);
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "SPU正在修改告警配置");
        }
        redisUtils.set(redisKey, spuGoodsId, RedisUtils.EXPIRE_5MIN_SECONDS);
        try {
            goodsSpuService.putSpuGoodsWarnSetting(clientReq, spuGoodsId);
        } finally {
            redisUtils.del(redisKey);
        }
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsSpuStockDetailRsp getGoodsSpuStockDetail(GoodsSpuStockDetailReq clientReq) {
        return goodsSpuService.getGoodsSpuStockDetail(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsSpuConditionRsp queryGoodsSpuConditionList(String chainId, Long spuGoodsId) {
        return goodsSpuService.queryGoodsSpuConditionList(chainId, spuGoodsId);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsSpuWaitSyncCountRsp getGoodsSpuWaitSyncCount(GoodsSpuWaitSyncCountReq clientReq) {
        return goodsSpuService.getGoodsSpuWaitSyncCount(clientReq);
    }

    public void syncSpuGoodsInfo(GoodsSpuWaitSyncCountReq clientReq) {
        goodsSpuService.syncSpuGoodsInfo(clientReq);
    }

    public GoodsConfigRsp createGoodsPharmacyStorage(UpdateGoodsStorageConfigReq clientReq) {
        goodsConfigRedisCache.createOrUpdateGoodsPharmacyStorage(clientReq);

        GoodsConfigHelper helperReturn = goodsRedisUtils.reloadChainGoodsConfig(clientReq.getChainId(), clientReq.getClinicId());
        return GoodsConfigRedisCache.pathReturnView(helperReturn, null, suggestConfiguration, cisClinicService, hisWardService, goodsSysTypeService, goodsRedisUtils);
    }

    public GoodsConfigRsp updateGoodsPharmacyStorage(UpdateGoodsStorageConfigReq clientReq) {
        goodsConfigRedisCache.createOrUpdateGoodsPharmacyStorage(clientReq);

        GoodsConfigHelper helperReturn = goodsRedisUtils.reloadChainGoodsConfig(clientReq.getChainId(), clientReq.getClinicId());
        return GoodsConfigRedisCache.pathReturnView(helperReturn, null, suggestConfiguration, cisClinicService, hisWardService, goodsSysTypeService, goodsRedisUtils);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ToClientGoodsPharmacyView getGoodsPharmacyStorage(String chainId, String clinicId, int clinicType, int viewMode, Long id) {
        return goodsConfigRedisCache.getGoodsPharmacyStorage(chainId, clinicId, clinicType, viewMode, id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdateGoodsPharmacyRule(GoodsPharmacyRuleConfigReq clientReq) {
        goodsPharmacyService.createOrUpdateGoodsPharmacyRule(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsPharmacyRuleView getGoodsPharmacyRule(String chainId, String clinicId, int hisType, Long id) {
        return goodsPharmacyService.getGoodsPharmacyRule(chainId, clinicId, hisType, id);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsPharmacyRuleListRsp listGoodsPharmacyRule(String chainId, String clinicId, int hisType) {
        return goodsPharmacyService.listGoodsPharmacyRule(chainId, clinicId, hisType);
    }

    // --------- 费用类型
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsFeeTypeView> getFeeTypeList(String headerChainId, Integer disable, Integer innerFlag, Integer scopeId) throws CisGoodsServiceException {
        return goodsFeeTypeRedisService.getFeeTypeList(headerChainId, disable, innerFlag, scopeId);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp createFeeType(String headerChainId, String headerEmployeeId, int headerClinicType, int headerViewMode, GoodsFeeTypeView clientReq) throws CisGoodsServiceException {
        return goodsFeeTypeRedisService.createFeeType(headerChainId, headerEmployeeId, headerClinicType, headerViewMode, clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp updateFeeType(String headerChainId, String headerEmployeeId, int headerClinicType, int headerViewMode, GoodsFeeTypeView clientReq) throws CisGoodsServiceException {
        return goodsFeeTypeRedisService.updateFeeType(headerChainId, headerEmployeeId, headerClinicType, headerViewMode, clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsFeeTypeView getFeeType(String headerChainId, Long feeTypeId) throws CisGoodsServiceException {
        return goodsFeeTypeRedisService.getFeeType(headerChainId, feeTypeId);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp deleteFeeType(String headerChainId, String headerEmployeeId, int headerClinicType, int headerViewMode, String feeTypeId) throws CisGoodsServiceException {
        return goodsFeeTypeRedisService.deleteFeeType(headerChainId, headerEmployeeId, headerClinicType, headerViewMode, feeTypeId);
    }


    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockReceptionOrderView getGoodsStockReceptionOrder(GetGoodsStockReceptionOrderReq clientReq) {
        return goodsStockReceptionOrderService.getGoodsStockReceptionOrder(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStockReceptionOrderListRsp getReceptionOrderList(GoodsStockReceptionOrderListReq clientReq) {
        return goodsStockReceptionOrderService.getReceptionOrderList(clientReq);
    }

    /*
     * 新增或修改领用单
     */
    public GoodsStockReceptionOrderView createOrUpdateGoodsStockReceptionOrder(CreateStockReceptionOrderReq clientReq) {
        GoodsOrderReceptionOrderOpsBase base;
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(),
                clientReq.getReqMd5Key(), GoodsRedisUtils.CHECKING_RECEPTION);
        try {
            base = goodsStockReceptionOrderService.createOrUpdateGoodsStockReceptionOrder(clientReq);
            goodsStockReceptionOrderService.asyncTrans(base);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(),
                    clientReq.getReqMd5Key(), false);
            throw exp;
        }
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(),
                clientReq.getReqMd5Key(), base.getGoodsStockReceptionOrder().isOrderStable());
        return new GoodsStockReceptionOrderView();
    }

    /*
     * 审核流程的领用单
     */
    public GoodsStockReceptionOrderView reviewOrRevokeGoodsStockReceptionOrder(CreateStockReceptionOrderReviewReq clientReq) {
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(),
                clientReq.getReqMd5Key(), GoodsRedisUtils.CHECKING_RECEPTION);
        try {
            GoodsOrderReceptionOrderOpsBase base = goodsStockReceptionOrderService.reviewOrRevokeGoodsStockReceptionOrder(clientReq);
            goodsStockReceptionOrderService.asyncTrans(base);
        } catch (Exception e) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(),
                    clientReq.getReqMd5Key(), false);
            throw e;
        }
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(),
                clientReq.getReqMd5Key(), true);
        return new GoodsStockReceptionOrderView();
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsStockInfo> queryStockInfoByGoodsIds(QueryByStockIdsReq queryByStockIdsReq) {
        String chainId = queryByStockIdsReq.getChainId();
        String clinicId = queryByStockIdsReq.getClinicId();
        List<Long> stockIds = queryByStockIdsReq.getStockIds();
        if (CollectionUtils.isEmpty(stockIds)) {
            throw new ParamRequiredException("stockIds");
        }
        AbcListPage<GoodsStockInfo> page = new AbcListPage<>();
        page.setRows(goodsStockService.queryStockInfoByGoodsIds(chainId, stockIds));
        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    public void sortGoodsPharmacyRuleList(GoodsPharmacyRuleConfigSortReq clientReq) {
        goodsPharmacyService.sortGoodsPharmacyRuleList(clientReq);
    }

    public GetStockGoodsListRsp pageListArchiveStockGoods(GetStockGoodsListReq clientReq) {
        GetStockGoodsListRsp goodsListRsp = new GetStockGoodsListRsp();
        String clinicId = clientReq.getQueryClinicId();
        if (StringUtils.isEmpty(clinicId)) { //为空一定是总部门店
            clinicId = clientReq.getHeaderClinicId();
        }
        ClinicConfig clinicConfig = ServiceUtils.getClinicConfig(cisClinicService, clinicId);
        if (clinicConfig == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.GET_CLINIC_CONFIG_ERROR);
        }
        goodsListV2Service.pageListArchiveStockGoods(clientReq, goodsListRsp);

        /*
         * 没有获取到数据页把数据送回去
         * pc没header页应该要能展示出defalut列
         * */
        int chainMode = clinicConfig.getViewMode();
        Organ organ = cisClinicService.getOrgan(clinicId);
        try {

            List<TableHeaderEmployeeItem> headerList;
            JsonNode jsonNode;
            jsonNode = propertyService.getTableHeaderEmployeeItemsByTableKey(clientReq.getEmployeeId(), "goods.archive.goodsBasicInfo", abcEnv, chainMode, clinicConfig.getClinicType(),
                    clinicConfig.getHisType(), (int) GoodsUtils.SwitchFlag.ON, null, null, organ != null ? organ.getAddressProvinceId() : null, organ != null ? organ.getAddressCityId() :
                            null, organ != null ? organ.getAddressDistrictId() : null);
            headerList = JsonUtils.readValue(jsonNode, new com.fasterxml.jackson.core.type.TypeReference<List<TableHeaderEmployeeItem>>() {
            });
            headerList = headerList.stream().filter(child -> {
                if (GoodsUtils.compareStrEqual(child.getKey(), "goodsPriceInfo.chainPackagePrice") || GoodsUtils.compareStrEqual(child.getKey(), "goodsPriceInfo.chainPiecePrice")) {
                    if (clinicConfig.isSingleMode()) {
                        return false;
                    }
                }
                if (GoodsUtils.compareStrEqual(child.getKey(), "goodsPriceInfo.packagePrice") || GoodsUtils.compareStrEqual(child.getKey(), "goodsPriceInfo.piecePrice")) {
                    return !clinicConfig.isHeadClinic() || clinicConfig.isSingleMode();
                }
                return true;
            }).collect(Collectors.toList());
            goodsListRsp.setGoodsHeader(headerList);
        } catch (Exception exp) {
            sLogger.error("pageListStockGoods getGoodsHeader=", exp);
        }
//        goodsListV2Service.updateListStockGoodsMatchStatus(clientReq.getChainId(), clinicId, list);
        return goodsListRsp;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public void exportPageListArchiveStockGoods(GetStockGoodsListReq clientReq) {
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_ARCHIVEIN_ORDER_CHECKING + clientReq.getHeaderClinicId(), clientReq.getEmployeeId(), GoodsRedisUtils.CHECKING_EXPORT_GOODSLIST);
        try {
            goodsListV2Service.exportPageListArchiveStockGoods(clientReq);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_ARCHIVEIN_ORDER_CHECKING + clientReq.getHeaderClinicId(), clientReq.getEmployeeId(), false);
            throw exp;
        }

        //false 马上把key删掉
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_ARCHIVEIN_ORDER_CHECKING + clientReq.getHeaderClinicId(), clientReq.getEmployeeId(), true);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportReceptionOrderList(GoodsStockReceptionOrderListReq clientReq) {

        sLogger.info("exportOutOrdersList getReqMd5Key={}", clientReq.getReqMd5Key());
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(), clientReq.getReqMd5Key(), GoodsRedisUtils.CHECKING_IN_EXPORT);
        try {
            goodsStockReceptionOrderService.exportReceptionOrderList(clientReq);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(), clientReq.getReqMd5Key(), false);
            sLogger.info("exportOutOrdersList getReqMd5Key={} deleted", clientReq.getReqMd5Key());
            throw exp;
        }
        //false 马上把key删掉
        goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + clientReq.getHeaderClinicId(), clientReq.getReqMd5Key(), true);
        sLogger.info("exportOutOrdersList getReqMd5Key={} deleted", clientReq.getReqMd5Key());

    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportGoodsStockReceptionOrder(GetGoodsStockReceptionOrderReq clientReq) throws CisGoodsServiceException {
        goodsStockReceptionOrderService.exportGoodsStockReceptionOrder(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetGoodsPharmacyConfigRsp getGoodsPharmacyConfig(GetGoodsPharmacyConfigReq clientReq) {
        return goodsPharmacyService.getGoodsPharmacyConfig(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public GetGoodsAsyncExportInfoRsp exportPageListStockGoodsSubDetail(GetStockGoodsListReq clientReq) throws CisGoodsServiceException {
        String redisBaseKey = RedisUtils.SCGOODS_REDISCACHEKEY_STOCKIN_ORDER_CHECKING + "detail:" + clientReq.getHeaderClinicId();
        goodsRedisUtils.orderIngFlagCheckAndSet(redisBaseKey, clientReq.getEmployeeId(), GoodsRedisUtils.CHECKING_EXPORT_GOODSLIST);
        GetGoodsAsyncExportInfoRsp rsp = new GetGoodsAsyncExportInfoRsp();
        try {
            goodsListV2Service.exportPageListStockGoodsSubDetail(clientReq, rsp);
        } catch (Exception exp) {
            goodsRedisUtils.orderingFlagClearOrFinished(redisBaseKey, clientReq.getEmployeeId(), false);
            throw exp;
        }
        //false 马上把key删掉
        goodsRedisUtils.orderingFlagClearOrFinished(redisBaseKey, clientReq.getEmployeeId(), true);
        return rsp;
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetGoodsAsyncExportInfoRsp getGoodsAsyncExportInfo(GetGoodsAsyncExportInfoReq clientReq) {
        return goodsAsyncExportService.getGoodsAsyncExportInfo(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public void downloadGoodsAsyncExport(GetGoodsAsyncExportInfoReq clientReq) {
        goodsAsyncExportService.downloadGoodsAsyncExport(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsItem> getExaminationAssayGoodsItemsByDeviceModelList(GetExaminationAssayDeviceModelsReq clientReq) {
        return examService.getExaminationAssayGoodsItemsByDeviceModelList(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp createOrUpdateGoodsFeeType(CreateOrUpdateGoodsFeeTypeReq clientReq) {
        goodsFeeTypeRedisService.createOrUpdateGoodsFeeType(clientReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp builtInConsultationGoods(String chainId) {
        goodsInfoModifyManageService.builtInConsultationGoods(chainId);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    /*
     * 拉取社保异动提醒单据
     *
     * @param clinicId 只能拉这个门店的医保异动推送
     * @param orderId  推送给cms的时候带出去的就是这个orderId 这个order当天可能会不停被加入新的变更内容
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ShebaoChangeOrderView getShebaoChangeOrder(String chainId, String clinicId, Long orderId, int type, Integer offset, Integer limit) {
        return goodsShebaoService.getShebaoChangeOrder(chainId, clinicId, orderId, type, offset, limit);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsFeeCategoryView> getSysFeeCategoryList(String chainId, int hisType) {
        return goodsSysFeeCategoryService.getSysFeeCategoryList(chainId, hisType);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public QueryGoodsFeeCategoryByVersionRsp queryGoodsFeeCategoryByVersion(QueryGoodsFeeCategoryByVersionReq clientReq) {
        return goodsHisVersionService.queryGoodsFeeCategoryByVersion(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsFeeTypeAssociateCategoryView> getFeeTypeAssociateCategoryList(String chainId, int hisType) {
        return goodsSysFeeCategoryService.getFeeTypeAssociateCategoryList(chainId, hisType);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsItem> getAutoChargingGoodsList(String chainId, String clinicId) {
        return queryCisGoodsListService.getAutoChargingGoodsList(chainId, clinicId);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<QueryGoodsRulePharmacyRsp> queryGoodsRulePharmacy(QueryGoodsRulePharmacyReq clientReq) {
        AbcListPage<QueryGoodsRulePharmacyRsp> abcListPage = new AbcListPage<>();
        abcListPage.setRows(new ArrayList<>());
        List<GoodsRulePharmacyItem> ruleItemList = clientReq.getRuleItemList();
        if (CollectionUtils.isEmpty(ruleItemList)) {
            return abcListPage;
        }
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(clientReq.getClinicId());
        if (clinicConfig == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.GET_CLINIC_CONFIG_ERROR);
        }
        ruleItemList.forEach(ruleItem -> {
            QueryGoodsRulePharmacyRsp rsp = new QueryGoodsRulePharmacyRsp();
            rsp.setTypeId(ruleItem.getTypeId());
            rsp.setKeyId(ruleItem.getKeyId());
            rsp.setCustomTypeId(ruleItem.getCustomTypeId());
            rsp.setSceneType(ruleItem.getSceneType());
            rsp.setDepartmentId(ruleItem.getDepartmentId());
            rsp.setWardAreaId(ruleItem.getWardAreaId());
            rsp.setProcessType(ruleItem.getProcessType());
            rsp.setProcessSubType(ruleItem.getProcessSubType());
            GoodsPharmacyView pharmacyView = clinicConfig.getClinicPharmacyIncDeleteByDepartmentId(ruleItem.getTypeId(),
                    ruleItem.getCustomTypeId(),
                    ruleItem.getSceneType(),
                    ruleItem.getDepartmentId(),
                    ruleItem.getWardAreaId(),
                    ruleItem.getProcessType(),
                    ruleItem.getProcessSubType());
            if (pharmacyView != null) {
                GoodsPharmacyBaseView pharmacyBaseView = new GoodsPharmacyBaseView();
                pharmacyBaseView.setChainId(pharmacyView.getChainId());
                pharmacyBaseView.setClinicId(pharmacyView.getClinicId());
                pharmacyBaseView.setNo(pharmacyView.getNo());
                pharmacyBaseView.setName(pharmacyView.getName());
                pharmacyBaseView.setType(pharmacyView.getType());
                pharmacyBaseView.setTypeName(pharmacyView.getTypeName());
                pharmacyBaseView.setStatus(pharmacyView.getStatus());
                pharmacyBaseView.setInnerFlag(pharmacyView.getInnerFlag());
                pharmacyBaseView.setSort(pharmacyView.getSort());
                pharmacyBaseView.setIsDeleted(pharmacyView.getIsDeleted());
                rsp.setPharmacy(pharmacyBaseView);
            }
            abcListPage.getRows().add(rsp);
        });
        return abcListPage;
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsProfitCategoryView getGoodsProfitCategoryTypes(String chainId) {
        return goodsCustomTypeRedisService.getGoodsProfitCategoryTypes(chainId);
    }

    @Transactional(rollbackFor = Exception.class)
    public GoodsProfitCategoryView updateGoodsProfitCategoryTypes(GoodsProfitCategoryView req) {
        return goodsCustomTypeRedisService.updateGoodsProfitCategoryTypes(req);
    }

    /*
     * 查询要货或采购订单
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetClaimOrPurchaseOrderRsp getClaimOrPurchaseOrderList(GetClaimOrPurchaseOrderReq req) {
        return goodsClaimOrderService.getClaimOrPurchaseOrderList(req);
    }

    /*
     * 查询要货或采购订单
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetClaimOrPurchaseOrderRsp getPurchaseOrderListOfReceive(GetClaimOrPurchaseOrderReq req) {
        return goodsClaimOrderService.getPurchaseOrderListOfReceive(req);
    }

    /*
     * 创建采购订单
     */
    @Transactional(rollbackFor = Exception.class)
    public SimplePurchaseOrderRsp createPurchaseOrder(CreatePurchaseOrderReq clientReq) {
        GoodsPurchaseOrder purchaseOrder = goodsPurchaseOrderService.createOfflinePurchaseOrder(clientReq);
        //采购单被加入了采购计划，重新计算采购计算的相关量
        if (!StringUtils.isEmpty(clientReq.getPlanId())) {
            String oldPlanId = goodsPurchasePlanService.addPurchaseOrderToPurchasePlan(clientReq.getPlanId(), purchaseOrder.getId(), clientReq.getHeaderEmployeeId(), clientReq.getHeaderChainId(), clientReq.getHeaderClinicType(), clientReq.getHeaderViewMode());
            goodsPurchasePlanService.refreshPurchasePlan(clientReq.getHeaderChainId(), clientReq.getHeaderEmployeeId(), clientReq.getPlanId(), oldPlanId);
        }
        SimplePurchaseOrderRsp clientRsp = new SimplePurchaseOrderRsp();
        clientRsp.setId(purchaseOrder.getId().toString());
        clientRsp.setOrderNo(purchaseOrder.getOrderNo());
        return clientRsp;
    }

    /*
     * 修改要货单数量
     */
    public OpsCommonRsp updatePurchaseOrder(CreatePurchaseOrderReq clientReq) {
        Pair<String, GoodsPurchaseOrder> oldPlanIdToPurchaseOrder = goodsPurchaseOrderService.updateOfflinePurchaseOrder(clientReq);
        goodsPurchasePlanService.refreshPurchasePlan(clientReq.getHeaderChainId(), clientReq.getHeaderEmployeeId(), clientReq.getPlanId(), oldPlanIdToPurchaseOrder.getFirst());
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    public OpsCommonRsp deleteDraftPurchaseOrder(CreatePurchaseOrderReq clientReq) {
        goodsPurchaseOrderService.deleteDraftPurchaseOrder(clientReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    /*
     * 创建要货单
     * 药房单据流：最新开始的地方，这个函数不会产生关联单据
     * 下一步：总部审核 要货单
     */
    public SimplePurchaseOrderRsp createClaimOrder(CreatePurchaseOrderReq clientReq) {
        clientReq.parameterCheck(cisClinicService,supplierUtils,null, null);
        //要货单
        GoodsPurchaseOrder claimOrder = goodsClaimOrderService.createClaimOrder(clientReq);
        SimplePurchaseOrderRsp simplePurchaseOrderRsp = new SimplePurchaseOrderRsp();
        simplePurchaseOrderRsp.setId(String.valueOf(claimOrder.getId()));
        simplePurchaseOrderRsp.setOrderNo(claimOrder.getOrderNo());
        return simplePurchaseOrderRsp;
    }

    public OpsCommonRsp updateClaimOrder(CreatePurchaseOrderReq clientReq) {
        String oldPlanId = goodsClaimOrderService.updateClaimOrder(clientReq);
        goodsPurchasePlanService.refreshPurchasePlan(clientReq.getHeaderChainId(), clientReq.getHeaderEmployeeId(), clientReq.getPlanId(), oldPlanId);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }


    /*
     * 创建收货单
     *
     * @param req 请求参数
     * @return 返回视图
     */
    @Transactional(rollbackFor = Exception.class)
    public ReceiveOrderRsp createReceiveOrder(ReceiveOrderReq req) {
        String reqMd5Key = req.getReqMd5Key();
        goodsRedisUtils.orderDupOptFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_RECEIVE_ORDER_CHECKING + req.getHeaderClinicId(), reqMd5Key, GoodsRedisUtils.CHECKING_RECEIVE);
        GoodsStockReceiveOrder stockReceiveOrder;
        try {
            ClinicConfig clinicConfig = cisClinicService.getClinicConfig(req.getHeaderClinicId());
            //收货单完成创建
            ReceiveOrderServiceBase opsBase = receiveOrderService.createOrUpdateGoodsReceiveOrder(clinicConfig, req);
            stockReceiveOrder = opsBase.getGoodsStockReceiveOrder();
            if (opsBase.getNeedReflushPurchaseOrder() && !stockReceiveOrder.isNeedApproval()) {
                GoodsUtils.runAfterTransaction(()->{ receiveOrderService.anotherAsynTransToReflushPurchaseOrder(stockReceiveOrder.getChainId(), stockReceiveOrder.getPurchaseOrderId(), req.getHeaderOperatorId()); });
            }
        } catch (Exception exp) {
            goodsRedisUtils.orderDupOptFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_RECEIVE_ORDER_CHECKING + req.getHeaderClinicId(), reqMd5Key, true);
            throw exp;
        }

        goodsRedisUtils.orderDupOptFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_RECEIVE_ORDER_CHECKING + req.getHeaderClinicId(),
                reqMd5Key,
                false);
        return receiveOrderService.toView(stockReceiveOrder);
    }


    /*
     * 更新收货单
     *
     * @param req 更新参数
     * @return 返回视图
     */
    @Transactional(rollbackFor = Exception.class)
    public ReceiveOrderRsp updateReceiveOrder(ReceiveOrderReq req) {
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(org.apache.commons.lang3.StringUtils.defaultIfBlank(req.getClinicId(), req.getHeaderClinicId()));
        ReceiveOrderServiceBase opsBase = receiveOrderService.createOrUpdateGoodsReceiveOrder(clinicConfig, req);
        GoodsStockReceiveOrder stockReceiveOrder = opsBase.getGoodsStockReceiveOrder();
        if (opsBase.getNeedReflushPurchaseOrder() && !stockReceiveOrder.isNeedApproval()) {
            GoodsUtils.runAfterTransaction(() -> receiveOrderService.anotherAsynTransToReflushPurchaseOrder(stockReceiveOrder.getChainId(), stockReceiveOrder.getPurchaseOrderId(), req.getHeaderOperatorId()));
        }
        return receiveOrderService.toView(stockReceiveOrder);
    }

    /**
     * 确认收货
     */
    @Transactional(rollbackFor = Exception.class)
    public ReceiveOrderRsp receiveOrderConfirmReceive(ReceiveOrderReq req) {
        String reqMd5Key = req.getReqMd5Key();
        goodsRedisUtils.orderDupOptFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_RECEIVE_ORDER_CHECKING + req.getHeaderClinicId(), reqMd5Key, GoodsRedisUtils.CHECKING_RECEIVE);

        GoodsStockReceiveOrder stockReceiveOrder;
        try {
            ClinicConfig clinicConfig = cisClinicService.getClinicConfig(req.getClinicId());
            ReceiveOrderServiceBase opsBase = receiveOrderService.createOrUpdateGoodsReceiveOrder(clinicConfig, req);
            stockReceiveOrder = opsBase.getGoodsStockReceiveOrder();
            if (opsBase.getNeedReflushPurchaseOrder() && !stockReceiveOrder.isNeedApproval()) {
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "anotherAsyncTransToReflushPurchaseOrder, receiveOrderId = {}", stockReceiveOrder.getId());
                GoodsUtils.runAfterTransaction(() -> receiveOrderService.anotherAsynTransToReflushPurchaseOrder(stockReceiveOrder.getChainId(), stockReceiveOrder.getPurchaseOrderId(), req.getHeaderOperatorId()));
            }
        } catch (Exception exp) {
            goodsRedisUtils.orderDupOptFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_RECEIVE_ORDER_CHECKING + req.getHeaderClinicId(), reqMd5Key, true);
            throw exp;
        }

        goodsRedisUtils.orderDupOptFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_RECEIVE_ORDER_CHECKING + req.getHeaderClinicId(),
                reqMd5Key,
                false);

        return receiveOrderService.toView(stockReceiveOrder);
    }

    /*
     * 收货单快速入库
     */
    @Transactional(rollbackFor = Exception.class)
    public ReceiveOrderRsp receiveOrderQuickInStock(ReceiveOrderReq req) {
        String reqMd5Key = req.getReqMd5Key();
        goodsRedisUtils.orderDupOptFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_RECEIVE_ORDER_CHECKING + req.getHeaderClinicId(), reqMd5Key, GoodsRedisUtils.CHECKING_RECEIVE);

        GoodsStockReceiveOrder stockReceiveOrder;
        try {
            ClinicConfig clinicConfig = cisClinicService.getClinicConfig(req.getClinicId());
            ReceiveOrderServiceBase opsBase = receiveOrderService.createOrUpdateGoodsReceiveOrder(clinicConfig, req);
            stockReceiveOrder = opsBase.getGoodsStockReceiveOrder();
            if (opsBase.getNeedReflushPurchaseOrder()) {
                GoodsUtils.runAfterTransaction(() -> receiveOrderService.anotherAsynTransToReflushPurchaseOrder(stockReceiveOrder.getChainId(), stockReceiveOrder.getPurchaseOrderId(), req.getHeaderOperatorId()));
            }
        } catch (Exception exp) {
            goodsRedisUtils.orderDupOptFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_RECEIVE_ORDER_CHECKING + req.getHeaderClinicId(), reqMd5Key, true);
            throw exp;
        }

        goodsRedisUtils.orderDupOptFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_RECEIVE_ORDER_CHECKING + req.getHeaderClinicId(),
                reqMd5Key,
                false);

        return receiveOrderService.toView(stockReceiveOrder);
    }

    /*
     * 供应商创建收货单
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GoodsReceiveOrderRsp createGoodsReceiveOrderFromErpPushBack(GoodsReceiveOrderReq req) {
        return receiveOrderService.createGoodsReceiveOrderFromErpPushBack(goodsJenkinsRpcService,req);
    }


    /*
     * 删除收货单
     *
     * @param chainId  连锁Id
     * @param clinicId 门店ID
     * @param orderId  订单ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteReceiveOrder(String chainId,
                                   String clinicId,
                                   String operatorId,
                                   Long orderId) {
        receiveOrderService.deleteReceiveOrder(chainId,
                clinicId,
                orderId,
                operatorId);
    }

    /*
     * 创建配货单
     *
     * @param req 请求参数
     * @return 返回视图
     */
    public DeliveryOrderRsp createDelivery(DeliveryOrderReq req) {
        // TODO ...大单子的并发提交
        try {
            DeliveryOrderServiceBase opsBase = deliveryOrderService.createOrUpdateDeliveryOrder(req);
            deliveryOrderService.asyncTrans(opsBase);
            GoodsStockDeliveryOrder goodsStockDeliveryOrder = opsBase.getGoodsStockDeliveryOrder();
            if (opsBase.getNeedFlushPurchase()) {
                GoodsUtils.runAfterTransaction(() -> opsBase.anotherAsynTransToReflushPurchaseOrder(goodsStockDeliveryOrder.getChainId(), goodsStockDeliveryOrder.getClaimOrderId(), req.getHeaderOperatorId()));
            }
            return DeliveryOrderService.toDeliveryOrderRsp(goodsStockDeliveryOrder);
        } catch (Exception exp) {
            sLogger.error("sendOrRecvOrWithDrawDeliveryOrder exp:{}", exp.toString());
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, exp);
        }
    }

    /*
     * 查询列表
     *
     * @param req 查询参数
     * @return 返回查询结果
     */

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ReceiveOrderListPageRsp listReceiveOrder(ReceiveOrderQueryReq req) {
        return receiveOrderService.pageReceiveOrder(req);
    }

    /*
     * 获取收货单详情
     *
     * @param chainId  连锁Id
     * @param clinicId 门店ID
     * @param orderId  订单ID
     * @return 返回视图
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ReceiveOrderRsp getReceiveOrder(String chainId, String clinicId, Long orderId) {
        return receiveOrderService.findReceiveOrderViewById(chainId, clinicId, orderId);
    }


    /*
     * 查询采购或要货订单详情
     */
    @Transactional(readOnly = true)
    public GoodsPurchaseOrderRsp getPurchaseOrder(String chainId, Long goodsPurchaseOrderId) {
        return goodsPurchaseOrderService.getPurchaseOrder(chainId, goodsPurchaseOrderId);
    }


    @Transactional(rollbackFor = Exception.class)
    public DeliveryOrderRsp sendOrRecvOrWithDrawDeliveryOrder(DeliveryOrderReviewReq req) {
        // TODO ...大单子的并发提交
        try {
            // 只能由总部 发起
            if (GoodsPrivUtils.isSubClinic(req.getHeaderClinicType(), req.getHeaderViewMode())) { //子店
                sLogger.info("sendOrRecvOrWithDrawDeliveryOrder 失败=只允许总店发货");
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "只允许总店发货");
            }
            DeliveryOrderServiceBase opsBase = deliveryOrderService.sendOrRecvOrWithDrawDeliveryOrder(req);
            deliveryOrderService.asyncTrans(opsBase);
            GoodsStockDeliveryOrder goodsStockDeliveryOrder = opsBase.getGoodsStockDeliveryOrder();
            //GoodsUtils.runAfterTransaction(() -> opsBase.anotherAsynTransToReflushPurchaseOrder(goodsStockDeliveryOrder.getChainId(), goodsStockDeliveryOrder.getClaimOrderId(), req.getHeaderEmployeeId()));
            return DeliveryOrderService.toDeliveryOrderRsp(goodsStockDeliveryOrder);
        } catch (Exception exp) {
            sLogger.error("sendOrRecvOrWithDrawDeliveryOrder exp:{}", exp.toString());
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, exp);
        }
    }

    /*
     * 删除配货单
     *
     * @param chainId  连锁Id
     * @param clinicId 门店ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteDeliveryOrder(String chainId,
                                    String clinicId,
                                    Long orderId,
                                    String operatorId
    ) {
        deliveryOrderService.deleteDeliveryOrder(chainId, clinicId, orderId, operatorId);
    }

    /*
     * 查询列表
     *
     * @param req 查询参数
     * @return 返回查询结果
     */

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public DeliveryOrderListPageRsp listDeliveryOrder(DeliveryOrderQueryReq req) {
        return deliveryOrderService.pageDeliveryOrder(req);
    }

    /*
     * 获取配货单详情
     *
     * @param chainId  连锁Id
     * @param clinicId 门店ID
     * @param orderId  订单ID
     * @return 返回视图
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public DeliveryOrderRsp getDeliveryOrderById(String chainId, String clinicId, Long orderId) {
        return deliveryOrderService.findDeliveryOrderViewById(chainId, clinicId, orderId);
    }


    /*
     * 用户修改验货单
     *
     * @param req 修改参数
     * @return 返回视图
     */
    @Transactional(rollbackFor = Exception.class)
    public InspectOrderRsp updateInspectOrder(InspectOrderReq req) {
        Pair<GoodsStockInspectOrder, InspectOrderRsp> rspPair = inspectOrderService.updateInspectOrder(req);
        return rspPair.getSecond();
    }

    /*
     * 确认验收入库
     */
    @Transactional(rollbackFor = Exception.class)
    public InspectOrderRsp inspectOrderConfirmInspect(InspectOrderReq req) {
        String reqMd5Key = req.getReqMd5Key();
        goodsRedisUtils.orderDupOptFlagCheckAndSet(RedisUtils.SCGOODS_REDISCACHEKEY_INSPECT_ORDER_CHECKING + req.getHeaderClinicId(), reqMd5Key, GoodsRedisUtils.CHECKING_INSPECT);
        InspectOrderRsp inspectOrderRsp;
        try {
            inspectOrderRsp = updateInspectOrder(req);
        } catch (Exception exp) {
            goodsRedisUtils.orderDupOptFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_INSPECT_ORDER_CHECKING + req.getHeaderClinicId(), reqMd5Key, true);
            throw exp;
        }

        goodsRedisUtils.orderDupOptFlagClearOrFinished(RedisUtils.SCGOODS_REDISCACHEKEY_INSPECT_ORDER_CHECKING + req.getHeaderClinicId(),
                reqMd5Key,
                false);
        return inspectOrderRsp;
    }

    /*
     * GSP 记录查询
     *
     * @param req 查询内容
     * @return
     */

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<GoodsInspectOrderRsp> queryGoodsInspectOrderForGsp(GoodsInspectOrderQueryReq req) {
        return inspectOrderService.queryGoodsStockInspectItem(receiveOrderService,req);
    }

    /*
     * 查询验货单
     *
     * @param inspectOrderQueryReq 查询
     * @return 返回视图
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public InspectOrderListPageRsp listInspectOrder(InspectOrderQueryReq inspectOrderQueryReq) {
        return inspectOrderService.pageInspectOrder(inspectOrderQueryReq);
    }

    /*
     * 查询详情
     *
     * @param chainId  连锁ID
     * @param clinicId 门店ID
     * @param orderId  ID
     * @return 返回视图
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public InspectOrderRsp getInspectOrder(String chainId,
                                           String clinicId,
                                           Long orderId) {
        return inspectOrderService.findInspectOrderViewById(chainId, clinicId, orderId);
    }

    public GoodsRecommendRsp pharmacySearchGoods(PharmacyRecommendReq req) {
        return searchGoodsService.pharmacySearchGoods(suggestConfiguration.getClientPharmacyGoodsRecommend(), req);
    }

    /*
     * 通过商品ID查询进行中的采购或要货
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<ClaimOrPurchaseOrderGoodsListView> listClaimOrPurchaseOrderByGoods(String headerChainId,
                                                                                   String headerClinicId,
                                                                                   int headerClinicType,
                                                                                   int headerHisType,
                                                                                   int headerViewMode,
                                                                                   String goodsId) {
        return goodsPurchaseOrderService.listClaimOrPurchaseOrderByGoods(headerChainId, headerClinicId, headerClinicType, headerHisType, headerViewMode, goodsId);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsRecommendRsp searchGoodsRecommend(GoodsSearchRecommendReq req) {
        return searchGoodsService.searchGoodsRecommend(req);
    }


    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp bindGoodsSupplierRelation(GoodsBindSupplierReq clientReq) {
        receiveOrderService.bindGoodsSupplierRelation(clientReq);
        return new OpsCommonRsp();
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void downloadReceiveOrderImportTemplate(ReceiveOrderImportTemplateDownloadReq clientReq) throws Exception {
        receiveOrderService.downloadReceiveOrderImportTemplate(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ImportStockInReceiveRsp importGoodsReceiveForStockInReceive(ImportStockInReceiveReq req) {
        return receiveOrderService.importGoodsReceiveForStockInReceive(req);
    }

    @Transactional(rollbackFor = Exception.class)
    public GoodsStockReceiveOrder getReceiveOrderForRpc(String chainId, Long receiveOrderId) {
        GoodsStockReceiveOrder receiveOrder = receiveOrderService.findReceiveOrderById(chainId, receiveOrderId);
        if (receiveOrder == null) {
            throw new NotFoundException("收货单不存在");
        }
        return receiveOrder;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateReceiveOrderTransportRecord(Long receiveOrderId, UpdateReceiveOrderTransportRecordReq req) {
        receiveOrderService.updateReceiveOrderTransportRecord(receiveOrderId, req);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<PharmacyGoodsStockBatchInfo> getPharmacyGoodsStockBatchList(String chainId, String clinicId) {
        return goodsStockService.getPharmacyGoodsStockBatchList(chainId, clinicId);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PharmacyGoodsBatchesForGoodsInfoRsp getPharmacyGoodsStockBatches(PharmacyGoodsStockBatchesReq clientReq) {
        AbcListPage<PharmacyGoodsBatchesForGoodsInfoRsp> result = goodsStockService.batchPharmacyGoodsStockBatches(clientReq);
        if (!result.getRows().isEmpty()) {
            return result.getRows().get(0);
        }
        return new PharmacyGoodsBatchesForGoodsInfoRsp();
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp updateMedicineMaintenanceTime(UpdateMedicineMaintenanceTimeReq req) {
        goodsStockService.updateMedicineMaintenanceTime(req);
        return new OpsCommonRsp();
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<PharmacyGoodsBatchesForGoodsInfoRsp> batchPharmacyGoodsStockBatches(PharmacyGoodsStockBatchesReq clientReq) {
        return goodsStockService.batchPharmacyGoodsStockBatches(clientReq);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public GoodsItem queryExistGoodsByName(String chainId, String name, int type, int subType) {
        return goodsListService.queryExistGoodsByName(chainId, name, type, subType);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GetGoodsInOrderOfImportRsp getInOrdersListForStockOutOfImport(GetStockInOrderListReq clientReq) {
        return goodsStockInOrderService.getInOrdersListForStockOutOfImport(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ImportGoodsSearchRsp searchImportGoods(ImportGoodsSearchReq req) {
        return goodsStockInOrderService.searchImportGoods(req);
    }

    public OpsCommonRsp createImportStockInOrderReturnOut(CreateImportStockInOrderReturnOutReq clientReq) {
        GoodsStockRowErrorDetail errorDetail = new GoodsStockRowErrorDetail();
        errorDetail.setErrorTitle("退货出库参数错误");
        clientReq.reRowIndex();
        List<GoodsStockInOrder> stockInOrderList = goodsStockInOrderService.getStockInOrderList(clientReq.getHeaderChainId(), clientReq.getStockInOrderIdList());
        Map<Long, GoodsStockInOrder> orderIdToGoodsStockInOrder = stockInOrderList.stream().collect(Collectors.toMap(GoodsStockInOrder::getId, Function.identity()));
        clientReq.parameterCheck(errorDetail, stockInOrderList);
        // 按照入库单ID、供应商ID分组
        Map<Long, Map<String, List<CreateImportStockInOrderReturnOutItemReq>>> orderIdToSupplierIdToItemReqList = clientReq.getList().stream().collect(Collectors.groupingBy(CreateImportStockInOrderReturnOutItemReq::getReturnInOrderId, Collectors.groupingBy(CreateImportStockInOrderReturnOutItemReq::getSupplierId)));
        for (Map.Entry<Long, Map<String, List<CreateImportStockInOrderReturnOutItemReq>>> entry : orderIdToSupplierIdToItemReqList.entrySet()) {
            Long stockInOrderId = entry.getKey();
            Map<String, List<CreateImportStockInOrderReturnOutItemReq>> supplierIdToItemReqList = entry.getValue();
            for (Map.Entry<String, List<CreateImportStockInOrderReturnOutItemReq>> supplierIdToItemReq : supplierIdToItemReqList.entrySet()) {
                String supplierId = supplierIdToItemReq.getKey();
                List<CreateImportStockInOrderReturnOutItemReq> itemReqList = supplierIdToItemReq.getValue();
                CreateStockInOrderReq inOrderReq = new CreateStockInOrderReq();
                inOrderReq.setList(new ArrayList<>());
                inOrderReq.setHeaderChainId(clientReq.getHeaderChainId());
                inOrderReq.setHeaderClinicId(clientReq.getHeaderClinicId());
                inOrderReq.setHeaderEmployeeId(clientReq.getHeaderEmployeeId());
                inOrderReq.setHeaderClinicType(clientReq.getHeaderClinicType());
                inOrderReq.setHeaderViewMode(clientReq.getHeaderViewMode());
                inOrderReq.setInOrderToClinicId(clientReq.getHeaderClinicId());
                inOrderReq.setReturnInOrderId(stockInOrderId);
                GoodsStockInOrder inOrder = orderIdToGoodsStockInOrder.get(stockInOrderId);
                if (inOrder != null) {
                    inOrderReq.setPharmacyNo(inOrder.getPharmacyNo()); //TODO ....
                } else {
                    inOrderReq.setPharmacyNo(GoodsUtils.PHARMACY_NO_0);
                }
                inOrderReq.setComment(clientReq.getComment());
                inOrderReq.setSupplierId(supplierId);
                if (Objects.equals(clientReq.getType(), StockInOrderType.RETURN_OUT_APPLY)) {
                    // 采购退货申请
                    inOrderReq.setType(StockInOrderType.RETURN_OUT_APPLY);
                } else {
                    inOrderReq.setType(StockInOrderType.RETURN_OUT);
                }
                inOrderReq.setNotCheckParam(true);
                for (CreateImportStockInOrderReturnOutItemReq outItemReq : itemReqList) {
                    CreateStockInItemViewReq stockInItemViewReq = new CreateStockInItemViewReq();
                    stockInItemViewReq.setGoodsId(outItemReq.getGoodsId());
                    stockInItemViewReq.setPieceCount(outItemReq.getPieceCount());
                    stockInItemViewReq.setPackageCount(outItemReq.getPackageCount());
                    stockInItemViewReq.setStockInId(outItemReq.getStockInId());
                    inOrderReq.getList().add(stockInItemViewReq);
                }
                // 生成退货出库单
                createOrUpdateInProgressStockInOrder(inOrderReq);
            }
        }

        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsSimpleInfo> queryGoodsByShortIds(QueryGoodsByShortIdsReq req) {
        return goodsListV2Service.queryGoodsByShortIds(req);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsModifySubClinicsPricesPreCheckRsp> preCheckGoodsSubClinicInfoList(GoodsModifySubClinicsPricesReq clientReq) {
        return goodsPriceService.preCheckGoodsSubClinicInfoList(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<SupplierGoodsMappingView> getSupplierGoodsMapping(String chainId, String clinicId,
                                                                         String goodsId, Integer offset, Integer limit) {
        return bisGoodsSupplierService.getSupplierGoodsMapping(chainId, clinicId, goodsId, offset, limit);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<SupplierGoodsItem> searchSupplierGoods(String chainId, String clinicId, String supplierId,
                                                              String keyword, Integer offset, Integer limit) {
        return bisGoodsSupplierService.searchSupplierGoods(chainId, clinicId, supplierId, keyword, offset, limit);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void bindSupplierGoods(BindSupplierGoodsMappingReq clientReq) {
        bisGoodsSupplierService.bindSupplierGoods(clientReq);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public void exportGoodsStockOfNotCheck(ExportGoodsStockOfNotCheckReq clientReq) throws IOException {
        goodsStockCheckOrderService.exportGoodsStockOfNotCheck(clientReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp recoverGoodsArchive(RecoverGoodsArchiveReq clientReq) {
        goodsInfoModifyManageService.recoverGoodsArchive(clientReq);
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "success");
    }

    /*
     * 查码的信息
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public Tuple3<GetTraceableCodeListRsp, Map<String, AliTraceCodeQueryResult>, Map<String, GoodsTraceCodeHelper>> queryTraceableCodeList(ClinicConfig clinicConfig, GetTraceableCodeListReq req) throws CisGoodsServiceException {
        return goodsTraceCodeService.queryTraceableCodeList(clinicConfig, req);
    }

    /*
     * 从阿里查码的信息
     */
    public void aliCodeStoreToDb(ClinicConfig clinicConfig, Map<String, AliTraceCodeQueryResult> noToAliTraceCode) throws CisGoodsServiceException {
        goodsTraceCodeService.storeAliCodeToDbWithTransaction(clinicConfig, noToAliTraceCode, null, null, null);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public GetTraceableCodeListForBatchSummeryRsp queryTraceableCodeListForBatchSummery(GetTraceableCodeListForBatchSummeryReq req) throws CisGoodsServiceException {
        return goodsTraceCodeService.queryTraceableCodeListForBatchSummery(req);
    }

    /*
     * 给药品资料绑定追溯码
     *
     * @param chainId 连锁ID 子店也可以初始化绑定
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp bindTraceableCode(String chainId, String clinicId, String employeeId, BatchUpdateDrugIdentificationCodeReq req) {
        return goodsTraceCodeService.bindTraceableCode(chainId, clinicId, employeeId, req);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public GetGoodsClinicPriceRsp getGoodsClinicStockPrice(GetGoodsClinicPriceReq clientReq) {
        List<Goods> goodsList = goodsRepository.findAllByIdInAndOrganIdAndStatusLessThan(Collections.singletonList(clientReq.getGoodsId()), clientReq.getChainId(), GoodsConst.GoodsStatus.DELETE);
        if (CollectionUtils.isEmpty(goodsList)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "商品不存在");
        }
        Goods goods = goodsList.get(0);
        clientReq.setGoods(goods);
        if (clientReq.getQueryType() == GetGoodsClinicPriceReq.QueryType.STOCK) {
            return goodsStockService.getGoodsClinicStock(clientReq);
        } else {
            return goodsPriceService.getGoodsClinicPrice(clientReq);
        }
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public GoodsListPage<PromotionTypeView> getSystemTypesForMall(String chainId, Integer queryType) {
        return goodsSysTypeService.getSystemTypesForMall(chainId, queryType);
    }

    public GoodsListPage<GoodsTagTypeView> getGoodsTagTypeList(String chainId) {
        List<GoodsTagTypeView> list = goodsCustomTypeRedisService.getGoodsTagTypeListFromRedis(chainId, null);
        GoodsListPage<GoodsTagTypeView> clientRsp = new GoodsListPage<>();
        clientRsp.setList(new ArrayList<>());
        Map<Long, GoodsTagTypeView> parentTagIdToTag = new HashMap<>();
        for (GoodsTagTypeView goodsTagTypeView : list) {
            goodsTagTypeView.setPy(PinyinUtils.genSearchText(goodsTagTypeView.getName()));
            if (goodsTagTypeView.getParentId() != null) {
                continue;
            }
            clientRsp.getList().add(goodsTagTypeView);
            goodsTagTypeView.setChildren(new ArrayList<>());
            parentTagIdToTag.put(goodsTagTypeView.getId(), goodsTagTypeView);
        }
        for (GoodsTagTypeView goodsTagTypeView : list) {
            if (goodsTagTypeView.getParentId() == null) {
                continue;
            }
            if (parentTagIdToTag.containsKey(goodsTagTypeView.getParentId())) {
                parentTagIdToTag.get(goodsTagTypeView.getParentId()).getChildren().add(goodsTagTypeView);
            }
        }
        return clientRsp;
    }

    public GoodsListPage<GoodsTagTypeView> updateGoodsTagTypeList(String headerChainId, String headerEmployeeId, GoodsListPage<GoodsTagTypeView> clientReq) {

        /*
         * 参数检查和准备
         * */
        if (CollectionUtils.isEmpty(clientReq.getList())) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "药品标签列表不能为空");
        }
        if (clientReq.getList().stream().anyMatch(it -> it.getOpType() == null)) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "药品标签列表操作类型不能为空");
        }
        int sort = 1;
        Set<String> parentNameSet = new HashSet<>();
        for (GoodsTagTypeView parent : clientReq.getList()) {
            if (parent.getOpType() != CreateOrUpdateGoodsFeeTypeReq.OpType.ADD && parent.getId() == null) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "药品标签id不能为空");
            }
            if (parent.getOpType() == CreateOrUpdateGoodsFeeTypeReq.OpType.DELETE) {
                continue;
            }
            if (StringUtils.isEmpty(parent.getName())) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "药品标签名称不能为空");
            }
            if (parentNameSet.contains(parent.getName())) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "标签名称:" + parent.getName() + "重复");
            }
            parentNameSet.add(parent.getName());
            if (parent.getOpType() == CreateOrUpdateGoodsFeeTypeReq.OpType.ADD && parent.getId() == null) {
                parent.setId(AbcIdUtils.getUUIDLong());
            }
            parent.setParentId(null);
            parent.setSort(sort++);
//            if(CollectionUtils.isEmpty(parent.getChildren())){
//                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER,"药品标签:"+parent.getName()+"子标签列表不能为空");
//            }
            int innerSort = 0;
            for (GoodsTagTypeView child : parent.getChildren()) {
                if (parent.getOpType() != CreateOrUpdateGoodsFeeTypeReq.OpType.ADD && parent.getId() == null) {
                    throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "药品标签id不能为空");
                }
                if (child.getOpType() == CreateOrUpdateGoodsFeeTypeReq.OpType.DELETE) {
                    continue;
                }
                if (StringUtils.isEmpty(child.getName())) {
                    throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "药品标签:" + parent.getName() + "子标签存在名字为空");
                }
                if (parentNameSet.contains(child.getName())) {
                    throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "标签名称:" + child.getName() + "重复");
                }
                parentNameSet.add(child.getName());
                if (child.getOpType() == CreateOrUpdateGoodsFeeTypeReq.OpType.ADD && child.getId() == null) {
                    child.setId(AbcIdUtils.getUUIDLong());
                }
                child.setSort(sort * 10000 + innerSort++);
                child.setParentId(parent.getId());
            }
            if (innerSort > 50) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "药品标签:" + parent.getName() + "不能超过20个");
            }
        }
        if (sort > 20) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "一级药品标签不能超过20个");
        }
        goodsCustomTypeRedisService.updateGoodsTagTypeList(headerChainId,
                headerEmployeeId,
                goodsRedisUtils,
                rocketMqProducer,
                clientReq.getList());
        goodsCustomTypeRedisService.cleanChainGoodsTagTypeRedisCache(headerChainId);
        return getGoodsTagTypeList(headerChainId);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<GoodsConfigRsp> batchGetGoodsConfig(BatchGetGoodsConfigReq req) {
        if (CollectionUtils.isEmpty(req.getClinicIds())) {
            return new AbcListPage<>();
        }
        AbcListPage<GoodsConfigRsp> result = new AbcListPage<>();
        result.setRows(new ArrayList<>());
        req.getClinicIds().forEach(clinicId -> result.getRows().add(getChainGoodsConfig(clinicId, null)));
        return result;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<BatchQueryGoodsClinicPriceRsp> batchQueryGoodsClinicPrice(BatchQueryGoodsClinicPriceReq req) {
        AbcListPage<BatchQueryGoodsClinicPriceRsp> result = new AbcListPage<>();
        List<BatchQueryGoodsClinicPriceRsp> stockPriceRsp = goodsStockService.batchQueryGoodsClinicStock(req);
        List<BatchQueryGoodsClinicPriceRsp> nonStockPriceRsp = goodsPriceService.batchQueryGoodsClinicPrice(req);
        result.setRows(new ArrayList<>());
        result.getRows().addAll(stockPriceRsp);
        result.getRows().addAll(nonStockPriceRsp);
        return result;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<QueryGoodsSubClinicInfoRsp> queryGoodsSubClinicInfoList(QueryGoodsSubClinicInfoReq clientReq) {
        return queryCisGoodsListService.queryGoodsSubClinicInfoList(clientReq);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public QueryGoodsSubClinicInfoRsp queryGoodsSubClinicInfoListForMall(QueryGoodsSubClinicInfoReq clientReq) {
        AbcListPage<QueryGoodsSubClinicInfoRsp> result = queryCisGoodsListService.queryGoodsSubClinicInfoList(clientReq);
        if (result != null && !CollectionUtils.isEmpty(result.getRows())) {
            return result.getRows().get(0);
        }
        return null;
    }


    /***
     * 计算追溯码的应采，以及每个码分配的数量
     * */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public CalculateTraceableCodeCountRsp calculateTraceableCodeNum(String employeeId, String clinicId, CalculateTraceableCodeCountReq req) {
        req.setClinicId(clinicId);
        req.setEnableTraceableCodeRule(1);
        req.setEnableStockInTraceableCodeHisCount(1);
        req.setEnableTraceableCodeDismantlingRequiresCollection(1);
        req.setEnableZjhzMixedSplitForce(1);
        return goodsTraceCodeService.calculateTraceableCodeNum(employeeId, clinicId, req);
    }


    /*
     * Switch Coop 商品
     *
     * @param chainId    连锁id
     * @param clinicId   诊所 ID
     * @param employeeId 员工 ID
     * @param req        要求
     * @return {@link OpsCommonRsp }
     */
    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp updateCoopGoods(String chainId, String clinicId, String employeeId, SwitchCoopGoodsReq req) {
        return goodsInfoModifyManageService.updateCoopGoods(chainId, clinicId, employeeId, req);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp deleteCoopGoods(String chainId, String clinicId, String employeeId, String goodsId) {
        return goodsInfoModifyManageService.deleteCoopGoods(chainId, clinicId, employeeId, goodsId);
    }

    @Transactional(rollbackFor = Exception.class)
    public OpsCommonRsp joinCoopGoods(String chainId, String clinicId, String employeeId, JoinCoopGoodsReq req) {
        return goodsInfoModifyManageService.joinCoopGoods(chainId, clinicId, employeeId, req);
    }

    @Transactional(readOnly = true)
    public AbcListPage<CoopGoodsListView> findCoopGoodsList(String chainId, String clinicId, QueryCoopGoodsReq queryCoopGoodsReq) {
        return goodsListService.findCoopGoodsList(chainId, clinicId, queryCoopGoodsReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public GoodsPharmacyView updateCoopPharmacy(String chainId, String clinicId, String employeeId, Long id, UpdateCoopPharmacyReq updateCoopPharmacyReq) {
        return goodsPharmacyService.updateCoopPharmacy(chainId, clinicId, employeeId, id, updateCoopPharmacyReq);
    }

    @Transactional(readOnly = true)
    public AbcListPage<CoopPharmacyView> getCoopPharmacies(String chainId, String clinicId, int pharmacyType) {
        return goodsPharmacyService.getCoopPharmacies(chainId, clinicId, pharmacyType);
    }


    @Transactional(readOnly = true)
    public AbcListPage<GoodsPharmacyView> getCoopCenterPharmacies(String chainId, String clinicId, String employeeId, int pharmacyType) {
        return goodsPharmacyService.getCoopCenterPharmacies(chainId, clinicId, employeeId, pharmacyType);
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<GoodsItem> getExamAssayDeviceAssociationGoodsList(String chainId, String clinicId, int goodsType, int goodsSubType) {
        return examService.getExamAssayDeviceAssociationGoodsList(chainId, clinicId, goodsType, goodsSubType);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<GoodsStockTraceableCodeBatLogView> getTraceableCodeBatLogs(String clinicId, String goodsId, int pharmacyNo, int action, int offset, int limit) {
        return goodsTraceCodeService.getTraceableCodeBatLogs(clinicId, goodsId, pharmacyNo, action, offset, limit);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<GoodsStockTraceableCodeView> getTraceableCodeAvailableLogs(String clinicId, String goodsId, int offset, int limit) {
        return goodsTraceCodeService.getTraceableCodeAvailableLogs(clinicId, goodsId, offset, limit);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public GoodsStockOutReasonTemplateRsp getStockOutReasonTemplateList(String chainId, int type) {
        return goodsStockOutReasonTemplateService.getStockOutReasonTemplateList(chainId, type);
    }

    @Transactional(rollbackFor = Exception.class)
    public GoodsStockOutReasonTemplateRsp updateStockOutReasonTemplate(UpdateGoodsStockOutReasonTemplateReq clientReq) {
        // 连锁维度,加个锁,只能有一个人修改
        String redisKey = goodsRedisUtils.getUpdateOutReasonTemplateKey(clientReq.getChainId(), clientReq.getType());
        Object redisValue = redisUtils.get(redisKey);
        if (redisValue != null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "正在更新模板，请稍后再试。");
        }
        redisUtils.set(redisKey, AbcIdUtils.getUUID(), 10);
        try {
            return goodsStockOutReasonTemplateService.updateStockOutReasonTemplate(clientReq);
        } finally {
            redisUtils.del(redisKey);
        }
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public GoodsStatRsp getGoodsStatListByGoodsIdList(GoodsStatReq statReq) {
        return goodsStatService.getGoodsStatListByGoodsIdList(statReq);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<GoodsStockOrderItemDiffView> getGoodsStockInOrderFixedOrders(String chainId, String clinicId, Long inOrderId, Integer offset, Integer limit) {
        return goodsStockInOrderService.getGoodsStockInOrderFixedOrders(chainId, clinicId, inOrderId, offset, limit);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<GoodsStockOrderItemDiffView> getStockCheckOrderFixedOrders(String chainId, String clinicId, Long inOrderId, Integer offset, Integer limit) {
        return goodsStockCheckOrderService.getGoodsStockCheckOrderFixedOrders(chainId, clinicId, inOrderId, offset, limit);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<GoodsStockOrderItemDiffView> getStockTransOrderFixedOrders(String chainId, String clinicId, Long inOrderId, Integer offset, Integer limit) {
        return goodsStockTransOrderService.getStockTransOrderFixedOrders(chainId, clinicId, inOrderId, offset, limit);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<GoodsStockOrderItemDiffView> getStockOutOrderFixedOrders(String chainId, String clinicId, Long inOrderId, Integer offset, Integer limit) {
        return goodsStockOutService.getStockOutOrderFixedOrders(chainId, clinicId, inOrderId, offset, limit);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<GoodsStockOrderItemDiffView> getStockReceptionOrderFixedOrders(String chainId, String clinicId, Long inOrderId, Integer offset, Integer limit) {
        return goodsStockReceptionOrderService.getStockReceptionOrderFixedOrders(chainId, clinicId, inOrderId, offset, limit);
    }

    //@Transactional(readOnly = true)
    //@UseReadOnlyDB
    public SpecificationTransformRsp calculateSpecificationTransformPrice(SpecificationTransformReq clientReq) {
        SpecificationTransformRsp transformRsp = new SpecificationTransformRsp();
        if (clientReq.getHisSpec() == null || clientReq.getShebaoSpec() == null) {
            return transformRsp;
        }
        if (clientReq.getHisSpec().getPieceNum() == null || clientReq.getShebaoSpec().getPieceNum() == null) {
            return transformRsp;
        }
        if (MathUtils.wrapBigDecimalCompare(clientReq.getHisSpec().getPieceNum(), BigDecimal.ZERO) <= 0
                || MathUtils.wrapBigDecimalCompare(clientReq.getShebaoSpec().getPieceNum(), BigDecimal.ZERO) <= 0) {
            return transformRsp;
        }
        if (clientReq.getShebaoLimitPrice() == null && clientReq.getShebaoListingPrice() == null) {
            return transformRsp;
        }
        transformRsp.setReq(clientReq);
        if (clientReq.getShebaoLimitPrice() != null) {
            SpecificationTransformResponse limitPriceTransResponse;
            if (clientReq.shebaoLimitPricePackageUnit()) {
                limitPriceTransResponse = SpecificationAnalyzer.getMedicalCareSpecificationTransform(
                        // 源规格组 --  shebao药品
                        clientReq.getHisSpec().getPieceNum().floatValue(), clientReq.getHisSpec().getPieceUnit(),
                        clientReq.getHisSpec().getPackageUnit(), null,
                        // 目标规格组 --  his药品
                        clientReq.getShebaoSpec().getPieceNum().floatValue(), clientReq.getShebaoSpec().getPieceUnit(),
                        clientReq.getShebaoSpec().getPackageUnit(), null,
                        // 源库存
                        1F, clientReq.getHisSpec().getPackageUnit(), "Mb");
            } else {
                limitPriceTransResponse = SpecificationAnalyzer.getMedicalCareSpecificationTransform(
                        // 源规格组 --  shebao药品
                        clientReq.getHisSpec().getPieceNum().floatValue(), clientReq.getHisSpec().getPieceUnit(),
                        clientReq.getHisSpec().getPackageUnit(), null,
                        // 目标规格组 --  his药品
                        clientReq.getShebaoSpec().getPieceNum().floatValue(), clientReq.getShebaoSpec().getPieceUnit(),
                        clientReq.getShebaoSpec().getPackageUnit(), null,
                        // 源库存
                        1F, clientReq.getHisSpec().getPieceUnit(), "Ms");
            }
            sLogger.info("limit calculateSpecificationTransformPrice{}", JsonUtils.dump(limitPriceTransResponse));
            if (limitPriceTransResponse != null && Boolean.TRUE.equals(limitPriceTransResponse.getIsTransformable())
                    && limitPriceTransResponse.getTransformCount() != null) {
                BigDecimal transCount = BigDecimal.valueOf(limitPriceTransResponse.getTransformCount());
                BigDecimal transPackagePrice;
                BigDecimal transPiecePrice;
                if (clientReq.shebaoLimitPricePackageUnit()) {
                    transPackagePrice = clientReq.getShebaoLimitPrice().multiply(transCount).setScale(4, RoundingMode.DOWN);
                    transPiecePrice = transPackagePrice.divide(clientReq.getHisSpec().getPieceNum(), 4, RoundingMode.DOWN);
                } else {
                    transPiecePrice = clientReq.getShebaoLimitPrice().multiply(transCount).divide(clientReq.getShebaoSpec().getPieceNum(), 4, RoundingMode.DOWN);
                    transPackagePrice = MathUtils.wrapBigDecimalMultiply(transPiecePrice, clientReq.getHisSpec().getPieceNum());
                }
                SpecificationTransformRsp.SpecificationTransformPrice shebaoLimitPrice = new SpecificationTransformRsp.SpecificationTransformPrice();
                shebaoLimitPrice.setPiecePrice(transPiecePrice);
                shebaoLimitPrice.setPackagePrice(transPackagePrice);
                transformRsp.setLimitPriceInfo(shebaoLimitPrice);
            }
        }
        if (clientReq.getShebaoListingPrice() != null) {
            SpecificationTransformResponse listingPriceTransResponse = SpecificationAnalyzer.getMedicalCareSpecificationTransform(
                    // 源规格组 -- his药品
                    clientReq.getHisSpec().getPieceNum().floatValue(), clientReq.getHisSpec().getPieceUnit(),
                    clientReq.getHisSpec().getPackageUnit(), null,
                    // 目标规格组 -- shebao药品
                    clientReq.getShebaoSpec().getPieceNum().floatValue(), clientReq.getShebaoSpec().getPieceUnit(),
                    clientReq.getShebaoSpec().getPackageUnit(), null,
                    // 源库存
                    1F, clientReq.getHisSpec().getPackageUnit(), "Mb");
            sLogger.info("listing calculateSpecificationTransformPrice packageUnit{}", JsonUtils.dump(listingPriceTransResponse));
            if (listingPriceTransResponse != null && Boolean.TRUE.equals(listingPriceTransResponse.getIsTransformable())
                    && listingPriceTransResponse.getTransformCount() != null) {
                BigDecimal transCount = BigDecimal.valueOf(listingPriceTransResponse.getTransformCount());
                BigDecimal transPackagePrice = clientReq.getShebaoListingPrice().multiply(transCount).setScale(4, RoundingMode.DOWN);
                SpecificationTransformRsp.SpecificationTransformPrice shebaoListingPrice = new SpecificationTransformRsp.SpecificationTransformPrice();
                shebaoListingPrice.setPackagePrice(transPackagePrice);
                transformRsp.setListingPriceInfo(shebaoListingPrice);
            }
            listingPriceTransResponse = SpecificationAnalyzer.getMedicalCareSpecificationTransform(
                    // 源规格组 -- his药品
                    clientReq.getHisSpec().getPieceNum().floatValue(), clientReq.getHisSpec().getPieceUnit(),
                    clientReq.getHisSpec().getPackageUnit(), null,
                    // 目标规格组 -- shebao药品
                    clientReq.getShebaoSpec().getPieceNum().floatValue(), clientReq.getShebaoSpec().getPieceUnit(),
                    clientReq.getShebaoSpec().getPackageUnit(), null,
                    // 源库存
                    1F, clientReq.getHisSpec().getPieceUnit(), "Ms");
            sLogger.info("listing calculateSpecificationTransformPrice pieceUnit{}", JsonUtils.dump(listingPriceTransResponse));
            if (listingPriceTransResponse != null && Boolean.TRUE.equals(listingPriceTransResponse.getIsTransformable())
                    && listingPriceTransResponse.getTransformCount() != null) {
                BigDecimal transCount = BigDecimal.valueOf(listingPriceTransResponse.getTransformCount());
                BigDecimal transPiecePrice =
                        clientReq.getShebaoListingPrice().multiply(transCount).divide(clientReq.getShebaoSpec().getPieceNum(), 4, RoundingMode.DOWN);
                if (transformRsp.getListingPriceInfo() == null) {
                    SpecificationTransformRsp.SpecificationTransformPrice shebaoListingPrice = new SpecificationTransformRsp.SpecificationTransformPrice();
                    transformRsp.setListingPriceInfo(shebaoListingPrice);
                }
                transformRsp.getListingPriceInfo().setPiecePrice(transPiecePrice);
            }
        }
        return transformRsp;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public void exportGoodsPurchaseOrder(GetGoodsPurchaseOrderReq clientReq) {
        goodsPurchaseOrderService.exportGoodsPurchaseOrder(clientReq);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public void exportGoodsReceiveOrder(GetReceiveOrderReq clientReq) {
        receiveOrderService.exportReceiveOrder(clientReq);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public void exportGoodsInspectOrder(GetInspectionOrderReq clientReq) {
        inspectOrderService.exportInspectOrder(clientReq);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public void exportGoodsReceiveOrderList(ReceiveOrderQueryReq req) {
        receiveOrderService.exportReceiveOrderList(req);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public void exportInspectOrderList(InspectOrderQueryReq req) {
        inspectOrderService.exportInspectOrderList(req);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public void exportPurchaseClaimOrderList(GetClaimOrPurchaseOrderReq clientReq) {
        sLogger.info("exportInOrdersList getReqMd5Key={}", clientReq.getReqMd5Key());
        goodsRedisUtils.orderIngFlagCheckAndSet(RedisUtils.SCGOODS_PURCHASE_CLAIM_ORDER_CHECKING + clientReq.getHeaderClinicId(), clientReq.getReqMd5Key(), GoodsRedisUtils.CHECKING_IN_EXPORT);
        try {
            goodsPurchaseOrderService.exportPurchaseOrderList(clientReq);
        } catch (Exception exp) {
            sLogger.info("exportInOrdersList getReqMd5Key={} deleted", clientReq.getReqMd5Key());
            throw exp;
        } finally {
            goodsRedisUtils.orderingFlagClearOrFinished(RedisUtils.SCGOODS_PURCHASE_CLAIM_ORDER_CHECKING + clientReq.getHeaderClinicId(), clientReq.getReqMd5Key(), false);
            sLogger.info("exportInOrdersList getReqMd5Key={} deleted", clientReq.getReqMd5Key());
        }
    }

    public AbcListPage<SearchCenterCodeRsp> searchCenterCode(SearchCenterCodeReq req) {
        return searchGoodsService.searchCenterCode(req);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<GoodsMultiPriceView> calculateMemberPrice(String chainId, String clinicId, List<CalculateGoodsMemberPriceReq.CalculateGoodsMemberTypePriceItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }

        return cisPromotionService.getMemberPriceListForCreate(chainId, clinicId, items);

    }

    public CalculateGoodsTypeMemberPriceRsp calculateGoodsTypeMemberPrice(String chainId, CalculateGoodsTypeMemberPriceReq req) {
        return cisPromotionService.getGoodsTypeMemberPriceList(chainId, req);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateFinishedReceiveOrderInfo(UpdateReceiveOrderInfoReq req) {
        receiveOrderService.updateFinishedOrderInfo(req);

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateFinishedReceiveOrderItem(UpdateFinishedReceiveOrderItemReq req) {
        receiveOrderService.updateFinishedOrderItem(req);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<GoodsStockInOrderItemView> getGoodsStockInOrderItems(GetStockInOrderItemReq clientReq) {
        return goodsStockInOrderService.getGoodsStockInOrderItems(clientReq);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public GoodsStockTraceableCodeView getTraceableCodeInfo(String chainId, String clinicId, String no, String goodsId, String employeeId) {
        return goodsTraceCodeService.getTraceableCodeInfoView(chainId, clinicId, no, goodsId, employeeId);
    }

    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'scGoods:updateTraceableCode:' + #req.no")
    public GoodsStockTraceableCodeView updateTraceableCodeNoInfo(UpdateTraceableCodeNoInfoReq req) {
        return goodsTraceCodeService.updateTraceableCodeNoInfo(req);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<GoodsStockTraceableCodeLogView> queryTraceableCodeLog(String chainId, String clinicId, String no, String goodId, int offset, int limit) {
        return goodsTraceCodeService.queryTraceableCodeLog(chainId, clinicId, no, goodId, offset, limit);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<GoodsStockTraceableCodeView> queryTraceableCodes(String clinicId, String goodsId, String no, int offset, int limit) {
        return goodsTraceCodeService.queryTraceableCodeViews(clinicId, goodsId, no, offset, limit);
    }

    /**
     * 删除已删除的goods的入库单
     *
     * @param req
     * @return
     */
    public OpsCommonRsp deleteInspectItem(DeleteStockInReq req) {
        if (req.getIds() == null || req.getIds().size() < 1) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "ids不能为空");
        }
        if (req.getOrderId() == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "orderId不能为空");
        }
        return inspectOrderService.deleteStockIn(req);
    }


    /**
     * 删除已删除的goods的入库单
     *
     * @param req
     * @return
     */
    public OpsCommonRsp deleteStockIn(DeleteStockInReq req) {
        if (req.getIds() == null || req.getIds().size() < 1) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "ids不能为空");
        }
        if (req.getOrderId() == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "orderId不能为空");
        }
        return goodsStockInOrderService.deleteStockIn(req);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public GoodsStockTraceCodeRsp queryTraceableCodesV2(String headerClinicId, String headerChainId, String goodsId, String no, int offset, int limit, Integer status, String clinicId) {
        return goodsTraceCodeService.queryTraceableCodeViewsV2(headerClinicId, headerChainId, goodsId, no, offset, limit, status, clinicId);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<SimpleStockInOrderRsp> getSimpleStockInOrderBatch(BatchGetSimpleStockInOrderReq req) {
        if (req.getIds() == null) {
            return new AbcListPage<>();
        }

        req.setIds(req.getIds().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(req.getIds())) {
            return new AbcListPage<>();
        }

        return goodsStockInOrderService.getSimpleStockInOrderBatch(req);
    }

    /**
     * 通过追溯码查询上游出库单
     */
    public TraceCodeUpOutOrderRsp queryTraceableCodeUpOutOrder(String chainId, String clinicId, String traceableCodeNo) {
        return goodsTraceCodeService.queryTraceableCodeUpOutOrder(chainId, clinicId, traceableCodeNo);
    }

    /**
     * 码上放心-上游出库单列表
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ListUpOutOrderAggRsp listAliHealthUpOutOrders(String chainId, String clinicId, ListUpOutOrderReq req) {
        return goodsTraceCodeService.listAliHealthUpOutOrders(chainId, clinicId, req);
    }

    /**
     * 按单据号批量查询上游出库单详情
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<TraceCodeUpOutOrderView> queryTraceableCodeUpOutOrderByBillCodes(String chainId, String clinicId, cn.abcyun.cis.goods.vo.frontend.tracecode.QueryUpOutDetailReq req) {
        if (req == null || CollectionUtils.isEmpty(req.getItems())) {
            return new ArrayList<>();
        }
        return goodsTraceCodeService.queryTraceableCodeUpOutOrders(chainId, clinicId, req.getItems());
    }
}
