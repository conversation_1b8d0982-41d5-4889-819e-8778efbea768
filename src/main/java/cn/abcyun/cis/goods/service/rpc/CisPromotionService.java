package cn.abcyun.cis.goods.service.rpc;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisPromotionFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.common.BaseResultRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsMultiPriceView;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.*;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.cis.goods.cache.redis.GoodsComposeRedisCache;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisCache;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.model.Goods;
import cn.abcyun.cis.goods.model.GoodsPrice;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.vo.frontend.goodsupdate.CalculateGoodsMemberPriceReq;
import cn.abcyun.cis.goods.vo.frontend.goodsupdate.CalculateGoodsTypeMemberPriceReq;
import cn.abcyun.cis.goods.vo.frontend.goodsupdate.CalculateGoodsTypeMemberPriceRsp;
import cn.abcyun.common.model.AbcListPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 营销业务
 *
 * <AUTHOR>
 * @since 2025/2/19 18:58
 **/
@Service
public class CisPromotionService {

    @Autowired
    private AbcCisPromotionFeignClient abcCisPromotionFeignClient;

    @Autowired
    private CisCrmService cisCrmService;

    /**
     * 新增或修改药店商品营销活动
     */
    public BaseResultRsp upsertPromotionDiscountGoodsForPharmacy(BatchSetPromotionDiscountGoodsReq discountGoodsReq) {
        return FeignClientRpcTemplate.dealRpcClientMethod("upsertPromotionDiscountGoodsForPharmacy", true,
                () -> abcCisPromotionFeignClient.upsertPromotionDiscountGoodsForPharmacy(discountGoodsReq),
                discountGoodsReq);
    }

    public List<GoodsMultiPriceView> getMemberPriceListByGoodsItem(ClinicConfig clinicConfig, GoodsItem goodsItem, GoodsRedisCache goods) {
        CalculateGoodsMemberPriceReq.CalculateGoodsMemberTypePriceItem item = new CalculateGoodsMemberPriceReq.CalculateGoodsMemberTypePriceItem();
        item.setGoodsId(goodsItem.getId());
        item.setGoodsType(goodsItem.getType());
        item.setGoodsSubType(goodsItem.getSubType());
        item.setCMSpec(goodsItem.getCMSpec());
        item.setCustomTypeId(goodsItem.getCustomTypeId());
        if (!CollectionUtils.isEmpty(goods.getCompositeComposeRedisCacheList())) {
            item.setCompositeGoodsGroupIds(goods.getCompositeComposeRedisCacheList().stream().map(GoodsComposeRedisCache::getParentGoodsId).collect(Collectors.toList()));
        }
        List<GoodsMultiPriceView> memberPriceList = getMemberPriceList(clinicConfig.getChainId(), clinicConfig.getClinicId(), Collections.singletonList(item));
        if (!CollectionUtils.isEmpty(memberPriceList)) {
            memberPriceList.forEach(memberPrice -> {
                if (Objects.equals(memberPrice.getDiscountType(), GoodsPrice.DiscountType.DISCOUNT)
                        || (Objects.equals(memberPrice.getDiscountType(), GoodsPrice.DiscountType.NO_DISCOUNT)) && memberPrice.getDiscountValue() != null) {

                    if (goodsItem.getPackagePrice() == null || MathUtils.wrapBigDecimalCompare(goodsItem.getPieceNum(), BigDecimal.ZERO) <= 0) {
                        return;
                    }

                    int scale = GoodsUtils.isChineseMedicine(goodsItem.getTypeId()) ? Goods.CLIENT_CHINESE_PRICE_PRECISION : Goods.CLIENT_PRICE_PRECISION;
                    memberPrice.setPackagePrice(GoodsUtils.calPriceByDiscount(goodsItem.getPackagePrice(), memberPrice.getDiscountValue(), scale));
                    memberPrice.setPiecePrice(memberPrice.getPackagePrice().divide(goodsItem.getPieceNum(), scale, RoundingMode.HALF_UP));
                }
            });
        }
        return memberPriceList;
    }

    public List<GoodsMultiPriceView> getMemberPriceByGoods(ClinicConfig clinicConfig, Goods goods, GoodsRedisCache goodsRedisCache) {
        if (!clinicConfig.isAbcPharmacy()) {
            return Collections.emptyList();
        }

        return getMemberPriceMapByGoodsList(clinicConfig, Collections.singletonList(goods), Collections.singletonList(goodsRedisCache)).getOrDefault(goods.getId(), Collections.emptyList());
    }

    public Map<String, List<GoodsMultiPriceView>> getMemberPriceMapByGoodsList(ClinicConfig clinicConfig, List<Goods> goodsList, List<GoodsRedisCache> goodsRedisCaches) {
        if (!clinicConfig.isAbcPharmacy() || CollectionUtils.isEmpty(goodsList)) {
            return new HashMap<>();
        }

        Map<String, GoodsRedisCache> goodsIdToGoodsRedisCache = goodsRedisCaches != null ? ListUtils.toMap(goodsRedisCaches, GoodsRedisCache::getId) : Collections.emptyMap();

        List<CalculateGoodsMemberPriceReq.CalculateGoodsMemberTypePriceItem> items = new ArrayList<>();
        for (Goods goods : goodsList) {
            CalculateGoodsMemberPriceReq.CalculateGoodsMemberTypePriceItem item = new CalculateGoodsMemberPriceReq.CalculateGoodsMemberTypePriceItem();
            item.setGoodsId(goods.getId());
            item.setGoodsType((int) goods.getType());
            item.setGoodsSubType((int) goods.getSubType());
            item.setCMSpec(goods.getCMSpec());
            item.setCustomTypeId(goods.getCustomTypeId());
            GoodsRedisCache goodsRedisCache = goodsIdToGoodsRedisCache.get(goods.getId());
            if (goodsRedisCache != null && !CollectionUtils.isEmpty(goodsRedisCache.getCompositeComposeRedisCacheList())) {
                item.setCompositeGoodsGroupIds(goodsRedisCache.getCompositeComposeRedisCacheList().stream().map(GoodsComposeRedisCache::getParentGoodsId).collect(Collectors.toList()));
            }
            items.add(item);
        }

        List<GoodsMultiPriceView> memberPriceList = getMemberPriceList(clinicConfig.getChainId(), clinicConfig.getClinicId(), items);

        if (CollectionUtils.isEmpty(memberPriceList)) {
            return new HashMap<>();
        }

        Map<String, List<GoodsMultiPriceView>> goodsIdToGoodsMemberPriceList = ListUtils.groupByKey(memberPriceList, GoodsMultiPriceView::getGoodsId);
        for (Goods goods : goodsList) {
            List<GoodsMultiPriceView> goodsMemberPriceList = goodsIdToGoodsMemberPriceList.get(goods.getId());
            if (CollectionUtils.isEmpty(goodsMemberPriceList)) {
                continue;
            }

            goodsMemberPriceList.forEach(memberPrice -> {
                if (Objects.equals(memberPrice.getDiscountType(), GoodsPrice.DiscountType.DISCOUNT)
                        || (Objects.equals(memberPrice.getDiscountType(), GoodsPrice.DiscountType.NO_DISCOUNT)) && memberPrice.getDiscountValue() != null) {

                    if (goods.getPackagePrice() == null || goods.getPieceNum() == null || goods.getPieceNum() <= 0) {
                        return;
                    }
                    int scale = GoodsUtils.isChineseMedicine(goods.getTypeId()) ? Goods.CLIENT_CHINESE_PRICE_PRECISION : Goods.CLIENT_PRICE_PRECISION;
                    memberPrice.setPackagePrice(GoodsUtils.calPriceByDiscount(goods.getPackagePrice(), memberPrice.getDiscountValue(), scale));
                    memberPrice.setPiecePrice(memberPrice.getPackagePrice().divide(BigDecimal.valueOf(goods.getPieceNum()), scale, RoundingMode.HALF_UP));
                }
                //promotion无折扣信息，就是按goods上的价格买，把goods价格填充进去
                if (Objects.equals(memberPrice.getDiscountType(), GoodsPrice.DiscountType.NO_DISCOUNT) && memberPrice.getDiscountValue() == null) {
                    memberPrice.setPackagePrice(goods.getPackagePrice());
                    memberPrice.setPiecePrice(goods.getPiecePrice());
                }
            });
        }

        return goodsIdToGoodsMemberPriceList;
    }

    public List<GoodsMultiPriceView> getMemberPriceListForCreate(String chainId,
                                                                 String clinicId,
                                                                 List<CalculateGoodsMemberPriceReq.CalculateGoodsMemberTypePriceItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }

        Map<String, String> memberTypeIdToMemberTypeName = cisCrmService.getChainMemberTypeInfo(chainId);
        if (CollectionUtils.isEmpty(memberTypeIdToMemberTypeName)) {
            return new ArrayList<>();
        }

        QueryPromotionDiscountGoodsBenefitsReq queryPromotionReq = new QueryPromotionDiscountGoodsBenefitsReq();
        queryPromotionReq.setChainId(chainId);
        queryPromotionReq.setClinicId(clinicId);
        List<GoodsBaseInfo> goodsBaseInfoList = new ArrayList<>();
        for (CalculateGoodsMemberPriceReq.CalculateGoodsMemberTypePriceItem item : items) {
            GoodsBaseInfo goodsBaseInfo = new GoodsBaseInfo();
            goodsBaseInfo.setGoodsId(item.getGoodsId());
            goodsBaseInfo.setGoodsType(item.getGoodsType());
            goodsBaseInfo.setGoodsSubType(item.getGoodsSubType());
            goodsBaseInfo.setGoodsCMSpec(item.getCMSpec());
            goodsBaseInfo.setCustomTypeId(item.getCustomTypeId());
            goodsBaseInfo.setPharmacyType(GoodsConst.PharmacyType.LOCAL_PHARMACY);
            goodsBaseInfo.setClinicId(clinicId);
            goodsBaseInfoList.add(goodsBaseInfo);
        }
        queryPromotionReq.setGoodsList(goodsBaseInfoList);
        QueryPromotionDiscountGoodsBenefitsRsp queryPromotionRsp = listPromotionDiscountGoodsForPharmacy(queryPromotionReq);

        List<QueryPromotionDiscountGoodsBenefitsRsp.GoodsResult.ClinicResult.MemberDiscountGoods> memberDiscountGoodsList = Optional.ofNullable(queryPromotionRsp)
                .map(QueryPromotionDiscountGoodsBenefitsRsp::getGoodsResultList).orElseGet(Collections::emptyList).stream()
                .map(QueryPromotionDiscountGoodsBenefitsRsp.GoodsResult::getClinicResults).flatMap(Collection::stream).filter(Objects::nonNull)
                .map(QueryPromotionDiscountGoodsBenefitsRsp.GoodsResult.ClinicResult::getMemberDiscountGoods).flatMap(Collection::stream)
                .filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, QueryPromotionDiscountGoodsBenefitsRsp.GoodsResult.ClinicResult.MemberDiscountGoods> memberTypeIdToMemberDiscountGoods = ListUtils.toMap(memberDiscountGoodsList, QueryPromotionDiscountGoodsBenefitsRsp.GoodsResult.ClinicResult.MemberDiscountGoods::getMemberId);

        List<GoodsMultiPriceView> multiPriceList = new ArrayList<>();
        memberTypeIdToMemberTypeName.forEach((memberTypeId, memberTypeName) -> {
            QueryPromotionDiscountGoodsBenefitsRsp.GoodsResult.ClinicResult.MemberDiscountGoods memberDiscountGoods = memberTypeIdToMemberDiscountGoods.get(memberTypeId);
            GoodsMultiPriceView multiPriceView = new GoodsMultiPriceView();
            multiPriceView.setMemberTypeId(memberTypeId);
            multiPriceView.setMemberCardName(memberTypeIdToMemberTypeName.get(memberTypeId));
            multiPriceView.setTargetType(GoodsPrice.PriceTargetType.MEMBER);
            if (memberDiscountGoods != null && memberDiscountGoods.getMemberDiscountInfo() != null
                    && memberDiscountGoods.getMemberDiscountInfo().getDiscount() != null && memberDiscountGoods.getMemberDiscountInfo().getDiscount() != null) {
                if (Objects.equals(memberDiscountGoods.getMemberDiscountInfo().getType(), 1)) {
                    // 如果命中了分类优惠
                    multiPriceView.setDiscountType(GoodsPrice.DiscountType.NO_DISCOUNT);
                } else {
                    // 命中了单品优惠
                    DiscountPromotionGoodsView discountInfo = memberDiscountGoods.getMemberDiscountInfo();
                    multiPriceView.setDiscountType(discountInfo.getDiscountType());
                }
                DiscountPromotionGoodsView discountInfo = memberDiscountGoods.getMemberDiscountInfo();
                multiPriceView.setDiscountValue(discountInfo.getDiscount());
                if (Objects.equals(multiPriceView.getDiscountType(), GoodsPrice.DiscountType.BY_FIXED_PRICE)) {
                    multiPriceView.setPackagePrice(discountInfo.getDiscount());
                }
            } else {
                // 没有配置优惠
                multiPriceView.setDiscountType(GoodsPrice.DiscountType.NO_DISCOUNT);
            }
            // 设置会员限购策略
            if (Objects.nonNull(memberDiscountGoods) && Objects.nonNull(memberDiscountGoods.getMemberDiscountInfo())) {
                DiscountPromotionGoodsView discountInfo = memberDiscountGoods.getMemberDiscountInfo();
                multiPriceView.setIsSaleLimit(discountInfo.getIsSaleLimit());
                multiPriceView.setSaleLimitRule(discountInfo.getSaleLimitRule());
            }
            multiPriceView.setTargetType(GoodsPrice.PriceTargetType.MEMBER);
            multiPriceList.add(multiPriceView);
        });
        // 按memberTypeId降序排序（增加空值保护）
        if (multiPriceList != null && !multiPriceList.isEmpty()) {
            multiPriceList.sort((a, b) -> {
                String memberTypeIdA = a != null ? a.getMemberTypeId() : "";
                String memberTypeIdB = b != null ? b.getMemberTypeId() : "";
                return memberTypeIdB.compareTo(memberTypeIdA);
            });
        }

        return multiPriceList;
    }

    public List<GoodsMultiPriceView> getMemberPriceList(String chainId,
                                                        String clinicId,
                                                        List<CalculateGoodsMemberPriceReq.CalculateGoodsMemberTypePriceItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }

        Map<String, String> memberTypeIdToMemberTypeName = cisCrmService.getChainMemberTypeInfo(chainId);
        if (CollectionUtils.isEmpty(memberTypeIdToMemberTypeName)) {
            return new ArrayList<>();
        }

        QueryPromotionDiscountGoodsBenefitsReq queryPromotionReq = new QueryPromotionDiscountGoodsBenefitsReq();
        queryPromotionReq.setChainId(chainId);
        queryPromotionReq.setClinicId(clinicId);
        List<GoodsBaseInfo> goodsBaseInfoList = new ArrayList<>();
        for (CalculateGoodsMemberPriceReq.CalculateGoodsMemberTypePriceItem item : items) {
            GoodsBaseInfo goodsBaseInfo = new GoodsBaseInfo();
            goodsBaseInfo.setGoodsId(item.getGoodsId());
            goodsBaseInfo.setGoodsType(item.getGoodsType());
            goodsBaseInfo.setGoodsSubType(item.getGoodsSubType());
            goodsBaseInfo.setGoodsCMSpec(item.getCMSpec());
            goodsBaseInfo.setCustomTypeId(item.getCustomTypeId());
            goodsBaseInfo.setPharmacyType(GoodsConst.PharmacyType.LOCAL_PHARMACY);
            goodsBaseInfo.setClinicId(clinicId);
            goodsBaseInfo.setCompositeGoodsGroupIds(item.getCompositeGoodsGroupIds());
            goodsBaseInfoList.add(goodsBaseInfo);
        }
        queryPromotionReq.setGoodsList(goodsBaseInfoList);
        QueryPromotionDiscountGoodsBenefitsRsp queryPromotionRsp = listPromotionDiscountGoodsForPharmacy(queryPromotionReq);

        // Map<商品ID, Map<会员ID, 会员折扣商品>>
        Map<String, Map<String, QueryPromotionDiscountGoodsBenefitsRsp.GoodsResult.ClinicResult.MemberDiscountGoods>> goodsIdToPromotionMemberPriceMap = new HashMap<>();
        if (queryPromotionRsp != null && !CollectionUtils.isEmpty(queryPromotionRsp.getGoodsResultList())) {
            queryPromotionRsp.getGoodsResultList().forEach(goodsResult -> {
                if (goodsResult != null && !CollectionUtils.isEmpty(goodsResult.getClinicResults())) {
                    goodsResult.getClinicResults().forEach(clinicResult -> {
                        if (clinicResult != null && !CollectionUtils.isEmpty(clinicResult.getMemberDiscountGoods())) {
                            clinicResult.getMemberDiscountGoods().forEach(memberDiscountGoods -> {
                                goodsIdToPromotionMemberPriceMap.computeIfAbsent(goodsResult.getGoodsId(), k -> new HashMap<>())
                                        .put(memberDiscountGoods.getMemberId(), memberDiscountGoods);
                            });
                        }
                    });
                }
            });
        }

        List<GoodsMultiPriceView> multiPriceList = new ArrayList<>();
        for (CalculateGoodsMemberPriceReq.CalculateGoodsMemberTypePriceItem item : items) {
            memberTypeIdToMemberTypeName.forEach((memberTypeId, memberTypeName) -> {
                String goodsId = item.getGoodsId();
                Map<String, QueryPromotionDiscountGoodsBenefitsRsp.GoodsResult.ClinicResult.MemberDiscountGoods> memberTypeIdToMemberPrice = goodsIdToPromotionMemberPriceMap.getOrDefault(goodsId, Collections.emptyMap());
                QueryPromotionDiscountGoodsBenefitsRsp.GoodsResult.ClinicResult.MemberDiscountGoods memberDiscountGoods = memberTypeIdToMemberPrice.get(memberTypeId);
                GoodsMultiPriceView multiPriceView = new GoodsMultiPriceView();
                multiPriceView.setMemberTypeId(memberTypeId);
                multiPriceView.setGoodsId(goodsId);
                multiPriceView.setMemberCardName(memberTypeIdToMemberTypeName.get(memberTypeId));
                multiPriceView.setTargetType(GoodsPrice.PriceTargetType.MEMBER);
                DiscountPromotionGoodsView discountPromotionGoods = memberDiscountGoods != null ? memberDiscountGoods.getMemberDiscountInfo() : null;
                fillMultiPriceViewByPromotionDiscount(multiPriceView, discountPromotionGoods);
                if (discountPromotionGoods == null || discountPromotionGoods.getDiscount() == null) {
                    if (memberTypeIdToMemberTypeName.size() == 1) {
                        // 只有一个会员价，给特价
                        multiPriceView.setDiscountType(GoodsPrice.DiscountType.BY_FIXED_PRICE);
                    }
                }
                multiPriceList.add(multiPriceView);
            });
        }

        return multiPriceList;
    }

    private void fillMultiPriceViewByPromotionDiscount(GoodsMultiPriceView multiPriceView, DiscountPromotionGoodsView discountPromotionGoods) {
        if (discountPromotionGoods != null && discountPromotionGoods.getDiscount() != null) {
            if (Objects.equals(discountPromotionGoods.getType(), PromotionConstant.PromotionGoodsType.CLASS)
                    || Objects.equals(discountPromotionGoods.getType(), PromotionConstant.PromotionGoodsType.GOODS_GROUP)) {
                // 如果命中了分类优惠
                multiPriceView.setDiscountType(GoodsPrice.DiscountType.NO_DISCOUNT);
                multiPriceView.setDiscountGoodsType(discountPromotionGoods.getType());
                if (Objects.equals(discountPromotionGoods.getType(), PromotionConstant.PromotionGoodsType.GOODS_GROUP)) {
                    multiPriceView.setDiscountGoodsDiscountType(discountPromotionGoods.getDiscountType());
                }
            } else {
                // 命中了单品优惠
                multiPriceView.setDiscountType(discountPromotionGoods.getDiscountType());
            }
            multiPriceView.setDiscountValue(discountPromotionGoods.getDiscount());
            if (Objects.equals(multiPriceView.getDiscountType(), GoodsPrice.DiscountType.BY_FIXED_PRICE)) {
                multiPriceView.setPackagePrice(discountPromotionGoods.getDiscount());
            }
        } else {
            // 没有配置优惠
            multiPriceView.setDiscountType(GoodsPrice.DiscountType.NO_DISCOUNT);
        }

        // 设置会员价限购策略
        if (discountPromotionGoods != null) {
            multiPriceView.setIsSaleLimit(discountPromotionGoods.getIsSaleLimit());
            multiPriceView.setSaleLimitRule(discountPromotionGoods.getSaleLimitRule());
        }
    }

    /**
     * 查询药店会员单品权益
     */
    public QueryPromotionDiscountGoodsBenefitsRsp listPromotionDiscountGoodsForPharmacy(QueryPromotionDiscountGoodsBenefitsReq req) {
        return FeignClientRpcTemplate.dealRpcClientMethod("queryPromotionDiscountGoodsForPharmacy",
                () -> abcCisPromotionFeignClient.listPromotionDiscountGoodsForPharmacy(req), req);
    }

    /**
     * 计算商品命中的分类折扣
     */
    public List<DiscountPromotionGoodsView> calculateTypeDiscountForPharmacy(CalculateTypeDiscountReq req) {
        AbcListPage<DiscountPromotionGoodsView> listPage = FeignClientRpcTemplate.dealRpcClientMethod("calculateTypeDiscountForPharmacy",
                () -> abcCisPromotionFeignClient.calculateTypeDiscountForPharmacy(req), req);
        if (listPage == null || CollectionUtils.isEmpty(listPage.getRows())) {
            return new ArrayList<>();
        }
        return listPage.getRows();
    }

    public CalculateGoodsTypeMemberPriceRsp getGoodsTypeMemberPriceList(String chainId, CalculateGoodsTypeMemberPriceReq calculateGoodsTypeMemberPriceReq) {
        CalculateGoodsTypeMemberPriceRsp calculateRsp = new CalculateGoodsTypeMemberPriceRsp();
        calculateRsp.setMemberTypeId(calculateGoodsTypeMemberPriceReq.getMemberTypeId());

        CalculateTypeDiscountReq calculateTypeDiscountReq = new CalculateTypeDiscountReq();
        calculateTypeDiscountReq.setChainId(chainId);
        GoodsBaseInfo goodsBaseInfo = new GoodsBaseInfo();
        goodsBaseInfo.setGoodsType(calculateGoodsTypeMemberPriceReq.getGoodsType());
        goodsBaseInfo.setGoodsSubType(calculateGoodsTypeMemberPriceReq.getGoodsSubType());
        goodsBaseInfo.setGoodsCMSpec(calculateGoodsTypeMemberPriceReq.getGoodsCMSpec());
        goodsBaseInfo.setCustomTypeId(calculateGoodsTypeMemberPriceReq.getCustomTypeId());
        goodsBaseInfo.setCompositeGoodsGroupIds(calculateGoodsTypeMemberPriceReq.getCompositeGoodsGroupIds());
        calculateTypeDiscountReq.setGoodsList(Collections.singletonList(goodsBaseInfo));
        List<DiscountPromotionGoodsView> discountPromotionGoodsViews = calculateTypeDiscountForPharmacy(calculateTypeDiscountReq);
        if (!CollectionUtils.isEmpty(discountPromotionGoodsViews)) {
            calculateRsp.setDiscountValue(discountPromotionGoodsViews.stream()
                    .filter(discountPromotionGoods -> Objects.equals(discountPromotionGoods.getAssociateMemberTypeId(), calculateGoodsTypeMemberPriceReq.getMemberTypeId()))
                    .findFirst().map(DiscountPromotionGoodsView::getDiscount).orElse(null));
        }

        return calculateRsp;
    }

    /**
     * 查询会员商品范围（分类折扣、商品组折扣）的折扣信息
     *
     * @param chainId       连锁ID
     * @param clinicId      门店ID
     * @param memberTypeIds 会员类型列表
     * @return 商品范围的折扣信息
     */
    public List<PromotionMemberDiscountDetail> listMemberGoodsScopeDiscount(String chainId, String clinicId, List<String> memberTypeIds) {
        QueryPromotionMemberDiscountsReq req = new QueryPromotionMemberDiscountsReq();
        req.setChainId(chainId);
        if (StringUtils.isNotBlank(clinicId)) {
            req.setClinicIds(Collections.singletonList(clinicId));
        }
        req.setMemberTypeIds(memberTypeIds);
        req.setWithAllGoods(0);
        req.setGoodsTypeOnly(1);
        QueryPromotionMemberDiscountsRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("listMemberDiscountByIds",
                () -> abcCisPromotionFeignClient.listMemberDiscountByIds(req), req);
        if (rsp == null || CollectionUtils.isEmpty(rsp.getRows())) {
            return new ArrayList<>();
        }

        return rsp.getRows();
    }

    /**
     * 查询商品类型会员价
     *
     * @return key->memberTypeId, value->分类折扣价
     */
    public Map<String, GoodsMultiPriceView> getGoodsTypePromotionPrice(ClinicConfig clinicConfig, Goods goods) {
        return getGoodsTypePromotionPrice(clinicConfig, Collections.singletonList(goods)).getOrDefault(goods.getId(), new HashMap<>());
    }

    /**
     * 查询商品类型会员价
     *
     * @return key -> goodsId value -> (key->memberTypeId, value->分类折扣价)
     */
    public Map<String, Map<String, GoodsMultiPriceView>> getGoodsTypePromotionPrice(ClinicConfig clinicConfig, List<Goods> goodsList) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return new HashMap<>();
        }

        CalculateTypeDiscountReq req = new CalculateTypeDiscountReq();
        req.setChainId(clinicConfig.getChainId());
        List<GoodsBaseInfo> goodsBaseInfoList = new ArrayList<>();
        for (Goods goods : goodsList) {
            GoodsBaseInfo goodsBaseInfo = new GoodsBaseInfo();
            goodsBaseInfo.setGoodsId(goods.getId());
            goodsBaseInfo.setGoodsType(goods.getType());
            goodsBaseInfo.setGoodsSubType(goods.getSubType());
            goodsBaseInfo.setGoodsCMSpec(goods.getCMSpec());
            goodsBaseInfo.setCustomTypeId(goods.getCustomTypeId());
            goodsBaseInfo.setClinicId(clinicConfig.getClinicId());
            goodsBaseInfoList.add(goodsBaseInfo);
        }
        req.setGoodsList(goodsBaseInfoList);
        List<DiscountPromotionGoodsView> discountPromotionGoodsViews = calculateTypeDiscountForPharmacy(req);
        if (CollectionUtils.isEmpty(discountPromotionGoodsViews)) {
            return new HashMap<>();
        }

        Map<String, Map<String, GoodsMultiPriceView>> goodsIdToMemberTypeDiscountMap = new HashMap<>(goodsList.size());
        for (DiscountPromotionGoodsView discountPromotionGoodsView : discountPromotionGoodsViews) {
            GoodsMultiPriceView goodsMultiPriceView = new GoodsMultiPriceView();
            goodsMultiPriceView.setTargetType(GoodsPrice.PriceTargetType.MEMBER);
            goodsMultiPriceView.setGoodsId(discountPromotionGoodsView.getGoodsId());
            goodsMultiPriceView.setMemberTypeId(discountPromotionGoodsView.getAssociateMemberTypeId());
            fillMultiPriceViewByPromotionDiscount(goodsMultiPriceView, discountPromotionGoodsView);
            goodsIdToMemberTypeDiscountMap.computeIfAbsent(discountPromotionGoodsView.getGoodsId(), k -> new HashMap<>())
                    .put(discountPromotionGoodsView.getAssociateMemberTypeId(), goodsMultiPriceView);
        }
        return goodsIdToMemberTypeDiscountMap;
    }

    public Map<String, QueryGoodsPromotionRsp.Goods> queryGoodsPromotionMap(String chainId, List<String> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return new HashMap<>();
        }

        QueryGoodsPromotionReq req = new QueryGoodsPromotionReq();
        req.setChainId(chainId);
        req.setGoodsIds(goodsIds);
        QueryGoodsPromotionRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("queryGoodsPromotion",
                () -> abcCisPromotionFeignClient.queryGoodsPromotion(req), req);
        if (rsp == null || CollectionUtils.isEmpty(rsp.getGoodsList())) {
            return new HashMap<>();
        }

        return ListUtils.toMap(rsp.getGoodsList(), QueryGoodsPromotionRsp.Goods::getGoodsId);
    }
}
