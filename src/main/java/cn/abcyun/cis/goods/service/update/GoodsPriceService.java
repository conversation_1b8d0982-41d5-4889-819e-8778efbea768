/*
  处理查询goods列表的服务
 */
package cn.abcyun.cis.goods.service.update;

import cn.abcyun.bis.rpc.sdk.cis.message.goods.ClearGoodsConfigCacheMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.useroperationlog.OpSource;
import cn.abcyun.bis.rpc.sdk.cis.message.useroperationlog.OpType;
import cn.abcyun.bis.rpc.sdk.cis.message.useroperationlog.OperationObjectType;
import cn.abcyun.bis.rpc.sdk.cis.model.approval.ApprovalConstant;
import cn.abcyun.bis.rpc.sdk.cis.model.approval.ApprovalProcessInstVO;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ClinicEmployeeDataPermission;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.BatchSetPromotionDiscountGoodsReq;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.SetPromotionDiscountGoodsReq;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.util.*;
import cn.abcyun.cis.goods.amqp.MQProducer;
import cn.abcyun.cis.goods.amqp.RocketMqProducer;
import cn.abcyun.cis.goods.cache.redis.ChoosePharmacyHelper;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisCache;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisUtils;
import cn.abcyun.cis.goods.cache.redis.GoodsStockRedisCache;
import cn.abcyun.cis.goods.consts.MediaTypeExtend;
import cn.abcyun.cis.goods.consts.ModifyPriceOrderOpType;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.domain.GoodsStatManager;
import cn.abcyun.cis.goods.domain.GoodsStockMountTrible;
import cn.abcyun.cis.goods.dto.price.ModifyPriceOrderResult;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.listener.GoodsModifyPriceOrderListener;
import cn.abcyun.cis.goods.mapper.GoodsMapper;
import cn.abcyun.cis.goods.mapper.GoodsPriceMapper;
import cn.abcyun.cis.goods.model.*;
import cn.abcyun.cis.goods.model.GoodsSysType;
import cn.abcyun.cis.goods.repository.*;
import cn.abcyun.cis.goods.service.GoodsStatService;
import cn.abcyun.cis.goods.service.hisversion.GoodsHisVersionManager;
import cn.abcyun.cis.goods.service.query.GoodsSysTypeService;
import cn.abcyun.cis.goods.service.rpc.CisClinicService;
import cn.abcyun.cis.goods.service.rpc.CisCrmService;
import cn.abcyun.cis.goods.service.rpc.CisPromotionService;
import cn.abcyun.cis.goods.service.rpc.GspService;
import cn.abcyun.cis.goods.service.todo.GoodsTodoCountService;
import cn.abcyun.cis.goods.stock.service.GoodsStockInOrderServiceBase;
import cn.abcyun.cis.goods.utils.*;
import cn.abcyun.cis.goods.utils.protocol.GoodsListProtocolFillUtils;
import cn.abcyun.cis.goods.vo.GetGoodsClinicPriceReq;
import cn.abcyun.cis.goods.vo.GetGoodsClinicPriceRsp;
import cn.abcyun.cis.goods.vo.frontend.EmployeeView;
import cn.abcyun.cis.goods.vo.frontend.goodsinfo.price.*;
import cn.abcyun.cis.goods.vo.frontend.goodsinfo.price.ModifyPriceTipsView;
import cn.abcyun.cis.goods.vo.frontend.goodsupdate.ClientGoodsDisableReq;
import cn.abcyun.cis.goods.vo.frontend.price.CreateModifyPriceOrderReq;
import cn.abcyun.cis.goods.vo.frontend.price.PrintGoodsPriceReq;
import cn.abcyun.cis.goods.vo.frontend.types.CreateOrUpdateGoodsFeeTypeReq;
import cn.abcyun.common.model.AbcListPage;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class GoodsPriceService {
    private static final Logger sLogger = LoggerFactory.getLogger(GoodsPriceService.class);
    @Autowired
    private ApplicationContext applicationContext;
    @Lazy
    @Autowired
    private GoodsSysTypeService goodsSysTypeService;
    @Autowired
    private GoodsPriceRepository goodsPriceRepository;
    @Autowired
    private GoodsRepository goodsRepository;
    @Autowired
    private GoodsExtendRepository goodsExtendRepository;
    @Autowired
    private GoodsComposePriceRepository goodsComposePriceRepository;
    @Autowired
    private ExaminationDeviceRepository examinationDeviceRepository;
    @Autowired
    private GoodsStatRepository goodsStatRepository;
    @Autowired
    private LoadGoodsUtils loadGoodsUtils;
    @Autowired
    private CisClinicService cisClinicService;
    @Autowired
    private GoodsRedisUtils goodsRedisUtils;
    @Autowired
    private OrganUtils organUtils;
    @Autowired
    private EmployeeUtils employeeUtils;
    @Autowired
    private GoodsMapper goodsMapper;
    @Autowired
    protected RocketMqProducer rocketMqProducer;
    @Autowired
    protected MQProducer mqProducer;
    @Autowired
    protected GoodsTodoCountService goodsTodoCountService;
    @Autowired
    private GoodsStatService goodsStatService;
    @Autowired
    private GoodsLogRepository goodsLogRepository;
    @Autowired
    private GoodsPriceMapper goodsPriceMapper;
    @Autowired
    private GoodsModifyPriceOrderRepository goodsModifyPriceOrderRepository;
    @Autowired
    private GoodsModifyPriceOrderItemRepository goodsModifyPriceOrderItemRepository;
    @Autowired
    private GoodsModifyPriceOrderLogRepository goodsModifyPriceOrderLogRepository;
    @Autowired
    private SupplierUtils supplierUtils;
    @Autowired
    private GoodsClinicConfigRepository goodsClinicConfigRepository;
    @Autowired
    private GoodsComposeNakedRepository goodsComposeNakedRepository;
    @Autowired
    private GoodsMedicalStatRepository goodsMedicalStatRepository;
    @Autowired
    private ObjectProvider<GoodsModifyPriceOrderListener> goodsModifyPriceOrderListeners;
    @Autowired
    private CisPromotionService cisPromotionService;
    @Autowired
    private ObjectProvider<GoodsPriceService> thisProvider;
    public static final String PREFIX_TJ = "TJ";
    public static final String PREFIX_TJ_CG = "TJ";
    public static final String PREFIX_TJ_CHAIN = "TJ";
    public static final String PREFIX_TJ_INORDER = "TJ";
    public static final int TJNO_LENGTH = 15;
    public static final int GET_GOODS_REDIS_CACHE_LIST_SIZE = 200;
    public static final int CREATE_DRAFT_ORDER_FROM_LIST = 0;
    public static final int CREATE_DRAFT_ORDER_GOODS = 2;
    public static final int CREATE_DRAFT_ORDER_QUICK_PRICE = 1;
    @Autowired
    private CisCrmService cisCrmService;

    private boolean flag = true;

    /**
     * 批量加入改价的Goods列表
     */
    public GetGoodsListForModifyGoodsPricesRsp getGoodsListForBatchModifyPrices(GetGoodsListForModifyGoodsPricesReq clientReq) throws CisGoodsServiceException {

        GetGoodsListForModifyGoodsPricesRsp clientRsp = new GetGoodsListForModifyGoodsPricesRsp();
        String clinicId = clientReq.getHeaderClinicId();
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(clinicId);
        if (GoodsPrivUtils.isHeadClinic(clientReq.getHeaderClinicType(), clientReq.getHeaderViewMode()) && !GoodsPrivUtils.isSingleMode(clientReq.getHeaderClinicType(), clientReq.getHeaderViewMode())) {
            clinicId = null;
        }

        /**
         * 最近供应商
         * */
        List<String> supplierNames = new ArrayList<>();
        if (!StringUtils.isEmpty(clientReq.getSupplierId())) {
            GoodsSupplierView goodsSupplier = supplierUtils.getSupplierCachedById(clinicConfig, clientReq.getHeaderChainId(), clientReq.getSupplierId());
            if (goodsSupplier != null) {
                if (!StringUtils.isEmpty(goodsSupplier.getName())) {
                    supplierNames.add(goodsSupplier.getName());
                }
                // 之前的老数据叫「盘点入库」
                String checkSupplierName = "盘点入库";
                if (Objects.equals(goodsSupplier.getId(), UserFillUtils.DEFUALT_ID) && !supplierNames.contains(checkSupplierName)) {
                    supplierNames.add(checkSupplierName);
                }
            }
        }

        /**
         * 药房 如果开了多药房为汇总药房
         * */
        int pharmacyNo = 0;
        if (clinicConfig.getOpenPharmacyFlag() == GoodsConst.OpenPharmacyFlag.MORE) {
            pharmacyNo = GoodsUtils.SUMMERY_PHARMACY_NO;
        } else {
            if (clinicConfig.isHeadClinic() && !clinicConfig.isSingleMode()) {
                pharmacyNo = GoodsUtils.SUMMERY_PHARMACY_NO;
            } else {
                pharmacyNo = GoodsUtils.PHARMACY_NO_0;
            }
        }

        /**
         * 拿到GoodsId
         * */
        List<String> goodsIdList = goodsMapper.findBatchUpdatePriceGoodsIdList(clientReq.getHeaderChainId(),
                clinicId,
                supplierNames,
                clientReq.getSheBaoOverPrice(),
                clientReq.getCustomTypeIdList(),
                clientReq.getOtherCustomTypeTypeIdList(),
                clientReq.getPharmacyType(),
                clientReq.getPriceType(),
                pharmacyNo,
                clientReq.getTypeId());
        if (goodsIdList.size() > 2000) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "本次加入修改价格药品数量:" + goodsIdList.size() + ",大于2000种,请按类型过滤调价");
        }

        /**
         * 从Reids加载Goods
         * */
        List<List<String>> goodsIdPartitionList = Lists.partition(goodsIdList, GET_GOODS_REDIS_CACHE_LIST_SIZE);
        for (List<String> goodsIdPartition : goodsIdPartitionList) {
            List<GoodsRedisCache> goodsList = new ArrayList<>();
            goodsList.addAll(goodsRedisUtils.loadQueryGoodsDataFromRedis(goodsIdPartition,
                    clinicConfig,
                    null,
                    GoodsUtils.SwitchFlag.OFF,
                    ChoosePharmacyHelper.ofSpecificPharmacy(clientReq.getPharmacyType() != null ? clientReq.getPharmacyType() : GoodsConst.PharmacyType.LOCAL_PHARMACY,
                            clientReq.getPharmacyType() != null && clientReq.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY ? null : pharmacyNo)));
            /**
             * 返回协议
             * */
            for (GoodsRedisCache goodsRedisCache : goodsList) {
                if (goodsRedisCache.getDisable() == GoodsConst.EnableStatus.DISABLE) {
                    continue;
                }
                if (goodsRedisCache.getStatus() == GoodsConst.GoodsStatus.DELETE) {
                    continue;
                }
                GoodsItem goodsItem = new GoodsItem();
                clientRsp.getList().add(GoodsListProtocolFillUtils.genRpcCommonGoodsItemByRedisCache(goodsItem, goodsRedisCache, GoodsUtils.isComposeGoods(goodsRedisCache), clinicConfig));
            }
        }


        clientRsp.setTotalCount(clientRsp.getList().size());
        return clientRsp;
    }

    /**
     * 查修改价格 单个订单
     */
    public ModifyPriceOrderView getModifyPriceOrderInfo(GetModifyPriceOrderReq clientReq) throws CisGoodsServiceException {
        GoodsModifyPriceOrder order = goodsModifyPriceOrderRepository.findByChainIdAndId(clientReq.getHeaderChainId(), clientReq.getOrderId()).orElse(null);
        if (order == null) {
            return null;
        }
        List<GoodsModifyPriceOrderItem> modifyPriceOrderItemList = null;
        if (StringUtils.isEmpty(clientReq.getGoodsId())) {
            modifyPriceOrderItemList = goodsModifyPriceOrderItemRepository.findByOrderIdAndIsDeleted(order.getId(), GoodsUtils.SwitchFlag.OFF);
        } else {
            modifyPriceOrderItemList = goodsModifyPriceOrderItemRepository.findByOrderIdAndGoodsIdAndIsDeleted(order.getId(), clientReq.getGoodsId(), GoodsUtils.SwitchFlag.OFF);
        }
        order.setList(modifyPriceOrderItemList);
        order.setLogs(goodsModifyPriceOrderLogRepository.findByChainIdAndOrderId(order.getChainId(), order.getId()));
        /**
         * 加载goods历史版本
         * */
        GoodsHisVersionManager goodsHisVersionManager = new GoodsHisVersionManager(cisClinicService.getClinicConfig(clientReq.getHeaderChainId()), goodsMapper, goodsRedisUtils);
        /**
         * 批量加载历史版本Goods
         * */
        goodsHisVersionManager.loadGoodsSnapHisVersion(modifyPriceOrderItemList.stream().map(it -> {
            if (it.getGoodsSnap() != null) {
                it.getGoodsSnap().setHelperGoodsId(it.getGoodsId());
            }
            return it.getGoodsSnap();
        }).collect(Collectors.toList()));
        Map<String, GoodsRedisCache> goodsIdToGoodsRedisCache = new HashMap<>();
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(order.getClinicId());
        if (!order.isOrderStable()) {
            goodsIdToGoodsRedisCache.putAll(goodsRedisUtils.getMapGoodsRedisCacheWithoutBatch(
                    modifyPriceOrderItemList.stream().map(GoodsModifyPriceOrderItem::getGoodsId).distinct().collect(Collectors.toList()),
                    ChoosePharmacyHelper.ofSpecificPharmacy(GoodsConst.PharmacyType.LOCAL_PHARMACY, null),
                    clinicConfig
            ));
        }
        Map<String, String> memberTypeIdToMemberTypeName = cisCrmService.getChainMemberTypeInfo(clientReq.getHeaderChainId());
        return fillModifyPriceOrderView(order, organUtils, employeeUtils, goodsHisVersionManager, clinicConfig, goodsIdToGoodsRedisCache, memberTypeIdToMemberTypeName);
    }

    /**
     * goods 修改价格的订单列表
     * QuickList
     */
    public GetModifyPriceListRsp getModifyPriceOrderList(GetModifyPriceListReq clientReq) throws CisGoodsServiceException {
        GetModifyPriceListRsp clientRsp = new GetModifyPriceListRsp();
        clientRsp.setLimit(clientReq.getLimit());
        clientRsp.setOffset(clientReq.getOffset());
        if (!StringUtils.isEmpty(clientReq.getPriceAdjuster())) {
            clientReq.setNeedDraft(YesOrNo.NO);
        }
        if (!StringUtils.isEmpty(clientReq.getKeyword())) {
            clientReq.setNeedDraft(YesOrNo.NO);
        }
        if (clientReq.getStatus() != null) {
            clientReq.setNeedDraft(YesOrNo.NO);
        }
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(clientReq.getHeaderClinicId());
        clientReq.setIsAbcPharmacy(clinicConfig.isAbcPharmacy() ? YesOrNo.YES : YesOrNo.NO);

        Map<Long, List<GoodsModifyPriceOrderItem>> orderIdToOrderItem = new HashMap<>();
        GoodsHisVersionManager goodsHisVersionManager = null;
        if (!StringUtils.isEmpty(clientReq.getGoodsId())) {
            orderIdToOrderItem.putAll(goodsModifyPriceOrderItemRepository.findAllByChainIdAndGoodsIdInAndIsDeleted(
                            clientReq.getHeaderChainId(),
                            Arrays.asList(clientReq.getGoodsId()),
                            YesOrNo.NO)
                    .stream()
                    .collect(Collectors.groupingBy(GoodsModifyPriceOrderItem::getOrderId)));
            clientReq.setGoodsId(null);
            if (orderIdToOrderItem.isEmpty()) {
                return clientRsp;
            }
            /**
             * 加载goods历史版本
             * */
            goodsHisVersionManager = new GoodsHisVersionManager(cisClinicService.getClinicConfig(clientReq.getHeaderChainId()), goodsMapper, goodsRedisUtils);
            /**
             * 批量加载历史版本Goods
             * */
            goodsHisVersionManager.loadGoodsSnapHisVersion(orderIdToOrderItem.values().stream().flatMap(it -> it.stream()).map(it -> {
                if (it.getGoodsSnap() != null) {
                    it.getGoodsSnap().setHelperGoodsId(it.getGoodsId());
                }
                return it.getGoodsSnap();
            }).collect(Collectors.toList()));
            clientReq.setOrderIdList(new ArrayList<>(orderIdToOrderItem.keySet()));
        }

        List<GoodsModifyPriceOrder> modifyPriceOrderList = goodsPriceMapper.getModifyPriceOrderList(clientReq);
        if (CollectionUtils.isEmpty(modifyPriceOrderList)) {
            return clientRsp;
        }

        //协议填充
        clientRsp.setTodoCount(goodsPriceMapper.countModifyPriceOrderList(clientReq));
        clientRsp.setTotal(clientRsp.getTodoCount().getTotalCount());


        clientRsp.setRows(new ArrayList<>());
        for (GoodsModifyPriceOrder goodsModifyPriceOrder : modifyPriceOrderList) {
            if (orderIdToOrderItem.containsKey(goodsModifyPriceOrder.getId())) {
                goodsModifyPriceOrder.getList().addAll(orderIdToOrderItem.get(goodsModifyPriceOrder.getId()));
            }


            clientRsp.getRows().add(fillModifyPriceOrderView(goodsModifyPriceOrder, organUtils, employeeUtils, goodsHisVersionManager, null, new HashMap<>(), null));
        }
        return clientRsp;
    }

    /**
     * 日志的协议组装
     */
    private static ModifyPriceOrderView fillModifyPriceOrderView(GoodsModifyPriceOrder order,
                                                                 OrganUtils organUtils,
                                                                 EmployeeUtils employeeUtils,
                                                                 GoodsHisVersionManager hisManager,
                                                                 ClinicConfig clinicConfig,
                                                                 Map<String, GoodsRedisCache> goodsIdToGoodsRedisCache,
                                                                 Map<String, String> memberTypeIdToMemberTypeName) {
        //修改价格 订单
        ModifyPriceOrderView view = toModifyPriceOrderView(order, organUtils, employeeUtils);

        /**
         * 调价条目协议的组装
         * */
        if (!CollectionUtils.isEmpty(order.getList())) {
            //订单里面单个goods的修改历史
            for (GoodsModifyPriceOrderItem modifyPriceOrderItem : order.getList()) {
                view.getList().add(toModifyPriceOrderItemView(order, modifyPriceOrderItem, hisManager, memberTypeIdToMemberTypeName));
            }
        }


        if (!CollectionUtils.isEmpty(order.getLogs())) {
            //订单里面单个goods的修改历史
            for (GoodsModifyPriceOrderLog log : order.getLogs()) {
                view.getLogs().add(toModifyPriceOrderLogView(log, organUtils, employeeUtils));
            }
        }
        return view;
    }

    /**
     * 协议组装
     */
    public static ModifyPriceOrderView toModifyPriceOrderView(GoodsModifyPriceOrder order, OrganUtils organUtils, EmployeeUtils employeeUtils) {
        if (order == null) {
            return null;
        }

        ModifyPriceOrderView view = new ModifyPriceOrderView();
        view.setId(order.getId());
        view.setCreatedUser(employeeUtils.getEmployeeView(order.getCreatedBy(), order.getChainId()));
        view.setCreated(order.getCreated());
        view.setModifyClinic(organUtils.getOrganView(order.getClinicId()));
        view.setKindCount(order.getKindCount());
        view.setOpType(order.getOpType());
        view.setSourceType(order.getSourceType());
        view.setStatus(order.getStatus());
        view.setStatusName(GoodsModifyPriceOrder.orderStatusName(order.getStatus()));

        view.setGspStatus(order.getGspStatus()); //Gsp状态
        view.setGsp(order.getGsp());//Gsp生贺时间

        view.setEffected(order.getEffected()); //生效时间
        view.setOrderNo(order.getOrderNo());
        view.setComment(order.getComment());
        if (order.getExtendInfos() == null) {
            return view;
        }
        /**
         * 调价比例
         * */
        view.setUpPercent(order.getExtendInfos().getUpPercent());
        /**
         * 影响的门店数量
         * */
        if (order.getExtendInfos() != null && !CollectionUtils.isEmpty(order.getExtendInfos().getAffectedClinicList())) {
            view.setAffectedClinicList(new ArrayList<>());
            for (String affectedClinicId : order.getExtendInfos().getAffectedClinicList()) {
                view.getAffectedClinicList().add(organUtils.getOrganView(affectedClinicId));
            }
        }
        /**
         * 单据里面类型和GoodsId数量的关系
         * */
        if (order.getExtendInfos() != null && !CollectionUtils.isEmpty(order.getExtendInfos().getTypeIdToGoodsCount())) {
            view.setTypeIdToGoodsCount(order.getExtendInfos().getTypeIdToGoodsCount());
        }
        return view;
    }

    /**
     * 基本条目数据
     */
    private static ModifyPriceOrderItemView toModifyPriceOrderItemView(GoodsModifyPriceOrder order,
                                                                       GoodsModifyPriceOrderItem modifyPriceOrderItem,
                                                                       GoodsHisVersionManager hisManager,
                                                                       Map<String, String> memberTypeIdToMemberTypeName) {
        ModifyPriceOrderItemView modifyPriceOrderItemView = new ModifyPriceOrderItemView();
        modifyPriceOrderItemView.setId(modifyPriceOrderItem.getId().toString());
        modifyPriceOrderItemView.setItemId(modifyPriceOrderItem.getItemId() != null ? modifyPriceOrderItem.getItemId().toString() : modifyPriceOrderItemView.getId());
        modifyPriceOrderItemView.setOrderId(modifyPriceOrderItem.getOrderId());
        modifyPriceOrderItemView.setPackageCostPrice(modifyPriceOrderItem.getPackageCostPrice());
        modifyPriceOrderItemView.setAvgPackageCostPrice(modifyPriceOrderItem.getAvgPackageCostPrice());
        modifyPriceOrderItemView.setLastSupplierName(modifyPriceOrderItem.getLastSupplierName());

        modifyPriceOrderItemView.setBeforePackagePrice(modifyPriceOrderItem.getBeforePackagePrice());
        modifyPriceOrderItemView.setBeforePiecePrice(modifyPriceOrderItem.getBeforePiecePrice());

        modifyPriceOrderItemView.setModifyBasePackagePrice(modifyPriceOrderItem.getModifyBasePackagePrice());
        modifyPriceOrderItemView.setModifyBasePiecePrice(modifyPriceOrderItem.getModifyBasePiecePrice());

        modifyPriceOrderItemView.setPackageUpPercent(modifyPriceOrderItem.getPackageUpPercent());
        modifyPriceOrderItemView.setPieceUpPercent(modifyPriceOrderItem.getPieceUpPercent());

        modifyPriceOrderItemView.setPackageOpType(modifyPriceOrderItem.getPackageOpType());
        modifyPriceOrderItemView.setPieceOpType(modifyPriceOrderItem.getPieceOpType());

        modifyPriceOrderItemView.setAfterPackagePrice(modifyPriceOrderItem.getAfterPackagePrice());
        modifyPriceOrderItemView.setAfterPiecePrice(modifyPriceOrderItem.getAfterPiecePrice());

        modifyPriceOrderItemView.setBeforePriceType(modifyPriceOrderItem.getBeforePriceType());
        modifyPriceOrderItemView.setAfterPriceType(modifyPriceOrderItem.getAfterPriceType());

        modifyPriceOrderItemView.setBeforeMakeUpPercent(modifyPriceOrderItem.getBeforeMakeUpPercent());
        modifyPriceOrderItemView.setAfterMakeUpPercent(modifyPriceOrderItem.getAfterMakeUpPercent());

        modifyPriceOrderItemView.setTargetType(modifyPriceOrderItem.getTargetType());
        modifyPriceOrderItemView.setMemberTypeId(modifyPriceOrderItem.getMemberTypeId());
        if (!CollectionUtils.isEmpty(memberTypeIdToMemberTypeName) && StringUtils.hasText(modifyPriceOrderItem.getMemberTypeId())) {
            modifyPriceOrderItemView.setMemberTypeName(memberTypeIdToMemberTypeName.get(modifyPriceOrderItem.getMemberTypeId()));
        }

        modifyPriceOrderItemView.setBeforeDiscountType(modifyPriceOrderItem.getBeforeDiscountType());
        modifyPriceOrderItemView.setAfterDiscountType(modifyPriceOrderItem.getAfterDiscountType());

        modifyPriceOrderItemView.setBeforeDiscountValue(modifyPriceOrderItem.getBeforeDiscountValue());
        modifyPriceOrderItemView.setAfterDiscountValue(modifyPriceOrderItem.getAfterDiscountValue());

        modifyPriceOrderItemView.setStatus(modifyPriceOrderItem.getStatus());
        modifyPriceOrderItemView.setGoods(modifyPriceOrderItem.getAssembleGoodsSnap(hisManager));

        /***
         * 以前糟糕的设计,这里需要对 中药进行特殊处理
         * */
        modifyPriceOrderItemView.setProfitRat(modifyPriceOrderItem.getProfitRat());
        modifyPriceOrderItemView.setBeforeProfitRat(modifyPriceOrderItem.getBeforeProfitRat());

        if (modifyPriceOrderItem.getEffected() != null) {
            modifyPriceOrderItemView.setEffected(modifyPriceOrderItem.getEffected());
        } else {
            modifyPriceOrderItemView.setEffected(order.getEffected());
        }
        return modifyPriceOrderItemView;
    }

    private static ModifyPriceOrderLogView toModifyPriceOrderLogView(GoodsModifyPriceOrderLog modifyPriceOrderLog, OrganUtils organUtils, EmployeeUtils employeeUtils) {
        ModifyPriceOrderLogView modifyPriceOrderLogView = new ModifyPriceOrderLogView();
        modifyPriceOrderLogView.setId(modifyPriceOrderLog.getId());
        modifyPriceOrderLogView.setOrderId(modifyPriceOrderLog.getOrderId());
        modifyPriceOrderLogView.setAction(modifyPriceOrderLog.getAction());
        modifyPriceOrderLogView.setCreated(modifyPriceOrderLog.getCreated());
        modifyPriceOrderLogView.setCreatedUser(employeeUtils.getEmployeeView(modifyPriceOrderLog.getCreatedBy(), modifyPriceOrderLog.getChainId()));
        return modifyPriceOrderLogView;
    }

    public List<ModifyPriceOrderItemView> getModifyPriceOrderItemViewsByOrderIds(String chainId, String clinicId, List<Long> orderIds) {
        List<GoodsModifyPriceOrderItem> goodsModifyPriceOrderItems = goodsModifyPriceOrderItemRepository.findAllByClinicIdAndOrderIdInAndIsDeleted(clinicId, orderIds, 0);
        // 批量加载历史版本Goods
        GoodsHisVersionManager goodsHisVersionManager = new GoodsHisVersionManager(cisClinicService.getClinicConfig(chainId), goodsMapper, goodsRedisUtils);
        goodsHisVersionManager.loadGoodsSnapHisVersion(goodsModifyPriceOrderItems.stream().map(it -> {
            if (it.getGoodsSnap() != null) {
                it.getGoodsSnap().setHelperGoodsId(it.getGoodsId());
            }
            return it.getGoodsSnap();
        }).collect(Collectors.toList()));
        return goodsModifyPriceOrderItems.stream().map(e -> {
            ModifyPriceOrderItemView itemView = new ModifyPriceOrderItemView();
            itemView.setId(String.valueOf(e.getId()));
            itemView.setOrderId(e.getOrderId());
            itemView.setPackageCostPrice(e.getPackageCostPrice());
            itemView.setAvgPackageCostPrice(e.getAvgPackageCostPrice());
            itemView.setLastSupplierName(e.getLastSupplierName());
            itemView.setBeforePackagePrice(e.getBeforePackagePrice());
            itemView.setBeforePiecePrice(e.getBeforePiecePrice());
            itemView.setModifyBasePackagePrice(e.getModifyBasePackagePrice());
            itemView.setModifyBasePiecePrice(e.getModifyBasePiecePrice());
            itemView.setPackageUpPercent(e.getPackageUpPercent());
            itemView.setPieceUpPercent(e.getPieceUpPercent());
            itemView.setPackageOpType(e.getPackageOpType());
            itemView.setPieceOpType(e.getPieceOpType());
            itemView.setAfterPackagePrice(e.getAfterPackagePrice());
            itemView.setAfterPiecePrice(e.getAfterPiecePrice());
            //修改前后 定价了行
            itemView.setBeforePriceType(e.getBeforePriceType());
            itemView.setAfterPriceType(e.getAfterPriceType());
            //修改前后加成比例
            itemView.setBeforeMakeUpPercent(e.getBeforeMakeUpPercent());
            itemView.setAfterMakeUpPercent(e.getAfterMakeUpPercent());
            itemView.setStatus(e.getStatus());

            itemView.setGoods(e.getAssembleGoodsSnap(goodsHisVersionManager));
            itemView.setProfitRat(e.getProfitRat());
            itemView.setBeforeProfitRat(e.getBeforeProfitRat());
            return itemView;
        }).collect(Collectors.toList());
    }

    /**
     * 按改价条目，修改Goods和子店定价
     *
     * @param goods                 裸goods
     * @param goodsModifyPriceOrder 改价单
     * @param clinicConfig          发起改价操作的诊所配置
     * @param goodsModifyPriceItem  改价条目
     * @param employeeId            操作人Id
     * @param goodsLogList          输出参数 goodsLog
     * @param priceWrapper          输入现有的Goods的子店定价
     * @param updateGoodsPriceList  新增或需要修改的子店定价
     */
    private void doUpdatePriceByGoodsModifyPriceOrderItem(Goods goods,
                                                          GoodsModifyPriceOrder goodsModifyPriceOrder,
                                                          ClinicConfig clinicConfig,
                                                          GoodsModifyPriceOrderItem goodsModifyPriceItem,
                                                          String employeeId,
                                                          List<GoodsLog> goodsLogList,
                                                          ClinicGoodsPriceWrapper priceWrapper,
                                                          Map<String, String> memberTypeIdToMemberTypeName,//检查是否有效
                                                          List<GoodsPrice> updateGoodsPriceList) {
        /**
         * 单据已经被删除了
         * */
        if (goodsModifyPriceItem.getIsDeleted() == YesOrNo.YES) {
            sLogger.error("调价记录已经被删除:{}", goodsModifyPriceItem);
            return;
        }
        /**
         * 单据已经完成调价了
         * */
        if (goodsModifyPriceItem.getStatus() != GoodsModifyPriceOrderItem.Status.WAITING) {
            sLogger.error("无效的调价状态:{}", goodsModifyPriceItem);
            return;
        }
        if (!StringUtils.isEmpty(goodsModifyPriceItem.getMemberTypeId()) && memberTypeIdToMemberTypeName != null) {
            if (!memberTypeIdToMemberTypeName.containsKey(goodsModifyPriceItem.getMemberTypeId())) {
                goodsModifyPriceItem.setIsDeleted(YesOrNo.YES);
                UserFillUtils.fillLastModifiedBy(goodsModifyPriceItem, employeeId);
                return;
            }
        }

        GoodsMultiPriceView beforeMemberPrice = null;
        GoodsMultiPriceView afterMemberPrice = null;
        if (goodsModifyPriceItem.getTargetType() == GoodsPrice.PriceTargetType.MEMBER) {
            if (goodsModifyPriceItem.getBeforeDiscountType() != null) {
                beforeMemberPrice = new GoodsMultiPriceView();
                beforeMemberPrice.setMemberTypeId(goodsModifyPriceItem.getMemberTypeId());
                beforeMemberPrice.setMemberCardName(memberTypeIdToMemberTypeName.get(goodsModifyPriceItem.getMemberTypeId()));
                beforeMemberPrice.setDiscountType(goodsModifyPriceItem.getBeforeDiscountType());
                beforeMemberPrice.setDiscountValue(goodsModifyPriceItem.getBeforeDiscountValue());
                beforeMemberPrice.setPackagePrice(goodsModifyPriceItem.getBeforePackagePrice());
                beforeMemberPrice.setIsSaleLimit(goodsModifyPriceItem.getBeforeIsSaleLimit());
                beforeMemberPrice.setSaleLimitRule(goodsModifyPriceItem.getBeforeSaleLimitRule());
            }

            if (goodsModifyPriceItem.getAfterDiscountType() != null) {
                afterMemberPrice = new GoodsMultiPriceView();
                afterMemberPrice.setMemberTypeId(goodsModifyPriceItem.getMemberTypeId());
                afterMemberPrice.setMemberCardName(memberTypeIdToMemberTypeName.get(goodsModifyPriceItem.getMemberTypeId()));
                afterMemberPrice.setDiscountType(goodsModifyPriceItem.getAfterDiscountType());
                afterMemberPrice.setDiscountValue(goodsModifyPriceItem.getAfterDiscountValue());
                afterMemberPrice.setPackagePrice(goodsModifyPriceItem.getAfterPackagePrice());
                afterMemberPrice.setIsSaleLimit(goodsModifyPriceItem.getAfterIsSaleLimit());
                afterMemberPrice.setSaleLimitRule(goodsModifyPriceItem.getAfterSaleLimitRule());
            }
        }

        /**
         * 决定是改Goods上的价格还是子店定价
         * 1.单店，毫无疑问是改goods上的价格
         * 2.子店, 毫无疑问是改子店的价格
         * 3.总部，要看order上是否填写了影响门店
         *          没填影响门店就是改goods
         *          否则改指定门店
         * */
        GoodsSnapV3 beforeGoodsSnap = GoodsUtils.genLogGoodsSnap(goods, beforeMemberPrice, clinicConfig);//改价前goods快照
        boolean updateHeadClinicGoodsPrice = false;
        if (clinicConfig.isSingleMode()) {
            updateHeadClinicGoodsPrice = true;
        } else if (clinicConfig.isSubClinic()) {
            updateHeadClinicGoodsPrice = false;
        } else if (clinicConfig.isHeadClinic()) { //总部发起
            if (GoodsUtils.compareStrEqual(goodsModifyPriceOrder.getClinicId(), goodsModifyPriceOrder.getChainId())) {
                updateHeadClinicGoodsPrice = true;
            } else {
                /**
                 * 总部，要看order上是否填写了影响门店
                 *          没填影响门店就是改goods
                 *          否则改指定门店
                 * */
                if (goodsModifyPriceOrder.getExtendInfos() == null
                        || CollectionUtils.isEmpty(goodsModifyPriceOrder.getExtendInfos().getAffectedClinicList())) {
                    updateHeadClinicGoodsPrice = true;
                }
            }
        }

        /**
         * 总部维护子店价格的改价单一定是操作GoodsPrice
         * */
        if (goodsModifyPriceOrder.getOpType() == GoodsModifyPriceOrder.OpType.OP_TYPE_SET_CHAIN_MOD_SUB_PRICE) {
            updateHeadClinicGoodsPrice = false;
        }

        /**
         * 改总部Goods上的价格
         * */
        if (updateHeadClinicGoodsPrice) {
            int chainSubPriceFlag = GoodsPrice.SubPriceFlag.NOT_INIT; //总部的多定价，这个字段无意义用-1
            MultiGoodsPriceUpdateResult updateResult = null;
            if (goodsModifyPriceItem.getTargetType() == GoodsPrice.PriceTargetType.GOODS) {
                GoodsMultiPriceView headClinicNormalPrice = goodsModifyPriceItem.toUpsertMultiPriceView(null, null);
                updateResult = createOrUpdateMultiGoodsPriceList(clinicConfig, goods, priceWrapper.getGoodsPriceList(clinicConfig.getClinicId(), goods.getId()), null, headClinicNormalPrice, chainSubPriceFlag, employeeId);
            } else if (goodsModifyPriceItem.getTargetType() == GoodsPrice.PriceTargetType.MEMBER) {
                GoodsPrice goodsPrice = priceWrapper.getByFullUniqKey(GoodsPrice.fullUniqKey(goods.getOrganId(), goods.getId(), goodsModifyPriceItem.getTargetType(), null, null, goodsModifyPriceItem.getMemberTypeId(), goodsModifyPriceItem.getBeforeDiscountType()));
                GoodsMultiPriceView headClinicMemberPrice = goodsModifyPriceItem.toUpsertMultiPriceView(goodsPrice, null);
                List<GoodsPrice> priceList = goodsPrice != null ? Lists.newArrayList(goodsPrice) : new ArrayList<>();
                updateResult = createOrUpdateMultiGoodsPriceList(clinicConfig, goods, priceList, Collections.singletonList(headClinicMemberPrice), null, chainSubPriceFlag, employeeId);
            }

            if (updateResult != null && updateResult.hasChange()) {
                updateGoodsPriceList.addAll(updateResult.getUpdatePrices());
                updateGoodsPriceList.addAll(updateResult.getNewPrices());
                updateGoodsPriceList.addAll(updateResult.getDeletePrices());

                priceWrapper.addAllGoodsPrice(updateResult.getNewPrices());
            }
        } else {
            /***
             * 改子店价格
             * */
            GoodsPrice goodsPrice = null;
            /**
             * 要删除子店定价
             * */
            if (goodsModifyPriceItem.getPackageOpType() == GoodsModifyPriceOrder.OpType.OP_TYPE_SET_CANCEL_CLINIC_PRICE) {
                goodsPrice = priceWrapper.getByFullUniqKey(GoodsPrice.fullUniqKey(goodsModifyPriceItem.getClinicId(), goods.getId(), goodsModifyPriceItem.getTargetType(), null, null, goodsModifyPriceItem.getMemberTypeId(), goodsModifyPriceItem.getBeforeDiscountType()));
                int subPriceFlag = GoodsPrice.SubPriceFlag.NOT_INIT;// 已经知道是删除了，所以传啥都行
                List<GoodsPrice> priceList = goodsPrice != null ? Lists.newArrayList(goodsPrice) : new ArrayList<>();
                GoodsPrice normalGoodsPrice = priceWrapper.getNormalPrice(goodsModifyPriceItem.getClinicId(), goods.getId());
                if (normalGoodsPrice != null) {
                    GoodsMultiPriceView normalGoodsPriceView = normalGoodsPrice.toGoodsMultiPriceView();
                    normalGoodsPriceView.setOpType(CreateOrUpdateGoodsFeeTypeReq.OpType.DELETE);
                    MultiGoodsPriceUpdateResult updateResult = createOrUpdateMultiGoodsPriceList(clinicConfig, goods, priceList, null, normalGoodsPriceView, subPriceFlag, employeeId);
                    if (updateResult.hasChange()) {
                        updateGoodsPriceList.addAll(updateResult.getDeletePrices());
                    }
                } else {
                    log.warn("删除 goodsId:{} clinicId:{} 子店定价，但是没有找到子店定价", goods.getId(), goodsModifyPriceItem.getClinicId());
                }
            } else {
                /****************
                 * 子店发起
                 * 只改子店自己的goodsPrice
                 * */
                if (goodsModifyPriceOrder.getExtendInfos() == null
                        || CollectionUtils.isEmpty(goodsModifyPriceOrder.getExtendInfos().getAffectedClinicList())) {
                    goodsPrice = priceWrapper.getByFullUniqKey(GoodsPrice.fullUniqKey(clinicConfig.getClinicId(), goods.getId(), goodsModifyPriceItem.getTargetType(), null, null, goodsModifyPriceItem.getMemberTypeId(), goodsModifyPriceItem.getBeforeDiscountType()));
                    int subPriceFlag = getSubPriceFlag(clinicConfig, goodsModifyPriceItem, Optional.ofNullable(goodsPrice).map(GoodsPrice::getSubPriceFlag).orElse(null));
                    List<GoodsMultiPriceView> goodsMultiPriceViewList = null;
                    GoodsMultiPriceView normalGoodsPriceView = null;
                    if (goodsModifyPriceItem.getTargetType() == GoodsPrice.PriceTargetType.GOODS) {
                        normalGoodsPriceView = goodsModifyPriceItem.toUpsertMultiPriceView(goodsPrice, null);
                    } else {
                        GoodsMultiPriceView clientSubClinicPrice = goodsModifyPriceItem.toUpsertMultiPriceView(goodsPrice, null);
                        if (clientSubClinicPrice != null) {
                            goodsMultiPriceViewList = Lists.newArrayList(clientSubClinicPrice);
                        }
                    }
                    List<GoodsPrice> priceList = goodsPrice != null ? Lists.newArrayList(goodsPrice) : new ArrayList<>();
                    MultiGoodsPriceUpdateResult updateResult = createOrUpdateMultiGoodsPriceList(clinicConfig, goods, priceList, goodsMultiPriceViewList, normalGoodsPriceView, subPriceFlag, employeeId);

                    if (updateResult.hasChange()) {
                        updateGoodsPriceList.addAll(updateResult.getUpdatePrices());
                        updateGoodsPriceList.addAll(updateResult.getNewPrices());

                        priceWrapper.addAllGoodsPrice(updateResult.getNewPrices());
                    }
                    updateClinicConfigIndependentPricingFlag(clinicConfig, clinicConfig.getClinicId());
                } else {
                    if (goodsModifyPriceOrder.getSourceType() == GoodsModifyPriceOrder.SourceFrom.FROM_CHAIN_GOODS_BATCH) {
                        // 总部给每个门店定价的调价单，只在这个门店生效，单子上面的影响门店只是个提示作用
                        goodsPrice = priceWrapper.getByFullUniqKey(GoodsPrice.fullUniqKey(goodsModifyPriceItem.getClinicId(), goods.getId(), goodsModifyPriceItem.getTargetType(), null, null, goodsModifyPriceItem.getMemberTypeId(), goodsModifyPriceItem.getBeforeDiscountType()));
                        int subPriceFlag = getSubPriceFlag(clinicConfig, goodsModifyPriceItem, Optional.ofNullable(goodsPrice).map(GoodsPrice::getSubPriceFlag).orElse(null));
                        List<GoodsPrice> priceList = goodsPrice != null ? Lists.newArrayList(goodsPrice) : new ArrayList<>();
                        List<GoodsMultiPriceView> goodsMultiPriceViewList = null;
                        GoodsMultiPriceView normalGoodsPriceView = null;
                        if (goodsModifyPriceItem.getTargetType() == GoodsPrice.PriceTargetType.GOODS) {
                            normalGoodsPriceView = goodsModifyPriceItem.toUpsertMultiPriceView(goodsPrice, null);
                        } else {
                            GoodsMultiPriceView clientSubClinicPrice = goodsModifyPriceItem.toUpsertMultiPriceView(goodsPrice, null);
                            if (clientSubClinicPrice != null) {
                                goodsMultiPriceViewList = Lists.newArrayList(clientSubClinicPrice);
                            }
                        }
                        MultiGoodsPriceUpdateResult updateResult = createOrUpdateMultiGoodsPriceList(clinicConfig, goods, priceList, goodsMultiPriceViewList, normalGoodsPriceView, subPriceFlag, employeeId);

                        if (updateResult.hasChange()) {
                            updateGoodsPriceList.addAll(updateResult.getUpdatePrices());
                            updateGoodsPriceList.addAll(updateResult.getNewPrices());

                            priceWrapper.addAllGoodsPrice(updateResult.getNewPrices());
                        }
                        updateClinicConfigIndependentPricingFlag(clinicConfig, clinicConfig.getClinicId());
                    } else {
                        /****************
                         * 总部发起
                         * 一个Item改了order上定义的影响门店
                         * */
                        for (String modClinicId : goodsModifyPriceOrder.getExtendInfos().getAffectedClinicList()) {
                            goodsPrice = priceWrapper.getByFullUniqKey(GoodsPrice.fullUniqKey(modClinicId, goods.getId(), goodsModifyPriceItem.getTargetType(), null, null, goodsModifyPriceItem.getMemberTypeId(), goodsModifyPriceItem.getBeforeDiscountType()));
                            int subPriceFlag = getSubPriceFlag(clinicConfig, goodsModifyPriceItem, Optional.ofNullable(goodsPrice).map(GoodsPrice::getSubPriceFlag).orElse(null));
                            List<GoodsPrice> priceList = goodsPrice != null ? Lists.newArrayList(goodsPrice) : new ArrayList<>();
                            List<GoodsMultiPriceView> goodsMultiPriceViewList = null;
                            GoodsMultiPriceView normalGoodsPriceView = null;
                            if (goodsModifyPriceItem.getTargetType() == GoodsPrice.PriceTargetType.GOODS) {
                                normalGoodsPriceView = goodsModifyPriceItem.toUpsertMultiPriceView(goodsPrice, null);
                            } else {
                                GoodsMultiPriceView clientSubClinicPrice = goodsModifyPriceItem.toUpsertMultiPriceView(goodsPrice, null);
                                if (clientSubClinicPrice != null) {
                                    goodsMultiPriceViewList = Lists.newArrayList(clientSubClinicPrice);
                                }
                            }
                            MultiGoodsPriceUpdateResult updateResult = createOrUpdateMultiGoodsPriceList(clinicConfig, goods, priceList, goodsMultiPriceViewList, normalGoodsPriceView, subPriceFlag, employeeId);

                            if (updateResult.hasChange()) {
                                updateGoodsPriceList.addAll(updateResult.getUpdatePrices());
                                updateGoodsPriceList.addAll(updateResult.getNewPrices());

                                priceWrapper.addAllGoodsPrice(updateResult.getNewPrices());
                            }
                            updateClinicConfigIndependentPricingFlag(clinicConfig, clinicConfig.getClinicId());
                        }
                    }
                }


            }
            flag = goodsPrice == null;
        }
        //8888
        goodsModifyPriceItem.setStatus(GoodsModifyPriceOrderItem.Status.FINISHED);
        goodsModifyPriceItem.setEffected(Instant.now());

        if (clinicConfig.isHeadClinic()) {
            GoodsLog goodsLog = GoodsUtils.createUpdateGoodsLog(GoodsLog.ACTION_CHANGE_PRICE, beforeGoodsSnap, goods, afterMemberPrice, clinicConfig.getClinicId(), employeeId, clinicConfig);
            //总部改价，设置影响子店
            List<Organ> organList = cisClinicService.getSubClinicList(clinicConfig.getChainId());
            List<GoodsPrice> goodsPriceList = goodsPriceRepository.findAllByGoodsId(goods.getId());
            List<String> pirceOrganIds = goodsPriceList.stream().map(m -> m.getOrganId()).collect(Collectors.toList());
            List<String> affectSubClinicList = organList.stream().filter(f -> !pirceOrganIds.contains(f.getId())).map(m -> m.getId())
                    .collect(Collectors.toList());
            goodsLog.setAffectSubClinic(affectSubClinicList);
            fetchPriceFromGoodsPriceItem(goodsModifyPriceItem, goodsLog);
            goodsLogList.add(goodsLog);
        } else {
            //子店改价日志记录太过于笼统
            String action = GoodsLog.ACTION_CLINIC_PRICE;
            //判断是否为总部价改为自主定价
            if (goodsModifyPriceOrder.getOpType() == GoodsModifyPriceOrder.OpType.OP_TYPE_SET_CHAIN_MOD_SUB_PRICE) {
                action = GoodsLog.CHAIN_MODIFY_FOR_CHINIC;
                if (goodsModifyPriceItem.getSubPriceFlag() != null && goodsModifyPriceItem.getSubPriceFlag() == GoodsPrice.SubPriceFlag.NO_PRIV_AND_NO_SUB_PRICE) {
                    goods.setClinicSelfGoodsPrice(null);
                }
            }
            GoodsLog goodsLog = GoodsUtils.createUpdateGoodsLog(action, beforeGoodsSnap, goods, afterMemberPrice, clinicConfig.getClinicId(), employeeId, clinicConfig);
            //如果之前是进价加成，设置为总部价
            if (goodsModifyPriceItem.getSubPriceFlag() != null && goodsModifyPriceItem.getSubPriceFlag() == GoodsPrice.SubPriceFlag.NO_PRIV_AND_NO_SUB_PRICE
                    && goodsModifyPriceItem.getPackageOpType() == GoodsModifyPriceOrder.OpType.OP_TYPE_SET_CANCEL_CLINIC_PRICE) {
                if (goodsModifyPriceItem.getBeforePackagePrice() != null) {
                    goodsLog.getAfter().setRemark("总部价");
                    goodsLog.getBefore().setRemark("单独定价-固定售价");
                }
                if (goodsModifyPriceItem.getBeforeMakeUpPercent() != null) {
                    goodsLog.getAfter().setRemark("总部价");
                    goodsLog.getBefore().setRemark("单独定价-进价加成");
                }
            }
            if (flag && goodsModifyPriceItem.getSubPriceFlag() != null && goodsModifyPriceItem.getSubPriceFlag() == GoodsPrice.SubPriceFlag.NO_PRIV_AND_HAS_SUB_PRICE
                    && goodsModifyPriceItem.getPackageOpType() == GoodsModifyPriceOrder.OpType.OP_TYPE_SET_BY_MAKE_UP) {
                goodsLog.getAfter().setRemark("单独定价-进价加成");
                goodsLog.getBefore().setRemark("总部价");
            }
            if (flag && goodsModifyPriceItem.getSubPriceFlag() != null && goodsModifyPriceItem.getSubPriceFlag() == GoodsPrice.SubPriceFlag.NO_PRIV_AND_HAS_SUB_PRICE
                    && goodsModifyPriceItem.getPackageOpType() == GoodsModifyPriceOrder.OpType.OP_TYPE_COMMON_SET) {
                goodsLog.getAfter().setRemark("单独定价-固定售价");
                goodsLog.getBefore().setRemark("总部价");
            }
            if (goodsModifyPriceItem.getSubPriceFlag() != null && goodsModifyPriceItem.getSubPriceFlag() == GoodsPrice.SubPriceFlag.NO_PRIV_AND_HAS_SUB_PRICE
                    && goodsModifyPriceItem.getPackageOpType() == GoodsModifyPriceOrder.OpType.OP_TYPE_SET_CANCEL_CLINIC_PRICE) {
                goodsLog.getAfter().setRemark("总部价");
                goodsLog.getBefore().setRemark("单独定价-固定售价");
            }
            fetchPriceFromGoodsPriceItem(goodsModifyPriceItem, goodsLog);
            goodsLogList.add(goodsLog);
        }
    }

    /**
     * 日志前后金额不对，这儿从改价单获取
     *
     * @param item
     */
    private void fetchPriceFromGoodsPriceItem(GoodsModifyPriceOrderItem item, GoodsLog goodsLog) {
        //beforeGoodsSnap 与 goods的金额都可能出错，这儿从改价单上获取。
        goodsLog.getBefore().setPiecePrice(item.getBeforePiecePrice());
        goodsLog.getBefore().setPackagePrice(item.getBeforePackagePrice());
        goodsLog.getBefore().setPriceMakeupPercent(item.getBeforeMakeUpPercent());
        goodsLog.getBefore().setPriceType(item.getBeforePriceType());

        goodsLog.getAfter().setPackagePrice(item.getAfterPackagePrice());
        goodsLog.getAfter().setPiecePrice(item.getAfterPiecePrice());
        goodsLog.getAfter().setPriceMakeupPercent(item.getAfterMakeUpPercent());
        goodsLog.getAfter().setPriceType(item.getAfterPriceType());
    }

    private static int getSubPriceFlag(ClinicConfig clinicConfig, GoodsModifyPriceOrderItem goodsModifyPriceItem, Integer beforeSubPriceFlag) {
        int subPriceFlag = GoodsPrice.SubPriceFlag.NOT_INIT;
        if (clinicConfig.isAbcPharmacy()) {
            if (goodsModifyPriceItem.getSubPriceFlag() == null) {
                subPriceFlag = GoodsPrice.SubPriceFlag.HAS_PRIV_AND_HAS_SUB_PRICE_SUBCLINIC_SET;
            } else {
                subPriceFlag = goodsModifyPriceItem.getSubPriceFlag();
            }
        } else if (beforeSubPriceFlag == null) {
            subPriceFlag = GoodsPrice.SubPriceFlag.HAS_PRIV_AND_HAS_SUB_PRICE_SUBCLINIC_SET;
        }

        return subPriceFlag;
    }

    private void updateClinicConfigIndependentPricingFlag(ClinicConfig clinicConfig, String modClinicId) {
        if (clinicConfig.getIndependentPricing() == GoodsPrice.ClinicPriceFlag.NO_PRIV_AND_NO_SUB_PRICE) {
            goodsClinicConfigRepository.findByClinicId(modClinicId).ifPresent(goodsClinicConfig -> {
                if (clinicConfig.isAbcPharmacy()) {
                    /**
                     * 产品文档
                     * A连锁统一定价 → C门店单独定价-门店定
                     *      ●门店已建档（入库）商品价格同步：
                     *              ○连锁统一定价商品：总部价格作为初始价格，切换后门店价格不受总部价格变化影响，需单独设置
                     *              ○单独设置定价模式商品：维持原有售价，不受模式切换影响
                     *      ●门店未建档商品价格同步：门店入库成功建档时，若总部已为门店定义价格，则执行总部定价价格；若总部未为门店定义价格，则同步总部此刻售价为门店初始价格
                     * */
                    goodsClinicConfig.setIndependentPricing(GoodsPrice.ClinicPriceFlag.HAS_PRIV_AND_HAS_SUB_PRICE_BUT_SUBPRICE_MAY_NOT_BORN_NOW);
                } else {
                    goodsClinicConfig.setIndependentPricing(GoodsPrice.ClinicPriceFlag.NO_PRIV_AND_HAS_SUB_PRICE);
                }
                GoodsUtils.runAfterTransaction(() -> goodsRedisUtils.reloadChainGoodsConfig(clinicConfig.getChainId(), clinicConfig.getClinicId()));
            });
        }
    }


    /***
     * 产品【调整到目标毛利润】 10%
     *      1.在当前这一刻 ，用当前这一刻 批次组成的库存量 ，通过利润率算一个销售价格
     *      eg  (批次1,10,1,10),(批次2,10,1000,10000)--> 这一刻的总成本 * 10% = 得到这一刻的售价
     *      如果没有批次组成的变更，所有这些批次买完是能赚10%的钱。
     *      但是如果在某一个新入库一个批次，成本不一样，用户没来得及即使调整10%，用上面得到的售价继续在这个批次买，这个批次赚不到10%的钱
     *      2.如果有入库，新批次产生，需要 【调整到目标毛利润】 10%，用户需要重新再按10%来调一次
     *      3.如果是子店的，只算子店批次
     *      4.如果是总部：算除开可以自主定价之外门店的批次的成本来算利润率
     *      5.停售批次 是否要踢掉，（不买，感觉应该要踢掉，不买的批次有数量有成本，但是这些不买不会产生利润）?
     *      6.返回null 此时无库存， 【调整到目标毛利润】 将调价异常不成功，分母都是0。是悄咪咪不调了还是提示出来？
     *      7.接着6 发起调价有库存，审核完成的库存没了，怎么干？
     * */
    public static BigDecimal calPackagePriceByProfitRatAtNowByChain(
            BigDecimal wantedProfitRat,
            Map<String, BigDecimal> clinicIdToSubPrice, //这里面是有子店定价，不需要算到成本里面调价的
            GoodsRedisCache goods
    ) {
        //毛利率不能超过100 售价 = 成本/（1-毛利）
        if (wantedProfitRat == null || wantedProfitRat.compareTo(BigDecimal.valueOf(100)) >= 0) {
            return null;
        }
        //没有库存直接退出
        if (CollectionUtils.isEmpty(goods.getKeyToGoodsStockRedisCache())) {
            return null;
        }


        AtomicReference<BigDecimal> totalCost = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> totalCount = new AtomicReference<>(BigDecimal.ZERO);

        goods.getKeyToGoodsStockRedisCache().forEach((key, goodsStock) -> {
            if (goodsStock.isLockingRecord()) {
                return;
            }
            if (clinicIdToSubPrice != null && clinicIdToSubPrice.containsKey(goodsStock.getClinicId())) {
                return;
            }
            GoodsStockMountTrible stdCount = GoodsStockUtils.toStandardStockCount(goodsStock.getPieceCount(), goodsStock.getPackageCount(), goods.getPieceNum(), goods.getTypeId());
            totalCount.set(GoodsStockUtils.add(totalCount.get(), stdCount.getOverallPackageCount()));
            totalCost.set(GoodsStockUtils.add(totalCost.get(), goodsStock.getLeftCost()));
        });

        //
        if (totalCount.get().compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }

        /**
         * 售价 = 成本/（1-毛利）
         * 单价
         * */
        BigDecimal calPricePercent = BigDecimal.ONE.subtract(wantedProfitRat.divide(BigDecimal.valueOf(100), 4, RoundingMode.UP));
        return totalCost.get().divide(calPricePercent, GoodsStockUtils.ROUND_NUM_TEN, RoundingMode.UP).divide(totalCount.get(), GoodsStockUtils.ROUND_NUM, RoundingMode.UP);
    }

    public static BigDecimal calPackagePriceByProfitRatAtNowByClinic(
            BigDecimal wantedProfitRat,
            String clinicId,
            GoodsRedisCache goods
    ) {
        //毛利率不能超过100 售价 = 成本/（1-毛利）
        if (wantedProfitRat == null || wantedProfitRat.compareTo(BigDecimal.valueOf(100)) >= 0) {
            return null;
        }
        //没有库存直接退出
        if (CollectionUtils.isEmpty(goods.getKeyToGoodsStockRedisCache())) {
            return null;
        }

        AtomicReference<BigDecimal> totalCost = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> totalCount = new AtomicReference<>(BigDecimal.ZERO);

        goods.getKeyToGoodsStockRedisCache().forEach((key, goodsStock) -> {
            if (goodsStock.isLockingRecord()) {
                return;
            }
            if (!GoodsUtils.compareStrEqual(goodsStock.getClinicId(), clinicId)) {
                return;
            }
            GoodsStockMountTrible stdCount = GoodsStockUtils.toStandardStockCount(goodsStock.getPieceCount(), goodsStock.getPackageCount(), goods.getPieceNum(), goods.getTypeId());
            totalCount.set(GoodsStockUtils.add(totalCount.get(), stdCount.getOverallPackageCount()));
            totalCost.set(GoodsStockUtils.add(totalCost.get(), goodsStock.getLeftCost()));
        });

        //
        if (totalCount.get().compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }

        /**
         * 售价 = 成本/（1-毛利）
         * 单价
         * */
        BigDecimal calPricePercent = BigDecimal.ONE.subtract(wantedProfitRat.divide(BigDecimal.valueOf(100), 4, RoundingMode.UP));
        return totalCost.get().divide(calPricePercent, GoodsStockUtils.ROUND_NUM_TEN, RoundingMode.UP).divide(totalCount.get(), GoodsStockUtils.ROUND_NUM, RoundingMode.UP);
    }

    /**
     * 2024 药店管家产品新的计算利润率方法：计算每个库存量值的利润率(eg.库存量 54盒和55盒会实时计算出来)
     * 问题：
     * 1.加入调价单时有库存，有利润率，审核完成时无库存了，调价单利润率没了。 产品确认这不是bug
     * 2.代价代配 先用后入库，库存负数？？利润率计算？
     * <p>
     * f(x) = 1 - a/x ;(0=< x <=库存量)
     * = ( 总剩余售价 - 总剩余成本 )/总剩余售价
     *
     * @param goodsPrice      总部goods的价格
     * @param keyToGoodsStock 这一个goods，全连锁的所有批次。里面有当前计算这一刻的库存量和成本。售价从前两个参数
     */
    public static BigDecimal calProfit(BigDecimal goodsPrice,
//                                       Map<String, BigDecimal> clinicIdToSubPrice,
                                       GoodsRedisCache goods,
                                       GoodsModifyPriceOrder goodsModifyPriceOrder,
                                       GoodsModifyPriceOrderItem modifyPriceOrderItem,
                                       Map<String, GoodsStockRedisCache> keyToGoodsStock) {
        if (goodsPrice == null) {
            return null;
        }

        //没有库存直接退出
        if (CollectionUtils.isEmpty(keyToGoodsStock)) {
            if (goodsPrice.compareTo(BigDecimal.ZERO) != 0 && goods.getLastPackageCostPrice() != null) {
                return goodsPrice.subtract(goods.getLastPackageCostPrice()).multiply(BigDecimal.valueOf(100)).divide(goodsPrice, 2, RoundingMode.UP);
            } else {
                return null;
            }
        }

        AtomicReference<BigDecimal> totalSalePrice = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> totalCost = new AtomicReference<>(BigDecimal.ZERO);

        keyToGoodsStock.forEach((key, goodsStock) -> {
            // 锁
            if (goodsStock.getType() != null && goodsStock.getType() > 0) {
                return;
            }
            BigDecimal packagePrice = goodsPrice;
            GoodsStockMountTrible stdCount = GoodsStockUtils.toStandardStockCount(goodsStock.getPieceCount(), goodsStock.getPackageCount(), goods.getPieceNum(), goods.getTypeId());
            //药品资料总部给门店调整价格
            if (goodsModifyPriceOrder != null && modifyPriceOrderItem != null) {
                if (goodsModifyPriceOrder.getSourceType() == GoodsModifyPriceOrder.SourceFrom.FROM_BATCH_PRICE) {
                    if (!GoodsUtils.compareStrEqual(modifyPriceOrderItem.getClinicId(), goodsStock.getClinicId())) {
                        return;
                    }
                } else { //影响部分门店
                    if (goodsModifyPriceOrder.getExtendInfos() != null
                            && goodsModifyPriceOrder.getExtendInfos().getAffectedClinicList() != null
                            && goodsModifyPriceOrder.getExtendInfos().getAffectedClinicList().stream().noneMatch(it -> GoodsUtils.compareStrEqual(it, goodsStock.getClinicId()))) {
                        return;
                    }
                }

            }

            totalSalePrice.set(GoodsStockUtils.add(totalSalePrice.get(), GoodsStockUtils.multiply(stdCount.getOverallPackageCount(), packagePrice)));
            totalCost.set(GoodsStockUtils.add(totalCost.get(), goodsStock.getLeftCost()));
        });

        if (totalSalePrice.get().compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }

        return totalSalePrice.get().subtract(totalCost.get()).multiply(BigDecimal.valueOf(100)).divide(totalSalePrice.get(), GoodsStockUtils.DISP_ROUND_NUM, RoundingMode.UP);
    }

    public static GoodsModifyPriceOrderItem createGoodsPriceItem(Long itemId,
                                                                 ClinicConfig clinicConfig,
                                                                 GoodsModifyPriceOrder goodsModifyPriceOrder,
                                                                 CreateModifyPriceOrderReq.GoodsModifyPriceItem clientPriceItem,
                                                                 GoodsRedisCache goodsRedisCache,
                                                                 String employeeId
    ) {
        GoodsModifyPriceOrderItem modifyPriceOrderItem = new GoodsModifyPriceOrderItem();
        modifyPriceOrderItem.setId(AbcIdUtils.getUUIDLong());
        modifyPriceOrderItem.setItemId(itemId);
        //goods改价日志
        modifyPriceOrderItem.setChainId(goodsModifyPriceOrder.getChainId());
        modifyPriceOrderItem.setClinicId(goodsModifyPriceOrder.getClinicId());
        modifyPriceOrderItem.setOrderId(goodsModifyPriceOrder.getId());
        modifyPriceOrderItem.setGoodsId(goodsRedisCache.getId());
        modifyPriceOrderItem.setTargetType(clientPriceItem.getTargetType());
        modifyPriceOrderItem.setMemberTypeId(clientPriceItem.getMemberTypeId());

        modifyPriceOrderItem.setLastSupplierName(clientPriceItem.getLastSupplierName());
        Integer discountType = null;
        BigDecimal discountValue = null;
        Optional<CreateModifyPriceOrderReq.ModifyPriceItem> updatePackagePriceItemOpt = Optional.ofNullable(clientPriceItem.getUpdatePackagePrice());
        Optional<CreateModifyPriceOrderReq.ModifyPriceItem> updatePiecePriceItemOpt = Optional.ofNullable(clientPriceItem.getUpdatePiecePrice());
        if (clinicConfig.isAbcPharmacy() && Objects.equals(clientPriceItem.getTargetType(), GoodsPrice.PriceTargetType.MEMBER)) {
            discountType = updatePackagePriceItemOpt.map(CreateModifyPriceOrderReq.ModifyPriceItem::getBeforeDiscountType).orElse(null);
            discountValue = updatePackagePriceItemOpt.map(CreateModifyPriceOrderReq.ModifyPriceItem::getBeforeDiscountValue).orElse(null);
            // 设置会员价限购策略
            modifyPriceOrderItem.setBeforeIsSaleLimit(updatePackagePriceItemOpt.map(CreateModifyPriceOrderReq.ModifyPriceItem::getBeforeIsSaleLimit).orElse(0));
            modifyPriceOrderItem.setBeforeSaleLimitRule(updatePackagePriceItemOpt.map(CreateModifyPriceOrderReq.ModifyPriceItem::getBeforeSaleLimitRule).orElse(null));
        }
        /*
            这里优先用 modifyPriceItem 中的 beforePrice 中的值，如果没有才使用 goodsRedisCache 中的值
         */
        modifyPriceOrderItem.setBeforePackagePrice(updatePackagePriceItemOpt.map(CreateModifyPriceOrderReq.ModifyPriceItem::getBeforePrice).orElse(goodsRedisCache.getPackagePrice()));
        modifyPriceOrderItem.setBeforePiecePrice(updatePiecePriceItemOpt.map(CreateModifyPriceOrderReq.ModifyPriceItem::getBeforePrice).orElse(goodsRedisCache.getPiecePrice()));
        modifyPriceOrderItem.setBeforeDiscountType(discountType);
        modifyPriceOrderItem.setBeforeDiscountValue(discountValue);
        modifyPriceOrderItem.setBeforeMakeUpPercent(goodsRedisCache.getPriceMakeupPercent());
        modifyPriceOrderItem.setBeforePriceType(goodsRedisCache.getPriceType()); //改前定价模式
        modifyPriceOrderItem.setPackageOpType(clientPriceItem.getUpdatePackagePrice().getOpType());
        if (clientPriceItem.getUpdatePackagePrice().getPriceType() != null && clientPriceItem.getUpdatePackagePrice().getPriceType() != 0) {
            modifyPriceOrderItem.setAfterPriceType(clientPriceItem.getUpdatePackagePrice().getPriceType());
        } else {
            modifyPriceOrderItem.setAfterPriceType(GoodsConst.PriceType.PRICE);
        }
        //待生效
        modifyPriceOrderItem.setStatus(GoodsModifyPriceOrderItem.Status.WAITING);
        if (goodsRedisCache.checkComposeFlag(GoodsConst.ComposeFlag.PRICING)) {
            modifyPriceOrderItem.setStatus(GoodsModifyPriceOrderItem.Status.FAILED_PRICING);
        }

        /**
         * 改进价加成
         * */
        if (modifyPriceOrderItem.getPackageOpType() == GoodsModifyPriceOrder.OpType.OP_TYPE_SET_BY_MAKE_UP) {
            //进价加成比例
            modifyPriceOrderItem.setAfterMakeUpPercent(clientPriceItem.getUpdatePackagePrice().getAfterUpPercent());
            modifyPriceOrderItem.setProfitRat(modifyPriceOrderItem.getAfterMakeUpPercent());
        } else {
            modifyPriceOrderItem.setModifyBasePackagePrice(clientPriceItem.getUpdatePackagePrice().getBeforePrice());
            //调价比例
            modifyPriceOrderItem.setPackageUpPercent(clientPriceItem.getUpdatePackagePrice().getAfterUpPercent());
            modifyPriceOrderItem.setAfterPackagePrice(clientPriceItem.getUpdatePackagePrice().getAfterPrice());
            modifyPriceOrderItem.setAfterDiscountType(clientPriceItem.getUpdatePackagePrice().getAfterDiscountType());
            modifyPriceOrderItem.setAfterDiscountValue(clientPriceItem.getUpdatePackagePrice().getAfterDiscountValue());
            modifyPriceOrderItem.setAfterIsSaleLimit(clientPriceItem.getUpdatePackagePrice().getAfterIsSaleLimit());
            modifyPriceOrderItem.setAfterSaleLimitRule(clientPriceItem.getUpdatePackagePrice().getAfterSaleLimitRule());

            if (clientPriceItem.getUpdatePiecePrice() != null) {
                modifyPriceOrderItem.setModifyBasePiecePrice(clientPriceItem.getUpdatePiecePrice().getBeforePrice());
                modifyPriceOrderItem.setPieceUpPercent(clientPriceItem.getUpdatePiecePrice().getAfterUpPercent());
                modifyPriceOrderItem.setAfterPiecePrice(clientPriceItem.getUpdatePiecePrice().getAfterPrice());
                modifyPriceOrderItem.setPieceOpType(clientPriceItem.getUpdatePiecePrice().getOpType());
            }
        }

        modifyPriceOrderItem.setAvgPackageCostPrice(clientPriceItem.getAvgPackageCostPrice());
        if (modifyPriceOrderItem.getAvgPackageCostPrice() == null) {
            modifyPriceOrderItem.setAvgPackageCostPrice(goodsRedisCache.getAvgPackageCostPrice());
        }

        modifyPriceOrderItem.setPackageCostPrice(clientPriceItem.getPackageCostPrice());
        if (modifyPriceOrderItem.getPackageCostPrice() == null) {
            modifyPriceOrderItem.setPackageCostPrice(goodsRedisCache.getLastPackageCostPrice());
        }

        modifyPriceOrderItem.setBeforeProfitRat(clientPriceItem.getBeforeProfitRat());
        if (clientPriceItem.getBeforeProfitRat() == null) {
            modifyPriceOrderItem.setBeforeProfitRat(calProfit(
                    modifyPriceOrderItem.getBeforePackagePrice(),
                    goodsRedisCache,
                    goodsModifyPriceOrder,
                    modifyPriceOrderItem,
                    goodsRedisCache.getKeyToGoodsStockRedisCache()
            ));
        }

        //用户提交算费的利润率 可能是库存量20算出来的
        modifyPriceOrderItem.setProfitRat(clientPriceItem.getProfitRat());
        if (clientPriceItem.getProfitRat() == null) {
            // 如果前端没传，后台自己算一下。 走到这个分钟大概率有出bug。
            // 用户提交的价格(算价格时，比如库存20得到的利润率)，
            // 提交后台这时，库存只有19，库存量去算，会得到一个新的利润率
            modifyPriceOrderItem.setProfitRat(calProfit(
                    //用户提交的价格
                    clientPriceItem.getUpdatePackagePrice().getAfterPrice(),
                    goodsRedisCache,
                    goodsModifyPriceOrder,
                    modifyPriceOrderItem,
                    goodsRedisCache.getKeyToGoodsStockRedisCache()
            ));
        }
        /**
         * 解决药店总部给子店定价 药品资料处。药品列表选子店，总部设置价格设置到的是子店上
         * */
        if (clientPriceItem.getSubPriceFlag() != null) {
            modifyPriceOrderItem.setSubPriceFlag(clientPriceItem.getSubPriceFlag());
        }
        /**
         * 放到这里，写入的是修改后的goods快照
         * */
        modifyPriceOrderItem.setGoodsSnap(GoodsUtils.genGoodsDbSnap(goodsRedisCache));
        UserFillUtils.fillCreatedBy(modifyPriceOrderItem, employeeId);
        return modifyPriceOrderItem;
    }

    public static void updateGoodsPriceItem(GoodsModifyPriceOrderItem svrModifyPriceItem,
                                            GoodsModifyPriceOrder order,
                                            ClinicConfig clinicConfig,
                                            CreateModifyPriceOrderReq.GoodsModifyPriceItem clientModifyPriceItem,
                                            GoodsRedisCache goodsRedisCache,
                                            String employeeId) {
        svrModifyPriceItem.setPackageOpType(clientModifyPriceItem.getUpdatePackagePrice().getOpType());
        /**
         * 这里记录Goods上的改前信息，其实这里怎么记录都不准确
         * 因为这里调价门店，各个调价门店的这几个值都是不一样的
         * */
        svrModifyPriceItem.setGoodsId(goodsRedisCache.getId());
        Optional<CreateModifyPriceOrderReq.ModifyPriceItem> updatePackagePriceItemOpt = Optional.ofNullable(clientModifyPriceItem.getUpdatePackagePrice());
        Optional<CreateModifyPriceOrderReq.ModifyPriceItem> updatePiecePriceItemOpt = Optional.ofNullable(clientModifyPriceItem.getUpdatePiecePrice());
        /*
            这里优先用 modifyPriceItem 中的 beforePrice 中的值，如果没有就保持原来的值
         */
        svrModifyPriceItem.setBeforePackagePrice(updatePackagePriceItemOpt.map(CreateModifyPriceOrderReq.ModifyPriceItem::getBeforePrice).orElse(svrModifyPriceItem.getBeforePackagePrice()));
        svrModifyPriceItem.setBeforePiecePrice(updatePiecePriceItemOpt.map(CreateModifyPriceOrderReq.ModifyPriceItem::getBeforePrice).orElse(svrModifyPriceItem.getBeforePackagePrice()));
        svrModifyPriceItem.setBeforeMakeUpPercent(goodsRedisCache.getPriceMakeupPercent());//改价加成比例
        svrModifyPriceItem.setBeforePriceType(goodsRedisCache.getPriceType()); //改前定价模式

        if (clientModifyPriceItem.getUpdatePackagePrice().getPriceType() != null
                && clientModifyPriceItem.getUpdatePackagePrice().getPriceType() != 0) {
            svrModifyPriceItem.setAfterPriceType(clientModifyPriceItem.getUpdatePackagePrice().getPriceType());
        } else {
            svrModifyPriceItem.setAfterPriceType(GoodsConst.PriceType.PRICE);
        }
        svrModifyPriceItem.setLastSupplierName(clientModifyPriceItem.getLastSupplierName());
        svrModifyPriceItem.setPackageCostPrice(goodsRedisCache.getPackageCostPrice());
        svrModifyPriceItem.setAvgPackageCostPrice(clientModifyPriceItem.getAvgPackageCostPrice());
        if (svrModifyPriceItem.getAvgPackageCostPrice() == null) {
            svrModifyPriceItem.setAvgPackageCostPrice(goodsRedisCache.getAvgPackageCostPrice());
        }


        svrModifyPriceItem.setBeforeProfitRat(clientModifyPriceItem.getBeforeProfitRat());
        if (clientModifyPriceItem.getBeforeProfitRat() == null) {
            svrModifyPriceItem.setBeforeProfitRat(calProfit(
                    svrModifyPriceItem.getBeforePackagePrice(),
//                    CollectionUtils.isEmpty(goodsRedisCache.getGoodsAllPriceRedisCacheList()) ? null :
//                            goodsRedisCache.getGoodsAllPriceRedisCacheList().stream().filter(it -> it.getTargetType() == GoodsPrice.PriceTargetType.GOODS).collect(Collectors.toMap(GoodsPriceRedisCache::getClinicId, GoodsPriceRedisCache::getPackagePrice, (a, b) -> a)),
                    goodsRedisCache,
                    order,
                    svrModifyPriceItem,
                    goodsRedisCache.getKeyToGoodsStockRedisCache()
            ));
        }
        svrModifyPriceItem.setProfitRat(clientModifyPriceItem.getProfitRat());
        if (clientModifyPriceItem.getProfitRat() == null) {
            // 如果前端没传，后台自己算一下。 走到这个分钟大概率有出bug。
            // 用户提交的价格(算价格时，比如库存20得到的利润率)，
            // 提交后台这时，库存只有19，库存量去算，会得到一个新的利润率
            svrModifyPriceItem.setProfitRat(calProfit(
                    //用户提交的价格
                    clientModifyPriceItem.getUpdatePackagePrice().getAfterPrice(),
//                    CollectionUtils.isEmpty(goodsRedisCache.getGoodsAllPriceRedisCacheList()) ? null :
//                            goodsRedisCache.getGoodsAllPriceRedisCacheList().stream().filter(it -> it.getTargetType() == GoodsPrice.PriceTargetType.GOODS).collect(Collectors.toMap(GoodsPriceRedisCache::getClinicId, GoodsPriceRedisCache::getPackagePrice, (a, b) -> a)),
                    goodsRedisCache,
                    order,
                    svrModifyPriceItem,
                    goodsRedisCache.getKeyToGoodsStockRedisCache()
            ));
        }
        if (GoodsUtils.checkFlagOn(svrModifyPriceItem.getBeforePriceType(), GoodsConst.PriceType.PKG_PRICE_MAKEUP)) {
            svrModifyPriceItem.setBeforeProfitRat(svrModifyPriceItem.getBeforeMakeUpPercent());
        }

        //待生效
        svrModifyPriceItem.setStatus(GoodsModifyPriceOrderItem.Status.WAITING);
        if (goodsRedisCache.checkComposeFlag(GoodsConst.ComposeFlag.PRICING)) {
            svrModifyPriceItem.setIsDeleted(YesOrNo.YES);
        }
        /**
         * 改进价加成
         * */
        if (svrModifyPriceItem.getPackageOpType() == GoodsModifyPriceOrder.OpType.OP_TYPE_SET_BY_MAKE_UP) {
            //进价加成比例
            svrModifyPriceItem.setAfterMakeUpPercent(clientModifyPriceItem.getUpdatePackagePrice().getAfterUpPercent());
            svrModifyPriceItem.setProfitRat(svrModifyPriceItem.getAfterMakeUpPercent());
        } else {
            svrModifyPriceItem.setModifyBasePackagePrice(clientModifyPriceItem.getUpdatePackagePrice().getBeforePrice());
            svrModifyPriceItem.setBeforeDiscountType(clientModifyPriceItem.getUpdatePackagePrice().getBeforeDiscountType());
            svrModifyPriceItem.setBeforeDiscountValue(clientModifyPriceItem.getUpdatePackagePrice().getBeforeDiscountValue());
            svrModifyPriceItem.setBeforeIsSaleLimit(clientModifyPriceItem.getUpdatePackagePrice().getBeforeIsSaleLimit());
            svrModifyPriceItem.setBeforeSaleLimitRule(clientModifyPriceItem.getUpdatePackagePrice().getBeforeSaleLimitRule());

            //调价比例
            svrModifyPriceItem.setPackageUpPercent(clientModifyPriceItem.getUpdatePackagePrice().getAfterUpPercent());
            svrModifyPriceItem.setAfterPackagePrice(clientModifyPriceItem.getUpdatePackagePrice().getAfterPrice());
            svrModifyPriceItem.setAfterDiscountType(clientModifyPriceItem.getUpdatePackagePrice().getAfterDiscountType());
            svrModifyPriceItem.setAfterDiscountValue(clientModifyPriceItem.getUpdatePackagePrice().getAfterDiscountValue());
            svrModifyPriceItem.setAfterIsSaleLimit(clientModifyPriceItem.getUpdatePackagePrice().getAfterIsSaleLimit());
            svrModifyPriceItem.setAfterSaleLimitRule(clientModifyPriceItem.getUpdatePackagePrice().getAfterSaleLimitRule());

            //拆0价
            if (clientModifyPriceItem.getUpdatePiecePrice() != null) {
                svrModifyPriceItem.setPieceOpType(clientModifyPriceItem.getUpdatePiecePrice().getOpType());
                svrModifyPriceItem.setModifyBasePiecePrice(clientModifyPriceItem.getUpdatePiecePrice().getBeforePrice());
                svrModifyPriceItem.setPieceUpPercent(clientModifyPriceItem.getUpdatePiecePrice().getAfterUpPercent());
                svrModifyPriceItem.setAfterPiecePrice(clientModifyPriceItem.getUpdatePiecePrice().getAfterPrice());
            }
        }

        /**
         * 解决药店总部给子店定价 药品资料处。药品列表选子店，总部设置价格设置到的是子店上
         * */
        if (clientModifyPriceItem.getSubPriceFlag() != null) {
            svrModifyPriceItem.setSubPriceFlag(clientModifyPriceItem.getSubPriceFlag());
        }
        /**
         * 放到这里，写入的是修改后的goods快照
         * */
        svrModifyPriceItem.setGoodsSnap(GoodsUtils.genGoodsDbSnap(goodsRedisCache));
        UserFillUtils.fillLastModifiedBy(svrModifyPriceItem, employeeId);
    }

    /**
     * 获取诊所配置和修改门店ID
     * 根据不同的门店类型（单店、子店、总部）来获取正确的诊所配置和修改门店ID
     * 单店模式：记录到看不见的连锁上，方便单店升级连锁时不需要刷数据
     * 子店模式：使用子店自己的配置和ID
     * 总部模式：可以给指定门店调价，或使用总部配置
     *
     * @param clientReq 客户端请求
     * @return Pair<ClinicConfig, String> 左边是诊所配置，右边是修改门店ID
     * @throws CisGoodsServiceException 获取诊所配置失败时抛出异常
     */
    public Pair<ClinicConfig, String> getClinicConfigAndModifyClinicId(CreateModifyPriceOrderReq clientReq) throws CisGoodsServiceException {
        ClinicConfig clinicConfig;
        String modifyClinicId; //默认发起门店

        //单店，把这个单子的clinicId记录到看不见的连锁上,单店升级连锁不刷数据就可以了
        if (GoodsPrivUtils.isSingleMode(clientReq.getHeaderClinicType(), clientReq.getHeaderViewMode())) {
            clinicConfig = cisClinicService.getClinicConfig(clientReq.getHeaderClinicId());
            modifyClinicId = clientReq.getHeaderChainId();
        } else if (GoodsPrivUtils.isSubClinic(clientReq.getHeaderClinicType(), clientReq.getHeaderViewMode())) {
            //子店
            clinicConfig = cisClinicService.getClinicConfig(clientReq.getHeaderClinicId());
            modifyClinicId = clientReq.getHeaderClinicId();
        } else {
            //总部
            if (StringUtils.isEmpty(clientReq.getClinicId())) {
                clinicConfig = cisClinicService.getClinicConfig(clientReq.getHeaderClinicId());
                modifyClinicId = clientReq.getHeaderClinicId();
            } else {
                //总部给指定门店调价
                clinicConfig = cisClinicService.getClinicConfig(clientReq.getClinicId());
                modifyClinicId = clientReq.getClinicId();
            }
        }

        if (clinicConfig == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.GET_CLINIC_CONFIG_ERROR);
        }

        return Pair.of(clinicConfig, modifyClinicId);
    }

    @Data
    @Builder
    public static class GoodsLoadResult {
        private List<Goods> goodsList;
        private Map<String, GoodsRedisCache> goodsIdToGoodsRedisCache;
        private Map<String, Goods> goodsIdToGoods;
        private int pharmacyNo;
        private ClinicGoodsPriceWrapper priceWrapper;
    }

    public GoodsLoadResult loadGoodsAndRelatedData(CreateModifyPriceOrderReq clientReq, ClinicConfig clinicConfig, long startTime) {
        /**
         * goods信息加载
         * 主要加载Goods和子店定价
         * */
        Tuple2<Integer, Integer> r = GoodsUtils.fixPharmacyNoAndChooseType(cisClinicService, clinicConfig, clientReq.getClinicId());
        List<Goods> goodsList = loadGoodsUtils.loadAssembledGoodsIdList(clientReq.getGoodsIdList(), clinicConfig, true, false,//加载二级分类
                ServiceUtils.maybeSetSubPrice(clinicConfig, clientReq.getClinicId()), //是否容许子店自主定价
                YesOrNo.NO, YesOrNo.NO,//加载库存
                true,//加载goodsStat里面的库存
                GoodsConst.PharmacyType.LOCAL_PHARMACY, //调价一定是本地药房
                r.getT1());
        sLogger.info("批量调价耗时统计 加载DBGoods: goods数量:{},耗时:{}", goodsList.size(), (System.currentTimeMillis() - startTime));
        startTime = System.currentTimeMillis();

        List<String> updateGoodsStatGoodsIdList = goodsList.stream().map(Goods::getId).collect(Collectors.toList());

        Map<String, GoodsRedisCache> goodsIdToGoodsRedisCache = goodsRedisUtils.getMapGoodsRedisCacheWithoutBatch(updateGoodsStatGoodsIdList,
                ChoosePharmacyHelper.ofSpecificPharmacy(GoodsConst.PharmacyType.LOCAL_PHARMACY, r.getT1(), r.getT2()),
                clinicConfig);
        sLogger.info("批量调价耗时统计 加载RedisGoods: goodsRedisCache数量:{},耗时:{}", goodsIdToGoodsRedisCache.size(), (System.currentTimeMillis() - startTime));
        startTime = System.currentTimeMillis();

        Map<String, Goods> goodsIdToGoods = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (a, b) -> a));
        ClinicGoodsPriceWrapper priceWrapper = new ClinicGoodsPriceWrapper(goodsPriceRepository.findAllByChainIdAndGoodsIdIn(clientReq.getHeaderChainId(), clientReq.getGoodsIdList()));
        sLogger.info("批量调价耗时统计 加载子店定价: goodsPrice数量:{},耗时:{}", priceWrapper.getAllPriceList().size(), (System.currentTimeMillis() - startTime));

        /**
         * 如果是子店定价
         * */
        if (clinicConfig.isSubClinic()) {
            for (Goods goods : goodsList) {
                goods.setClinicSelfGoodsPrice(priceWrapper.getNormalPrice(clinicConfig.getClinicId(), goods.getId()));
                goods.setPharmacyTypePharmacyNoToGoodsPrice(priceWrapper.getPharmacyTypeNoToGoodsPrice(clinicConfig.getClinicId(), goods.getId()));
            }
        }

        return GoodsLoadResult.builder()
                .goodsList(goodsList)
                .goodsIdToGoodsRedisCache(goodsIdToGoodsRedisCache)
                .goodsIdToGoods(goodsIdToGoods)
                .priceWrapper(priceWrapper)
                .build();
    }

    /**
     * 0 普通调价单
     * 1 普通草稿单
     * 2 修改药品调价草稿单
     */
    private Tuple2<GoodsModifyPriceOrder, Boolean> loadOrCreateModifyPriceOrder(int addToDraftScene, CreateModifyPriceOrderReq clientReq, String modifyClinicId, boolean needApproval, GspService gspService, ClinicConfig clinicConfig) {
        GoodsModifyPriceOrder goodsModifyPriceOrder = null;
        List<GoodsModifyPriceOrderItem> svrExistedPriceOrderItemList = new ArrayList<>();
        //改价单
        if (addToDraftScene == CREATE_DRAFT_ORDER_FROM_LIST) { //api 改价单接口
            if (clientReq.getOrderId() != null) {
                goodsModifyPriceOrder = goodsModifyPriceOrderRepository.findByChainIdAndId(clientReq.getHeaderChainId(), clientReq.getOrderId()).orElse(null);
                if (goodsModifyPriceOrder == null) {
                    throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "未找到调价单");
                }
                if (goodsModifyPriceOrder.isOrderStable()) {
                    throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "调价单已经完成，不能修改");
                }
//                //变成非草稿了
//                if (goodsModifyPriceOrder.getStatus() == GoodsModifyPriceOrder.Status.DRAFT && clientReq.getIsSubmit() == true) {
//                    toNormalModifyPriceOrder = true;
//                }
                UserFillUtils.fillLastModifiedBy(goodsModifyPriceOrder, clientReq.getHeaderEmployeeId());
            }
        } else if (addToDraftScene == CREATE_DRAFT_ORDER_QUICK_PRICE) { //快捷调价 单或草稿
            if (needApproval) {
                List<GoodsModifyPriceOrder> allDraftList = goodsModifyPriceOrderRepository.findByChainIdAndCreatedByAndClinicIdAndStatusAndSourceTypeAndIsDeleted(
                        clientReq.getHeaderChainId(),
                        clientReq.getHeaderEmployeeId(),
                        modifyClinicId,
                        GoodsModifyPriceOrder.Status.DRAFT,
                        GoodsModifyPriceOrder.SourceFrom.FROM_GOODS,
                        YesOrNo.NO);
                List<String> affectedClinicIdList = null;
                if (!CollectionUtils.isEmpty(clientReq.getAffectedClinicIdList())) {
                    affectedClinicIdList = clientReq.getAffectedClinicIdList();
                } else {
                    affectedClinicIdList = getAffectedClinicIdList(clinicConfig);
                }
                List<String> finalAffectedClinicIdList = affectedClinicIdList;
                goodsModifyPriceOrder = allDraftList.stream().filter(it -> it.getExtendInfos() != null && GoodsUtils.compareListEqual(it.getExtendInfos().getAffectedClinicList(), finalAffectedClinicIdList)).findFirst().orElse(null);
            } else {
                List<GoodsModifyPriceOrder> allDraftList = goodsModifyPriceOrderRepository.findByChainIdAndStatusNotAndClinicIdAndIsDeleted(
                        clientReq.getHeaderChainId(),
                        GoodsModifyPriceOrder.Status.DRAFT,
                        modifyClinicId, //发起调价门店的门店ID
                        YesOrNo.NO);
                List<String> affectedClinicIdList = null;
                if (!CollectionUtils.isEmpty(clientReq.getAffectedClinicIdList())) {
                    affectedClinicIdList = clientReq.getAffectedClinicIdList();
                } else {
                    affectedClinicIdList = getAffectedClinicIdList(clinicConfig);
                }
                List<String> finalAffectedClinicIdList = affectedClinicIdList;
                goodsModifyPriceOrder = allDraftList.stream().filter(it -> it.getExtendInfos() != null && GoodsUtils.compareListEqual(it.getExtendInfos().getAffectedClinicList(), finalAffectedClinicIdList)).findFirst().orElse(null);
            }
        } else if (addToDraftScene == CREATE_DRAFT_ORDER_GOODS) { //修改药品调价草稿单
            if (needApproval) {
                /***
                 * 总部每个人要给每个门店的调价单分别生成默认草稿单，多草稿了
                 * */
                List<GoodsModifyPriceOrder> allDraftList = goodsModifyPriceOrderRepository.findByChainIdAndCreatedByAndClinicIdAndStatusAndSourceTypeAndIsDeleted(
                        clientReq.getHeaderChainId(),
                        clientReq.getHeaderEmployeeId(),
                        modifyClinicId, //发起调价门店的门店ID
                        GoodsModifyPriceOrder.Status.DRAFT,
                        GoodsModifyPriceOrder.SourceFrom.FROM_GOODS,
                        YesOrNo.NO);
                List<String> affectedClinicIdList = null;
                if (!CollectionUtils.isEmpty(clientReq.getAffectedClinicIdList())) {
                    affectedClinicIdList = clientReq.getAffectedClinicIdList();
                } else {
                    affectedClinicIdList = getAffectedClinicIdList(clinicConfig);
                }
                List<String> finalAffectedClinicIdList = affectedClinicIdList;
                goodsModifyPriceOrder = allDraftList.stream().filter(it -> it.getExtendInfos() != null && GoodsUtils.compareListEqual(it.getExtendInfos().getAffectedClinicList(), finalAffectedClinicIdList)).findFirst().orElse(null);
            } else {
                //在调价列表建出来的草稿
                List<GoodsModifyPriceOrder> allDraftList = goodsModifyPriceOrderRepository.findByChainIdAndStatusNotAndClinicIdAndIsDeleted(
                        clientReq.getHeaderChainId(),
                        GoodsModifyPriceOrder.Status.DRAFT,
                        modifyClinicId, //发起调价门店的门店ID
                        YesOrNo.NO);
                List<String> affectedClinicIdList = null;
                if (!CollectionUtils.isEmpty(clientReq.getAffectedClinicIdList())) {
                    affectedClinicIdList = clientReq.getAffectedClinicIdList();
                } else {
                    affectedClinicIdList = getAffectedClinicIdList(clinicConfig);
                }
                List<String> finalAffectedClinicIdList = affectedClinicIdList;
                goodsModifyPriceOrder = allDraftList.stream().filter(it -> it.getExtendInfos() != null && GoodsUtils.compareListEqual(it.getExtendInfos().getAffectedClinicList(), finalAffectedClinicIdList)).findFirst().orElse(null);
            }
        }


        if (goodsModifyPriceOrder != null) {
            svrExistedPriceOrderItemList.addAll(goodsModifyPriceOrderItemRepository.findAllByClinicIdAndOrderIdInAndIsDeleted(goodsModifyPriceOrder.getClinicId(), Arrays.asList(goodsModifyPriceOrder.getId()), YesOrNo.NO));
            if (!needApproval) {
                goodsModifyPriceOrder.setStatus(GoodsModifyPriceOrder.Status.FINISHED);
            }
            UserFillUtils.fillLastModifiedBy(goodsModifyPriceOrder, clientReq.getHeaderEmployeeId());
        } else {
            goodsModifyPriceOrder = new GoodsModifyPriceOrder();
            goodsModifyPriceOrder.setId(AbcIdUtils.getUUIDLong());
            goodsModifyPriceOrder.setChainId(clientReq.getHeaderChainId());
            goodsModifyPriceOrder.setOrderNo(genTodayOrderNo(PREFIX_TJ, getTodayOrderNoIndexAndFixConflictPreOrderNo(PREFIX_TJ_CG, clientReq.getHeaderChainId()) + 1));
            if (!needApproval) {
                goodsModifyPriceOrder.setStatus(GoodsModifyPriceOrder.Status.FINISHED);
            }
            UserFillUtils.fillCreatedBy(goodsModifyPriceOrder, clientReq.getHeaderEmployeeId());
            goodsModifyPriceOrder.setKindCount(0);
            goodsModifyPriceOrder.setSourceType(clientReq.getSourceType());
            if (clientReq.getSourceType() == GoodsModifyPriceOrder.SourceFrom.FROM_GOODS_LIST
                    || clientReq.getSourceType() == GoodsModifyPriceOrder.SourceFrom.FROM_PURCHASE_ORDER
                    || clientReq.getSourceType() == GoodsModifyPriceOrder.SourceFrom.FROM_GOODS_BATCH_LIST) {
                goodsModifyPriceOrder.setSourceType(GoodsModifyPriceOrder.SourceFrom.FROM_GOODS);
            }
        }

        goodsModifyPriceOrder.setClinicId(modifyClinicId);
        goodsModifyPriceOrder.setOpType(clientReq.getOpType());
        goodsModifyPriceOrder.setList(svrExistedPriceOrderItemList);
        goodsModifyPriceOrder.setComment(clientReq.getComment());

//        if (toNormalModifyPriceOrder) {
//            goodsModifyPriceOrder.setOrderNo(genTodayOrderNo(PREFIX_TJ, getTodayOrderNoIndexAndFixConflictPreOrderNo(PREFIX_TJ, clientReq.getHeaderChainId()) + 1));
//        }
        if (addToDraftScene != CREATE_DRAFT_ORDER_FROM_LIST) {
            if (addToDraftScene == CREATE_DRAFT_ORDER_GOODS) {
                if (!needApproval) {
                    goodsModifyPriceOrder.setStatus(GoodsModifyPriceOrder.Status.FINISHED);
                    goodsModifyPriceOrder.setEffected(clientReq.getEffected()); //立即生效
                }
                return Tuples.of(goodsModifyPriceOrder, !needApproval);
            } else {
                return Tuples.of(goodsModifyPriceOrder, false);
            }
        }
        /**
         * 准备改价
         * 1.是否提交调价单 true 提交 ；前端不传默认为true（新建可以直接提交，修改可以提交也可以继续保存草稿）
         * */
        if (clientReq.getIsSubmit() == false) { //用户不提交一定是草稿
            goodsModifyPriceOrder.setStatus(GoodsModifyPriceOrder.Status.DRAFT);
            goodsModifyPriceOrder.setEffected(clientReq.getEffected()); //立即生效
            return Tuples.of(goodsModifyPriceOrder, false);
        }
        boolean modifyPriceNow = true;
        /**
         * 状态审核
         * */
        if (GoodsPrivUtils.isAbcPharmacy(clientReq.getHeaderHisType()) && clinicConfig.modifyPriceNeedApproval()) {
            modifyPriceNow = false;
            //发起GSP审核
            Long gspTaskId = gspService.submitModifyPriceOrderGspTask(clientReq.getHeaderChainId(), clientReq.getHeaderClinicId(), clientReq.getHeaderEmployeeId(), clientReq.getHeaderEmployeeId(), Instant.now(), goodsModifyPriceOrder);
            GoodsGsp gsp = goodsModifyPriceOrder.getGsp();
            if (gsp == null) {
                gsp = new GoodsGsp();
                goodsModifyPriceOrder.setGsp(gsp);
            }
            if (gspTaskId != null) {
                gsp.setGspInstId(gspTaskId);
                goodsModifyPriceOrder.setGspStatus(ApprovalConstant.InstStatus.ING);
            } else {
                gsp.setGspInstId(null);
                goodsModifyPriceOrder.setGspStatus(ApprovalConstant.InstStatus.NORMAL);
            }

            //需要审核
            goodsModifyPriceOrder.setStatus(GoodsModifyPriceOrder.Status.AUDITING);
            //如果在生效之前没审核通过
            goodsModifyPriceOrder.setEffected(clientReq.getEffected()); //延迟生效

        } else {
            //不需要GSP审核
            if (clientReq.getEffected() == null
                    || clientReq.getEffected().compareTo(Instant.now()) <= 0) {
                modifyPriceNow = true;
                goodsModifyPriceOrder.setStatus(GoodsModifyPriceOrder.Status.FINISHED);
                goodsModifyPriceOrder.setEffected(Instant.now()); //立即生效
            } else {
                modifyPriceNow = false;
                goodsModifyPriceOrder.setStatus(GoodsModifyPriceOrder.Status.WAITING_AFFECTED);
                goodsModifyPriceOrder.setEffected(clientReq.getEffected()); //延迟生效
            }
        }
        if (modifyPriceNow) {
            goodsModifyPriceOrder.setEffected(clientReq.getEffected()); //立即生效
        }
        return Tuples.of(goodsModifyPriceOrder, modifyPriceNow);
    }

    @Transactional(rollbackFor = Exception.class)
    public ModifyPriceOrderResult addToDefaultDraftPriceOrder(CreateModifyPriceOrderReq clientReq,
                                                              Pair<ClinicConfig, String> clinicConfigPair,
                                                              GoodsLoadResult goodsLoadResult,
                                                              GspService gspService,
                                                              int addToDraftScene) throws CisGoodsServiceException {
        // 获取诊所配置和修改门店ID
        ClinicConfig clinicConfig = clinicConfigPair.getFirst();

        // 是否有调价权限
        checkPriceAuth(clinicConfig, clientReq.getHeaderChainId(), clientReq.getHeaderClinicId(), clientReq.getHeaderEmployeeId());

        /**
         * 检查客户端参数
         * */
        clientReq.setClinicConfig(clinicConfig);
        clientReq.parameterCheck();

        /**
         * 加载Goods后再次进行参数检查
         * */
        Map<String, Goods> goodsIdToGoods = goodsLoadResult.getGoodsIdToGoods();
        clientReq.parameterCheckWithGoodsLoad(goodsIdToGoods);

        /*
            TODO 这里因为产品还没有设计药店会员价的调价，所以暂时不处理 beforePrice 值
         */
        return addToDefaultDraftPriceOrderCore(clientReq, clinicConfigPair, goodsLoadResult, gspService, addToDraftScene);
    }

    @Transactional(rollbackFor = Exception.class)
    public ModifyPriceOrderResult innerAddToDefaultDraftPriceOrder(CreateModifyPriceOrderReq clientReq,
                                                                   Pair<ClinicConfig, String> clinicConfigPair,
                                                                   GoodsLoadResult goodsLoadResult,
                                                                   GspService gspService,
                                                                   int addToDraftScene) throws CisGoodsServiceException {
        // 获取诊所配置和修改门店ID
        ClinicConfig clinicConfig = clinicConfigPair.getFirst();

        // 是否有调价权限
        checkPriceAuth(clinicConfig, clientReq.getHeaderChainId(), clientReq.getHeaderClinicId(), clientReq.getHeaderEmployeeId());

        /**
         * 检查客户端参数
         * */
        clientReq.setClinicConfig(clinicConfig);
        clientReq.parameterCheck();

        /**
         * 加载Goods后再次进行参数检查
         * */
        Map<String, Goods> goodsIdToGoods = goodsLoadResult.getGoodsIdToGoods();
        clientReq.parameterCheckWithGoodsLoad(goodsIdToGoods);
        return addToDefaultDraftPriceOrderCore(clientReq, clinicConfigPair, goodsLoadResult, gspService, addToDraftScene);
    }

    @Transactional(rollbackFor = Exception.class)
    public ModifyPriceOrderResult addToDefaultDraftPriceOrderCore(CreateModifyPriceOrderReq clientReq,
                                                                  Pair<ClinicConfig, String> clinicConfigPair,
                                                                  GoodsLoadResult goodsLoadResult,
                                                                  GspService gspService,
                                                                  int addToDraftScene) throws CisGoodsServiceException {
        long sTime = System.currentTimeMillis();
        ModifyPriceOrderResult result = new ModifyPriceOrderResult();
        /**
         * 加载诊所配置
         * 选对的门店
         * */
        // 获取诊所配置和修改门店ID
        ClinicConfig clinicConfig = clinicConfigPair.getFirst();
        String modifyClinicId = clinicConfigPair.getSecond();

        /**
         * 决定改价单上的clinicId记录成那个ID
         * 这里主要解决的是单店模式
         * 单店模式升级到总部 如果记录的clinicId是单店的ClinicId，
         * 升级成总部这个调价单就成了子店调价单
         * */
        // 加载商品信息和相关数据
        List<Goods> goodsList = goodsLoadResult.getGoodsList();
        Map<String, GoodsRedisCache> goodsIdToGoodsRedisCache = goodsLoadResult.getGoodsIdToGoodsRedisCache();

        /**
         * 新增改价单
         * */
        Tuple2<GoodsModifyPriceOrder, Boolean> orderAndModifyPricePair = loadOrCreateModifyPriceOrder(addToDraftScene, clientReq, modifyClinicId, clinicConfig.modifyPriceNeedApproval(), gspService, clinicConfig);
        Map<Integer, Integer> typeIdToTypeCount = new HashMap<>();
        GoodsModifyPriceOrder goodsModifyPriceOrder = orderAndModifyPricePair.getT1();


        sLogger.info("批量调价耗时统计 加载调价单 耗时:{}", (System.currentTimeMillis() - sTime));
        sTime = System.currentTimeMillis();
        CreateModifyPriceOrderReq.GoodsModifyPriceItem clientItem = clientReq.getList().get(0);
        List<GoodsModifyPriceOrderItem> dbList = new ArrayList<>();
        List<Tuple2<String, List<GoodsModifyPriceOrderItem>>> goodsIdToSrvModifyPriceItemMapList = goodsModifyPriceOrder.getList().stream()
                .filter(it -> Objects.equals(it.getStatus(), GoodsModifyPriceOrderItem.Status.WAITING) && GoodsUtils.compareStrEqual(it.getGoodsId(), clientItem.getGoodsId()))
                .collect(Collectors.groupingBy(GoodsModifyPriceOrderItem::getGoodsId))
                .entrySet().stream()
                .map(entry -> Tuples.of(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
        List<Tuple2<String, List<CreateModifyPriceOrderReq.GoodsModifyPriceItem>>> goodsIdToCliModifyPriceItemMapList = clientReq.getList().stream()
                .filter(it -> GoodsUtils.compareStrEqual(it.getGoodsId(), clientItem.getGoodsId()))
                .collect(Collectors.groupingBy(CreateModifyPriceOrderReq.GoodsModifyPriceItem::getGoodsId))
                .entrySet().stream()
                .map(entry -> Tuples.of(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());


        MergeTool<Tuple2<String, List<CreateModifyPriceOrderReq.GoodsModifyPriceItem>>, Tuple2<String, List<GoodsModifyPriceOrderItem>>> mergeTool = new MergeTool<>();
        BiFunction<CreateModifyPriceOrderReq.GoodsModifyPriceItem, GoodsModifyPriceOrderItem, Boolean> isEqualKeyGoodsIdAndMemberTypeIdFunc = (cli, svr) -> (cli.getGoodsId() + Objects.toString(cli.getMemberTypeId(), "")).equals((svr.getGoodsId() + Objects.toString(svr.getMemberTypeId(), "")));
        mergeTool.setSrc(goodsIdToCliModifyPriceItemMapList)
                .setDst(goodsIdToSrvModifyPriceItemMapList)
                .setIsEqualKeyFunc((cli, svr) -> cli.getT1().equals(svr.getT1()))
                .setInsertFunc(cli -> {
                    //如果有新的改价，要用新的改价
                    String goodsId = cli.getT1();
                    List<GoodsModifyPriceOrderItem> srvModifyPriceItems = new ArrayList<>();
                    List<CreateModifyPriceOrderReq.GoodsModifyPriceItem> cliModifyPriceItems = cli.getT2();
                    Long itemId = createOrUpdateMultiGoodsPriceOrderItemList(clinicConfig,
                            goodsModifyPriceOrder,
                            goodsIdToGoodsRedisCache.get(goodsId),
                            isEqualKeyGoodsIdAndMemberTypeIdFunc,
                            srvModifyPriceItems,
                            cliModifyPriceItems,
                            clientReq.getHeaderEmployeeId()
                    );
                    result.getGoodsIdToOrderItemId().put(goodsId, itemId);
                    dbList.addAll(srvModifyPriceItems);
                    return Tuples.of(goodsId, srvModifyPriceItems);
                })
                .setUpdateFunc((cli, svr) -> {
                    String goodsId = cli.getT1();
                    List<GoodsModifyPriceOrderItem> srvModifyPriceItems = svr.getT2();
                    List<CreateModifyPriceOrderReq.GoodsModifyPriceItem> cliModifyPriceItems = cli.getT2();
                    Long itemId = createOrUpdateMultiGoodsPriceOrderItemList(clinicConfig,
                            goodsModifyPriceOrder,
                            goodsIdToGoodsRedisCache.get(goodsId),
                            isEqualKeyGoodsIdAndMemberTypeIdFunc,
                            srvModifyPriceItems,
                            cliModifyPriceItems,
                            clientReq.getHeaderEmployeeId()
                    );
                    result.getGoodsIdToOrderItemId().put(goodsId, itemId);
                    dbList.addAll(srvModifyPriceItems);
                })
                .setDeleteFunc(svr -> {
                    for (GoodsModifyPriceOrderItem goodsModifyPriceOrderItem : svr.getT2()) {
                        goodsModifyPriceOrderItem.setIsDeleted(YesOrNo.YES);
                        goodsModifyPriceOrderItem.setStatus(GoodsModifyPriceOrderItem.Status.FAILED);
                        UserFillUtils.fillLastModifiedBy(goodsModifyPriceOrderItem, clientReq.getHeaderEmployeeId());
                    }
                    dbList.addAll(svr.getT2());
                    return false;
                });
        mergeTool.doMerge();


        GoodsModifyPriceOrderExtendInfos extendInfos = goodsModifyPriceOrder.getExtendInfos();
        if (extendInfos == null) {
            extendInfos = new GoodsModifyPriceOrderExtendInfos();
            extendInfos.setUpPercent(null);
            if (!CollectionUtils.isEmpty(clientReq.getAffectedClinicIdList())) {
                extendInfos.setAffectedClinicList(clientReq.getAffectedClinicIdList());
            } else {
                extendInfos.setAffectedClinicList(getAffectedClinicIdList(clinicConfig));
            }
            extendInfos.setTypeIdToGoodsCount(new HashMap<>());
        }
        extendInfos.setUpPercent(clientReq.getUpPercent());
        if (extendInfos.getTypeIdToGoodsCount() != null) {
            typeIdToTypeCount.putAll(extendInfos.getTypeIdToGoodsCount());
        }
        GoodsRedisCache goodsRedisCache = goodsIdToGoodsRedisCache.get(clientItem.getGoodsId());
        if (goodsRedisCache != null) {
            //统计数量
            if (!typeIdToTypeCount.containsKey(goodsRedisCache.getTypeId())) {
                typeIdToTypeCount.put(goodsRedisCache.getTypeId(), 1);
            } else {
                typeIdToTypeCount.put(goodsRedisCache.getTypeId(), typeIdToTypeCount.get(goodsRedisCache.getTypeId()) + 1);
            }
        }
        extendInfos.setTypeIdToGoodsCount(typeIdToTypeCount);
        goodsModifyPriceOrder.setKindCount(goodsModifyPriceOrder.getKindCount() + 1);
        goodsModifyPriceOrder.setExtendInfos(extendInfos);

        sLogger.info("批量调价耗时统计 计算调价差异 耗时:{}", (System.currentTimeMillis() - sTime));


        /**
         * 要刷GoodsStat
         * */
        if (goodsModifyPriceOrder.getExtendInfos() != null && !CollectionUtils.isEmpty(goodsModifyPriceOrder.getExtendInfos().getAffectedClinicList())) {
            result.getUpdateGoodsStatClinicIdList().addAll(goodsModifyPriceOrder.getExtendInfos().getAffectedClinicList());
            if (result.getUpdateGoodsStatClinicIdList().contains(clinicConfig.getChainId())) {
                result.getUpdateGoodsStatClinicIdList().add(GoodsUtils.WHOLE_CHAIN_ID);
            }
        }
        GoodsUtils.runAfterTransaction(() -> {
                    long sTimeIn = System.currentTimeMillis();
                    // 把待生效的itemId刷到goodsStat
                    // 把待生效的itemId刷到goodsStat
                    applicationContext.getBean(GoodsPriceService.class).updateGoodsStatWaitingEffectPrice(clientReq.getHeaderChainId(), clientReq.getGoodsIdList(), result.getUpdateGoodsStatClinicIdList());
                    sLogger.info("批量调价耗时统计 调价事务提交后 updateGoodsStatHasModifyPriceOrder 耗时:{}", (System.currentTimeMillis() - sTimeIn));
                }
        );
        List<GoodsModifyPriceOrderLog> logList = new ArrayList<>();

        /***
         * 改价日志
         * */
        logList.add(createGoodsModifyPriceOrderLog(clientReq.getHeaderChainId(), goodsModifyPriceOrder.getClinicId(), goodsModifyPriceOrder.getId(), GoodsModifyPriceOrderLog.ACTION_CREATE, clientReq.getHeaderEmployeeId(), clientReq.getComment(), goodsModifyPriceOrder));
        logList.add(createGoodsModifyPriceOrderLog(clientReq.getHeaderChainId(), goodsModifyPriceOrder.getClinicId(), goodsModifyPriceOrder.getId(), GoodsModifyPriceOrderLog.ACTION_REVIEW_START, clientReq.getHeaderEmployeeId(), clientReq.getComment(), goodsModifyPriceOrder));
        //写数据库落地
        if (!CollectionUtils.isEmpty(dbList)) {
            goodsModifyPriceOrderItemRepository.saveAll(dbList);
        }
        goodsModifyPriceOrderRepository.save(goodsModifyPriceOrder);
        goodsModifyPriceOrder.setLogs(logList);

        sTime = System.currentTimeMillis();
        aftersStatusChange(null, goodsModifyPriceOrder);
        sLogger.info("批量调价 耗时数量统计  耗时:{}", (System.currentTimeMillis() - sTime));
        result.setGoodsList(goodsList);
        result.setGoodsModifyPriceOrder(goodsModifyPriceOrder);
        result.setUpdatePriceNow(orderAndModifyPricePair.getT2());
        result.setCurGoodsModifyPriceOrderItems(dbList.stream().filter(item -> item.getIsDeleted() == 0).collect(Collectors.toList()));
        return result;
    }

    /***
     * 新建调价单
     * */
    @Transactional(rollbackFor = Exception.class)
    public ModifyPriceOrderResult createOrUpdatePriceOrder(CreateModifyPriceOrderReq clientReq, GspService gspService) throws CisGoodsServiceException {
        long sTime = System.currentTimeMillis();

        // 获取诊所配置和修改门店ID
        Pair<ClinicConfig, String> clinicConfigPair = getClinicConfigAndModifyClinicId(clientReq);
        ClinicConfig clinicConfig = clinicConfigPair.getFirst();
        String modifyClinicId = clinicConfigPair.getSecond();

        // 是否有调价权限
        checkPriceAuth(clinicConfig, clientReq.getHeaderChainId(), clientReq.getHeaderClinicId(), clientReq.getHeaderEmployeeId());

        /**
         * 检查客户端参数
         * */
        clientReq.setClinicConfig(clinicConfig);
        clientReq.parameterCheck();

        // 加载商品信息和相关数据
        GoodsLoadResult goodsLoadResult = loadGoodsAndRelatedData(clientReq, clinicConfig, sTime);
        List<Goods> goodsList = goodsLoadResult.getGoodsList();
        Map<String, GoodsRedisCache> goodsIdToGoodsRedisCache = goodsLoadResult.getGoodsIdToGoodsRedisCache();
        Map<String, Goods> goodsIdToGoods = goodsLoadResult.getGoodsIdToGoods();
        ClinicGoodsPriceWrapper priceWrapper = goodsLoadResult.getPriceWrapper();

        sTime = System.currentTimeMillis();


        /**
         * 加载Goods后再次进行参数检查
         * */
        clientReq.parameterCheckWithGoodsLoad(goodsIdToGoods);


        sLogger.info("批量调价耗时统计 加载调价单 耗时:{}", (System.currentTimeMillis() - sTime));
        sTime = System.currentTimeMillis();
        Tuple2<GoodsModifyPriceOrder, Boolean> orderAndModifyPricePair = loadOrCreateModifyPriceOrder(CREATE_DRAFT_ORDER_FROM_LIST, clientReq, modifyClinicId, clinicConfig.modifyPriceNeedApproval(), gspService, clinicConfig);
        GoodsModifyPriceOrder goodsModifyPriceOrder = orderAndModifyPricePair.getT1();


        List<GoodsModifyPriceOrderItem> needSaveItems = new ArrayList<>();
        List<GoodsModifyPriceOrderItem> newPriceOrderItems = new ArrayList<>();
        List<Tuple2<String, List<GoodsModifyPriceOrderItem>>> goodsIdToSrvModifyPriceItemMapList = goodsModifyPriceOrder.getList().stream()
                .collect(Collectors.groupingBy(GoodsModifyPriceOrderItem::getGoodsId))
                .entrySet().stream()
                .map(entry -> Tuples.of(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
        List<Tuple2<String, List<CreateModifyPriceOrderReq.GoodsModifyPriceItem>>> goodsIdToCliModifyPriceItemMapList = clientReq.getList().stream()
                .collect(Collectors.groupingBy(CreateModifyPriceOrderReq.GoodsModifyPriceItem::getGoodsId))
                .entrySet().stream()
                .map(entry -> Tuples.of(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());

        MergeTool<Tuple2<String, List<CreateModifyPriceOrderReq.GoodsModifyPriceItem>>, Tuple2<String, List<GoodsModifyPriceOrderItem>>> mergeTool = new MergeTool<>();
        BiFunction<CreateModifyPriceOrderReq.GoodsModifyPriceItem, GoodsModifyPriceOrderItem, Boolean> isEqualKeyIdFunc =
                (view, svr) -> GoodsUtils.compareLongEqual(view.getId(), svr.getId());
        mergeTool.setSrc(goodsIdToCliModifyPriceItemMapList)
                .setDst(goodsIdToSrvModifyPriceItemMapList)
                .setIsEqualKeyFunc((cli, svr) -> cli.getT1().equals(svr.getT1()))
                .setInsertFunc(cli -> {
                    List<GoodsModifyPriceOrderItem> createSvrList = new ArrayList<>();
                    createOrUpdateMultiGoodsPriceOrderItemList(clinicConfig,
                            goodsModifyPriceOrder,
                            goodsIdToGoodsRedisCache.get(cli.getT1()),
                            isEqualKeyIdFunc,
                            createSvrList,
                            cli.getT2(),
                            clientReq.getHeaderEmployeeId());
                    needSaveItems.addAll(createSvrList);
                    newPriceOrderItems.addAll(createSvrList);
                    return Tuples.of(cli.getT1(), createSvrList);
                })
                .setUpdateFunc((cli, svr) -> {
                    createOrUpdateMultiGoodsPriceOrderItemList(clinicConfig,
                            goodsModifyPriceOrder,
                            goodsIdToGoodsRedisCache.get(cli.getT1()),
                            isEqualKeyIdFunc,
                            svr.getT2(),
                            cli.getT2(),
                            clientReq.getHeaderEmployeeId());
                    needSaveItems.addAll(svr.getT2());
                })
                .setDeleteFunc(svr -> {
                    for (GoodsModifyPriceOrderItem goodsModifyPriceOrderItem : svr.getT2()) {
                        goodsModifyPriceOrderItem.setIsDeleted(YesOrNo.YES);
                        goodsModifyPriceOrderItem.setStatus(GoodsModifyPriceOrderItem.Status.FAILED);
                        UserFillUtils.fillLastModifiedBy(goodsModifyPriceOrderItem, clientReq.getHeaderEmployeeId());
                    }
                    needSaveItems.addAll(svr.getT2());
                    return false;
                });
        mergeTool.doMerge();

        if (!CollectionUtils.isEmpty(newPriceOrderItems)) {
            if (goodsModifyPriceOrder.getList() == null) {
                goodsModifyPriceOrder.setList(new ArrayList<>());
            }
            goodsModifyPriceOrder.getList().addAll(newPriceOrderItems);
        }


        Map<Integer, Set<String>> typeIdToGoodsIdList = new HashMap<>();
        Map<Integer, Integer> typeIdToTypeCount = new HashMap<>();
        for (CreateModifyPriceOrderReq.GoodsModifyPriceItem goodsModifyPriceItem : clientReq.getList()) {
            Goods goods = goodsModifyPriceItem.getGoods();
            if (goods == null) {
                continue;
            }

            typeIdToGoodsIdList.computeIfAbsent(goods.getTypeId(), k -> new HashSet<>()).add(goods.getId());
        }
        typeIdToGoodsIdList.forEach((k, v) -> typeIdToTypeCount.put(k, v.size()));
        GoodsModifyPriceOrderExtendInfos extendInfos = new GoodsModifyPriceOrderExtendInfos();
        extendInfos.setUpPercent(clientReq.getUpPercent());
        extendInfos.setAffectedClinicList(getAffectedClinicIdList(clinicConfig));
        extendInfos.setTypeIdToGoodsCount(typeIdToTypeCount);
        goodsModifyPriceOrder.setExtendInfos(extendInfos);
        goodsModifyPriceOrder.summery();
        sLogger.info("批量调价耗时统计 计算调价差异 耗时:{}", (System.currentTimeMillis() - sTime));
        sTime = System.currentTimeMillis();


        /**
         * 改价核心函数
         * */
        if (orderAndModifyPricePair.getT2()) {
            updateGoodsPriceByModifyPriceOrder(goodsModifyPriceOrder,
                    needSaveItems,
                    clinicConfig,
                    goodsIdToGoods,
                    priceWrapper,
                    clientReq.getHeaderEmployeeId()
            );
            sLogger.info("批量调价耗时统计 实际调价 耗时:{}", (System.currentTimeMillis() - sTime));
        }

        /**
         * 要刷GoodsStat
         * */
        Set<String> updateGoodsStatClinicIdList = new HashSet<>();
        if (goodsModifyPriceOrder.getExtendInfos() != null && !CollectionUtils.isEmpty(goodsModifyPriceOrder.getExtendInfos().getAffectedClinicList())) {
            updateGoodsStatClinicIdList.addAll(goodsModifyPriceOrder.getExtendInfos().getAffectedClinicList());
            if (updateGoodsStatClinicIdList.contains(clinicConfig.getChainId())) {
                updateGoodsStatClinicIdList.add(GoodsUtils.WHOLE_CHAIN_ID);
            }
        }
        GoodsUtils.runAfterTransaction(() -> {
                    long sTimeIn = System.currentTimeMillis();
                    // 把待生效的itemId刷到goodsStat
                    applicationContext.getBean(GoodsPriceService.class).updateGoodsStatWaitingEffectPrice(clientReq.getHeaderChainId(),
                            goodsModifyPriceOrder.getList().stream().map(GoodsModifyPriceOrderItem::getGoodsId).collect(Collectors.toList()),
                            updateGoodsStatClinicIdList);

                    sLogger.info("批量调价耗时统计 调价事务提交后 updateGoodsStatHasModifyPriceOrder 耗时:{}", (System.currentTimeMillis() - sTimeIn));
                }
        );
        List<GoodsModifyPriceOrderLog> logList = new ArrayList<>();

        /***
         * 改价日志
         * */
        logList.add(createGoodsModifyPriceOrderLog(clientReq.getHeaderChainId(), goodsModifyPriceOrder.getClinicId(), goodsModifyPriceOrder.getId(), GoodsModifyPriceOrderLog.ACTION_CREATE, clientReq.getHeaderEmployeeId(), clientReq.getComment(), goodsModifyPriceOrder));
        if (orderAndModifyPricePair.getT2()) {
            logList.add(createGoodsModifyPriceOrderLog(clientReq.getHeaderChainId(), goodsModifyPriceOrder.getClinicId(), goodsModifyPriceOrder.getId(), GoodsModifyPriceOrderLog.ACTION_FINISHED, clientReq.getHeaderEmployeeId(), clientReq.getComment(), goodsModifyPriceOrder));
        } else {
            logList.add(createGoodsModifyPriceOrderLog(clientReq.getHeaderChainId(), goodsModifyPriceOrder.getClinicId(), goodsModifyPriceOrder.getId(), GoodsModifyPriceOrderLog.ACTION_REVIEW_START, clientReq.getHeaderEmployeeId(), clientReq.getComment(), goodsModifyPriceOrder));
        }
        //写数据库落地
        if (!CollectionUtils.isEmpty(needSaveItems)) {
            goodsModifyPriceOrderItemRepository.saveAll(needSaveItems);
        }
        goodsModifyPriceOrderRepository.save(goodsModifyPriceOrder);
        goodsModifyPriceOrder.setLogs(logList);

        sTime = System.currentTimeMillis();
        aftersStatusChange(null, goodsModifyPriceOrder);
        sLogger.info("批量调价 耗时数量统计  耗时:{}", (System.currentTimeMillis() - sTime));

        ModifyPriceOrderResult modifyPriceOrderResult = new ModifyPriceOrderResult();
        modifyPriceOrderResult.setGoodsModifyPriceOrder(goodsModifyPriceOrder);
        modifyPriceOrderResult.setGoodsList(goodsList);
        return modifyPriceOrderResult;
    }

    private void checkPriceAuth(ClinicConfig clinicConfig, String chainId, String clinicId, String employeeId) {
        if (!clinicConfig.isAbcPharmacy()) {
            return;
        }
        ClinicEmployeeDataPermission.Inventory inventory = cisClinicService.getEmployeePermission(employeeId, chainId, clinicId);
        if (inventory.getIsCanOperateGoodsAdjustPrice() != YesOrNo.YES) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "无调价权限");
        }
    }

    private void aftersStatusChange(GoodsModifyPriceOrder beforeGoodsModifyPriceOrder, GoodsModifyPriceOrder goodsModifyPriceOrder) {
        String chainId = goodsModifyPriceOrder.getChainId();
        String clinicId = goodsModifyPriceOrder.getClinicId();
        if (beforeGoodsModifyPriceOrder == null || beforeGoodsModifyPriceOrder.getStatus() == GoodsModifyPriceOrder.Status.DRAFT) {
            if (goodsModifyPriceOrder.getStatus() == GoodsModifyPriceOrder.Status.AUDITING) {
                goodsTodoCountService.chkClinicTotoCount(chainId, clinicId, GoodsTodoCountService.MODIFY_PRICE_ORDER_WAIT_AUDIT_TODO_COUNT, 1);
            }
        } else if (beforeGoodsModifyPriceOrder.getStatus() == GoodsModifyPriceOrder.Status.AUDITING) {
            goodsTodoCountService.chkClinicTotoCount(chainId, clinicId, GoodsTodoCountService.MODIFY_PRICE_ORDER_WAIT_AUDIT_TODO_COUNT, -1);
        }
    }

    /***
     * 获取改价影响门店的列表
     * 如果单店和子店就自己
     * 如果总部，要去掉自主定价的子店 + 总店自己
     * */
    public List<String> getAffectedClinicIdList(ClinicConfig clinicConfig) {
        //总部
        if (clinicConfig.isHeadClinic() && !clinicConfig.isSingleMode()) {
            //没开 开关 影响所有子店
            List<String> allOrganIdList = cisClinicService.getSubClinicIdList(clinicConfig.getChainId());
            if (clinicConfig.getSubSetPrice() == null || clinicConfig.getSubSetPrice() == YesOrNo.NO) {
                allOrganIdList.add(clinicConfig.getChainId());
                return allOrganIdList;
            }
            //所有子店都有自主定价
            if (clinicConfig.getSubSetPriceAllClinics() != null && clinicConfig.getSubSetPriceAllClinics() == YesOrNo.YES) {
                return Arrays.asList(clinicConfig.getChainId());
            }
            List<String> affectedList = new ArrayList<>();
            affectedList.add(clinicConfig.getChainId());
            //子店定价的门店
            Map<String, ClinicConfig.SubSetPriceClinic> clinicIdToSubPrice = clinicConfig.getClinicIdToSubSetPriceClinics();
            //有
            if (!CollectionUtils.isEmpty(clinicIdToSubPrice)) {
                for (String clinicId : allOrganIdList) {
                    if (!clinicIdToSubPrice.containsKey(clinicId)) {
                        affectedList.add(clinicId);
                    }
                }
                return affectedList;
            } else {
                affectedList.addAll(allOrganIdList);
                return affectedList;
            }
        }
        return Collections.singletonList(clinicConfig.getClinicId());
    }

    public void deletePriceOrder(String chainId, String employeeId, Long orderId) {
        GoodsModifyPriceOrder order = goodsModifyPriceOrderRepository.findByChainIdAndId(chainId, orderId).orElse(null);
        if (order == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "单据未找到");
        }
        if (order.getStatus() != GoodsModifyPriceOrder.Status.DRAFT) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "当前单据状态:" + GoodsModifyPriceOrder.orderStatusName(order.getStatus()) + "，不能修改");
        }
        order.setIsDeleted(YesOrNo.YES);
        UserFillUtils.fillLastModifiedBy(order, employeeId);
        List<GoodsModifyPriceOrderItem> svrExistedPriceOrderItemList = goodsModifyPriceOrderItemRepository.findByOrderIdAndIsDeleted(orderId, YesOrNo.NO);
        for (GoodsModifyPriceOrderItem item : svrExistedPriceOrderItemList) {
            item.setIsDeleted(YesOrNo.YES);
            UserFillUtils.fillLastModifiedBy(item, employeeId);
        }
        if (svrExistedPriceOrderItemList.isEmpty()) {
            return;
        }

        /**
         * 要刷GoodsStat
         * */
        Set<String> updateGoodsStatClinicIdList = new HashSet<>();
        if (order.getExtendInfos() != null && !CollectionUtils.isEmpty(order.getExtendInfos().getAffectedClinicList())) {
            updateGoodsStatClinicIdList.addAll(order.getExtendInfos().getAffectedClinicList());
            if (updateGoodsStatClinicIdList.contains(chainId)) {
                updateGoodsStatClinicIdList.add(GoodsUtils.WHOLE_CHAIN_ID);
            }
        }
        GoodsModifyPriceOrder finalGoodsModifyPriceOrder1 = order;
        GoodsUtils.runAfterTransaction(() -> {
                    // 把待生效的itemId刷到goodsStat
                    applicationContext.getBean(GoodsPriceService.class).updateGoodsStatWaitingEffectPrice(finalGoodsModifyPriceOrder1.getChainId(),
                            svrExistedPriceOrderItemList.stream().map(GoodsModifyPriceOrderItem::getGoodsId).collect(Collectors.toList()),
                            updateGoodsStatClinicIdList);
                }
        );
    }

    public void cancelPriceOrder(String chainId, String employeeId, Long orderId) {
        GoodsModifyPriceOrder order = goodsModifyPriceOrderRepository.findByChainIdAndId(chainId, orderId).orElse(null);
        if (order == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "单据未找到");
        }
        if (order.getStatus() != GoodsModifyPriceOrder.Status.WAITING_AFFECTED) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "当前单据状态:" + GoodsModifyPriceOrder.orderStatusName(order.getStatus()) + "，不能撤回");
        }
        order.setStatus(GoodsModifyPriceOrder.Status.DENIED);
        UserFillUtils.fillLastModifiedBy(order, employeeId);
        createGoodsModifyPriceOrderLog(chainId, order.getClinicId(), orderId, GoodsModifyPriceOrderLog.ACTION_CANCEL, employeeId, null, order);
        List<GoodsModifyPriceOrderItem> svrExistedPriceOrderItemList = goodsModifyPriceOrderItemRepository.findByOrderIdAndIsDeleted(orderId, YesOrNo.NO);
        for (GoodsModifyPriceOrderItem item : svrExistedPriceOrderItemList) {
            item.setStatus(GoodsModifyPriceOrderItem.Status.FAILED);
            UserFillUtils.fillLastModifiedBy(item, employeeId);
        }
        if (svrExistedPriceOrderItemList.isEmpty()) {
            return;
        }

        /**
         * 要刷GoodsStat
         * */
        Set<String> updateGoodsStatClinicIdList = new HashSet<>();
        if (order.getExtendInfos() != null && !CollectionUtils.isEmpty(order.getExtendInfos().getAffectedClinicList())) {
            updateGoodsStatClinicIdList.addAll(order.getExtendInfos().getAffectedClinicList());
            if (updateGoodsStatClinicIdList.contains(chainId)) {
                updateGoodsStatClinicIdList.add(GoodsUtils.WHOLE_CHAIN_ID);
            }
        }
        GoodsModifyPriceOrder finalGoodsModifyPriceOrder1 = order;
        GoodsUtils.runAfterTransaction(() -> {
                    // 把待生效的itemId刷到goodsStat
                    applicationContext.getBean(GoodsPriceService.class).updateGoodsStatWaitingEffectPrice(finalGoodsModifyPriceOrder1.getChainId(),
                            svrExistedPriceOrderItemList.stream().map(GoodsModifyPriceOrderItem::getGoodsId).collect(Collectors.toList()),
                            updateGoodsStatClinicIdList);
                }
        );
    }

    /**
     * 通过改价单修改Goods价格
     * 抽成一个函数，这样除了药品资料改价的场景，都可以调用这个入库函数进行改价格了
     */
    public void updateGoodsPriceByModifyPriceOrder(GoodsModifyPriceOrder goodsModifyPriceOrder,//改价单
                                                   List<GoodsModifyPriceOrderItem> orderItemList,//条目
                                                   ClinicConfig clinicConfig, //发起调价门店配置
                                                   Map<String, Goods> goodsIdToGoods, //Goods
                                                   ClinicGoodsPriceWrapper priceWrapper,//子店定价
                                                   String employeeId //操作人
    ) {
        /**
         * [改价] 药店和医院，如果总部改价，要把子店的价格实例化出来，子店不能被总部影响
         * */
        if (clinicConfig != null && (clinicConfig.isAbcPharmacy() || clinicConfig.isHospital())
                && clinicConfig.isHeadClinic()
                && !clinicConfig.isSingleMode()) {
            Map<String, List<GoodsPrice>> goodsIdToPriceList = instantSubClinicPriceNowByWholeGoodsRedisCache(
                    goodsRedisUtils.loadWholeGoods(orderItemList.stream().map(GoodsModifyPriceOrderItem::getGoodsId).collect(Collectors.toList()), clinicConfig, goodsRedisUtils).values().stream().collect(Collectors.toList()),
                    clinicConfig,
                    cisClinicService.getSubClinicIdList(clinicConfig.getChainId()),
                    employeeId
            );
            if (!CollectionUtils.isEmpty(goodsIdToPriceList)) {
                // 把实例化后的子店价格加入到priceWrapper里面
                priceWrapper.addAllGoodsPrice(goodsIdToPriceList.values().stream().flatMap(List::stream).collect(Collectors.toList()));
            }
        }
        /**
         * 检查下会员是否有效
         * */
        Map<String, String> memberTypeIdToMemberTypeName = Collections.emptyMap();
        if (clinicConfig.isAbcPharmacy() && orderItemList.stream().anyMatch(it -> !StringUtils.isEmpty(it.getMemberTypeId()))) {
            memberTypeIdToMemberTypeName = cisCrmService.getChainMemberTypeInfo(clinicConfig.getChainId());
        }

        List<GoodsLog> goodsLogList = new ArrayList<>(); //输出
        List<GoodsPrice> updateGoodsPriceList = new ArrayList<>();//输出
        for (GoodsModifyPriceOrderItem modifyPriceOrderItem : orderItemList) {
            Goods goods = goodsIdToGoods.get(modifyPriceOrderItem.getGoodsId());
            if (goods == null) {
                log.info("goods不存在:{}", JsonUtils.dump(modifyPriceOrderItem));
                continue;
            }
            ClinicConfig clinicConfigModify = clinicConfig;
            if (goodsModifyPriceOrder.getOpType() == GoodsModifyPriceOrder.OpType.OP_TYPE_SET_CHAIN_MOD_SUB_PRICE) {
                clinicConfigModify = cisClinicService.getClinicConfig(modifyPriceOrderItem.getClinicId());
            }

            doUpdatePriceByGoodsModifyPriceOrderItem(goods,
                    goodsModifyPriceOrder,
                    clinicConfigModify,
                    modifyPriceOrderItem,
                    employeeId,
                    goodsLogList,
                    priceWrapper,
                    memberTypeIdToMemberTypeName,
                    updateGoodsPriceList
            );
        }
        goodsModifyPriceOrder.setEffected(Instant.now());
        UserFillUtils.fillLastModifiedBy(goodsModifyPriceOrder, employeeId);

        goodsModifyPriceOrderRepository.save(goodsModifyPriceOrder);
        if (!CollectionUtils.isEmpty(orderItemList)) {
            goodsModifyPriceOrderItemRepository.saveAll(orderItemList);
        }
        if (!CollectionUtils.isEmpty(updateGoodsPriceList)) {
            goodsPriceRepository.saveAll(updateGoodsPriceList);
        }
        //给药品 写入药品物资处的log
        if (!CollectionUtils.isEmpty(goodsLogList)) {
            goodsLogRepository.saveAll(goodsLogList);
        }
//        goodsMemberPriceService.insertOrUpdatePromotion(goodsModifyPriceOrder, orderItemList, employeeId);
    }

    /**
     * 建日志
     */
    public GoodsModifyPriceOrderLog createGoodsModifyPriceOrderLog(String chainId,
                                                                   String clinicId,
                                                                   Long orderId,
                                                                   String action,
                                                                   String employeeId,
                                                                   String comment,
                                                                   GoodsModifyPriceOrder after) {
        GoodsModifyPriceOrderLog log = new GoodsModifyPriceOrderLog();
        log.setId(AbcIdUtils.getUUIDLong());
        log.setOrderId(orderId);
        log.setAction(action);
        log.setChainId(chainId);
        log.setClinicId(clinicId);
        log.setComment(comment);
        log.setCreatedBy(employeeId);
        log.setCreated(Instant.now());
        log.setAfter(after);
        goodsModifyPriceOrderLogRepository.save(log);
        return log;
    }

    /**
     * 订单批量改价过后 另外异步事务
     * 主要更新goodsStat,另起事务是更新stat耗时，即使没更新成功问题也不大
     */
    @Async("longTimeAsyncTaskExecutor")
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void batchModifyGoodsPricesAfter(GoodsModifyPriceOrder order, List<Goods> updatePriceGoodsList, String clinicId, String chainId) throws CisGoodsServiceException {
        /**
         * 加载诊所配置
         * */
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(clinicId);
        if (clinicConfig == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.GET_CLINIC_CONFIG_ERROR);
        }
        if (!order.isOrderStable()) {
            return;
        }
        Map<String, List<GoodsPrice>> goodsIdToGoodsPriceList = new HashMap<>();
        Map<String, List<GoodsStat>> goodsIdToGoodsStatList = new HashMap<>();
        List<GoodsStat> statList = new ArrayList<>();
        List<String> goodsIdList = updatePriceGoodsList.stream().map(Goods::getId).collect(Collectors.toList());
        if (clinicConfig.isSubClinic()) {
            /**
             * 子店改了自主定价  goods里面已经有goodsPrice 了这里不用管
             * */
            statList.addAll(goodsStatRepository.findByGoodsIdInAndChainIdAndClinicId(goodsIdList, clinicConfig.getChainId(), clinicConfig.getClinicId()));
            goodsIdToGoodsStatList.putAll(statList.stream().collect(Collectors.groupingBy(GoodsStat::getGoodsId)));
            goodsIdToGoodsPriceList.putAll(goodsPriceRepository.findAllByGoodsIdInAndOrganId(goodsIdList, clinicConfig.getClinicId()).stream().collect(Collectors.groupingBy(GoodsPrice::getGoodsId)));
        } else {
            /**
             * 总部改了价格，要把所有门店都刷一下goodsStat 因为不知道哪些门店没有自助定价
             * */
            statList.addAll(goodsStatRepository.findByGoodsIdInAndChainId(goodsIdList, clinicConfig.getChainId()));
            goodsIdToGoodsStatList.putAll(statList.stream().collect(Collectors.groupingBy(GoodsStat::getGoodsId)));
            goodsIdToGoodsPriceList.putAll(goodsPriceRepository.findAllByChainIdAndGoodsIdIn(clinicConfig.getChainId(), goodsIdList).stream().collect(Collectors.groupingBy(GoodsPrice::getGoodsId)));
        }

        for (Goods goods : updatePriceGoodsList) {
            /**
             * 创建goodsStat并发送广播消息
             * */
            if (!GoodsUtils.isStockGoods(goods.getType())) {
                continue;
            }
            if (clinicConfig.isSubClinic()) {
                goodsStatService.updateSingleClinicGoodsStat(goods,
                        clinicConfig,
                        goodsIdToGoodsStatList.get(goods.getId()),
                        null
                );
            } else {
                goodsStatService.updateMainClinicGoodsStatAndSubClinicPrice(goods,
                        clinicConfig,
                        goodsIdToGoodsStatList.get(goods.getId()),
                        goodsIdToGoodsPriceList.get(goods.getId()),
                        order.getExtendInfos().getAffectedClinicList(),
                        null
                );
            }
        }
        goodsStatRepository.saveAll(statList);
        /**
         * 清理让价格生效,后一个事务里面再修改再清理一次
         * */
        goodsRedisUtils.clearGoodsRedisCache(chainId, goodsIdList);
        // 清理统计缓存
        goodsRedisUtils.clearClinicGoodsStockCountCache(chainId, clinicId);
    }

    public ResponseEntity<byte[]> exportModifyPriceOrderList(GetModifyPriceListReq clientReq) {
        // 导出不分页
        Organ clinic = cisClinicService.getOrganByClinicId(clientReq.getHeaderClinicId());
        GetModifyPriceListRsp page = this.getModifyPriceOrderList(clientReq);
        List<ModifyPriceOrderView> rows = page.getRows();
        List<Long> modifyPriceOrderIds = new ArrayList<>();
        List<ModifyPriceOrderExportView> exportViews = rows.stream().map(e -> {
            ModifyPriceOrderExportView view = new ModifyPriceOrderExportView();
            modifyPriceOrderIds.add(e.getId());
            view.setId(e.getId());
            view.setCreateUserName(Optional.ofNullable(e.getCreatedUser()).map(EmployeeView::getName).orElse(""));
            view.setCreated(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss").format(LocalDateTime.ofInstant(e.getCreated(), ZoneId.systemDefault())));
            view.setKindCount(e.getKindCount());
            view.setOpType(e.getOpType());
            view.setUpPercent(MathUtils.wrapBigDecimal(e.getUpPercent(), BigDecimal.ZERO));
            StringBuilder builder = new StringBuilder(ModifyPriceOrderOpType.valueString(e.getOpType()));
            if (!Objects.equals(view.getOpType(), ModifyPriceOrderOpType.MANUAL_MODIFY_PRICE)) {
                builder.append(view.getUpPercent().compareTo(BigDecimal.ZERO) >= 0 ? "上调" : "下调").append(view.getUpPercent()).append("%");
            }
            view.setOpTypeString(builder.toString());
            view.setClinicNames(Optional.ofNullable(e.getAffectedClinicList()).filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty).orElse(Optional.ofNullable(e.getModifyClinic()).map(Collections::singletonList).orElse(Lists.newArrayList())).stream().map(OrganView::getName).collect(Collectors.joining("、")));
            return view;
        }).collect(Collectors.toList());
        Map<Long, ModifyPriceOrderExportView> modifyPriceOrderExportViewMap = ListUtils.toMap(exportViews, ModifyPriceOrderExportView::getId);
        String clinicId = StringUtils.isEmpty(clientReq.getClinicId()) ? clientReq.getHeaderClinicId() : clientReq.getClinicId();
        if (ClinicUtils.isSingle(clientReq.getHeaderClinicType(), clientReq.getHeaderViewMode())) {
            // 单店模式换成chainId
            clinicId = clientReq.getHeaderChainId();
        }
        List<ModifyPriceOrderItemView> modifyPriceOrderItemViews = getModifyPriceOrderItemViewsByOrderIds(clientReq.getHeaderChainId(), clinicId, modifyPriceOrderIds);
        Map<Long, GoodsSysType> goodsSysTypeMap = ListUtils.toMap(goodsSysTypeService.getHisGoodsSysTypesList(), GoodsSysType::getId);
        List<ModifyPriceOrderItemExportView> exportItemViews = modifyPriceOrderItemViews.stream().map(f -> {
            ModifyPriceOrderItemExportView itemView = new ModifyPriceOrderItemExportView();
            GoodsSnapV3 goods = f.getGoods();
            if (Objects.nonNull(goods)) {
                itemView.setDisplayName(goods.getDisplayName());
                itemView.setDisplaySpec(goods.getDisplaySpec());
                itemView.setManufacturer(goods.getManufacturer());
                GoodsSysType goodsSysType = goodsSysTypeMap.get((long) goods.getTypeId());
                if (Objects.nonNull(goodsSysType)) {
                    itemView.setTypeName(goodsSysType.getName());
                }
            }
            itemView.setLastSupplierName(f.getLastSupplierName());
            itemView.setBeforePackagePrice(f.getBeforePackagePrice());
            itemView.setBeforePiecePrice(f.getBeforePiecePrice());
            itemView.setPackageUpPercent(f.getPackageUpPercent());
            itemView.setAfterPackagePrice(f.getAfterPackagePrice());
            itemView.setAfterPiecePrice(f.getAfterPiecePrice());
            itemView.setProfitRat(MathUtils.wrapBigDecimal(f.getProfitRat(), BigDecimal.ZERO).toPlainString() + "%");
            ModifyPriceOrderExportView view = modifyPriceOrderExportViewMap.get(f.getOrderId());
            if (Objects.nonNull(view)) {
                itemView.setCreated(view.getCreated());
            }
            return itemView;
        }).sorted(Comparator.comparing(ModifyPriceOrderItemExportView::getCreated).reversed()).collect(Collectors.toList());
        ExcelWriter excelWriter = null;
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            excelWriter = EasyExcel.write(baos).withTemplate(ResourceUtils.getFile("classpath:excel/modify_price_order_template.xlsx")).build();
            WriteSheet modifyPriceOrderSheet = EasyExcel.writerSheet(0)
                    //.registerWriteHandler(new ModifyPriceOrderHandler())
                    //.head(ModifyPriceOrderItemExportView.class)
                    .build();
            WriteSheet modifyPriceOrderItemSheet = EasyExcel.writerSheet(1)
                    //.registerWriteHandler(new ModifyPriceOrderItemHandler())
                    //.head(ModifyPriceOrderItemExportView.class)
                    .build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(exportViews, fillConfig, modifyPriceOrderSheet);
            excelWriter.fill(exportItemViews, fillConfig, modifyPriceOrderItemSheet);
            // finish 一手
            excelWriter.finish();
            //设置响应头
            HttpHeaders headers = new HttpHeaders();
            //通知浏览器以下载的方式打开文件
            headers.setContentDispositionFormData("attachment", URLEncoder.encode(LocalDate.now() + clinic.getInnerDisplayName() + "调价记录.xlsx", StandardCharsets.UTF_8.name()));
            //定义以流的形式下载返回文件数据
            headers.setContentType(MediaTypeExtend.EXCEL);
            //使用springmvc框架的ResponseEntity对象封装返回数据
            return new ResponseEntity<>(baos.toByteArray(), headers, HttpStatus.OK);
        } catch (IOException e) {
            if (Objects.nonNull(excelWriter)) {
                excelWriter.finish();
            }
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "导出失败");
        }
    }

    /**
     * 子店定价 && 子店药品停用
     * 目前业务逻辑只支持检查检验 goods的批量 设置价格和 禁用
     * 但是实现上支持所有类型goods的 子店批量 设置价格和禁用
     */
    public Goods modifyGoodsSubClinicInfoList(GoodsModifySubClinicsPricesReq clientReq, GoodsInfoModifyManageService goodsInfoModifyManageService, GspService gspService) throws CisGoodsServiceException {
        Goods goods = goodsRepository.findByIdAndStatusLessThan(clientReq.getGoodsId(), GoodsConst.GoodsStatus.DELETE).orElse(null);
        if (goods == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "未找到Goods信息");
        }
        //检查下是否总部已经停用了
        if (goods.getDisable() == GoodsConst.EnableStatus.DISABLE) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "停用项目不能修改");
        }
        //库存商品检查下总部是否已经停用了
        if (GoodsUtils.isStockGoods(goods) && GoodsPrivUtils.isHeadClinic(clientReq.getHeaderClinicType(), clientReq.getHeaderViewMode()) && !GoodsPrivUtils.isSingleMode(clientReq.getHeaderClinicType(), clientReq.getHeaderViewMode())) {
            int chainV2DisableStatus = goods.getV2DisableStatus();
            if (chainV2DisableStatus != GoodsUtils.GoodsV2DisableStatus.ENABLE) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "药品物资已经被总部停用,不能修改");
            }
        }

        /**
         * 检验Goods ，找出这个goods在所有门店的设备列表
         * */
        Map<String, ExaminationDevice> clinicIdToDevice = new HashMap<>();
        if (GoodsTypeHelper.isExaminationAssay(goods.getType(), goods.getSubType()) && !GoodsUtils.isUnknownExamAssayDevice(goods.getBizRelevantId())) {
            clinicIdToDevice.putAll(examinationDeviceRepository.findAllByChainIdAndDeviceModelIdAndGoodsTypeInAndGoodsSubTypeAndStatusLessThanOrderBySortAsc(goods.getOrganId(), Long.parseLong(goods.getBizRelevantId()), GoodsTypeHelper.examinationTypes(), GoodsConst.ExaminationSubType.ASSAY, ExaminationDevice.Status.DELETED).stream().collect(Collectors.toMap(ExaminationDevice::getClinicId, Function.identity(), (a, b) -> a)));
        }

        /**
         * 所有子店的goodsExtend
         * */
        GoodsStatManager.call("Ext_6_1");
        Map<String, GoodsExtend> clinicIdToExtend = goodsExtendRepository.findAllByChainIdAndGoodsId(goods.getOrganId(), clientReq.getGoodsId()).stream().collect(Collectors.toMap(GoodsExtend::getOrganId, Function.identity(), (a, b) -> a));

        /**
         * 所有子店的goodsPrice
         * */
        ClinicGoodsPriceWrapper priceWrapper = new ClinicGoodsPriceWrapper(goodsPriceRepository.findAllByGoodsId(clientReq.getGoodsId()));

        ClinicConfig chainClinicConfig = cisClinicService.getClinicConfig(clientReq.getHeaderChainId());

        /**
         * 参数检查，这里过了 参数就没问题了
         * */
        clientReq.parameterCheck(cisClinicService, chainClinicConfig, goods, clinicIdToExtend, priceWrapper, clinicIdToDevice);

        /**
         * 一个连锁一个连锁的停
         * */
        for (SubClinicGoodsInfo subClinicGoodsInfo : clientReq.getDisableList()) {
            ClientGoodsDisableReq disableReq = new ClientGoodsDisableReq();
            disableReq.setHeaderChainId(clientReq.getHeaderChainId());
            //每个门店停自己
            disableReq.setHeaderClinicId(subClinicGoodsInfo.getOrgan().getClinicId());
            disableReq.setEmployeeId(clientReq.getHeaderEmployeeId());
            disableReq.setHeaderClinicType(clientReq.getHeaderClinicType());
            disableReq.setHeaderViewMode(clientReq.getHeaderViewMode());
            disableReq.setGoodsId(goods.getId());
            if (GoodsUtils.isStockGoods(goods)) {
                disableReq.setV2DisableStatus(subClinicGoodsInfo.getV2DisableStatus());
                disableReq.setForceDisable(clientReq.getForceDisable() != null ? clientReq.getForceDisable() : false);// 自动把套餐拉起---
                disableReq.setThrowExpIfComposed(false);
                goodsInfoModifyManageService.stockGoodsV2Disable(disableReq, true); //不用处理异常，失败会抛异常
            } else {
                disableReq.setDisable(subClinicGoodsInfo.getDisable());
                disableReq.setForceDisable(clientReq.getForceDisable() != null ? clientReq.getForceDisable() : false);// 自动把套餐拉起---
                goodsInfoModifyManageService.disableGoods(disableReq); //不用处理异常，失败会抛异常
            }
        }

        /**
         * 价钱这里直接改，前面参数已经完成了对子店价格的检查了
         * */
        if (GoodsUtils.isStockGoods(goods)) {
            doModifyStockGoodsSubClinicInfoList(goods,
                    chainClinicConfig,
                    clientReq.getHeaderEmployeeId(),
                    clientReq.getUpdatePriceList(),
                    priceWrapper
            );
        } else {
            doModifyNonStockGoodsSubClinicInfoList(goods,
                    chainClinicConfig,
                    clientReq.getHeaderEmployeeId(),
                    clientReq.getUpdatePriceList(),
                    priceWrapper,
                    goodsInfoModifyManageService);
        }
        try {
            rocketMqProducer.sendUserOperationLogMessageAfterTrans(//药品停用
                    clientReq.getHeaderChainId(),
                    clientReq.getHeaderClinicId(),
                    clientReq.getHeaderEmployeeId(),
                    OpType.UPDATE,
                    0,
                    GoodsUtils.isStockGoods(goods.getType()) ? OperationObjectType.GOODS : OperationObjectType.PROJECT,
                    GoodsUtils.isStockGoods(goods.getType()) ? OperationObjectType.GoodsSubObjectType.GOODS_INFO : OperationObjectType.ProjectSubObjectType.TREATMENT_PROJECT,
                    OpSource.PC, //TODO xian
                    goods.getId(),
                    goods.getShortId(),
                    createSummery(goods, clientReq),
                    null,
                    null
            );
        } catch (Exception exp) {
            sLogger.error("sendUserOperationLogMessageAfterTrans exp={}", exp.getMessage());
        }
        return goods;
    }


    /**
     * 把内容拼好
     *
     * @param goods
     * @param clientReq
     * @return
     */
    public String createSummery(Goods goods, GoodsModifySubClinicsPricesReq clientReq) {
        StringBuilder summery = new StringBuilder();
        summery.append("门店定价【").append(GoodsUtils.goodsFullName(goods)).append("】");
        for (SubClinicGoodsInfo subClinicGoodsInfo : clientReq.getSubClinicGoodsInfoList()) {
            String priceType = subClinicGoodsInfo.getPriceType() == null ? "" :
                    subClinicGoodsInfo.getPriceType() == GoodsConst.PriceType.PRICE ? "固定售价" :
                            subClinicGoodsInfo.getPriceType() == GoodsConst.PriceType.VALID_VALUE_BIT ? "进价加成" : "";

            String opType = subClinicGoodsInfo.getSubClinicPriceFlag() == null ? "" :
                    subClinicGoodsInfo.getSubClinicPriceFlag() == GoodsPrice.SubPriceFlag.NO_PRIV_AND_NO_SUB_PRICE ? "总部价" : "单独定价";

            String status = subClinicGoodsInfo.getV2DisableStatus() == null ? "" :
                    subClinicGoodsInfo.getV2DisableStatus() == GoodsUtils.GoodsV2DisableStatus.ENABLE ? "启用" :
                            subClinicGoodsInfo.getV2DisableStatus() == GoodsUtils.GoodsV2DisableStatus.DISABLE_INORDER_ONLY_AND_GOON_SELL ? "停用：余量可用" : "停用：禁售";
            String packagePrice = GoodsUtils.bigDecimalToString(subClinicGoodsInfo.getPackagePrice());
            String priceMakeupPercent = GoodsUtils.bigDecimalToString(subClinicGoodsInfo.getPriceMakeupPercent()) + "%";
            String desc = String.format("[门店名称:%s；启用状态:%s；定价方式:%s；定价模式:%s", organUtils.getOrganShortNameFirst(subClinicGoodsInfo.getOrgan().getClinicId()), status, priceType, opType);
            if (subClinicGoodsInfo.getPriceType() == GoodsConst.PriceType.PRICE) {
                desc = desc + "；零售价格:" + packagePrice;
            }
            if (subClinicGoodsInfo.getPriceType() == GoodsConst.PriceType.VALID_VALUE_BIT) {
                desc = desc + "；加成率:" + priceMakeupPercent;
            }
            desc += "]";
            summery.append(desc);
        }
        return summery.toString();
    }

    /**
     * 总部给门店定价
     * 产品逻辑
     * 1.总部不能给有自主定价权，而且子店已经定过价格的门店定价
     * 2.2024药店，这里改成了这个场景也会生成一个改价单
     */
    private void doModifyStockGoodsSubClinicInfoList(Goods goods,
                                                     ClinicConfig clinicConfig,
                                                     String employeeId,
                                                     List<SubClinicGoodsInfo> clientUpdatePriceList,
                                                     ClinicGoodsPriceWrapper priceWrapper) {
        if (goods.checkComposeFlag(GoodsConst.ComposeFlag.PRICING)) {
            sLogger.info("改价中:{}", goods);
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "当前改价失败, 还存在待生效的改价");
        }

        String chainId = clinicConfig.getChainId();
        Map<String, Goods> goodsIdToGoods = new HashMap<>();
        goodsIdToGoods.put(goods.getId(), goods);

        /**
         * 新增改价单
         * */
        List<GoodsModifyPriceOrderItem> svrExistedPriceOrderItemList = new ArrayList<>();
        GoodsModifyPriceOrder goodsModifyPriceOrder = new GoodsModifyPriceOrder();
        goodsModifyPriceOrder.setId(AbcIdUtils.getUUIDLong());
        goodsModifyPriceOrder.setChainId(chainId);
        goodsModifyPriceOrder.setClinicId(chainId);
        goodsModifyPriceOrder.setOrderNo(genTodayOrderNo(PREFIX_TJ_CHAIN, getTodayOrderNoIndexAndFixConflictPreOrderNo(PREFIX_TJ_CHAIN, chainId) + 1));
        goodsModifyPriceOrder.setKindCount(1); //一个改价品种
        /**
         * 特殊的操作类型
         * */
        goodsModifyPriceOrder.setOpType(GoodsModifyPriceOrder.OpType.OP_TYPE_SET_CHAIN_MOD_SUB_PRICE);
        goodsModifyPriceOrder.setSourceType(GoodsModifyPriceOrder.SourceFrom.FROM_CHAIN_GOODS_BATCH);
        goodsModifyPriceOrder.setList(svrExistedPriceOrderItemList);
        UserFillUtils.fillCreatedBy(goodsModifyPriceOrder, employeeId);
        goodsModifyPriceOrder.setComment("总部维护子店价格");
        Map<String, GoodsStat> keyToGoodsStat = goodsStatRepository.findByGoodsIdInAndChainId(Arrays.asList(goods.getId()), chainId).stream().collect(Collectors.toMap(it -> it.getClinicId() + "_" + it.getPharmacyNo(), Function.identity(), (a, b) -> a));

        /**
         * 特殊的改价单，每个条目是这个goods每个门店的改价
         * */
        Map<Integer, Integer> typeIdToTypeCount = new HashMap<>();
        Set<String> affectefClinicIds = new HashSet<>();
        for (SubClinicGoodsInfo subClinicGoodsInfo : clientUpdatePriceList) {
            String clinicId = subClinicGoodsInfo.getOrgan().getClinicId();
            affectefClinicIds.add(clinicId);
            ClinicConfig clinicConfigIn = cisClinicService.getClinicConfig(clinicId);
            GoodsStat goodsStat = null;
            if (clinicConfigIn.getOpenPharmacyFlag() == GoodsConst.OpenPharmacyFlag.MORE) {
                goodsStat = keyToGoodsStat.get(clinicId + "_" + GoodsUtils.SUMMERY_PHARMACY_NO);
            } else {
                goodsStat = keyToGoodsStat.get(clinicId + "_" + GoodsUtils.PHARMACY_NO_0);
            }
            GoodsPrice goodsPrice = priceWrapper.getByFullUniqKey(GoodsPrice.fullUniqKey(clinicId, goods.getId(), subClinicGoodsInfo.getTargetType(), null, null, subClinicGoodsInfo.getMemberTypeId(), subClinicGoodsInfo.getDiscountType()));
            //改价改到当前 ，goodsPrice可能为空，无自主定价的请
            goods.setClinicSelfGoodsPrice(goodsPrice);
            goods.setGoodsStat(goodsStat);


            //goods改价日志
            GoodsModifyPriceOrderItem modifyPriceOrderItem = new GoodsModifyPriceOrderItem();
            modifyPriceOrderItem.setId(AbcIdUtils.getUUIDLong());
            modifyPriceOrderItem.setChainId(chainId);
            /**
             * 这里记录的是每个门店的Id，是同一个goodsId
             * */
            modifyPriceOrderItem.setOrderId(goodsModifyPriceOrder.getId());
            modifyPriceOrderItem.setGoodsId(goods.getId());
            modifyPriceOrderItem.setClinicId(subClinicGoodsInfo.getOrgan().getClinicId());

            modifyPriceOrderItem.setPackageCostPrice(goods.getLastPackageCostPriceFirstFromStat());
            modifyPriceOrderItem.setAvgPackageCostPrice(goods.getAvgPackageCostPrice());
            if (goodsStat != null) {
                modifyPriceOrderItem.setLastSupplierName(goodsStat.getLastStockInOrderSupplier());
            }
            modifyPriceOrderItem.setBeforePackagePrice(goods.getPackagePrice());
            modifyPriceOrderItem.setBeforePiecePrice(goods.getPiecePrice());
            modifyPriceOrderItem.setBeforeMakeUpPercent(goods.getPriceMakeupPercent());

            modifyPriceOrderItem.setBeforePriceType(goods.getPriceType()); //改前定价模式
            modifyPriceOrderItem.setAfterPriceType(subClinicGoodsInfo.getPriceType());
            modifyPriceOrderItem.setTargetType(subClinicGoodsInfo.getTargetType());
            if (modifyPriceOrderItem.getTargetType() == GoodsPrice.PriceTargetType.MEMBER) {
                modifyPriceOrderItem.setMemberTypeId(subClinicGoodsInfo.getMemberTypeId());
                if (goodsPrice != null) {
                    modifyPriceOrderItem.setBeforeDiscountType(goodsPrice.getDiscountType());
                    modifyPriceOrderItem.setBeforeDiscountValue(goodsPrice.getDiscountValue());
                }
                modifyPriceOrderItem.setAfterDiscountType(subClinicGoodsInfo.getDiscountType());
                modifyPriceOrderItem.setAfterDiscountValue(subClinicGoodsInfo.getDiscountValue());
            }

            /**
             * 这是要取消子店的定价
             * */
            modifyPriceOrderItem.setIndividualPricingType(subClinicGoodsInfo.getIndividualPricingType());
            modifyPriceOrderItem.setSubPriceFlag(subClinicGoodsInfo.getSubClinicPriceFlag());
            if (modifyPriceOrderItem.getSubPriceFlag() == GoodsPrice.SubPriceFlag.NO_PRIV_AND_NO_SUB_PRICE) {
                if (clinicConfig.isAbcPharmacy()  //药店
                        && modifyPriceOrderItem.getIndividualPricingType() != null  //单独设置 跟随总部
                        && modifyPriceOrderItem.getIndividualPricingType() == GoodsConst.IndividualPricingType.INDIVIDUAL_PRICING
                ) {
                    //不能删，只存定价模式和定价方式，价格不存，跟随总部
                    modifyPriceOrderItem.setPackageOpType(GoodsModifyPriceOrder.OpType.OP_TYPE_COMMON_SET);
                    modifyPriceOrderItem.setAfterPackagePrice(null);
                    modifyPriceOrderItem.setAfterPiecePrice(null);
                    modifyPriceOrderItem.setAfterMakeUpPercent(null);
                } else {
                    // 诊所，和其他情况，都是直接删除记录
                    modifyPriceOrderItem.setPackageOpType(GoodsModifyPriceOrder.OpType.OP_TYPE_SET_CANCEL_CLINIC_PRICE);
                    modifyPriceOrderItem.setAfterPackagePrice(goods.getChainPackagePrice());
                    modifyPriceOrderItem.setAfterPiecePrice(goods.getChainPiecePrice());
                    modifyPriceOrderItem.setAfterMakeUpPercent(subClinicGoodsInfo.getPriceMakeupPercent());
                }
                modifyPriceOrderItem.setModifyBasePackagePrice(null);
                modifyPriceOrderItem.setPackageUpPercent(null);
            } else { //子店定价分支
                if (GoodsUtils.checkFlagOn(subClinicGoodsInfo.getPriceType(), GoodsConst.PriceType.PKG_PRICE_MAKEUP)) {
                    modifyPriceOrderItem.setPackageOpType(GoodsModifyPriceOrder.OpType.OP_TYPE_SET_BY_MAKE_UP);
                } else {
                    modifyPriceOrderItem.setPackageOpType(GoodsModifyPriceOrder.OpType.OP_TYPE_COMMON_SET);
                }

                /**
                 * 改进价加成
                 * */
                if (modifyPriceOrderItem.getPackageOpType() == GoodsModifyPriceOrder.OpType.OP_TYPE_SET_BY_MAKE_UP) {
                    //进价加成比例
                    modifyPriceOrderItem.setAfterMakeUpPercent(subClinicGoodsInfo.getPriceMakeupPercent());
                } else {
                    modifyPriceOrderItem.setModifyBasePackagePrice(null);
                    modifyPriceOrderItem.setPackageUpPercent(null);
                    //调价比例
                    modifyPriceOrderItem.setAfterPackagePrice(subClinicGoodsInfo.getPackagePrice());
                    if (subClinicGoodsInfo.getPiecePrice() != null) {
                        modifyPriceOrderItem.setModifyBasePiecePrice(null);
                        modifyPriceOrderItem.setPieceUpPercent(null);
                        modifyPriceOrderItem.setAfterPiecePrice(subClinicGoodsInfo.getPiecePrice());
                        if (GoodsUtils.checkFlagOn(subClinicGoodsInfo.getPriceType(), GoodsConst.PriceType.PKG_PRICE_MAKEUP)) {
                            modifyPriceOrderItem.setPieceOpType(GoodsModifyPriceOrder.OpType.OP_TYPE_SET_BY_MAKE_UP);
                        } else {
                            modifyPriceOrderItem.setPieceOpType(GoodsModifyPriceOrder.OpType.OP_TYPE_COMMON_SET);
                        }
                    }
                }
            }

            //待生效
            modifyPriceOrderItem.setStatus(GoodsModifyPriceOrderItem.Status.WAITING);

            modifyPriceOrderItem.setGoodsSnap(GoodsUtils.genGoodsDbSnap(goods));
            UserFillUtils.fillCreatedBy(modifyPriceOrderItem, employeeId);
            svrExistedPriceOrderItemList.add(modifyPriceOrderItem);
        }
        if (CollectionUtils.isEmpty(svrExistedPriceOrderItemList)) {
            sLogger.info("没有需要改价的条目");
            return;
        }

        GoodsModifyPriceOrderExtendInfos extendInfos = new GoodsModifyPriceOrderExtendInfos();
        typeIdToTypeCount.put(goods.getTypeId(), 1);
        extendInfos.setTypeIdToGoodsCount(typeIdToTypeCount);
        extendInfos.setAffectedClinicList(new ArrayList<>(affectefClinicIds));
        goodsModifyPriceOrder.setExtendInfos(extendInfos);

        updateGoodsPriceByModifyPriceOrder(goodsModifyPriceOrder,
                svrExistedPriceOrderItemList,
                clinicConfig,
                goodsIdToGoods,
                priceWrapper,
                employeeId
        );
        goodsModifyPriceOrder.setStatus(GoodsModifyPriceOrder.Status.FINISHED);

        List<GoodsModifyPriceOrderLog> logList = new ArrayList<>();

        /***
         * 改价日志
         * */
        logList.add(createGoodsModifyPriceOrderLog(chainId, goodsModifyPriceOrder.getClinicId(), goodsModifyPriceOrder.getId(), GoodsModifyPriceOrderLog.ACTION_CREATE, employeeId, goodsModifyPriceOrder.getComment(), goodsModifyPriceOrder));
        logList.add(createGoodsModifyPriceOrderLog(chainId, goodsModifyPriceOrder.getClinicId(), goodsModifyPriceOrder.getId(), GoodsModifyPriceOrderLog.ACTION_FINISHED, employeeId, goodsModifyPriceOrder.getComment(), goodsModifyPriceOrder));
        //写数据库落地
        if (!CollectionUtils.isEmpty(svrExistedPriceOrderItemList)) {
            goodsModifyPriceOrderItemRepository.saveAll(svrExistedPriceOrderItemList);
        }
        goodsModifyPriceOrderRepository.save(goodsModifyPriceOrder);
        goodsModifyPriceOrder.setLogs(logList);
        goodsModifyPriceOrderListeners.forEach(goodsModifyPriceOrderListener -> {
            goodsModifyPriceOrderListener.onGoodsModifyPriceOrderConfirm(goodsModifyPriceOrder, goodsModifyPriceOrder.getList(), employeeId);
        });
    }

    /**
     * 非库存商品的，总部给子店的的改价格
     * 1.没有进价加成
     * 2.因为有子店同步总部对码，每个子店的码都不一样，所以还要算一下摊费
     */
    private void doModifyNonStockGoodsSubClinicInfoList(Goods goods,
                                                        ClinicConfig clinicConfig,
                                                        String employeeId,
                                                        List<SubClinicGoodsInfo> clientUpdatePriceList,
                                                        ClinicGoodsPriceWrapper priceWrapper,
                                                        GoodsInfoModifyManageService goodsInfoModifyManageService) {
        // 是否为诊所
        boolean isClinic = clinicConfig.getHisType() != Organ.HisType.CIS_HIS_TYPE_HOSPITAL;
        // 套餐
        boolean composeGoods = GoodsUtils.isComposeGoods(goods);
        // 子店自主compose
        Map<String, List<GoodsComposeNaked>> clinicIdToGoodsComposeList = new HashMap<>();
        Map<String, GoodsComposeNaked> chainGoodsIdToGoodsCompose = new HashMap<>();
        /**
         * 非医院 的 非套餐
         * 产品上是可以子店单独的对码的
         * 加载这个套餐所有子店的组合关系
         * clinicId = 空 为全连锁的套餐组成
         * clinicId = 非空 为门店的组成
         * */
        if (isClinic && !composeGoods) {
            List<GoodsComposeNaked> composeNakedList = goodsComposeNakedRepository.findAllByChainIdAndParentGoodsIdAndIsDeleted(goods.getOrganId(), goods.getId(), GoodsUtils.DeleteFlag.OK);
            clinicIdToGoodsComposeList.putAll(composeNakedList.stream()
                    .filter(it -> it.getComposeType() == GoodsComposeNaked.ComposeType.FEE
                            && !StringUtils.isEmpty(it.getClinicId()))
                    .collect(Collectors.groupingBy(GoodsComposeNaked::getClinicId)));
            chainGoodsIdToGoodsCompose.putAll(composeNakedList.stream()
                    .filter(it -> it.getComposeType() == GoodsComposeNaked.ComposeType.FEE
                            && StringUtils.isEmpty(it.getClinicId()))
                    .collect(Collectors.toMap(GoodsComposeNaked::getGoodsId,
                            Function.identity(), (a, b) -> a)));
        }
        /**
         * 医院 的 非套餐
         * */
        Map<String, List<GoodsComposePrice>> clinicIdToGoodsComposePriceList = new HashMap<>();
        Map<String, GoodsComposeNaked> goodsIdToGoodsCompose = new HashMap<>();
        if (!isClinic && !composeGoods) {
            clinicIdToGoodsComposePriceList.putAll(goodsComposePriceRepository.findAllByChainIdAndParentGoodsIdInAndIsDeleted(goods.getOrganId(), Collections.singletonList(goods.getId()), GoodsUtils.DeleteFlag.OK).stream().filter(it -> it.getComposeType() == GoodsComposeNaked.ComposeType.FEE).collect(Collectors.groupingBy(GoodsComposePrice::getOrganId)));
            goodsIdToGoodsCompose.putAll(goodsComposeNakedRepository.findAllByChainIdAndParentGoodsIdAndIsDeleted(goods.getOrganId(), goods.getId(), GoodsUtils.DeleteFlag.OK).stream().filter(it -> it.getComposeType() == GoodsComposeNaked.ComposeType.FEE).collect(Collectors.toMap(GoodsComposeNaked::getGoodsId, Function.identity(), (a, b) -> a)));
        }
        if (composeGoods) {
            clinicIdToGoodsComposePriceList.putAll(goodsComposePriceRepository.findAllByChainIdAndParentGoodsIdInAndIsDeleted(goods.getOrganId(), Collections.singletonList(goods.getId()), GoodsUtils.DeleteFlag.OK).stream().filter(it -> it.getComposeType() == GoodsComposeNaked.ComposeType.COMPOSE).collect(Collectors.groupingBy(GoodsComposePrice::getOrganId)));
            goodsIdToGoodsCompose.putAll(goodsComposeNakedRepository.findAllByChainIdAndParentGoodsIdAndIsDeleted(goods.getOrganId(), goods.getId(), GoodsUtils.DeleteFlag.OK).stream().filter(it -> it.getComposeType() == GoodsComposeNaked.ComposeType.COMPOSE).collect(Collectors.toMap(GoodsComposeNaked::getGoodsId, Function.identity(), (a, b) -> a)));
        }
        /**
         * 价钱这里直接改，前面参数已经完成了对子店价格的检查了
         * */
        List<GoodsPrice> deletePriceList = new ArrayList<>();
        List<GoodsPrice> updatePriceList = new ArrayList<>();
        List<GoodsLog> goodsLogList = new ArrayList<>();
        List<GoodsComposeNaked> updateGoodsComposeList = new ArrayList<>();
        List<GoodsComposePrice> updateGoodsComposePriceList = new ArrayList<>();
        List<GoodsMedicalStat> updateGoodsMedicalStatList = new ArrayList<>();
        // 总部给子店定价
        List<String> needUpdateConfigIdList = new ArrayList<>();
        for (SubClinicGoodsInfo subClinicGoodsInfo : clientUpdatePriceList) {
//            GoodsPrice goodsPrice = priceWrapper.getNormalPrice(subClinicGoodsInfo.getOrgan().getId(), goods.getId());
            GoodsPrice goodsPrice = priceWrapper.getByFullUniqKey(GoodsPrice.fullUniqKey(subClinicGoodsInfo.getOrgan().getId(), goods.getId(), subClinicGoodsInfo.getTargetType(), null, null, subClinicGoodsInfo.getMemberTypeId(), subClinicGoodsInfo.getDiscountType()));
            goods.setClinicSelfGoodsPrice(goodsPrice);
            GoodsSnapV3 beforeGoodsSnap = GoodsUtils.genClientGoodsSnap(goods);//改价前goods快照
            goods.setClinicSelfGoodsPrice(null);
            /**
             * 取消子店自主定价
             * */
            if (subClinicGoodsInfo.getSubClinicPriceFlag() == GoodsPrice.SubPriceFlag.NO_PRIV_AND_NO_SUB_PRICE) {
                if (goodsPrice != null) {
                    //子店定价删除掉
                    deletePriceList.add(goodsPrice);
                    goodsLogList.add(GoodsUtils.createUpdateGoodsLog(GoodsLog.ACTION_CLINIC_PRICE_CANCLE, beforeGoodsSnap, goods, null, subClinicGoodsInfo.getOrgan().getId(), employeeId, clinicConfig));

                    // 删除子店compose
                    List<GoodsComposeNaked> existGoodsComposeList = clinicIdToGoodsComposeList.get(subClinicGoodsInfo.getOrgan().getId());
                    if(!CollectionUtils.isEmpty(existGoodsComposeList)){
                        GoodsInfoModifyManageService.doCalculateGoodsComposePrice(updateGoodsComposeList, existGoodsComposeList, goods.getPackagePrice(), cisClinicService.getClinicConfig(subClinicGoodsInfo.getOrgan().getId()),goodsRedisUtils, GoodsUtils.SwitchFlag.OFF);
                    }
                    // 删除子店composePrice
                    List<GoodsComposePrice> existGoodComposePriceList = clinicIdToGoodsComposePriceList.get(subClinicGoodsInfo.getOrgan().getId());
                    if (!CollectionUtils.isEmpty(existGoodComposePriceList)) {
                        updateGoodsComposePriceList.addAll(existGoodComposePriceList.stream().map(it -> {
                            it.setIsDeleted(GoodsUtils.DeleteFlag.DELETE);
                            UserFillUtils.fillLastModifiedUserId(it, employeeId);
                            return it;
                        }).collect(Collectors.toList()));
                    }
                    continue;
                }
            }
            /**
             * 子店自主定价
             * */
            if (subClinicGoodsInfo.getSubClinicPriceFlag() == GoodsPrice.SubPriceFlag.NO_PRIV_AND_HAS_SUB_PRICE) {
                if (goodsPrice == null) {
                    goodsPrice = new GoodsPrice();
                    goodsPrice.setId(AbcIdUtils.getUUIDLong());
                    goodsPrice.setOrganId(subClinicGoodsInfo.getOrgan().getId());
                    goodsPrice.setChainId(goods.getOrganId());
                    goodsPrice.setGoodsId(goods.getId());
                    UserFillUtils.fillCreatedUserId(goodsPrice, employeeId);
                    // put
                    //TODO add bach to  priceWrapper
//                    uniqKeyToPrice.computeIfAbsent(GoodsPrice.uniqKey(goodsPrice.getOrganId(), goods.getId()), key -> new HashMap<>())
//                            .put(org.apache.commons.lang3.tuple.Pair.of(null, null), goodsPrice);
                } else {
                    UserFillUtils.fillLastModifiedUserId(goodsPrice, employeeId);
                }
                goodsPrice.setSubPriceFlag(GoodsPrice.SubPriceFlag.NO_PRIV_AND_HAS_SUB_PRICE);
                goodsPrice.setPackagePrice(subClinicGoodsInfo.getPackagePrice());
                goodsPrice.setPiecePrice(subClinicGoodsInfo.getPiecePrice());
                goodsPrice.setPackageCostPrice(subClinicGoodsInfo.getPackageCostPrice());
                //TODO ...
                updatePriceList.add(goodsPrice);
                goods.setClinicSelfGoodsPrice(goodsPrice);
                goodsLogList.add(GoodsUtils.createUpdateGoodsLog(GoodsLog.ACTION_CLINIC_PRICE, beforeGoodsSnap, goods, null, subClinicGoodsInfo.getOrgan().getId(), employeeId, clinicConfig));
                goods.setClinicSelfGoodsPrice(null);
                needUpdateConfigIdList.add(subClinicGoodsInfo.getOrgan().getId());


                /***
                 * 诊所管家 的非套餐
                 * */
                // 摊费compose
                if (isClinic && !composeGoods) {
                    List<GoodsComposeNaked> existGoodsComposeList = clinicIdToGoodsComposeList.get(subClinicGoodsInfo.getOrgan().getId());
                    if (!CollectionUtils.isEmpty(existGoodsComposeList)) {
                        GoodsInfoModifyManageService.doCalculateGoodsComposePrice(updateGoodsComposeList, existGoodsComposeList, subClinicGoodsInfo.getPackagePrice(), cisClinicService.getClinicConfig(subClinicGoodsInfo.getOrgan().getId()), goodsRedisUtils, GoodsUtils.SwitchFlag.OFF);
                    } else {
                        // 不存在需要插入并摊费
                        existGoodsComposeList = new ArrayList<>();
                        for (GoodsComposeNaked compose : chainGoodsIdToGoodsCompose.values()) {
                            GoodsComposeNaked goodsComposeNaked = new GoodsComposeNaked();
                            BeanUtils.copyProperties(compose, goodsComposeNaked);
                            goodsComposeNaked.setId(AbcIdUtils.getUUIDLong());
                            goodsComposeNaked.setClinicId(subClinicGoodsInfo.getOrgan().getId());
                            UserFillUtils.fillCreatedUserId(goodsComposeNaked, employeeId);
                            existGoodsComposeList.add(goodsComposeNaked);
                        }
                        GoodsInfoModifyManageService.doCalculateGoodsComposePrice(updateGoodsComposeList, existGoodsComposeList, subClinicGoodsInfo.getPackagePrice(), cisClinicService.getClinicConfig(subClinicGoodsInfo.getOrgan().getId()),goodsRedisUtils, GoodsUtils.SwitchFlag.ON);
                    }
                } else {
                    // 医院摊费composePrice
                    List<GoodsComposePrice> existGoodComposePriceList = clinicIdToGoodsComposePriceList.get(subClinicGoodsInfo.getOrgan().getId());
                    if (!CollectionUtils.isEmpty(existGoodComposePriceList)) {
                        doCalculateGoodsComposePrice(updateGoodsComposePriceList, existGoodComposePriceList, goodsIdToGoodsCompose,
                                subClinicGoodsInfo.getPackagePrice(), subClinicGoodsInfo.getOrgan().getId(), GoodsUtils.SwitchFlag.OFF, goodsInfoModifyManageService, composeGoods);
                    } else {
                        // 不存在需要插入并摊费,没有子店单项总价需要使用数量占比摊费
                        existGoodComposePriceList = new ArrayList<>();
                        for (GoodsComposeNaked compose : goodsIdToGoodsCompose.values()) {
                            GoodsComposePrice goodsComposePrice = new GoodsComposePrice();
                            goodsComposePrice.setId(AbcIdUtils.getUUIDLong());
                            goodsComposePrice.setComposeId(compose.getId());
                            goodsComposePrice.setChainId(compose.getChainId());
                            goodsComposePrice.setParentGoodsId(compose.getParentGoodsId());
                            goodsComposePrice.setGoodsId(compose.getGoodsId());
                            goodsComposePrice.setOrganId(subClinicGoodsInfo.getOrgan().getId());
                            goodsComposePrice.setComposeType(composeGoods ? GoodsComposeNaked.ComposeType.COMPOSE : GoodsComposeNaked.ComposeType.FEE);
                            goodsComposePrice.setComposePrice(compose.getComposePrice());
                            goodsComposePrice.setComposePiecePrice(compose.getComposePiecePrice());
                            goodsComposePrice.setComposePackagePrice(compose.getComposePackagePrice());
                            goodsComposePrice.setComposeFractionPrice(compose.getComposeFractionPrice());
                            goodsComposePrice.setIsDeleted(GoodsUtils.DeleteFlag.OK);
                            UserFillUtils.fillCreatedBy(goodsComposePrice, employeeId);
                            existGoodComposePriceList.add(goodsComposePrice);
                        }
                        doCalculateGoodsComposePrice(updateGoodsComposePriceList, existGoodComposePriceList,
                                goodsIdToGoodsCompose, subClinicGoodsInfo.getPackagePrice(), subClinicGoodsInfo.getOrgan().getId(),
                                GoodsUtils.SwitchFlag.ON, goodsInfoModifyManageService, composeGoods);
                    }
                }
            }
        }
        if (!needUpdateConfigIdList.isEmpty()) {
            for (String subClinicId : needUpdateConfigIdList) {
                ClinicConfig subClinicConfig = cisClinicService.getClinicConfig(subClinicId);
                updateClinicIndependentPricing(subClinicConfig, subClinicId);
            }
        }
        if (!CollectionUtils.isEmpty(updatePriceList)) {
            goodsPriceRepository.saveAll(updatePriceList);
        }
        if (!CollectionUtils.isEmpty(deletePriceList)) {
            goodsPriceRepository.deleteAll(deletePriceList, employeeId);
        }
        if (!CollectionUtils.isEmpty(goodsLogList)) {
            goodsLogRepository.saveAll(goodsLogList);
        }
        if (!CollectionUtils.isEmpty(updateGoodsComposeList)) {
            goodsComposeNakedRepository.saveAll(updateGoodsComposeList);
        }
        if (!CollectionUtils.isEmpty(updateGoodsComposePriceList)) {
            goodsComposePriceRepository.saveAll(updateGoodsComposePriceList);
        }
        if (!CollectionUtils.isEmpty(updateGoodsMedicalStatList)) {
            goodsMedicalStatRepository.saveAll(updateGoodsMedicalStatList);
        }
        /**
         * 改了价格必须清理Rediscache
         * */
        GoodsUtils.runAfterTransaction(() -> {
            goodsRedisUtils.clearGoodsRedisCache(goods.getOrganId(), Arrays.asList(goods.getId()));
        });
    }

    private void updateClinicIndependentPricing(ClinicConfig clinicConfig, String clinicId) {
        if (clinicConfig.getIndependentPricing() == GoodsPrice.ClinicPriceFlag.NO_PRIV_AND_NO_SUB_PRICE) {
            goodsClinicConfigRepository.findByClinicId(clinicId).ifPresent(goodsClinicConfig -> {
                if (clinicConfig.isAbcPharmacy()) {
                    /**
                     * 产品文档
                     * A连锁统一定价 → C门店单独定价-门店定
                     *      ●门店已建档（入库）商品价格同步：
                     *              ○连锁统一定价商品：总部价格作为初始价格，切换后门店价格不受总部价格变化影响，需单独设置
                     *              ○单独设置定价模式商品：维持原有售价，不受模式切换影响
                     *      ●门店未建档商品价格同步：门店入库成功建档时，若总部已为门店定义价格，则执行总部定价价格；若总部未为门店定义价格，则同步总部此刻售价为门店初始价格
                     * */
                    goodsClinicConfig.setIndependentPricing(GoodsPrice.ClinicPriceFlag.HAS_PRIV_AND_HAS_SUB_PRICE_BUT_SUBPRICE_MAY_NOT_BORN_NOW);
                } else {
                    goodsClinicConfig.setIndependentPricing(GoodsPrice.ClinicPriceFlag.NO_PRIV_AND_HAS_SUB_PRICE);
                }
                GoodsUtils.runAfterTransaction(() -> goodsRedisUtils.reloadChainGoodsConfig(clinicConfig.getChainId(), clinicConfig.getClinicId()));
            });
        }
    }

    public void doCalculateGoodsComposePrice(List<GoodsComposePrice> updateComposePriceList, List<GoodsComposePrice> existComposePriceList,
                                             Map<String, GoodsComposeNaked> goodsIdToGoodsCompose, BigDecimal composePrice, String clinicId,
                                             int useCount, GoodsInfoModifyManageService goodsInfoModifyManageService,
                                             boolean composeFlag) {
        if (CollectionUtils.isEmpty(existComposePriceList) || CollectionUtils.isEmpty(goodsIdToGoodsCompose)) {
            return;
        }
        if (composePrice == null || composePrice.compareTo(BigDecimal.ZERO) < 0) {
            return;
        }
        sLogger.info("doCalculateGoodsComposePrice, before existComposePriceList:{}, composePrice = {}, useCount = {}", JsonUtils.dump(existComposePriceList), composePrice, useCount);
        Map<String, GoodsComposePrice> srvGoodsIdToComposePrice = existComposePriceList.stream().collect(Collectors.toMap(GoodsComposePrice::getGoodsId, Function.identity(), (a, b) -> a));
        CalGoodsComposePriceReq req = new CalGoodsComposePriceReq();
        req.setComposePrice(composePrice);
        req.setModifyIdentification(3);
        req.setUseCount(composeFlag ? YesOrNo.NO : useCount);
        req.setType(composeFlag ? YesOrNo.NO : CalGoodsComposePriceReq.NO_GOODS_TYPE);
        req.setItemList(new ArrayList<>());
        for (GoodsComposePrice priceItem : existComposePriceList) {
            CalGoodsComposePriceReq.CalGoodsComposePriceItem item = new CalGoodsComposePriceReq.CalGoodsComposePriceItem();
            GoodsComposeNaked goodsComposeNaked = goodsIdToGoodsCompose.get(priceItem.getGoodsId());
            if (goodsComposeNaked == null) {
                throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "不存在费用项子项");
            }
            item.setGoodsId(priceItem.getGoodsId());
            item.setComposeUseDismounting((int) goodsComposeNaked.getComposeUseDismounting());
            item.setComposePrice(priceItem.getComposePrice());
            item.setComposePackagePrice(priceItem.getComposePackagePrice());
            item.setComposePackageCount(goodsComposeNaked.getComposePackageCount());
            item.setComposePiecePrice(priceItem.getComposePiecePrice());
            item.setComposePieceCount(goodsComposeNaked.getComposePieceCount());
            req.getItemList().add(item);
        }
        GoodsInfoModifyManageService.calculateComposePrice(req, cisClinicService.getClinicConfig(clinicId),goodsRedisUtils);
        for (CalGoodsComposePriceReq.CalGoodsComposePriceItem item : req.getItemList()) {
            GoodsComposePrice goodsComposePrice = srvGoodsIdToComposePrice.get(item.getGoodsId());
            if (goodsComposePrice != null) {
                goodsComposePrice.setComposePackagePrice(item.getComposePackagePrice());
                goodsComposePrice.setComposePrice(item.getComposePrice());
                goodsComposePrice.setComposeFractionPrice(item.getComposeFractionPrice());
                goodsComposePrice.setComposePiecePrice(item.getComposePiecePrice());
                updateComposePriceList.add(goodsComposePrice);
            }
        }
        sLogger.info("doCalculateGoodsComposePrice, after existComposePriceList:{}, composePrice = {}, useCount = {}", JsonUtils.dump(existComposePriceList), composePrice, useCount);
    }

    /**
     * 获取当天的连锁诊所生成了多少入库单号
     * 如果前一orderNo有重复，修复
     *
     * @param chainId 连锁ID
     */
    public Integer getTodayOrderNoIndexAndFixConflictPreOrderNo(String PREFIX_TJ, String chainId) {
        String prefix = PREFIX_TJ + DateUtils.formatLocalDate(LocalDate.now(), GoodsStockInOrderServiceBase.sFormatterDate);
        /**
         * 返回的数据 第一列 当前的maxNo 第二列 totalCount
         * */
        List<Object> dbRawResult = null;
        try {
            dbRawResult = goodsModifyPriceOrderRepository.maxOrderNo(chainId, prefix);
        } catch (Exception exp) {
            sLogger.error("getTodayOrderNoIndexAndFixConflictPreOrderNo getCurrentOrderNo Err={}", exp);
        }
        if (CollectionUtils.isEmpty(dbRawResult) || dbRawResult.get(0) == null) { //
            return 0;
        }

        /**
         * 解析返回结果
         * */
        Object[] rowArray = (Object[]) dbRawResult.get(0); //只会有一行
        String maxCGNo = (String) rowArray[0]; //第一列
        if (StringUtils.isEmpty(maxCGNo) || maxCGNo.length() != TJNO_LENGTH) { //无效的单号直接拦截 从0 开始
            return 0;
        }
        int currentMaxNo = Integer.parseInt(maxCGNo.substring(10));// 解析出来 日期后面的单号
        int totalCount = ((BigInteger) rowArray[1]).intValue();

        /**
         * 没有重复
         * */
        if (currentMaxNo == totalCount) {
            return currentMaxNo;
        }

        /**
         * 重复的单子 按ID ASC
         * 不是主路径，异常也有继续创建正常的单据
         * */
        try {
            List<GoodsModifyPriceOrder> conflictOrderList = goodsModifyPriceOrderRepository.findByChainIdAndOrderNoOrderByIdAsc(chainId, maxCGNo);
            if (CollectionUtils.isEmpty(conflictOrderList)) { //防御性编程，理论上永远进不来
                return currentMaxNo;
            }
            sLogger.info("getTodayOrderNoIndexAndFixConflictPreOrderNo conflictOrderList = {}", conflictOrderList);
            /**
             *  修复前几次重复的单号。
             *  重复的单号是最后一次入库，用户反馈来了直接改库
             * */
            for (int i = 0; i < conflictOrderList.size(); i++) {
                if (i == 0) { //安全 ,i 不要从0开始
                    continue;
                }
                GoodsModifyPriceOrder order = conflictOrderList.get(i);
                int orderNo = Integer.parseInt(order.getOrderNo().substring(10)) + i;
                order.setOrderNo(genTodayOrderNo(PREFIX_TJ, orderNo));
                currentMaxNo++;
            }
            goodsModifyPriceOrderRepository.saveAll(conflictOrderList);
        } catch (Exception exp) {
            sLogger.error("getTodayOrderNoIndexAndFixConflictPreOrderNo fixConflictOrderNo Err={}", exp);
        }
        return currentMaxNo;

    }

    public void doReviewOrRejectPriceOrder(int reviewStatus,
                                           Long gspInstId,
                                           String chainId,
                                           String employeeId,
                                           String comment,
                                           Long modifyPriceOrderId) {

        GoodsModifyPriceOrder goodsModifyPriceOrder = goodsModifyPriceOrderRepository.findByChainIdAndId(chainId, modifyPriceOrderId).orElse(null);
        if (goodsModifyPriceOrder == null) {
            return;
        }
        GoodsModifyPriceOrder beforeGoodsModifyPriceOrder = JsonUtils.readValue(JsonUtils.dump(goodsModifyPriceOrder), GoodsModifyPriceOrder.class);
        goodsModifyPriceOrder.setGspStatus(reviewStatus);
        GoodsGsp gsp = goodsModifyPriceOrder.getGsp();
        if (gsp == null) {
            gsp = new GoodsGsp();
            goodsModifyPriceOrder.setGsp(gsp);
        }
        gsp.setGspInstId(gspInstId);

        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(goodsModifyPriceOrder.getClinicId());
        List<String> updateGoodsStatGoodsIdList = new ArrayList<>();
        List<GoodsModifyPriceOrderItem> svrExistedPriceOrderItemList = goodsModifyPriceOrderItemRepository.findAllByOrderIdInAndStatusAndIsDeleted(
                Arrays.asList(goodsModifyPriceOrder.getId()),
                GoodsModifyPriceOrderItem.Status.WAITING,
                YesOrNo.NO);
        if (CollectionUtils.isEmpty(svrExistedPriceOrderItemList)) {
            return;
        }
        goodsModifyPriceOrder.setList(svrExistedPriceOrderItemList);
        updateGoodsStatGoodsIdList.addAll(svrExistedPriceOrderItemList.stream().map(GoodsModifyPriceOrderItem::getGoodsId).distinct().collect(Collectors.toList()));
        /**
         * 被拒绝掉了，写log退出
         * */
        if (reviewStatus == ApprovalConstant.InstStatus.REJECT) {
            goodsModifyPriceOrder.setStatus(GoodsModifyPriceOrder.Status.DENIED);
            svrExistedPriceOrderItemList.forEach(it -> {
                it.setStatus(GoodsModifyPriceOrderItem.Status.FAILED);
                UserFillUtils.fillLastModifiedBy(it, employeeId);
            });
            createGoodsModifyPriceOrderLog(chainId, goodsModifyPriceOrder.getClinicId(), modifyPriceOrderId, GoodsModifyPriceOrderLog.ACTION_REVIEW_REJECT, employeeId, comment, goodsModifyPriceOrder);
        } else if (reviewStatus == ApprovalConstant.InstStatus.CANCEL) {
            goodsModifyPriceOrder.setStatus(GoodsModifyPriceOrder.Status.WITHDRAW);
            svrExistedPriceOrderItemList.forEach(it -> {
                it.setStatus(GoodsModifyPriceOrderItem.Status.FAILED);
                UserFillUtils.fillLastModifiedBy(it, employeeId);
            });
            createGoodsModifyPriceOrderLog(chainId, goodsModifyPriceOrder.getClinicId(), modifyPriceOrderId, GoodsModifyPriceOrderLog.ACTION_WITHDRAW, employeeId, comment, goodsModifyPriceOrder);
        } else if (reviewStatus == ApprovalConstant.InstStatus.PASS) {
            long sTime = System.currentTimeMillis();
            createGoodsModifyPriceOrderLog(chainId, goodsModifyPriceOrder.getClinicId(), modifyPriceOrderId, GoodsModifyPriceOrderLog.ACTION_REVIEW_PASS, employeeId, comment, goodsModifyPriceOrder);
            /**
             * 立即生效
             * 或者已经过期了
             * 直接改
             * */
            if (goodsModifyPriceOrder.getEffected() == null
                    //审核通过回来，发现改价单的生效时间过期了如何处理？
                    // 目前按直接改价处理
                    || goodsModifyPriceOrder.getEffected().compareTo(Instant.now()) <= 0) {
                List<Goods> goodsList = goodsRepository.findAllByIdInAndOrganIdAndStatusLessThan(updateGoodsStatGoodsIdList, clinicConfig.getChainId(), GoodsConst.GoodsStatus.DELETE);
                if (CollectionUtils.isEmpty(goodsList)) {
                    return;
                }
                Map<String, Goods> goodsIdToGoods = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (a, b) -> a));
                ClinicGoodsPriceWrapper priceWrapper = new ClinicGoodsPriceWrapper();
                priceWrapper.setGoodsPriceList(goodsPriceRepository.findAllByChainIdAndGoodsIdIn(clinicConfig.getChainId(), updateGoodsStatGoodsIdList));

                /**
                 * 改价核心函数
                 * 从Gsp审核异步回掉回来的改价
                 * */
                updateGoodsPriceByModifyPriceOrder(goodsModifyPriceOrder,
                        svrExistedPriceOrderItemList,
                        clinicConfig,
                        goodsIdToGoods,
                        priceWrapper,
                        employeeId);
                //写数据库落地
                if (!CollectionUtils.isEmpty(svrExistedPriceOrderItemList)) {
                    goodsModifyPriceOrderItemRepository.saveAll(svrExistedPriceOrderItemList);
                }
                createGoodsModifyPriceOrderLog(chainId, goodsModifyPriceOrder.getClinicId(), modifyPriceOrderId, GoodsModifyPriceOrderLog.ACTION_FINISHED, employeeId, null, goodsModifyPriceOrder);
                goodsModifyPriceOrder.setEffected(Instant.now());
                goodsModifyPriceOrder.setStatus(GoodsModifyPriceOrder.Status.FINISHED);
                GoodsUtils.runAfterTransaction(() -> goodsStatService.refreshGoodsStatSync(updateGoodsStatGoodsIdList, chainId, clinicConfig));
            } else {
                goodsModifyPriceOrder.setStatus(GoodsModifyPriceOrder.Status.WAITING_AFFECTED);
            }
        }
        UserFillUtils.fillLastModifiedBy(goodsModifyPriceOrder, employeeId);
        goodsModifyPriceOrderRepository.save(goodsModifyPriceOrder);

        /**
         * 要刷GoodsStat
         * */
        Set<String> updateGoodsStatClinicIdList = new HashSet<>();
        if (goodsModifyPriceOrder.getExtendInfos() != null && !CollectionUtils.isEmpty(goodsModifyPriceOrder.getExtendInfos().getAffectedClinicList())) {
            updateGoodsStatClinicIdList.addAll(goodsModifyPriceOrder.getExtendInfos().getAffectedClinicList());
            if (updateGoodsStatClinicIdList.contains(clinicConfig.getChainId())) {
                updateGoodsStatClinicIdList.add(GoodsUtils.WHOLE_CHAIN_ID);
            }
        }
        GoodsUtils.runAfterTransaction(() -> {
            applicationContext.getBean(GoodsPriceService.class).updateGoodsStatWaitingEffectPrice(goodsModifyPriceOrder.getChainId(), updateGoodsStatGoodsIdList, updateGoodsStatClinicIdList);
        });

        if (beforeGoodsModifyPriceOrder.getStatus() != goodsModifyPriceOrder.getStatus()) {
            aftersStatusChange(beforeGoodsModifyPriceOrder, goodsModifyPriceOrder);
        }

        if (reviewStatus == ApprovalConstant.InstStatus.PASS) {
            goodsModifyPriceOrderListeners.forEach(goodsModifyPriceOrderListener -> {
                goodsModifyPriceOrderListener.onGoodsModifyPriceOrderConfirm(goodsModifyPriceOrder, goodsModifyPriceOrder.getList(), employeeId);
            });
        }
    }

    /**
     * 审批流回来过后的调价
     */
    public void modifyPriceOrderApprovalCallBack(ApprovalProcessInstVO callBack) {
        sLogger.info("modifyPriceOrderApprovalCallBack:{}", JsonUtils.dump(callBack));
        doReviewOrRejectPriceOrder(callBack.getStatus(),
                callBack.getId(),
                callBack.getChainId(),
                callBack.getAssigneeUserId(),
                callBack.getComments(),
                GoodsUtils.getLongId(callBack.getBusinessId()));
    }

    /**
     * 可读的盘点单号
     */
    public String genTodayOrderNo(String PREFIX_TJ, Integer currentIndex) {
        String prefix = PREFIX_TJ + DateUtils.formatLocalDate(LocalDate.now(), GoodsStockInOrderServiceBase.sFormatterDate);
        return prefix + String.format("%05d", currentIndex % 99999);
    }

    /**
     * 一个小时改2000个调价单 sleep 10s
     */
    @Transactional
    public void scheduleXPriceOrderUpdatePrice(Long modifyPriceOrderId) {
        GoodsModifyPriceOrder goodsModifyPriceOrder = goodsModifyPriceOrderRepository.findById(modifyPriceOrderId).orElse(null);
        if (goodsModifyPriceOrder == null || goodsModifyPriceOrder.getIsDeleted() == YesOrNo.YES) {
            sLogger.error("scheduleXPriceOrderUpdatePrice:{}", goodsModifyPriceOrder);
            return;
        }
        if (goodsModifyPriceOrder.getStatus() != GoodsModifyPriceOrder.Status.WAITING_AFFECTED) {
            sLogger.error("scheduleXPriceOrderUpdatePrice:不是需要待生效的单子 order:{}", goodsModifyPriceOrder);
            return;
        }
        long sTime = System.currentTimeMillis();
        String chainId = goodsModifyPriceOrder.getChainId();
        String employeeId = UserFillUtils.DEFUALT_ID;
        /**
         * 通过
         * */
        createGoodsModifyPriceOrderLog(chainId, goodsModifyPriceOrder.getClinicId(), modifyPriceOrderId, GoodsModifyPriceOrderLog.ACTION_REVIEW_PASS, employeeId, null, goodsModifyPriceOrder);

        /**
         * 立即生效
         * */
        List<GoodsModifyPriceOrderItem> svrExistedPriceOrderItemList = goodsModifyPriceOrderItemRepository.findByOrderIdAndIsDeleted(goodsModifyPriceOrder.getId(), YesOrNo.NO);
        if (CollectionUtils.isEmpty(svrExistedPriceOrderItemList)) {
            sLogger.error("scheduleXPriceOrderUpdatePrice:调价单内容为空 order:{}", goodsModifyPriceOrder);
            return;
        }
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(goodsModifyPriceOrder.getClinicId());
        List<String> goodsIdList = svrExistedPriceOrderItemList.stream().map(GoodsModifyPriceOrderItem::getGoodsId).distinct().collect(Collectors.toList());
        List<Goods> goodsList = goodsRepository.findAllByIdInAndOrganIdAndStatusLessThan(goodsIdList, clinicConfig.getChainId(), GoodsConst.GoodsStatus.DELETE);
        if (CollectionUtils.isEmpty(goodsList)) {
            sLogger.error("scheduleXPriceOrderUpdatePrice:goods列表为 order:{}", goodsModifyPriceOrder);
            return;
        }
        Map<String, Goods> goodsIdToGoods = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (a, b) -> a));
        ClinicGoodsPriceWrapper priceWrapper = new ClinicGoodsPriceWrapper();
        priceWrapper.setGoodsPriceList(goodsPriceRepository.findAllByChainIdAndGoodsIdIn(clinicConfig.getChainId(), goodsIdList));

        /**
         * 改价核心函数
         * 定时生效，后台的自动改价
         * */
        try {
            updateGoodsPriceByModifyPriceOrder(goodsModifyPriceOrder,
                    svrExistedPriceOrderItemList,
                    clinicConfig,
                    goodsIdToGoods,
                    priceWrapper,
                    employeeId);

        } catch (Exception exp) {
            sLogger.error("scheduleXPriceOrderUpdatePrice:调价发生异常exp={}", exp);
        }
        //写数据库落地
        if (!CollectionUtils.isEmpty(svrExistedPriceOrderItemList)) {
            goodsModifyPriceOrderItemRepository.saveAll(svrExistedPriceOrderItemList);
        }

        GoodsUtils.runAfterTransaction(() -> goodsStatService.refreshGoodsStatSync(goodsIdList, chainId, clinicConfig));
        createGoodsModifyPriceOrderLog(chainId, goodsModifyPriceOrder.getClinicId(), modifyPriceOrderId, GoodsModifyPriceOrderLog.ACTION_FINISHED, employeeId, null, goodsModifyPriceOrder);
        goodsModifyPriceOrder.setStatus(GoodsModifyPriceOrder.Status.FINISHED);
        UserFillUtils.fillLastModifiedBy(goodsModifyPriceOrder, employeeId);
        goodsModifyPriceOrderRepository.save(goodsModifyPriceOrder);
        /**
         * 要刷GoodsStat
         * */
        Set<String> updateGoodsStatClinicIdList = new HashSet<>();
        if (goodsModifyPriceOrder.getExtendInfos() != null && !CollectionUtils.isEmpty(goodsModifyPriceOrder.getExtendInfos().getAffectedClinicList())) {
            updateGoodsStatClinicIdList.addAll(goodsModifyPriceOrder.getExtendInfos().getAffectedClinicList());
            if (updateGoodsStatClinicIdList.contains(clinicConfig.getChainId())) {
                updateGoodsStatClinicIdList.add(GoodsUtils.WHOLE_CHAIN_ID);
            }
        }
        GoodsUtils.runAfterTransaction(() -> {
            applicationContext.getBean(GoodsPriceService.class).updateGoodsStatWaitingEffectPrice(goodsModifyPriceOrder.getChainId(), goodsIdList, updateGoodsStatClinicIdList);
        });
    }

    /**
     * 拉一个goods一次调价的详情
     */
    public static Map<Long, ModifyPriceTipsView> getModifyPriceOrderItemTips(CisClinicService cisClinicService,
                                                                             GoodsModifyPriceOrderItemRepository goodsModifyPriceOrderItemRepository,
                                                                             GoodsModifyPriceOrderRepository goodsModifyPriceOrderRepository,
                                                                             GoodsMapper goodsMapper,
                                                                             GoodsRedisUtils goodsRedisUtils,
                                                                             String chainId,
                                                                             List<Long> orderItemIdList,
                                                                             String clinicId,
                                                                             Integer filterNotSubmitPrice,
                                                                             CacheSupplier<Map<String, String>> memberTypeMapSupplier) throws CisGoodsServiceException {
        Map<Long, ModifyPriceTipsView> result = new HashMap<>();
        if (CollectionUtils.isEmpty(orderItemIdList)) {
            return result;
        }
        ClinicConfig clinicConfig = cisClinicService.getClinicConfig(chainId);
        if (clinicConfig == null) {
            return result;
        }
        Map<String, String> memberTypeIdToMemberTypeName = clinicConfig.isAbcPharmacy() ? memberTypeMapSupplier.get() : Collections.emptyMap();
        List<GoodsModifyPriceOrderItem> itemList = goodsModifyPriceOrderItemRepository.findByItemIdInAndIsDeleted(orderItemIdList, YesOrNo.NO);
        if (itemList.isEmpty()) {
            return result;
        }
        Map<Long, GoodsModifyPriceOrder> orderIdToOrder = goodsModifyPriceOrderRepository.findByChainIdAndIdIn(chainId, itemList.stream().map(it -> it.getOrderId()).collect(Collectors.toList())).stream().filter(
                it -> filterNotSubmitPrice != null && filterNotSubmitPrice == YesOrNo.YES ? it.getStatus() != GoodsModifyPriceOrder.Status.DRAFT : true
        ).collect(Collectors.toMap(GoodsModifyPriceOrder::getId, Function.identity(), (a, b) -> a));
        itemList = itemList.stream().filter(it -> orderIdToOrder.containsKey(it.getOrderId())).collect(Collectors.toList());
        if (itemList.isEmpty()) {
            return result;
        }
        /**
         * 批量加载历史版本Goods
         * */
        GoodsHisVersionManager goodsHisVersionManager = new GoodsHisVersionManager(cisClinicService.getClinicConfig(clinicId), goodsMapper, goodsRedisUtils);
        goodsHisVersionManager.loadGoodsSnapHisVersion(itemList.stream().map(it -> {
            if (it.getGoodsSnap() != null) {
                it.getGoodsSnap().setHelperGoodsId(it.getGoodsId());
            }
            return it.getGoodsSnap();
        }).collect(Collectors.toList()));
        //itemList 按itemId进行分组
        Map<Long, List<GoodsModifyPriceOrderItem>> itemIdToItemList = itemList.stream().filter(it -> it.getItemId() != null).collect(Collectors.groupingBy(GoodsModifyPriceOrderItem::getItemId));
        itemIdToItemList.forEach((itemId, list) -> {
            GoodsModifyPriceOrderItem itemFirst = list.get(0);
            if (itemFirst == null || itemFirst.getStatus() != GoodsModifyPriceOrderItem.Status.WAITING || itemFirst.getIsDeleted() == YesOrNo.YES) {
                return;
            }
            GoodsModifyPriceOrder order = orderIdToOrder.get(itemFirst.getOrderId());
            if (order == null || order.getStatus() >= GoodsModifyPriceOrder.Status.FINISHED) {
                return;
            }
            ModifyPriceTipsView clientRsp = new ModifyPriceTipsView();
            clientRsp.setCreated(order.getCreated());
            clientRsp.setEffected(order.getEffected());
            clientRsp.setOrderId(order.getId());
            clientRsp.setOrderNo(order.getOrderNo());
            clientRsp.setStatus(order.getStatus());
            clientRsp.setOpType(order.getOpType());
            clientRsp.setSourceType(order.getSourceType());
            clientRsp.setStatusName(GoodsModifyPriceOrder.orderStatusName(order.getStatus()));
            clientRsp.setLastSupplierName(itemFirst.getLastSupplierName());

            // 价签打印没这么快，先返回数据兼容
            clientRsp.setPackageOpType(itemFirst.getPackageOpType());
            clientRsp.setPieceOpType(itemFirst.getPieceOpType());

            clientRsp.setPackageUpPercent(itemFirst.getPackageUpPercent());
            clientRsp.setPieceUpPercent(itemFirst.getPieceUpPercent());

            clientRsp.setBeforePackagePrice(itemFirst.getBeforePackagePrice());
            clientRsp.setAfterPackagePrice(itemFirst.getAfterPackagePrice());

            clientRsp.setBeforePiecePrice(itemFirst.getBeforePiecePrice());
            clientRsp.setAfterPiecePrice(itemFirst.getAfterPiecePrice());

            clientRsp.setBeforePriceType(itemFirst.getBeforePriceType());
            clientRsp.setAfterPriceType(itemFirst.getAfterPriceType());

            clientRsp.setBeforeMakeUpPercent(itemFirst.getBeforeMakeUpPercent());
            clientRsp.setAfterMakeUpPercent(itemFirst.getAfterMakeUpPercent());

            clientRsp.setModifyBasePackagePrice(itemFirst.getModifyBasePackagePrice());
            clientRsp.setModifyBasePiecePrice(itemFirst.getModifyBasePiecePrice());

            clientRsp.setPackageCostPrice(itemFirst.getPackageCostPrice());
            clientRsp.setAvgPackageCostPrice(itemFirst.getAvgPackageCostPrice());
            clientRsp.setProfitRat(itemFirst.getProfitRat());
            clientRsp.setBeforeProfitRat(itemFirst.getBeforeProfitRat());
            /**
             * 加载goods历史版本
             * */
            clientRsp.setGoods(itemFirst.getAssembleGoodsSnap(goodsHisVersionManager));
            for (GoodsModifyPriceOrderItem item : list) {
                if (item == null || item.getStatus() != GoodsModifyPriceOrderItem.Status.WAITING || item.getIsDeleted() == YesOrNo.YES) {
                    continue;
                }
                clientRsp.getWaitingEffectPriceList().add(toModifyPriceOrderItemView(order, item, goodsHisVersionManager, memberTypeIdToMemberTypeName));
            }
            result.put(itemFirst.getItemId(), clientRsp);
        });
        return result;
    }

    /**
     * 功能：药店和医院 跟 诊所不太一样。
     * 1.诊所是没设置过子店价格跟随总部，用总部
     * 2.药店和医院：子店有定价权没设置过子店定价，子店价格不会跟谁总部(入库和总部改价格)
     * 诊所的逻辑是ABC一直的逻辑，不敢调整
     * GoodsRedisCache 是完整的goodsRedisCache检查是否实例化价格 /如果不是完整的只能是某个门店的， subClinicIdList  是否入库过
     */
    public Map<String, List<GoodsPrice>> instantSubClinicPriceNowByWholeGoodsRedisCache(List<GoodsRedisCache> goodsList,
                                                                                        ClinicConfig chainConfig,
                                                                                        List<String> subClinicIdList,
                                                                                        String employeeId) {
        String chainId = chainConfig.getChainId();
        //药店，非单店模式
        //药店加载所有子店价格
        if (!chainConfig.isAbcPharmacy() && !chainConfig.isHospital()) {
            return null;
        }
        Map<String, List<GoodsPrice>> goodsIdToSubGoodsPriceList = goodsPriceRepository.findAllByChainIdAndGoodsIdIn(chainConfig.getChainId(),
                        goodsList.stream()
                                .map(GoodsRedisCache::getId)
                                .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.groupingBy(GoodsPrice::getGoodsId));
        List<GoodsPrice> newGoodsPriceList = new ArrayList<>();
        List<GoodsPrice> newDiscountGoodsPriceList = new ArrayList<>();
        /**
         * 这一批Goods里面有进价加成的
         * */
        Map<String, GoodsClinicConfig> clinicIdToClinicConfigRepo = new HashMap<>();
        clinicIdToClinicConfigRepo.putAll(goodsClinicConfigRepository.findByChainId(chainId).stream().collect(Collectors.toMap(GoodsClinicConfig::getClinicId, Function.identity(), (a, b) -> a)));
        for (String clinicId : subClinicIdList) {
            ClinicConfig subClinicConfig = cisClinicService.getClinicConfig(clinicId);
            //
            if (subClinicConfig.getIndependentPricing() != GoodsPrice.ClinicPriceFlag.HAS_PRIV_AND_HAS_SUB_PRICE_BUT_SUBPRICE_MAY_NOT_BORN_NOW
                    && subClinicConfig.getIndependentPricing() != GoodsPrice.ClinicPriceFlag.HAS_PRIV_AND_HAS_SUB_PRICE_HAS_PRICE_MAKEUP_MAY_NOT_BORN_NOW) {
                continue;
            }

            for (GoodsRedisCache goods : goodsList) {
                //库存
                if (GoodsUtils.isStockGoods(goods.getType()) && !goods.isStockInOfClinic(clinicId)) {
                    // 产品逻辑要入库的时候才实际初始化价格
                    continue;
                }

                List<GoodsPrice> subGoodsPriceList = goodsIdToSubGoodsPriceList.getOrDefault(goods.getId(), new ArrayList<>());
                List<GoodsPrice> chainPromotionGoodsPriceList = subGoodsPriceList.stream().filter(it -> it.getTargetType() == GoodsPrice.PriceTargetType.MEMBER
                        && it.getOwnType() == 0
                        && GoodsUtils.compareStrEqual(it.getOrganId(), chainId)).collect(Collectors.toList());


                //已经有定价了直接continue
                if (subGoodsPriceList.stream().noneMatch(it ->
                        GoodsUtils.compareStrEqual(it.getOrganId(), subClinicConfig.getClinicId())
                                && it.getTargetType() == GoodsPrice.PriceTargetType.GOODS)) {
                    GoodsPrice goodsPriceSub = instantAClinicPrice(clinicIdToClinicConfigRepo.get(subClinicConfig.getClinicId()), goods.getId(),
                            goods.getChainPackagePrice(), goods.getChainPiecePrice(), goods.getChainPackageCostPrice(),
                            goods.getChainPriceType(), goods.getChainPriceMakeupPercent(),
                            GoodsPrice.PriceTargetType.GOODS, null, null, null,
                            employeeId);
                    newGoodsPriceList.add(goodsPriceSub);
                    subGoodsPriceList.add(goodsPriceSub);
                    goodsIdToSubGoodsPriceList.put(goods.getId(), subGoodsPriceList);
                }
                /***
                 * 会员折扣价的实例化
                 * */
                for (GoodsPrice chainPrice : chainPromotionGoodsPriceList) {
                    if (subGoodsPriceList.stream().anyMatch(it ->
                            GoodsUtils.compareStrEqual(it.getOrganId(), subClinicConfig.getClinicId())
                                    && GoodsUtils.compareStrEqual(it.getMemberTypeId(), chainPrice.getMemberTypeId())
                                    && it.getTargetType() == chainPrice.getTargetType())) {
                        continue;
                    }
                    GoodsPrice discountGoodsPriceSub = instantAClinicPrice(clinicIdToClinicConfigRepo.get(subClinicConfig.getClinicId()), goods.getId(),
                            chainPrice.getPackagePrice(), chainPrice.getPiecePrice(), chainPrice.getPackageCostPrice(),
                            chainPrice.getPriceType(), chainPrice.getPriceMakeupPercent(),
                            chainPrice.getTargetType(), chainPrice.getMemberTypeId(), chainPrice.getDiscountType(), chainPrice.getDiscountValue(),
                            employeeId);
                    newGoodsPriceList.add(discountGoodsPriceSub);
                    subGoodsPriceList.add(discountGoodsPriceSub);
                    newDiscountGoodsPriceList.add(discountGoodsPriceSub);
                    goodsIdToSubGoodsPriceList.put(goods.getId(), subGoodsPriceList);
                }
            }
        }
        /**
         * 这里直接存 不审核了
         * TODO 没写日志
         * */
        if (!newGoodsPriceList.isEmpty()) {
            goodsPriceRepository.saveAll(newGoodsPriceList);
            goodsClinicConfigRepository.saveAll(clinicIdToClinicConfigRepo.values().stream().collect(Collectors.toList()));
            GoodsUtils.runAfterTransaction(() -> {
                goodsRedisUtils.reloadChainGoodsConfig(chainConfig.getChainId(), null);
                rocketMqProducer.sendClearGoodsConfigCacheMessage(new ClearGoodsConfigCacheMessage(chainConfig.getChainId(), null, null, null));
            });
        }

        //TODO 发送给promotion 这里不是全部哦
        if (!newDiscountGoodsPriceList.isEmpty()) {

        }
        return goodsIdToSubGoodsPriceList;
    }

    private GoodsPrice instantAClinicPrice(GoodsClinicConfig clinicConfigRepo, String goodsId,
                                           BigDecimal initPackagePrice, BigDecimal initPiecePrice, BigDecimal initPackageCostPrice,
                                           int initPriceType, BigDecimal initPriceMakeUpPercent,
                                           int initTargetType, String memberTypeId, Integer discountType, BigDecimal discountValue,
                                           String employeeId) {
        GoodsPrice goodsPriceSub = new GoodsPrice();
        goodsPriceSub.setId(AbcIdUtils.getUUIDLong());
        goodsPriceSub.setOrganId(clinicConfigRepo.getClinicId());
        goodsPriceSub.setChainId(clinicConfigRepo.getChainId());
        goodsPriceSub.setGoodsId(goodsId);
        goodsPriceSub.setMemberTypeId(memberTypeId);
        goodsPriceSub.setDiscountType(discountType);
        goodsPriceSub.setDiscountValue(discountValue);
        goodsPriceSub.setOwnType(0); //门店的

        //跟随总部
        if (GoodsUtils.isAbcPharmacy(clinicConfigRepo.getHisType())) {
            goodsPriceSub.setIndividualPricingType(GoodsConst.IndividualPricingType.FOLLOW_CHAIN_CONFIG);
        }
        //子店 这里一定是有定价权限的
        goodsPriceSub.setSubPriceFlag(GoodsPrice.SubPriceFlag.HAS_PRIV_AND_HAS_SUB_PRICE_SUBCLINIC_SET);
        goodsPriceSub.setPackagePrice(initPackagePrice);
        goodsPriceSub.setPackageCostPrice(initPackageCostPrice);
        goodsPriceSub.setPiecePrice(initPiecePrice);
        goodsPriceSub.setPriceType(initPriceType);
        goodsPriceSub.setTargetType(initTargetType);
        //拷贝
        goodsPriceSub.setPriceMakeupPercent(initPriceMakeUpPercent);
        clinicConfigRepo.setHasSubPriceCount(clinicConfigRepo.getHasSubPriceCount() + 1);
        if (GoodsUtils.checkFlagOn(goodsPriceSub.getPriceType(), GoodsConst.PriceType.PKG_PRICE_MAKEUP)) {
            clinicConfigRepo.setHasSubPriceUpCount(clinicConfigRepo.getHasSubPriceUpCount() + 1);
        }
        /**
         * https://www.tapd.cn/22044681/bugtrace/bugs/view?bug_id=1122044681001055775&url_cache_key=from_url_bug_query_list_8991e5cf7874df5c8ae7d3ffcd153ed9
         * 2024.05.产品逻辑：如果是进价加成的药品，因为入库 子店价格被拷贝了，要把门店上自主定价的开关打开
         * 只要有一个药品总部开了进价加成  入库 自动打开开关
         * */
        if (GoodsUtils.checkFlagOn(initPriceType, GoodsConst.PriceType.PKG_PRICE_MAKEUP)) {
            clinicConfigRepo.setPriceMode(3); //进价加成
        }
        UserFillUtils.fillCreatedUserId(goodsPriceSub, employeeId);
        return goodsPriceSub;
    }


    public AbcListPage<GoodsModifySubClinicsPricesPreCheckRsp> preCheckGoodsSubClinicInfoList(GoodsModifySubClinicsPricesReq clientReq) {
        AbcListPage<GoodsModifySubClinicsPricesPreCheckRsp> result = new AbcListPage<>();
        result.setRows(new ArrayList<>());
        List<SubClinicGoodsInfo> subClinicGoodsInfoList = clientReq.getSubClinicGoodsInfoList();
        if (CollectionUtils.isEmpty(subClinicGoodsInfoList)) {
            return result;
        }
        String goodsId = clientReq.getGoodsId();
        Goods goods = goodsRepository.findByIdAndOrganId(goodsId, clientReq.getHeaderChainId()).orElse(null);
        if (goods == null) {
            throw new NotFoundException();
        }
        if (!GoodsUtils.isComposeGoods(goods)) {
            return result;
        }
        // 只有启用的才提示
        List<SubClinicGoodsInfo> enableSubInfoList = subClinicGoodsInfoList.stream().filter(it -> it.getDisable() != null && it.getDisable() == GoodsConst.EnableStatus.ENABLE).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(enableSubInfoList)) {
            return result;
        }
        List<GoodsComposeNaked> composeList = goodsComposeNakedRepository.findAllByChainIdAndParentGoodsIdInAndComposeTypeAndIsDeleted(clientReq.getHeaderChainId(), Collections.singletonList(goodsId), GoodsComposeNaked.ComposeType.COMPOSE, YesOrNo.NO);
        if (CollectionUtils.isEmpty(composeList)) {
            return result;
        }
        List<String> composeGoodsIdList = composeList.stream().map(GoodsComposeNaked::getGoodsId).distinct().collect(Collectors.toList());
        // 看子店是否有停用的项目
        enableSubInfoList.forEach(it -> {
            OrganView organ = it.getOrgan();
            ClinicConfig subClinicConfig = cisClinicService.getClinicConfig(organ.getId());
            List<GoodsRedisCache> redisCacheList = goodsRedisUtils.getGoodsRedisCacheWithOutGoodsBatch(composeGoodsIdList, ChoosePharmacyHelper.getDefaultLocalPharmacyHelper(), subClinicConfig);
            List<GoodsItem> goodsItemList = new ArrayList<>();
            for (GoodsRedisCache cache : redisCacheList) {
                if (cache.getDisable() == GoodsConst.EnableStatus.ENABLE) {
                    continue;
                }
                goodsItemList.add(GoodsListProtocolFillUtils.genRpcCommonGoodsItemByRedisCache(new GoodsItem(), cache, GoodsUtils.isCompositeGoods(cache), subClinicConfig));
            }
            if (CollectionUtils.isEmpty(goodsItemList)) {
                return;
            }
            GoodsModifySubClinicsPricesPreCheckRsp rsp = new GoodsModifySubClinicsPricesPreCheckRsp();
            rsp.setOrgan(organ);
            rsp.setDisabledItems(goodsItemList);
            result.getRows().add(rsp);
        });

        return result;
    }


    public GetGoodsClinicPriceRsp getGoodsClinicPrice(GetGoodsClinicPriceReq req) {
        Map<String, GoodsPrice> clinicIdToGoodsPrice = goodsPriceRepository.findAllByChainIdAndGoodsIdAndOrganIdIn(req.getChainId(), req.getGoodsId(), req.getClinicIds())
                .stream().collect(Collectors.toMap(GoodsPrice::getOrganId, Function.identity(), (a, b) -> a));
        Goods goods = req.getGoods();
        GetGoodsClinicPriceRsp rsp = new GetGoodsClinicPriceRsp();
        req.getClinicIds().forEach(it -> {
            GetGoodsClinicPriceRsp.GoodsClinicPriceItem item = new GetGoodsClinicPriceRsp.GoodsClinicPriceItem();
            item.setPieceNum(goods.getPieceNum());
            item.setPieceUnit(goods.getPieceUnit());
            item.setPackageUnit(goods.getPackageUnit());
            item.setClinicId(it);
            item.setPackageCostPrice(goods.getPackageCostPrice());
            GoodsPrice goodsPrice = clinicIdToGoodsPrice.get(it);
            if (Objects.nonNull(goodsPrice)) {
                item.setPackageCostPrice(goodsPrice.getPackageCostPrice());
            }
            rsp.getRows().add(item);
        });
        return rsp;
    }

    public List<BatchQueryGoodsClinicPriceRsp> batchQueryGoodsClinicPrice(BatchQueryGoodsClinicPriceReq req) {
        List<BatchQueryGoodsClinicPriceRsp> rspList = new ArrayList<>();
        BatchQueryGoodsClinicPriceReq.QueryGoodsClinicPriceReq nonStockReq = req.getNonStockGoodsList();
        if (nonStockReq == null || CollectionUtils.isEmpty(nonStockReq.getClinicIdList())
                || CollectionUtils.isEmpty(nonStockReq.getGoodsIdList())) {
            return rspList;
        }
        List<Goods> goodsList = goodsRepository.findAllByIdInAndOrganIdAndStatusLessThan(nonStockReq.getGoodsIdList(),
                req.getChainId(), GoodsConst.GoodsStatus.DELETE);
        Map<String, GoodsPrice> goodsIdClinicIdToPrice = goodsPriceRepository.findAllByChainIdAndGoodsIdInAndOrganIdIn(req.getChainId(), nonStockReq.getGoodsIdList(), nonStockReq.getClinicIdList())
                .stream().collect(Collectors.toMap(it -> it.getGoodsId() + it.getOrganId(), Function.identity(), (a, b) -> a));
        goodsList.forEach(goods -> {
            nonStockReq.getClinicIdList().forEach(clinicId -> {
                BatchQueryGoodsClinicPriceRsp rsp = new BatchQueryGoodsClinicPriceRsp();
                rsp.setClinicId(clinicId);
                rsp.setGoodsId(goods.getId());
                rsp.setPackageCostPrice(goods.getPackagePrice());
                GoodsPrice goodsPrice = goodsIdClinicIdToPrice.get(goods.getId() + clinicId);
                if (Objects.nonNull(goodsPrice)) {
                    rsp.setPackageCostPrice(goodsPrice.getPackagePrice());
                }
                rspList.add(rsp);
            });
        });
        return rspList;
    }

    public OpsCommonRsp printGoodsPrice(PrintGoodsPriceReq clientReq) throws CisGoodsServiceException {
        if (CollectionUtils.isEmpty(clientReq.getList())) {
            return new OpsCommonRsp(OpsCommonRsp.SUCC, "Success");
        }
        List<GoodsStat> goodsStatList = new ArrayList<>();
        if (GoodsPrivUtils.isSingleMode(clientReq.getHeaderClinicType(), clientReq.getHeaderViewMode()) || GoodsPrivUtils.isSubClinic(clientReq.getHeaderClinicType(), clientReq.getHeaderViewMode())) {
            goodsStatList.addAll(goodsStatRepository.findByGoodsIdInAndChainIdAndClinicId(
                    clientReq.getList().stream().map(it -> it.getGoodsId()).collect(Collectors.toList()),
                    clientReq.getHeaderChainId(),
                    clientReq.getHeaderClinicId()
            ));
        } else {
            goodsStatList.addAll(goodsStatRepository.findAllByGoodsIdInAndChainIdAndClinicIdInAndPharmacyType(
                    clientReq.getList().stream().map(it -> it.getGoodsId()).collect(Collectors.toList()),
                    clientReq.getHeaderChainId(),
                    Arrays.asList(clientReq.getHeaderClinicId(), GoodsUtils.WHOLE_CHAIN_ID),
                    GoodsConst.PharmacyType.LOCAL_PHARMACY
            ));
        }
        if (CollectionUtils.isEmpty(goodsStatList)) {
            return new OpsCommonRsp(OpsCommonRsp.SUCC, "Success");
        }
        Map<String, PrintGoodsPriceReq.PrintPriceItem> goodsIdToPrintPriceItem = clientReq.getList().stream().collect(Collectors.toMap(PrintGoodsPriceReq.PrintPriceItem::getGoodsId, Function.identity(), (a, b) -> a));
        for (GoodsStat goodsStat : goodsStatList) {
            PrintGoodsPriceReq.PrintPriceItem printPriceItem = goodsIdToPrintPriceItem.get(goodsStat.getGoodsId());
            if (printPriceItem == null) {
                continue;
            }
            goodsStat.setPricePrintFlag(printPriceItem.getPrintPriceFlag());
        }
        return new OpsCommonRsp(OpsCommonRsp.SUCC, "Success");
    }

    /**
     * [把价格落地到调价]
     *
     * @return 此次调价的ItemId
     */
    public static Long createOrUpdateMultiGoodsPriceOrderItemList(ClinicConfig clinicConfig,
                                                                  GoodsModifyPriceOrder order,
                                                                  GoodsRedisCache goodsRedisCache,
                                                                  BiFunction<CreateModifyPriceOrderReq.GoodsModifyPriceItem, GoodsModifyPriceOrderItem, Boolean> isEqualKeyFunc,
                                                                  List<GoodsModifyPriceOrderItem> goodsSvrPriceList,
                                                                  List<CreateModifyPriceOrderReq.GoodsModifyPriceItem> goodsClientPriceList,
                                                                  String employeeId) {
        //找itemId 如果svrItemList非空找一个itemId
        Long itemId = !CollectionUtils.isEmpty(goodsSvrPriceList) ? goodsSvrPriceList.get(0).getItemId() : AbcIdUtils.getUUIDLong();

        MergeTool<CreateModifyPriceOrderReq.GoodsModifyPriceItem, GoodsModifyPriceOrderItem> mergeTool = new MergeTool<>();
        mergeTool.setSrc(goodsClientPriceList);
        mergeTool.setDst(goodsSvrPriceList);
        mergeTool.setIsEqualKeyFunc(isEqualKeyFunc);
        mergeTool.setUpdateFunc((view, priceOrderItem) -> updateGoodsPriceItem(
                priceOrderItem,
                order,
                clinicConfig,
                view,
                goodsRedisCache,
                employeeId
        ));
        mergeTool.setInsertFunc(view -> createGoodsPriceItem(
                itemId,
                clinicConfig,
                order,
                view,
                goodsRedisCache,
                employeeId
        ));
        mergeTool.setDeleteFunc(priceOrderItem -> {
            priceOrderItem.setIsDeleted(YesOrNo.YES);
            priceOrderItem.setStatus(GoodsModifyPriceOrderItem.Status.FAILED);
            return false;
        });
        mergeTool.doMerge();
        return itemId;
    }

    /**
     * [把价格落地到v2_goods/v2_goods_price]给药品设置更新多售价 & 子店定价
     *
     * @param allGoodsPriceList       服务端现有的售价列表
     * @param goodsMultiPriceViewList 客户端请求的新售价列表
     * @param employeeId              员工ID
     * @param subPriceFlag            子店价格的状态，怎么设置的？{@link GoodsPrice.SubPriceFlag}
     * @return 更新后的售价列表
     */
    public static MultiGoodsPriceUpdateResult createOrUpdateMultiGoodsPriceList(ClinicConfig clinicConfig,
                                                                                Goods goods,
                                                                                List<GoodsPrice> allGoodsPriceList,
                                                                                List<GoodsMultiPriceView> goodsMultiPriceViewList,
                                                                                GoodsMultiPriceView normalPrice,
                                                                                int subPriceFlag,
                                                                                String employeeId) {
        List<GoodsPrice> clinicGoodsPriceList = new ArrayList<>();
        List<GoodsPrice> chainGoodsPriceList = new ArrayList<>();
        List<GoodsPrice> updateGoodsPriceList = new ArrayList<>();
        List<GoodsPrice> newGoodsPriceList = new ArrayList<>();
        List<GoodsPrice> deleteGoodsPriceList = new ArrayList<>();
        GoodsPrice subClinicPrice = null; //子店普通定价
        if (!CollectionUtils.isEmpty(goodsMultiPriceViewList)) {
            goodsMultiPriceViewList = goodsMultiPriceViewList.stream().filter(it -> {
                if (!Objects.equals(it.getDiscountType(), GoodsPrice.DiscountType.BY_FIXED_PRICE)) {
                    return true;
                }
                // 特价，没有设置值，不处理
                return it.getPackagePrice() != null;
            }).collect(Collectors.toList());
        }
        //找到总部价格列表和子店价格列表
        if (!CollectionUtils.isEmpty(allGoodsPriceList)) {
            for (GoodsPrice goodsPrice : allGoodsPriceList) {
                if (goodsPrice.getTargetType() == GoodsPrice.PriceTargetType.GOODS) {
                    if (goodsPrice.getOwnType() == 1) {
                    } else {
                        if (GoodsUtils.compareStrEqual(goodsPrice.getOrganId(), clinicConfig.getClinicId())) {
                            subClinicPrice = goodsPrice;
                        }
                    }
                } else {
                    if (goodsPrice.getOwnType() == 1) {
                        chainGoodsPriceList.add(goodsPrice);
                    } else {
                        if (GoodsUtils.compareStrEqual(goodsPrice.getOrganId(), clinicConfig.getClinicId())) {
                            clinicGoodsPriceList.add(goodsPrice);
                        }
                    }
                }
            }
        }

        /**
         * 单店 或总部
         * */
        if (clinicConfig.isHeadClinic()) {
            /**
             * 处理普通价格
             * */
            if (normalPrice != null) {
                goods.setPriceType(normalPrice.getPriceType());
                goods.setPackagePrice(normalPrice.getPackagePrice());
                goods.setPiecePrice(normalPrice.getPiecePrice());
                goods.setPackageCostPrice(normalPrice.getPackageCostPrice());
                // 历史原因，修正重要的价格
                if (GoodsUtils.isChineseMedicine(goods)) {
                    BigDecimal chooseCMPackagePrice = goods.getPiecePrice() != null
                            && goods.getPiecePrice().compareTo(BigDecimal.ZERO) > 0
                            ? goods.getPiecePrice()
                            : goods.getPackagePrice();
                    goods.setPackagePrice(chooseCMPackagePrice);
                    goods.setPiecePrice(chooseCMPackagePrice);
                }
                goods.setPriceMakeupPercent(normalPrice.getPriceMakeupPercent());
                UserFillUtils.fillLastModifiedUserId(goods, employeeId);
            }
            /**
             * 处理多售价 -折扣价
             * */
            if (!CollectionUtils.isEmpty(goodsMultiPriceViewList) || !CollectionUtils.isEmpty(chainGoodsPriceList)) {
                int ownType = 1;
                int chainSubPriceFlag = GoodsPrice.SubPriceFlag.NOT_INIT; //总部无意义 设置成-1
                MergeTool<GoodsMultiPriceView, GoodsPrice> mergeTool = new MergeTool<>();
                mergeTool.setSrc(goodsMultiPriceViewList);
                mergeTool.setDst(chainGoodsPriceList);
                mergeTool.setIsEqualKeyFunc((view, price) -> GoodsUtils.compareLongEqual(view.getId(), price.getId()));
                mergeTool.setUpdateFunc((view, price) -> {
                    if (view.getOpType() != null && view.getOpType() == CreateOrUpdateGoodsFeeTypeReq.OpType.DELETE) {
                        price.setIsDeleted(YesOrNo.YES);
                        UserFillUtils.fillLastModifiedUserId(price, employeeId);
                        deleteGoodsPriceList.add(price);
                        return;
                    }
                    price.setDiscountType(view.getDiscountType());
                    price.setDiscountValue(view.getDiscountValue());
                    price.setOwnType(ownType);
                    price.setSubPriceFlag(chainSubPriceFlag);
                    price.setPriceType(view.getPriceType());
                    price.setPackagePrice(view.getPackagePrice());
                    price.setPiecePrice(view.getPiecePrice());
                    if (!GoodsUtils.isStockGoods(goods)) {
                        price.setPackageCostPrice(view.getPackageCostPrice());
                    }
                    price.setPriceMakeupPercent(view.getPriceMakeupPercent());
                    price.setTargetType(view.getTargetType());
                    price.setMemberTypeId(view.getMemberTypeId());
                    price.setDiscountType(view.getDiscountType());
                    price.setDiscountValue(view.getDiscountValue());
                    // 设置会员限购策略
                    price.setIsSaleLimit(view.getIsSaleLimit());
                    price.setSaleLimitRule(view.getSaleLimitRule());
                    price.setSort(view.getSort() != null ? view.getSort() : 0);
                    price.setIsDeleted(YesOrNo.NO);
                    UserFillUtils.fillLastModifiedUserId(price, employeeId);
                    updateGoodsPriceList.add(price);
                });
                mergeTool.setInsertFunc(view -> {
                    GoodsPrice price = new GoodsPrice();
                    price.setId(AbcIdUtils.getUUIDLong());
                    price.setChainId(clinicConfig.getChainId());
                    price.setOrganId(clinicConfig.getChainId());
                    price.setGoodsId(goods.getId());
                    price.setMemberTypeId(view.getMemberTypeId());

                    price.setDiscountType(view.getDiscountType());
                    price.setDiscountValue(view.getDiscountValue());
                    price.setOwnType(ownType);
                    price.setSubPriceFlag(chainSubPriceFlag);
                    price.setPriceType(view.getPriceType());
                    price.setPackagePrice(view.getPackagePrice());
                    price.setPiecePrice(view.getPiecePrice());
                    if (!GoodsUtils.isStockGoods(goods)) {
                        price.setPackageCostPrice(view.getPackageCostPrice());
                    }
                    price.setPriceMakeupPercent(view.getPriceMakeupPercent());
                    price.setTargetType(view.getTargetType());
                    price.setMemberTypeId(view.getMemberTypeId());
                    price.setDiscountType(view.getDiscountType());
                    price.setDiscountValue(view.getDiscountValue());
                    // 设置会员限购策略
                    price.setIsSaleLimit(view.getIsSaleLimit());
                    price.setSaleLimitRule(view.getSaleLimitRule());
                    price.setIsDeleted(YesOrNo.NO);
                    price.setSort(view.getSort() != null ? view.getSort() : 0);
                    UserFillUtils.fillCreatedUserId(price, employeeId);
                    allGoodsPriceList.add(price);
                    newGoodsPriceList.add(price);
                    return price;
                });
                mergeTool.setDeleteFunc(goodsTag -> {
                    return false;
                });
                mergeTool.doMerge();
            }
        } else {
            int ownType = 0; //门店所有

            /**
             * 处理普通价格
             * */
            if (subClinicPrice != null || normalPrice != null) {
                if (normalPrice != null) {
                    if (normalPrice.getOpType() != null && normalPrice.getOpType() == CreateOrUpdateGoodsFeeTypeReq.OpType.DELETE) {
                        if (subClinicPrice != null) {
                            subClinicPrice.setIsDeleted(YesOrNo.YES);
                            UserFillUtils.fillLastModifiedUserId(subClinicPrice, employeeId);
                            deleteGoodsPriceList.add(subClinicPrice);
                        }
                    } else {
                        if (subClinicPrice == null) {
                            //新生成
                            subClinicPrice = new GoodsPrice();
                            subClinicPrice.setId(AbcIdUtils.getUUIDLong());
                            subClinicPrice.setChainId(clinicConfig.getChainId());
                            subClinicPrice.setOrganId(clinicConfig.getClinicId());
                            subClinicPrice.setGoodsId(goods.getId());
                            subClinicPrice.setOwnType(ownType);
                            subClinicPrice.setSubPriceFlag(subPriceFlag);
                            if (clinicConfig.isAbcPharmacy()) {
                                subClinicPrice.setIndividualPricingType(normalPrice.getIndividualPricingType());
                            }
                            subClinicPrice.setIsDeleted(YesOrNo.NO);
                            UserFillUtils.fillCreatedUserId(subClinicPrice, employeeId);
                            allGoodsPriceList.add(subClinicPrice);
                            goods.setClinicSelfGoodsPrice(subClinicPrice);
                            newGoodsPriceList.add(subClinicPrice);
                        } else {
                            subClinicPrice.setSubPriceFlag(subPriceFlag);
                            if (clinicConfig.isAbcPharmacy()) {
                                subClinicPrice.setIndividualPricingType(normalPrice.getIndividualPricingType());
                            }
                            updateGoodsPriceList.add(subClinicPrice);
                        }
                        subClinicPrice.setPriceType(normalPrice.getPriceType());
                        subClinicPrice.setPackagePrice(normalPrice.getPackagePrice());
                        subClinicPrice.setPiecePrice(normalPrice.getPiecePrice());
                        subClinicPrice.setPriceMakeupPercent(normalPrice.getPriceMakeupPercent());
                        if (!GoodsUtils.isStockGoods(goods)) {
                            subClinicPrice.setPackageCostPrice(normalPrice.getPackageCostPrice());
                        }
                        // 历史原因，修正重要的价格
                        if (GoodsUtils.isChineseMedicine(goods)) {
                            BigDecimal chooseCMPackagePrice = subClinicPrice.getPiecePrice() != null
                                    && subClinicPrice.getPiecePrice().compareTo(BigDecimal.ZERO) > 0
                                    ? subClinicPrice.getPiecePrice()
                                    : subClinicPrice.getPackagePrice();
                            subClinicPrice.setPackagePrice(chooseCMPackagePrice);
                            subClinicPrice.setPiecePrice(chooseCMPackagePrice);
                        }
                        if (goods.getType() == GoodsConst.GoodsType.EYE) {
                            subClinicPrice.setPiecePrice(subClinicPrice.getPackagePrice());
                        }
                        UserFillUtils.fillLastModifiedUserId(subClinicPrice, employeeId);
                    }
                } else {
                    if (subClinicPrice != null) {
                        subClinicPrice.setIsDeleted(YesOrNo.YES);
                        UserFillUtils.fillLastModifiedUserId(subClinicPrice, employeeId);
                        deleteGoodsPriceList.add(subClinicPrice);
                    }
                }
            }
            /**
             * 处理多售价 -折扣价
             * */
            if (!CollectionUtils.isEmpty(goodsMultiPriceViewList) || !CollectionUtils.isEmpty(clinicGoodsPriceList)) {
                MergeTool<GoodsMultiPriceView, GoodsPrice> mergeTool = new MergeTool<>();
                mergeTool.setSrc(goodsMultiPriceViewList);
                mergeTool.setDst(clinicGoodsPriceList);
                mergeTool.setIsEqualKeyFunc((view, price) -> GoodsUtils.compareLongEqual(view.getId(), price.getId()));
                mergeTool.setUpdateFunc((view, price) -> {
                    if (view.getOpType() != null && view.getOpType() == CreateOrUpdateGoodsFeeTypeReq.OpType.DELETE) {
                        price.setIsDeleted(YesOrNo.YES);
                        UserFillUtils.fillLastModifiedUserId(price, employeeId);
                        deleteGoodsPriceList.add(price);
                        return;
                    }
                    price.setDiscountType(view.getDiscountType());
                    price.setDiscountValue(view.getDiscountValue());
                    price.setOwnType(ownType);
                    price.setSubPriceFlag(subPriceFlag);
                    if (clinicConfig.isAbcPharmacy()) {
                        price.setIndividualPricingType(view.getIndividualPricingType());
                    }
                    price.setPriceType(view.getPriceType());
                    price.setPackagePrice(view.getPackagePrice());
                    price.setPiecePrice(view.getPiecePrice());
                    if (!GoodsUtils.isStockGoods(goods)) {
                        price.setPackageCostPrice(view.getPackageCostPrice());
                    }
                    price.setPriceMakeupPercent(view.getPriceMakeupPercent());
                    price.setSort(view.getSort() != null ? view.getSort() : 0);
                    price.setTargetType(view.getTargetType());
                    price.setMemberTypeId(view.getMemberTypeId());
                    price.setDiscountType(view.getDiscountType());
                    price.setDiscountValue(view.getDiscountValue());
                    // 设置会员限购策略
                    price.setIsSaleLimit(view.getIsSaleLimit());
                    price.setSaleLimitRule(view.getSaleLimitRule());
                    price.setIsDeleted(YesOrNo.NO);
                    UserFillUtils.fillLastModifiedUserId(price, employeeId);
                    updateGoodsPriceList.add(price);
                });
                mergeTool.setInsertFunc(view -> {
                    GoodsPrice price = new GoodsPrice();
                    price.setId(AbcIdUtils.getUUIDLong());
                    price.setChainId(clinicConfig.getChainId());
                    price.setOrganId(clinicConfig.getClinicId());
                    price.setGoodsId(goods.getId());
                    price.setMemberTypeId(view.getMemberTypeId());

                    price.setDiscountType(view.getDiscountType());
                    price.setDiscountValue(view.getDiscountValue());
                    price.setOwnType(ownType);
                    price.setSubPriceFlag(subPriceFlag);
                    if (clinicConfig.isAbcPharmacy()) {
                        price.setIndividualPricingType(view.getIndividualPricingType());
                    }
                    price.setPriceType(view.getPriceType());
                    price.setPackagePrice(view.getPackagePrice());
                    price.setPiecePrice(view.getPiecePrice());
                    if (!GoodsUtils.isStockGoods(goods)) {
                        price.setPackageCostPrice(view.getPackageCostPrice());
                    }
                    price.setPriceMakeupPercent(view.getPriceMakeupPercent());
                    price.setTargetType(view.getTargetType());
                    price.setMemberTypeId(view.getMemberTypeId());
                    price.setDiscountType(view.getDiscountType());
                    price.setDiscountValue(view.getDiscountValue());
                    // 设置会员限购策略
                    price.setIsSaleLimit(view.getIsSaleLimit());
                    price.setSaleLimitRule(view.getSaleLimitRule());
                    price.setIsDeleted(YesOrNo.NO);
                    price.setSort(view.getSort() != null ? view.getSort() : 0);
                    UserFillUtils.fillCreatedUserId(price, employeeId);
                    allGoodsPriceList.add(price);
                    newGoodsPriceList.add(price);
                    return price;
                });
                mergeTool.setDeleteFunc(price -> {
                    return false; // 软删除
                });
                mergeTool.doMerge();
            }
        }
        if (allGoodsPriceList.stream().anyMatch(it -> it.getIsDeleted() == YesOrNo.NO)) {
            goods.onComposeFlag(GoodsConst.ComposeFlag.HAS_MULTI_PRICE);
        } else {
            goods.offComposeFlag(GoodsConst.ComposeFlag.HAS_MULTI_PRICE);
        }
        return new MultiGoodsPriceUpdateResult(newGoodsPriceList, updateGoodsPriceList, deleteGoodsPriceList);
    }

    /**
     * 更新商品统计表的待生效价格项
     *
     * @param chainId      连锁店ID
     * @param goodsIdList  商品ID列表
     * @param clinicIdList 门店ID列表
     */
    @Async("longTimeAsyncTaskExecutor")
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateGoodsStatWaitingEffectPrice(String chainId, List<String> goodsIdList, Set<String> clinicIdList) {
        // 查询需要更新的GoodsStat记录
        List<GoodsStat> goodsStatList = goodsStatRepository.findByGoodsIdInAndChainIdAndClinicIdIn(
                goodsIdList, chainId, clinicIdList);

        // 查询待生效的价格修改项
        List<GoodsModifyPriceOrderItem> priceList = goodsMapper.selectPendingPriceItems(chainId, goodsIdList);

        // 按商品ID分组，找出每个商品最大的价格修改项ID
        Map<String, Long> goodsIdToId = priceList.stream()
                .filter(it -> it.getItemId() != null)
                .collect(Collectors.groupingBy(
                        GoodsModifyPriceOrderItem::getGoodsId,
                        Collectors.collectingAndThen(
                                Collectors.maxBy(Comparator.comparing(GoodsModifyPriceOrderItem::getItemId)),
                                opt -> opt.map(GoodsModifyPriceOrderItem::getItemId).orElse(null)
                        )
                ));

        // 更新每个GoodsStat的待生效价格项ID
        for (GoodsStat goodsStat : goodsStatList) {
            if (clinicIdList.contains(goodsStat.getClinicId())) {
                Long priceItemId = goodsIdToId.get(goodsStat.getGoodsId());
                if (GoodsUtils.compareLongEqual(priceItemId, goodsStat.getWaitingEffectUpdatePriceItemId())) {
                    continue;
                }
                goodsStat.setWaitingEffectUpdatePriceItemId(priceItemId);
            }
        }
        // 批量保存更新后的GoodsStat
        goodsStatRepository.saveAll(goodsStatList);
        goodsRedisUtils.writeGoodsStatToRedis(chainId, goodsStatList);
    }

    /**
     * ClinicId -> GoodsPrice
     * 会员折扣价设置
     */
    public void upsertPromotionDiscountGoodsForPharmacy(String employeeId, Goods goods, List<GoodsPrice> multiPriceList) {
        if (CollectionUtils.isEmpty(multiPriceList)) {
            return;
        }
        List<GoodsPrice> promotionPriceList = multiPriceList.stream().filter(it -> it.getTargetType() == GoodsPrice.PriceTargetType.MEMBER ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(promotionPriceList)) {
            return;
        }

        BatchSetPromotionDiscountGoodsReq rpcReq = new BatchSetPromotionDiscountGoodsReq();
        rpcReq.setOperatorId(employeeId);
        rpcReq.setItems(new ArrayList<>());
        SetPromotionDiscountGoodsReq setGoodsReq = new SetPromotionDiscountGoodsReq();

        String chainId = goods.getOrganId();
        //按clinicId分组
        Map<String, List<GoodsPrice>> clinicIdToPriceList = promotionPriceList.stream().collect(Collectors.groupingBy(GoodsPrice::getOrganId));
        for (Map.Entry<String, List<GoodsPrice>> entry : clinicIdToPriceList.entrySet()) {
            String clinicId = entry.getKey();
            List<GoodsPrice> list = entry.getValue();
            rpcReq.getItems().add(setGoodsReq);
            setGoodsReq.setChainId(chainId);
            setGoodsReq.setClinicId(clinicId);

            //Goods
            setGoodsReq.setDiscountGoodsList(new ArrayList<>());
            SetPromotionDiscountGoodsReq.DiscountGoods discountGoods = new SetPromotionDiscountGoodsReq.DiscountGoods();
            discountGoods.setGoodsId(goods.getId());
            discountGoods.setGoodsType((int) goods.getType());
            discountGoods.setGoodsSubType((int) goods.getSubType());
            discountGoods.setCustomTypeId(goods.getCustomTypeId());
            discountGoods.setPharmacyType(GoodsConst.PharmacyType.LOCAL_PHARMACY);
            discountGoods.setGoodsCMSpec(goods.getCMSpec());
            setGoodsReq.getDiscountGoodsList().add(discountGoods);

            discountGoods.setDiscountDetailList(new ArrayList<>());
            for (GoodsPrice price : list) {
                SetPromotionDiscountGoodsReq.DiscountGoods.DiscountDetail discountDetail = new SetPromotionDiscountGoodsReq.DiscountGoods.DiscountDetail();
                discountDetail.setDiscountType(price.getDiscountType() != null ? price.getDiscountType() : GoodsPrice.DiscountType.NO_DISCOUNT);
                discountDetail.setMemberTypeId(price.getMemberTypeId());
                // 库存模块折扣和折扣价是两个字段存的，库存的前端也是认认两个字段
                if (discountDetail.getDiscountType() == GoodsPrice.DiscountType.DISCOUNT) {
                    discountDetail.setDiscountValue(price.getDiscountValue());
                } else {
                    discountDetail.setDiscountValue(price.getPackagePrice());
                }
                if (Objects.equals(price.getDiscountType(), GoodsPrice.DiscountType.NO_DISCOUNT) || price.getIsDeleted() == YesOrNo.YES) {
                    discountDetail.setOperateType(SetPromotionDiscountGoodsReq.DiscountGoods.DiscountDetail.OperateType.DELETE);
                    discountDetail.setDiscountValue(BigDecimal.ZERO);
                } else {
                    // TODO 这里都是写的 UPDATE，promotion 那边会做兼容
                    discountDetail.setOperateType(SetPromotionDiscountGoodsReq.DiscountGoods.DiscountDetail.OperateType.UPDATE);
                }
                // 设置会员限购策略
                discountDetail.setIsSaleLimit(price.getIsSaleLimit());
                discountDetail.setSaleLimitRule(price.getSaleLimitRule());

                discountGoods.getDiscountDetailList().add(discountDetail);
            }
        }

        cisPromotionService.upsertPromotionDiscountGoodsForPharmacy(rpcReq);
    }

    /**
     * 售价更新结果
     */
    @Getter
    public static class MultiGoodsPriceUpdateResult {
        /**
         * 新增的售价列表
         */
        private final List<GoodsPrice> newPrices;

        /**
         * 变更的售价列表
         */
        private final List<GoodsPrice> updatePrices;

        /**
         * 删除的售价列表
         */
        private final List<GoodsPrice> deletePrices;

        public MultiGoodsPriceUpdateResult(List<GoodsPrice> newPrices, List<GoodsPrice> updatePrices, List<GoodsPrice> deletePrices) {
            Assert.notNull(newPrices, "newPrices must not be null");
            Assert.notNull(updatePrices, "updatePrices must not be null");
            Assert.notNull(deletePrices, "deletePrices must not be null");
            this.newPrices = Collections.unmodifiableList(newPrices);
            this.updatePrices = Collections.unmodifiableList(updatePrices);
            this.deletePrices = Collections.unmodifiableList(deletePrices);
        }

        public boolean hasChange() {
            return !CollectionUtils.isEmpty(newPrices) || !CollectionUtils.isEmpty(updatePrices) || !CollectionUtils.isEmpty(deletePrices);
        }
    }

}
