package cn.abcyun.cis.goods.service.export;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsCustomTypeView;
import cn.abcyun.cis.commons.util.FillUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.goods.cache.redis.*;
import cn.abcyun.cis.goods.conf.OssConfiguration;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.dto.list.QueryStockGoodsListGoodsReqDto;
import cn.abcyun.cis.goods.dto.list.StockGoodsListGoodsDtoV2;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.mapper.GoodsListMapperV2;
import cn.abcyun.cis.goods.model.GoodsAsyncExportTask;
import cn.abcyun.cis.goods.model.GoodsPrice;
import cn.abcyun.cis.goods.model.GoodsSysType;
import cn.abcyun.cis.goods.model.GoodsTagRedisCache;
import cn.abcyun.cis.goods.repository.GoodsAsyncExportTaskRepository;
import cn.abcyun.cis.goods.service.query.ExcelCustomWidthStyleStrategy;
import cn.abcyun.cis.goods.service.query.GoodsSysTypeService;
import cn.abcyun.cis.goods.service.rpc.CisClinicService;
import cn.abcyun.cis.goods.utils.AliyunOssUtils;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.utils.SheBaoUtils;
import cn.abcyun.cis.goods.utils.protocol.ExcelExportProtocolUtils;
import cn.abcyun.cis.goods.utils.protocol.GoodsListProtocolFillUtils;
import cn.abcyun.cis.goods.vo.frontend.export.GetGoodsAsyncExportInfoReq;
import cn.abcyun.cis.goods.vo.frontend.export.GetGoodsAsyncExportInfoRsp;
import cn.abcyun.cis.goods.vo.frontend.goodslist.GetStockGoodsListReq;
import com.alibaba.excel.EasyExcelFactory;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-24 13:28:21
 */
@Service
@Slf4j
public class GoodsAsyncExportService {

    @Autowired
    private GoodsAsyncExportTaskRepository goodsAsyncExportTaskRepository;
    @Autowired
    private AliyunOssUtils aliyunOssUtils;
    @Autowired
    private CisClinicService cisClinicService;
    @Autowired
    private GoodsListMapperV2 goodsListMapperV2;
    @Autowired
    private GoodsCustomTypeRedisService goodsCustomTypeRedisService;
    @Autowired
    private GoodsSysTypeService goodsSysTypeService;
    @Autowired
    private GoodsRedisUtils goodsRedisUtils;
    @Autowired
    private OssConfiguration ossConfiguration;

    @Async("longTimeAsyncTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void exportPageListStockGoodsSubDetail(GetStockGoodsListReq clientReq, QueryStockGoodsListGoodsReqDto queryParam, Long taskId) {
        GoodsAsyncExportTask exportTask = goodsAsyncExportTaskRepository.findByIdAndIsDeleted(taskId, GoodsUtils.DeleteFlag.OK).orElse(null);
        if (exportTask == null) {
            log.error("exportPageListStockGoodsSubDetail task not exist,taskId={}", taskId);
            return;
        }
        String clinicId = clientReq.getHeaderClinicId();
        String fileName = String.format("goods-export/%s/" + ExcelExportProtocolUtils.getExportExcelFileName("药品物资门店分布") + ".xlsx", clientReq.getChainId());
        try {
            ClinicConfig clinicConfig = cisClinicService.getClinicConfig(clinicId);
            List<Organ> organList = cisClinicService.getChainAllSubOrgan(clientReq.getChainId());
            List<List<String>> exportSubDetailHeader = getExportSubDetailHeader(clinicId, organList);
            Map<Long, GoodsCustomTypeView> customTypeIdToGoodsCustomType = goodsCustomTypeRedisService.doLoadChainCustomTypeInToRedis(clientReq.getChainId(), null, clinicConfig.getHisType(),null);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            int startIndex = 0;
            // 每次最多两百条
            int perCount = 200;
            int len;
            List<List<Object>> dataList = new ArrayList<>();
            do {
                queryParam.setOffset(startIndex);
                queryParam.setLimit(perCount);
                long s1 = System.currentTimeMillis();
                List<StockGoodsListGoodsDtoV2> stockGoodsListGoodsDtoList = goodsListMapperV2.pageListStockGoodsV2(queryParam);
                long e1 = System.currentTimeMillis();
                log.info("startIndex = {}, 查询数据库耗时:{}ms", startIndex, e1 - s1);
                if (CollectionUtils.isEmpty(stockGoodsListGoodsDtoList)) {
                    break;
                }
                List<String> goodsIdList = stockGoodsListGoodsDtoList.stream().map(StockGoodsListGoodsDtoV2::getId).distinct().collect(Collectors.toList());
                Map<String, GoodsRedisCache> goodsIdToGoodsRedisCache = goodsRedisUtils.loadWholeGoods(goodsIdList, clinicConfig, goodsRedisUtils);
                long e2 = System.currentTimeMillis();
                log.info("startIndex = {}, 查询redis耗时:{}ms", startIndex, e2 - e1);
                dataList.addAll(transExportStockGoodsSubDetail(stockGoodsListGoodsDtoList,
                        goodsIdToGoodsRedisCache, organList, clinicId, customTypeIdToGoodsCustomType, clinicConfig, clientReq.getPharmacyType()));
                long e3 = System.currentTimeMillis();
                log.info("startIndex = {}, 转换数据耗时:{}ms", startIndex, e3 - e2);
                len = stockGoodsListGoodsDtoList.size();
                startIndex += len;
                if (len < perCount) {
                    break;
                }
            } while (true);

            long writeStart = System.currentTimeMillis();
            EasyExcelFactory.write(outputStream)
                    .head(exportSubDetailHeader)
                    .registerWriteHandler(GoodsUtils.goodsExcelStyle())
                    .registerWriteHandler(new ExcelCustomWidthStyleStrategy())
                    .sheet(0, "药品物资列表")
                    .doWrite(dataList);
            long writeEnd = System.currentTimeMillis();
            log.info("写入excel耗时:{}ms", writeEnd - writeStart);
            // 上传oss
            String downloadUrl = aliyunOssUtils.uploadToOss(fileName, outputStream);
            log.info("上传oss成功,耗时:{}ms", System.currentTimeMillis() - writeEnd);
            exportTask.setFileName(fileName);
            exportTask.setStatus(GoodsAsyncExportTask.Status.FINISHED);
            exportTask.setLastModified(Instant.now());
            exportTask.setOss(downloadUrl);
            goodsAsyncExportTaskRepository.save(exportTask);

            outputStream.close();
        } catch (Exception e) {
            log.error("exportPageListStockGoodsSubDetail error", e);
            exportTask.setFileName(fileName);
            exportTask.setStatus(GoodsAsyncExportTask.Status.FAILED);
            exportTask.setLastModified(Instant.now());
            exportTask.setErrorReason(e.getMessage());
            goodsAsyncExportTaskRepository.save(exportTask);
        }
    }

    public GetGoodsAsyncExportInfoRsp getGoodsAsyncExportInfo(GetGoodsAsyncExportInfoReq clientReq) {

        GoodsAsyncExportTask exportTask = goodsAsyncExportTaskRepository.findByIdAndIsDeleted(clientReq.getId(), 0).orElse(null);
        if (exportTask == null || !exportTask.getCreatedBy().equals(clientReq.getEmployeeId())) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER);
        }
        GetGoodsAsyncExportInfoRsp rsp = new GetGoodsAsyncExportInfoRsp();
        rsp.setId(exportTask.getId());
        rsp.setStatus(exportTask.getStatus());
//        rsp.setUrl(exportTask.getOss());
        rsp.setCreated(exportTask.getCreated());
        return rsp;
    }

    public void downloadGoodsAsyncExport(GetGoodsAsyncExportInfoReq clientReq) {
        GoodsAsyncExportTask exportTask = goodsAsyncExportTaskRepository.findByIdAndIsDeleted(clientReq.getId(), 0).orElse(null);
        if (exportTask == null || !exportTask.getCreatedBy().equals(clientReq.getEmployeeId())
                || exportTask.getStatus() != GoodsAsyncExportTask.Status.FINISHED) {
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "导出失败");
        }
        String fileName = exportTask.getFileName();

        HttpServletResponse httpServletResponse = clientReq.getHttpServletResponse();
        BufferedInputStream inputStream = null;
        OutputStream outputStream = null;
        OSS ossClient = null;
        try {
            httpServletResponse.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            httpServletResponse.setCharacterEncoding("utf-8");
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName.substring(fileName.lastIndexOf("/") + 1), "UTF-8"));
            long s = System.currentTimeMillis();
            ossClient = aliyunOssUtils.getOssClient();
            OSSObject ossObject = ossClient.getObject(ossConfiguration.getBucket(), fileName);
            log.info("下载oss耗时:{}ms", System.currentTimeMillis() - s);
            exportTask.setStatus(GoodsAsyncExportTask.Status.DOWNLOAD);
            FillUtils.fillLastModifiedBy(exportTask, clientReq.getEmployeeId());
            goodsAsyncExportTaskRepository.save(exportTask);

            inputStream = new BufferedInputStream(ossObject.getObjectContent());
            byte[] buffer = new byte[1024];
            outputStream = httpServletResponse.getOutputStream();
            int read;
            while ((read = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, read);
            }
            outputStream.flush();
            ossObject.close();
        } catch (Exception e) {
            log.error("异步excel下载发生异常=", e);
            httpServletResponse.reset();
            httpServletResponse.setContentType("application/json");
            httpServletResponse.setCharacterEncoding("utf-8");
            throw new CisGoodsServiceException(CisGoodsServiceError.CIS_GOODS_ERROR_PARAMETER, "导出异常");
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            } catch (IOException e) {
                log.error("异步excel下载发生异常=", e);
            }
        }
    }

    private List<List<Object>> transExportStockGoodsSubDetail(List<StockGoodsListGoodsDtoV2> stockGoodsListGoodsDtoList,
                                                              Map<String, GoodsRedisCache> goodsIdToGoodsRedisCache,
                                                              List<Organ> organList, String clinicId,
                                                              Map<Long, GoodsCustomTypeView> customTypeIdToGoodsCustomType,
                                                              ClinicConfig clinicConfig,
                                                              int pharmacyType) {
        List<List<Object>> rowList = new ArrayList<>();
        if (CollectionUtils.isEmpty(stockGoodsListGoodsDtoList)) {
            return rowList;
        }
        for (StockGoodsListGoodsDtoV2 goodsStockDto : stockGoodsListGoodsDtoList) {
            GoodsRedisCache goodsRedisCache = goodsIdToGoodsRedisCache.get(goodsStockDto.getId());
            if (goodsRedisCache == null) {
                continue;
            }
            List<Object> row = new ArrayList<>();
            // 药品编码
            row.add(goodsRedisCache.getShortId());
            // 药品名称
            row.add(GoodsUtils.goodsFullName(goodsRedisCache));
            // 类型
            GoodsSysType goodsSysType = goodsSysTypeService.getHisGoodsSysType(goodsRedisCache.getTypeId());
            if (goodsSysType != null) {
                row.add(goodsSysType.getName());
            } else {
                row.add("");
            }
            // 二级分类
            if (goodsRedisCache.getCustomTypeId() != null && goodsRedisCache.getCustomTypeId() != 0) {
                GoodsCustomTypeView customTypeView = customTypeIdToGoodsCustomType.get(goodsRedisCache.getCustomTypeId());
                if (customTypeView != null) {
                    row.add(customTypeView.getName());
                } else {
                    row.add("");
                }
            } else {
                row.add("");
            }
            // 规格
            row.add(GoodsUtils.displaySpec(goodsRedisCache));
            // 厂家
            row.add(goodsRedisCache.getManufacturerFull());
            // 准字
            row.add(goodsRedisCache.getMedicineNmpn());
            // 条形码
            row.add(goodsRedisCache.getBarCode());
            /**
             * 标签
             * */
            List<GoodsTagRedisCache> useTagList = GoodsListProtocolFillUtils.findClinicGoodsTagList(goodsRedisCache.getGoodsTagList(), clinicConfig);
            String tagStr = "";
            if (!CollectionUtils.isEmpty(useTagList)) {
                tagStr = useTagList.stream().map(GoodsTagRedisCache::getName).collect(Collectors.joining(","));
            } else {
                if (goodsRedisCache.getSpuRedisCache() != null && !CollectionUtils.isEmpty(goodsRedisCache.getSpuRedisCache().getSpuTagList())) {
                    List<GoodsTagRedisCache> spuTagList = GoodsListProtocolFillUtils.findClinicGoodsTagList(goodsRedisCache.getSpuRedisCache().getSpuTagList(), clinicConfig);
                    if (!CollectionUtils.isEmpty(spuTagList)) {
                        tagStr = spuTagList.stream().map(GoodsTagRedisCache::getName).collect(Collectors.joining(","));
                    }
                }
            }
            row.add(tagStr);
            // 总部定价
            row.add(goodsRedisCache.getChainPackagePrice());
            // 总部定价拆零
            row.add(goodsRedisCache.getChainPiecePrice());
            // 税率
            if (goodsRedisCache.getInTaxRat() != null && goodsRedisCache.getInTaxRat().compareTo(BigDecimal.ZERO) != 0) {
                row.add(goodsRedisCache.getInTaxRat() + "%");
            } else {
                row.add("");
            }
            if (goodsRedisCache.getOutTaxRat() != null && goodsRedisCache.getOutTaxRat().compareTo(BigDecimal.ZERO) != 0) {
                row.add(goodsRedisCache.getOutTaxRat() + "%");
            } else {
                row.add("");
            }
            // 库存
            row.add(GoodsUtils.dispGoodsCount(goodsRedisCache.getType(), goodsRedisCache.getSubType(),
                    goodsStockDto.getPieceCount(), goodsStockDto.getPackageCount(), goodsRedisCache.getPieceUnit(),
                    goodsRedisCache.getPackageUnit(), goodsRedisCache.getPieceNum()));
            Pair<BigDecimal, BigDecimal> countPair = getPackageOrPieceCount(goodsStockDto.getPackageCount(), goodsStockDto.getPieceCount(), goodsRedisCache.getPieceNum());
            row.add(countPair.getFirst());
            row.add(countPair.getSecond());
            GoodsExtendRedisCache goodsExtendRedisCache = Optional.ofNullable(goodsRedisCache.getGoodsAllExtendRedisCacheList()).orElse(new ArrayList<>())
                    .stream().filter(it -> it.getClinicId().equals(clinicId)).findFirst().orElse(null);
            // 医保相关
            if (goodsExtendRedisCache != null) {
                row.add(goodsExtendRedisCache.getNationalCode());
                row.add(goodsExtendRedisCache.getShebaoCodeNationalCurrentPriceLimited());
                row.add(SheBaoUtils.shebaoMedicalFeedGradeName(goodsRedisCache.getType(),goodsRedisCache.getSubType(),goodsExtendRedisCache.getMedicalFeeGrade()));
                row.add(SheBaoUtils.sheBaoPayModeName(goodsExtendRedisCache.getShebaoPayMode(),
                        goodsExtendRedisCache.getShebaoCodeNationalMatchedStatus() != null ? goodsExtendRedisCache.getShebaoCodeNationalMatchedStatus().intValue() : null));
            } else {
                row.add("");
                row.add("");
                row.add("");
                row.add("");
            }
            // 门店
            for (Organ organ : organList) {
                // 门店售价,拆零
                GoodsPriceRedisCache clinicPriceRedisCache = Optional.ofNullable(goodsRedisCache.getGoodsAllPriceRedisCacheList()).orElse(new ArrayList<>()).stream().filter(it -> it.getClinicId().equals(organ.getId()) && it.getTargetType() == GoodsPrice.PriceTargetType.GOODS).findFirst().orElse(null);
                if (clinicPriceRedisCache != null) {
                    row.add(clinicPriceRedisCache.getPackagePrice());
                    row.add(clinicPriceRedisCache.getPiecePrice());
                } else {
                    // 门店没有取总部
                    row.add(goodsRedisCache.getChainPackagePrice());
                    row.add(goodsRedisCache.getChainPiecePrice());
                }
                ClinicConfig subClinicConfig = cisClinicService.getClinicConfig(organ.getId());
                GoodsStatRedisCache goodsStatRedisCache;
                if (subClinicConfig.getOpenPharmacyFlag() == GoodsConst.OpenPharmacyFlag.MORE) {
                    goodsStatRedisCache = Optional.ofNullable(goodsRedisCache.getGoodsAllSummaryRedisCacheList()).orElse(new ArrayList<>())
                            .stream().filter(it -> it.getClinicId().equals(organ.getId()) && it.getPharmacyType() == pharmacyType).findFirst().orElse(null);
                } else {
                    goodsStatRedisCache = Optional.ofNullable(goodsRedisCache.getGoodsAllStatRedisCacheList()).orElse(new ArrayList<>())
                            .stream().filter(it -> it.getClinicId().equals(organ.getId()) && it.getPharmacyNo() == GoodsUtils.PHARMACY_NO_0 && it.getPharmacyType() == pharmacyType).findFirst().orElse(null);
                }
                // 库存
                if (goodsStatRedisCache != null) {
                    row.add(GoodsUtils.dispGoodsCount(goodsRedisCache.getType(), goodsRedisCache.getSubType(),
                            goodsStatRedisCache.getPieceCount(), goodsStatRedisCache.getPackageCount(), goodsRedisCache.getPieceUnit(),
                            goodsRedisCache.getPackageUnit(), goodsRedisCache.getPieceNum()));
                    Pair<BigDecimal, BigDecimal> clinicCountPair = getPackageOrPieceCount(goodsStatRedisCache.getPackageCount(), goodsStatRedisCache.getPieceCount(), goodsRedisCache.getPieceNum());
                    row.add(clinicCountPair.getFirst());
                    row.add(clinicCountPair.getSecond());
                    // 成本
                    row.add(goodsStatRedisCache.getTotalCost());
                    // 效期
                    row.add(goodsStatRedisCache.getMinExpiryDate());
                    // 最近进价
                    row.add(goodsStatRedisCache.getLastPackageCostPrice());
                    // 供应商
                    row.add(goodsStatRedisCache.getLastStockInOrderSupplier());
                    // 毛利率
                    if (goodsStatRedisCache.getProfitRat() != null) {
                        row.add(goodsStatRedisCache.getProfitRat() + "%");
                    } else {
                        row.add("");
                    }
                    // 日均销量
                    String unit = getGoodsUnit(goodsRedisCache.getType(), goodsRedisCache.getSubType(), goodsRedisCache.getPackageUnit(), goodsRedisCache.getPieceUnit());
                    if (goodsStatRedisCache.getRecentAvgSell() != null) {
                        row.add(goodsStatRedisCache.getRecentAvgSell() + unit);
                    } else {
                        row.add("0" + unit);
                    }
                    // 周转天数
                    if (goodsStatRedisCache.getTurnoverDays() != null) {
                        row.add(goodsStatRedisCache.getTurnoverDays() + "天");
                    } else {
                        row.add("0天");
                    }
                } else {
                    row.add("");
                    row.add("");
                    row.add("");
                    row.add("");
                    row.add("");
                    row.add("");
                    row.add("");
                    row.add("");
                    row.add("");
                    row.add("");
                }
            }

            rowList.add(row);
        }
        return rowList;
    }

    private String getGoodsUnit(int type, int subType, String packageUnit, String pieceUnit) {
        if (type == GoodsConst.GoodsType.MEDICINE && subType == GoodsConst.GoodsMedicineSubType.CHINESE_MEDICINE) {
            return pieceUnit != null ? pieceUnit : "";
        }
        return packageUnit != null ? packageUnit : "";
    }

    /**
     * 获取化整化零库存数
     *
     * @param packageCount
     * @param pieceCount
     * @param pieceNum
     * @return
     */
    private Pair<BigDecimal, BigDecimal> getPackageOrPieceCount(BigDecimal packageCount, BigDecimal pieceCount, Integer pieceNum) {
        if (packageCount == null) {
            packageCount = BigDecimal.ZERO;
        }
        if (pieceCount == null) {
            pieceCount = BigDecimal.ZERO;
        }
        if (pieceNum == null) {
            pieceNum = 1;
        }
        // 化整
        BigDecimal pacCount = MathUtils.wrapBigDecimalAdd(packageCount, pieceCount.divide(new BigDecimal(pieceNum), 2, RoundingMode.HALF_UP));
        // 化零
        BigDecimal pieCount = MathUtils.wrapBigDecimalAdd(MathUtils.wrapBigDecimalMultiply(packageCount, new BigDecimal(pieceNum)), pieceCount);
        return Pair.of(pacCount, pieCount);
    }
    private static List<String> createHeaderRow(String value) {
        List<String> row = new ArrayList<>();
        row.add(value);
        return row;
    }
    private List<List<String>> getExportSubDetailHeader(String clinicId, List<Organ> organList) {
        List<List<String>> header = new ArrayList<>();
        // 总部
        header.add(createHeaderRow("药品编码"));
        header.add(createHeaderRow("药品"));
        header.add(createHeaderRow("类型"));
        header.add(createHeaderRow("二级分类"));
        header.add(createHeaderRow("规格"));
        header.add(createHeaderRow("厂家"));
        header.add(createHeaderRow("批准文号"));
        header.add(createHeaderRow("条码"));
        header.add(createHeaderRow("标签"));
        header.add(createHeaderRow("总部定价"));
        header.add(createHeaderRow("总部定价(拆零)"));
        header.add(createHeaderRow("进项税"));
        header.add(createHeaderRow("销项税"));
        header.add(createHeaderRow("当前库存数"));
        header.add(createHeaderRow("当前库存数(化整)"));
        header.add(createHeaderRow("当前底库存数(化零)"));
        header.add(createHeaderRow("医保码"));
        header.add(createHeaderRow("医保限价"));
        header.add(createHeaderRow("医保类别"));
        header.add(createHeaderRow("医保支付"));
        // 门店
        for (Organ organ : organList) {
            String name;
            if (organ.getId().equals(clinicId)) {
                // 总部
                name = "总部";
            } else {
                name = organ.getName();
            }
            header.add(new ArrayList<String>() {{
                add(name);
                add("门店售价");
            }});
            header.add(new ArrayList<String>() {{
                add(name);
                add("门店售价(拆零)");
            }});
            header.add(new ArrayList<String>() {{
                add(name);
                add("当前库存");
            }});
            header.add(new ArrayList<String>() {{
                add(name);
                add("当前库存数(化整)");
            }});
            header.add(new ArrayList<String>() {{
                add(name);
                add("当前库存数(化零)");
            }});
            header.add(new ArrayList<String>() {{
                add(name);
                add("成本");
            }});
            header.add(new ArrayList<String>() {{
                add(name);
                add("最近效期");
            }});
            header.add(new ArrayList<String>() {{
                add(name);
                add("最近进价");
            }});
            header.add(new ArrayList<String>() {{
                add(name);
                add("最近供应商");
            }});
            header.add(new ArrayList<String>() {{
                add(name);
                add("毛利率");
            }});
            header.add(new ArrayList<String>() {{
                add(name);
                add("日均消耗量");
            }});
            header.add(new ArrayList<String>() {{
                add(name);
                add("周转天数");
            }});
        }

        return header;
    }
}
