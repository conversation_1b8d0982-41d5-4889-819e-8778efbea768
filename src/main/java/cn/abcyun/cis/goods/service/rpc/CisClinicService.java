package cn.abcyun.cis.goods.service.rpc;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisScClinicFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.message.goods.ClearGoodsConfigCacheMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.*;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ClinicEmployeeDataPermission.Inventory;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.device.DeviceBindRpcReq;
import cn.abcyun.bis.rpc.sdk.cis.model.device.DeviceBindRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.device.DeviceConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.exam.ExamDeviceConst;
import cn.abcyun.cis.commons.exception.FeignRuntimeException;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.cis.goods.cache.redis.GoodsConfigRedisCache;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.domain.LockConfig;
import cn.abcyun.cis.goods.domain.*;
import cn.abcyun.cis.goods.dto.EmployeeNeedToPushOrganList;
import cn.abcyun.cis.goods.exception.CisGoodsServiceError;
import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.model.ExaminationDevice;
import cn.abcyun.cis.goods.model.ExaminationDeviceModel;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponseBody;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CisClinicService {
    private static final Logger sLogger = LoggerFactory.getLogger(CisClinicService.class);
    @Autowired
    private AbcCisScClinicFeignClient abcCisScClinicFeignClient;
    @Autowired
    private GoodsConfigRedisCache goodsConfigRedisCache;
    /***
     * 线程安全的结构
     * */
    private Cache<String, ClinicConfig> clinicIdToClinicConfig = Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();
    private Cache<String, List<String>> chainIdToSubClinicIdList = Caffeine.newBuilder()
            .maximumSize(200)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();
    private Cache<String, List<Organ>> chainIdToSubClinicList = Caffeine.newBuilder()
            .maximumSize(200)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    private Cache<String, List<Department>> chainIdToDepartments = Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    private final Cache<String, Integer> COOP_CENTER_CLINIC_CACHE = Caffeine.newBuilder()
            .maximumSize(500)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    private final Cache<String, Integer> COOP_CLINIC_CACHE = Caffeine.newBuilder()
            .maximumSize(500)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    /**
     * ******************************************************************************************************
     * 本次缓存 -> Redis -> DB
     * 带本地缓存的 获取clinicId的配置
     * 失败，异常 返回null，需要判空
     * 有本地内存缓存的 goodsclinicConfig （因为有了这个功能 这个函数可以在for循环里面调用了，否则即时是redis可能也顶不住）
     ***********************************************************************************************************/
    public ClinicConfig getClinicConfig(String clinicId) {
        if (TextUtils.isEmpty(clinicId)) {
            sLogger.error("拉取诊所配置,诊所Id为空");
            throw new CisGoodsServiceException(CisGoodsServiceError.GET_CLINIC_CONFIG_ERROR);
        }
        ClinicConfig clinicConfig = clinicIdToClinicConfig.getIfPresent(clinicId);
        if (clinicConfig != null) {
            return clinicConfig;
        }
        clinicConfig = toClinicConfig(goodsConfigRedisCache.getGoodsConfig(clinicId));
        if (clinicConfig == null) {
            sLogger.error("拉取诊所配置,诊所配置为空:{}", clinicId);
            throw new CisGoodsServiceException(CisGoodsServiceError.GET_CLINIC_CONFIG_ERROR);
        }
        clinicIdToClinicConfig.put(clinicId, clinicConfig);
        return clinicConfig;
    }


    /**
     * 从ScGoods维护的诊所配置填充clinicConfig
     */
    private ClinicConfig toClinicConfig(GoodsConfigHelper goodsConfigHelper) {
        if (goodsConfigHelper == null) {
            throw new CisGoodsServiceException(CisGoodsServiceError.GET_CLINIC_CONFIG_ERROR);
        }
        ClinicConfig clinicConfig = new ClinicConfig();
        //药房
        clinicConfig.setPharmacyRuleList(goodsConfigHelper.getPharmacyRuleList());

        clinicConfig.setChainId(goodsConfigHelper.getStockChainConfig().getChainId());
        clinicConfig.setClinicId(goodsConfigHelper.getStockChainConfig().getChainId());
        clinicConfig.setClinicType(goodsConfigHelper.getStockChainConfig().getNodeType());
        clinicConfig.setViewMode(goodsConfigHelper.getStockChainConfig().getViewMode());
        clinicConfig.setHisType(goodsConfigHelper.getStockChainConfig().getHisType());
        //存到clinicConfig里面，scGoods好使用，这个加价比例不会给到前端
        clinicConfig.setBisPriceConfigView(goodsConfigHelper.getBisPriceConfigView());
        //总部配置 码上放心
        clinicConfig.setExternalConfig(goodsConfigHelper.getStockChainConfig().getExternalConfig());

        /*
         * 药房相关配置
         * */
        if (goodsConfigHelper.getStockClinicConfig() != null) {
            clinicConfig.setOpenPharmacyFlag(goodsConfigHelper.getStockClinicConfig().getOpenPharmacyFlag());
            clinicConfig.setVirtualOpenPharmacyFlag(goodsConfigHelper.getStockClinicConfig().getVirtualOpenPharmacyFlag());
            clinicConfig.setBusSupportFlag(goodsConfigHelper.getStockClinicConfig().getBusSupportFlag());
            clinicConfig.setNeedSummeryGoodsStat(goodsConfigHelper.getStockClinicConfig().getNeedSummeryGoodsStat());
            /*
             * 锁库配置
             * */
            clinicConfig.setLockConfigs(goodsConfigHelper.getStockClinicConfig().getLockConfigs());
        }
        if (goodsConfigHelper.getStockChainConfig().getChainReview() != null) {
            if (clinicConfig.isHeadClinic() && !clinicConfig.isSingleMode()) {
                clinicConfig.setVirtualOpenPharmacyFlag(goodsConfigHelper.getStockChainConfig().getChainReview().getVirtualOpenPharmacyFlag());
            }
            clinicConfig.setNeedSummeryGoodsStatChain(goodsConfigHelper.getStockChainConfig().getChainReview().getNeedSummeryGoodsStat());
        }
        clinicConfig.setPharmacyList(goodsConfigHelper.getPharmacyList());

        //门店配置 码上放心
        if (goodsConfigHelper.getStockClinicConfig().getExternalConfig() != null) {
            clinicConfig.setExternalConfig(goodsConfigHelper.getStockClinicConfig().getExternalConfig());
            clinicConfig.setSelfPurchaseGoodsTypes(goodsConfigHelper.getStockClinicConfig().getExternalConfig().getSelfPurchaseGoodsTypes());
        }

        /**
         *  填充税率
         * */
        if (!CollectionUtils.isEmpty(goodsConfigHelper.getStockChainConfig().getInOutTaxList())) {
            for (GoodsConfigInOutTaxItem item : goodsConfigHelper.getStockChainConfig().getInOutTaxList()) {
                switch (item.getTypeId()) {
                    case 12:
                        clinicConfig.setInTaxTypeid12(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid12(item.getOutTaxRat());
                        break;
                    case 14:
                        clinicConfig.setInTaxTypeid14(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid14(item.getOutTaxRat());
                        break;
                    case 15:
                        clinicConfig.setInTaxTypeid15(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid15(item.getOutTaxRat());
                        break;
                    case 16:
                        clinicConfig.setInTaxTypeid16(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid16(item.getOutTaxRat());
                        break;
                    case 17:
                        clinicConfig.setInTaxTypeid17(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid17(item.getOutTaxRat());
                        break;
                    case 25:
                        clinicConfig.setInTaxTypeid25(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid25(item.getOutTaxRat());
                        break;
                    case 26:
                        clinicConfig.setInTaxTypeid26(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid26(item.getOutTaxRat());
                        break;
                    case 27:
                        clinicConfig.setInTaxTypeid27(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid27(item.getOutTaxRat());
                        break;
                    case 28:
                        clinicConfig.setInTaxTypeid28(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid28(item.getOutTaxRat());
                        break;
                    // 64到69的税率
                    case 64:
                        clinicConfig.setInTaxTypeid64(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid64(item.getOutTaxRat());
                        break;
                    case 65:
                        clinicConfig.setInTaxTypeid65(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid65(item.getOutTaxRat());
                        break;
                    case 66:
                        clinicConfig.setInTaxTypeid66(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid66(item.getOutTaxRat());
                        break;
                    case 67:
                        clinicConfig.setInTaxTypeid67(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid67(item.getOutTaxRat());
                        break;
                    case 68:
                        clinicConfig.setInTaxTypeid68(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid68(item.getOutTaxRat());
                        break;
                    case 69:
                        clinicConfig.setInTaxTypeid69(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid69(item.getOutTaxRat());
                        break;
                    case 91:
                        clinicConfig.setInTaxTypeid91(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid91(item.getOutTaxRat());
                        break;
                    case 92:
                        clinicConfig.setInTaxTypeid92(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid92(item.getOutTaxRat());
                        break;
                    case 93:
                        clinicConfig.setInTaxTypeid93(item.getInTaxRat());
                        clinicConfig.setOutTaxTypeid93(item.getOutTaxRat());
                        break;
                }
            }
        }
        /**
         * 填充自主定价
         * */
        if (goodsConfigHelper.getStockChainConfig().getSubClinicPrice() != null) {
            GoodsConfigSubClinicPrice subClinicPrice = goodsConfigHelper.getStockChainConfig().getSubClinicPrice();

            clinicConfig.setSubSetPrice(subClinicPrice.getSubSetPrice());

            clinicConfig.setSubSetPriceAllClinics(subClinicPrice.getSubSetPriceAllClinics());
            /**
             * 判断子店定价还是比较频繁，组织成map
             * */
            if (!CollectionUtils.isEmpty(subClinicPrice.getSubSetPriceClinics())) {
                clinicConfig.setClinicIdToSubSetPriceClinics(subClinicPrice.getSubSetPriceClinics().stream().collect(Collectors.toMap(ClinicConfig.SubSetPriceClinic::getClinicId, Function.identity(), (a, b) -> a)));
            }
            if (!CollectionUtils.isEmpty(subClinicPrice.getPriceModeMinMaxConfig())) {
                clinicConfig.setTypeIdToPriceMakeUpConfig(subClinicPrice.getPriceModeMinMaxConfig().stream().collect(Collectors.toMap(GoodsPkgCostPriceMakeUpMinMaxItem::getTypeId, Function.identity(), (a, b) -> a)));
            }
            clinicConfig.setMaxPricePercent(subClinicPrice.getMaxPricePercent());
            clinicConfig.setMinPricePercent(subClinicPrice.getMinPricePercent());
            /**
             * 进价加成的设置
             * */
            clinicConfig.setPriceMode(subClinicPrice.getPriceMode());
            if (!CollectionUtils.isEmpty(subClinicPrice.getPriceModeMinMaxConfig())) {
                clinicConfig.getTypeIdToPriceMakeUpConfig().putAll(subClinicPrice.getPriceModeMinMaxConfig().stream().collect(Collectors.toMap(GoodsPkgCostPriceMakeUpMinMaxItem::getTypeId, Function.identity(), (a, b) -> a)));
            }
        }
        /**
         * 填充单据审核信息
         * */
        if (goodsConfigHelper.getStockChainConfig().getChainReview() != null) {
            GoodsConfigChainReview chainReview = goodsConfigHelper.getStockChainConfig().getChainReview();
            clinicConfig.setGoodsTodoReminderCycle(chainReview.getGoodsTodoReminderCycle());
            clinicConfig.setStockCheckChainReview(chainReview.getStockCheckChainReview());
            clinicConfig.setStockInChainReview(chainReview.getStockInChainReview());
            clinicConfig.setStockOutChainReview(chainReview.getStockOutChainReview());
            clinicConfig.setStockTransChainReview(chainReview.getStockTransChainReview());
            clinicConfig.setStockTransDiffPrice(chainReview.getStockTransDiffPrice());
            clinicConfig.setStockReceptionChainReview(chainReview.getStockReceptionChainReview());
            clinicConfig.setCostPrecision(chainReview.getCostPrecision());
            clinicConfig.setChainExternalFlag(chainReview.getChainExternalFlag());
            clinicConfig.setHasPriceUpCount(chainReview.getHasPriceUpCount());
            clinicConfig.setClinicCount(chainReview.getClinicCount());
            clinicConfig.setRegionId(chainReview.getRegionId());
        } else {
            clinicConfig.setGoodsTodoReminderCycle(15);
        }


        /**
         * 如果查的是子店 填充子店库存配置信息
         * */
        if (goodsConfigHelper.getStockClinicConfig() != null) {
            GoodsClinicConfigView clinicConfigView = goodsConfigHelper.getStockClinicConfig();
            clinicConfig.setChainId(clinicConfigView.getChainId());
            clinicConfig.setClinicId(clinicConfigView.getClinicId());

            clinicConfig.setClinicType(clinicConfigView.getNodeType());
            clinicConfig.setViewMode(clinicConfigView.getViewMode());
            clinicConfig.setHisType(clinicConfigView.getHisType());


            clinicConfig.setGoodsPurchaseCycleDays(clinicConfigView.getGoodsPurchaseCycleDays());
            clinicConfig.setStockDaysOfDayAvgSell(clinicConfigView.getCalAvgSellDays());
            clinicConfig.setDisableExpiredGoods(clinicConfigView.getDisableExpiredGoods());
            clinicConfig.setDisableNoStockGoods(clinicConfigView.getDisableNoStockGoods());
            clinicConfig.setStockWarnGoodsTurnoverDays(clinicConfigView.getStockWarnDays());
            clinicConfig.setStockWarnGoodsWillExpiredMonth(clinicConfigView.getExpiredWarnMonth());
            clinicConfig.setShebaoRegion(clinicConfigView.getShebaoRegion());
            clinicConfig.setCostPriceWarnPercent(clinicConfigView.getCostPriceWarnPercent());
            clinicConfig.setCostPriceWarnType(clinicConfigView.getCostPriceWarnType());
            clinicConfig.setUnsalableWarnDays(clinicConfigView.getUnsalableWarnDays());
            clinicConfig.setClinicExternalFlag(clinicConfigView.getClinicExternalFlag());
            clinicConfig.setAddressCityId(clinicConfigView.getAddressCityId());
            clinicConfig.setAddressProvinceId(clinicConfigView.getAddressProvinceId());
            clinicConfig.setAddressDistrictId(clinicConfigView.getAddressDistrictId());
            /**
             * 门店的定价模式
             * {@link cn.abcyun.cis.goods.model.GoodsPrice.ClinicPriceFlag}
             * */
            clinicConfig.setIndependentPricing(clinicConfigView.getIndependentPricing());
            /**
             * 医院
             * 子店特别处理过，用子店的价格模式
             * */
            if (clinicConfigView.getPriceMode() != 0 && clinicConfig.isSubClinic()) {
                clinicConfig.setPriceMode(clinicConfigView.getPriceMode());
            }
            clinicConfig.setProfitWarnFlag(clinicConfigView.getProfitWarnFlag());
            clinicConfig.setHasSubPriceCount(clinicConfigView.getHasSubPriceCount());
            clinicConfig.setHasSubPriceUpCount(clinicConfigView.getHasSubPriceUpCount());
            clinicConfig.setSameOrganRegion(clinicConfigView.isSameOrganRegion());
            clinicConfig.setSupplierMustFieldFlag(clinicConfigView.getSupplierMustFieldFlag());
        } else {
            clinicConfig.setStockWarnGoodsTurnoverDays(30);
            clinicConfig.setStockDaysOfDayAvgSell(7);
            clinicConfig.setStockWarnGoodsWillExpiredMonth(3);
            clinicConfig.setUnsalableWarnDays(30);
            clinicConfig.setCostPriceWarnPercent(BigDecimal.valueOf(10));
            clinicConfig.setCostPriceWarnType(GoodsUtils.CostPriceWarnType.UP_LOW_COST);
            clinicConfig.setProfitWarnFlag(GoodsUtils.PROFIT_WARN_LOW_PROFIT | GoodsUtils.PROFIT_WARN_OVER_PROFIT);
        }
        boolean lockEnable = false;
        if (!CollectionUtils.isEmpty(clinicConfig.getLockConfigs())) {
            lockEnable = clinicConfig.getLockConfigs().stream().anyMatch(cfg -> cfg.getLockFlag() != LockConfig.LockFlag.NO_LOCK || cfg.getLockBatch() == LockConfig.LockBatch.YES);
        }
        //开了批次锁库，子店有进价加成goods，总部有进价加成goods
        if (lockEnable || clinicConfig.getHasPriceUpCount() > 0 || clinicConfig.getHasSubPriceUpCount() > 0) {
            clinicConfig.setOpenLock(YesOrNo.YES);
        }
        if (goodsConfigHelper.getStockChainConfig().getSubClinicArchive() != null) {
            clinicConfig.setSubSetArchiveClinics(goodsConfigHelper.getStockChainConfig().getSubClinicArchive().getSubSetArchiveClinics());
        }

        return clinicConfig;
    }

    /**
     * 在服务发生异常的时候返回一个默认的诊所配置
     * 保证业务流程能走下去
     * 如果连clinic也挂了，那真的是无能未力
     */

    public ClinicConfig.GoodsPurchaseCycleDay getGoodsPurchaseCycleDay(ClinicConfig clinicConfig, int typeId) {
        if (clinicConfig == null || CollectionUtils.isEmpty(clinicConfig.getGoodsPurchaseCycleDays())) {
            return null;
        }
        for (ClinicConfig.GoodsPurchaseCycleDay goodsPurchaseCycleDay : clinicConfig.getGoodsPurchaseCycleDays()) {
            if (goodsPurchaseCycleDay != null && goodsPurchaseCycleDay.getTypeId() == typeId) {
                return goodsPurchaseCycleDay;
            }
        }
        return null;
    }

    public Organ getOrganByShortId(String shortId) {
        if (TextUtils.isEmpty(shortId)) {
            return null;
        }

        try {
            AbcServiceResponseBody<Organ> rspBody = abcCisScClinicFeignClient.getOrganByShortId(shortId);
            if (rspBody != null && rspBody.getData() != null) {
                return rspBody.getData();
            }
        } catch (Exception e) {
            log.error("getOrganByShortId error:", e);
            return null;
        }
        return null;
    }

    public Organ getOrganByClinicId(String clinicId) {
        if (TextUtils.isEmpty(clinicId)) {
            return null;
        }

        try {
            AbcServiceResponseBody<Organ> rspBody = abcCisScClinicFeignClient.getOrgan(clinicId);
            if (rspBody != null && rspBody.getData() != null) {
                return rspBody.getData();
            }
        } catch (Exception e) {
            log.error("getOrganByShortId error:", e);
            return null;
        }
        return null;
    }

    /**
     * 通过员工短Id获取员工信息
     */
    public Employee getEmployeeByShortId(String shortId, String chainId) {
        if (TextUtils.isEmpty(shortId)) {
            return null;
        }
        try {
            long startRequestTime = System.currentTimeMillis();
            AbcServiceResponseBody<Employee> rspBody = abcCisScClinicFeignClient.queryEmployeeByShortId(shortId, chainId);

            if (rspBody != null && rspBody.getData() != null) {
                return rspBody.getData();
            } else {
                log.info("getEmployeeByShortId cost time:{}ms, rsp:{}", (System.currentTimeMillis() - startRequestTime));
            }
        } catch (Exception e) {
            log.error("getEmployeeByShortId error:", e);
            return null;
        }
        return null;
    }

    public ClinicEmployee getClinicEmployee(String employeeId, String clinicId) {
        if (org.apache.commons.lang3.StringUtils.isAnyBlank(employeeId, clinicId)) {
            return null;
        }
        return FeignClientRpcTemplate.dealRpcClientMethod(
                "queryEmployeeClinicInfoById",
                () -> abcCisScClinicFeignClient.queryEmployeeClinicInfoById(employeeId, clinicId),
                employeeId, clinicId
        );
    }

    /**
     * 通过employeeId查Employ
     */
    public Employee getEmployeeById(String employeeId, String chainId) {
        if (TextUtils.isEmpty(employeeId) || GoodsUtils.DEFAULT_ID.equals(employeeId)) {
            return null;
        }
        try {
            long startRequestTime = System.currentTimeMillis();
            AbcServiceResponseBody<Employee> rspBody = abcCisScClinicFeignClient.queryEmployeeById(employeeId, chainId);

            if (rspBody != null && rspBody.getData() != null) {
                return rspBody.getData();
            } else {
                log.info("getEmployeeById cost time:{}ms", (System.currentTimeMillis() - startRequestTime));
            }
        } catch (Exception e) {
            if (e instanceof FeignRuntimeException && ((FeignRuntimeException) e).getCode() == 404) {
                log.warn("chain:{}, employeeId:{} not found", chainId, employeeId);
            } else {
                log.error("getEmployeeByShortId error:", e);
            }
            return null;
        }
        return null;
    }

    /**
     * 确定门店 版本类型
     * 0 无版本 none
     * 10 基础版 basic
     * 20 专业版 professional
     * 30 旗舰版 ultimate
     * 40 大客版 vip
     */
    public String checkClinicEditiion(String clinicId) {
        if (StringUtils.isEmpty(clinicId)) {
            return "none";
        }
        try {
            AbcServiceResponseBody<ClinicCurrentEditionComposeView> rspBody = abcCisScClinicFeignClient.getClinicEditionById(clinicId);

            if (rspBody != null && rspBody.getData() != null && rspBody.getData().getEdition() != null && !StringUtils.isEmpty(rspBody.getData().getEdition().getKey())) {
                return rspBody.getData().getEdition().getKey();
            }
        } catch (Exception e) {
            return "none";
        }
        return "none";
    }

    /**
     * 获取版本功能
     */
    public ClinicPurchaseItemView getEditionFunction(String clinicId, String functionKey) {
        if (StringUtils.isEmpty(clinicId)) {
            return null;
        }
        try {
            AbcServiceResponseBody<ClinicPurchaseItemView> rspBody = abcCisScClinicFeignClient.getClinicPurchaseItem(clinicId, functionKey);
            if (rspBody != null && rspBody.getData() != null && rspBody.getData() != null) {
                return rspBody.getData();
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    /**
     * 门店是否过期
     * 1 过期 0 不过期
     */
    public int checkClinicExpired(String chainId, String clinicId) {
        if (StringUtils.isEmpty(clinicId)) {
            return 0;
        }
        try {
            AbcServiceResponseBody<ClinicCurrentEditionComposeView> rspBody = abcCisScClinicFeignClient.getClinicEditionById(clinicId);

            if (rspBody != null
                    && rspBody.getData() != null
                    && rspBody.getData().getEdition() != null
                    && rspBody.getData().getEdition().getEndDate() != null) {
//                sLogger.info("[凌晨全量更新社保信息]checkClinicExpired chainId={} clinicId={},rspBody.getData()={}",chainId,clinicId, JsonUtils.dump(rspBody.getData()));
                return Instant.now().toEpochMilli() - rspBody.getData().getEdition().getEndDate().toEpochMilli() >= 0 ? 1 : 0;
            }
        } catch (NotFoundException exp) {
            sLogger.error("[凌晨全量更新社保信息]checkClinicExpired chainId={} clinicId={} 未找到版本信息不同步社保", chainId, clinicId);
            return 1;
        } catch (Exception exp) {
            sLogger.error("[凌晨全量更新社保信息]checkClinicExpired chainId={} clinicId={},exp", chainId, clinicId, exp);
            return 0;
        }
        return 0;
    }

    /**
     * 检查employee是否是管理远
     */
    public boolean queryEmployeeInClinic(String employeeId, ClinicConfig clinicConfig) {
        if (TextUtils.isEmpty(employeeId) || clinicConfig == null) {
            return false;
        }
        try {
            /**
             * 看门店是否为管理员
             * */
            AbcServiceResponseBody<ClinicEmployee> rspBody = abcCisScClinicFeignClient.queryEmployeeClinicInfoById(employeeId, clinicConfig.getClinicId());
            if (rspBody != null && rspBody.getData() != null) {
                ClinicEmployee subClinicEmployee = rspBody.getData();
                if (subClinicEmployee.getClinicInfo() != null
                        && (GoodsUtils.compareStrEqual(clinicConfig.getClinicId(), subClinicEmployee.getClinicInfo().getClinicId())
                        || GoodsUtils.compareStrEqual(clinicConfig.getClinicId(), subClinicEmployee.getClinicInfo().getClinicId()))
                ) {
                    return true;
                }
                //如果是子店，再查下总店
                if (!clinicConfig.isSubClinic()) {
                    return false;
                }
                try {
                    /**
                     * 看下这个人在总店是否是管理员
                     * */
                    AbcServiceResponseBody<ClinicEmployee> rspBodyChain = abcCisScClinicFeignClient.queryEmployeeClinicInfoById(employeeId, clinicConfig.getChainId());
                    if (rspBodyChain != null && rspBodyChain.getData() != null) {
                        ClinicEmployee chainClinicEmployee = rspBodyChain.getData();

                        if (chainClinicEmployee.getClinicInfo() != null
                                && (GoodsUtils.compareStrEqual(clinicConfig.getClinicId(), chainClinicEmployee.getClinicInfo().getClinicId())
                                || GoodsUtils.compareStrEqual(clinicConfig.getClinicId(), chainClinicEmployee.getClinicInfo().getClinicId()))
                        ) {
                            return true;
                        }
                    }
                } catch (Exception e) {
                    log.error("queryEmployeeIsAdmin exp:{}", e);
                }
            }
        } catch (Exception e) {
            log.error("queryEmployeeIsAdmin exp:{}", e);
        }
        return false;
    }

    /**
     * 获取人员的库存权限
     */
    public Inventory getEmployeePermission(String employeeId, String chainId, String clinicId) {
        try {
            AbcServiceResponseBody<ClinicEmployeePermissionRsp> rspBody = abcCisScClinicFeignClient.getEmployeePermission(employeeId, chainId, clinicId);
            if (rspBody != null && rspBody.getData() != null && rspBody.getData().getDataPermission() != null && rspBody.getData().getDataPermission().getInventory() != null) {
                return rspBody.getData().getDataPermission().getInventory();
            }
        } catch (Exception exp) {
            sLogger.error("abcCisScClinicFeignClient.getEmployeePermission exp={}", exp);
        }
        Inventory defaultInventory = new Inventory();
        defaultInventory.setIsCanSeeGoodsCost(GoodsUtils.SwitchFlag.ON);
        defaultInventory.setIsCanSeeGoodsProfit(GoodsUtils.SwitchFlag.ON);
        return defaultInventory;
    }

    public ClinicEmployeePermission getEmployeeClinicPermission(String employeeId, String chainId, String clinicId) {
        if (StringUtils.isEmpty(employeeId)) {
            return null;
        }
        try {
            AbcServiceResponseBody<ClinicEmployeePermissionRsp> rspBody = abcCisScClinicFeignClient.getEmployeePermission(employeeId, chainId, clinicId);
            if (rspBody != null && rspBody.getData() != null && rspBody.getData().getClinicPermission() != null) {
                return rspBody.getData().getClinicPermission();
            }
        } catch (Exception exp) {
            sLogger.error("abcCisScClinicFeignClient.getEmployeeClinicPermission exp={}", exp);
        }
        return null;
    }

    /**
     * 批量查employee
     */
    public List<Employee> getEmployeeById(List<String> employeeIdList, String chainId) {
        List<Employee> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(employeeIdList)) {
            return list;
        }
        try {
            QueryEmployeeListByIdReq req = new QueryEmployeeListByIdReq();
            req.setIds(employeeIdList);
            req.setChainId(chainId);
            long startRequestTime = System.currentTimeMillis();
            AbcServiceResponseBody<AbcListPage<Employee>> rspBody = abcCisScClinicFeignClient.queryEmployeeListById(req);

            if (rspBody != null && rspBody.getData() != null && !CollectionUtils.isEmpty(rspBody.getData().getRows())) {
                return rspBody.getData().getRows();
            } else {
                log.info("getEmployeeById cost time:{}ms, rsp:{}", (System.currentTimeMillis() - startRequestTime));
            }
        } catch (Exception e) {
            log.error("getEmployeeByShortId error:", e);
            return list;
        }
        return list;
    }

    /**
     * 查询主店下的所有科室
     */
    public Map<String, Department> getClinicDepartmentByIds(String chainId, List<String> departmentIdList) {
        Map<String, Department> departmentIdToDepartment = new HashMap<>();
        if (CollectionUtils.isEmpty(departmentIdList)) {
            return departmentIdToDepartment;
        }
        try {

            AbcServiceResponseBody<AbcListPage<Department>> rspBody = abcCisScClinicFeignClient.queryDepartmentsByChain(chainId, null, null);
            if (rspBody != null && rspBody.getData() != null && !CollectionUtils.isEmpty(rspBody.getData().getRows())) {
                List<Department> abcListPage = rspBody.getData().getRows();
                Map<String, Department> departmentIdToDepartmentAll = abcListPage.stream().collect(Collectors.toMap(Department::getId, Function.identity(), (a, b) -> a));
                for (String s : departmentIdList) {
                    Department ret = departmentIdToDepartmentAll.get(s);
                    if (ret != null) {
                        departmentIdToDepartment.put(s, ret);
                    }
                }
            }

        } catch (Exception e) {
            log.error("getClinicDepartmentByIds error:", e);
            return departmentIdToDepartment;
        }
        return departmentIdToDepartment;
    }

    public Organ getOrgan(String clinicId) {
        try {
            AbcServiceResponseBody<Organ> rspBody = abcCisScClinicFeignClient.getOrgan(clinicId);
            if (rspBody != null && rspBody.getData() != null) {
                return rspBody.getData();
            }
        } catch (Exception e) {
            log.error("getClinicDepartmentByIds error:", e);
            return null;
        }
        return null;
    }

    /**
     * 子店
     */
    @Deprecated
    public List<Organ> getOrgans(List<String> clinicIdList) {
        try {
            OrganBatchQueryReq req = new OrganBatchQueryReq();
            req.setIds(clinicIdList);
            AbcServiceResponseBody<OrganBatchQueryRsp> rspBody = abcCisScClinicFeignClient.listOrganBatch(req);
            if (rspBody != null && rspBody.getData() != null && !CollectionUtils.isEmpty(rspBody.getData().getOrganList())) {
                return rspBody.getData().getOrganList();
            }
        } catch (Exception e) {
            log.error("getClinicDepartmentByIds error:", e);
            return null;
        }
        return null;
    }

    public List<String> getSubClinicIdList(String chainId) {
        List<String> subClinicIdList = chainIdToSubClinicIdList.getIfPresent(chainId);
        // caffeine强引用，一定要new一个新的
        if (subClinicIdList != null) {
            return new ArrayList<>(subClinicIdList);
        }
        subClinicIdList = querySubClinicIdList(chainId);
        chainIdToSubClinicIdList.put(chainId, subClinicIdList);
        return new ArrayList<>(subClinicIdList);
    }

    public List<Organ> getSubClinicList(String chainId) {
        List<Organ> subClinicList = chainIdToSubClinicList.getIfPresent(chainId);
        // caffeine强引用，一定要new一个新的
        if (subClinicList != null) {
            return new ArrayList<>(subClinicList);
        }
        subClinicList = querySubClinicList(chainId);
        chainIdToSubClinicList.put(chainId, subClinicList);
        return new ArrayList<>(subClinicList);
    }


    public List<Organ> getChainOrgan(String chainId) {
        try {

            AbcServiceResponseBody<ChainOrgan> rspBody = abcCisScClinicFeignClient.getChainOrgan(chainId);
            if (rspBody != null && rspBody.getData() != null && !CollectionUtils.isEmpty(rspBody.getData().getChildren())) {
                return rspBody.getData().getChildren();
            }

        } catch (Exception e) {
            log.error("getClinicDepartmentByIds error:", e);
            return new ArrayList<>();
        }
        return new ArrayList<>();
    }

    public int getClinicRegionInfoById(String chainId) {
        try {
            AbcServiceResponseBody<ClinicRegionView> rspBody = abcCisScClinicFeignClient.getClinicRegionInfoById(chainId);
            if (rspBody != null && rspBody.getData() != null) {
                if (GoodsUtils.compareStrEqual(rspBody.getData().getId(), ClinicRegionView.RegionId.REGION1)) {
                    return 1;
                }
                if (GoodsUtils.compareStrEqual(rspBody.getData().getId(), ClinicRegionView.RegionId.REGION2)) {
                    return 2;
                }
            }
        } catch (Exception e) {
            log.error("getClinicRegionInfoById error:", e);
        }
        return 0;
    }

    public ChainOrgan getChainOrganV1(String chainId) {
        try {
            AbcServiceResponseBody<ChainOrgan> rspBody = abcCisScClinicFeignClient.getChainOrgan(chainId);
            if (rspBody != null && rspBody.getData() != null) {
                return rspBody.getData();
            }
        } catch (Exception e) {
            log.error("queryAllOrgans error:", e);
        }
        return null;
    }

    //把注定和子店打平了一起返回
    public List<Organ> getChainAllSubOrgan(String chainId) {
        List<Organ> organList = new ArrayList<>();
        try {
            AbcServiceResponseBody<ChainOrgan> rspBody = abcCisScClinicFeignClient.getChainOrgan(chainId);
            if (rspBody != null && rspBody.getData() != null) {
                Organ chainOrgan = new Organ();
                BeanUtils.copyProperties(rspBody.getData(), chainOrgan);
                organList.add(chainOrgan);
            }
            if (rspBody != null && rspBody.getData() != null && !CollectionUtils.isEmpty(rspBody.getData().getChildren())) {
                organList.addAll(rspBody.getData().getChildren());
            }


        } catch (Exception e) {
            log.error("getClinicDepartmentByIds error:", e);
        }
        return organList;
    }

    /**
     * 找出连锁下所有有库存权限的员工列表
     *
     * @param chainId         连锁ID
     * @param clinicIdToOrgan 过滤指定门店
     */
    public List<EmployeeNeedToPushOrganList> findClinicEmployeesHasStockPrivInChain(String chainId, Map<String, Organ> clinicIdToOrgan) {
        List<EmployeeNeedToPushOrganList> returnList = new ArrayList<>();
        try {
            AbcServiceResponseBody<AbcListPage<ChainEmployee>> rspBody = abcCisScClinicFeignClient.queryChainEmployeeList(chainId);
            if (rspBody == null || rspBody.getData() == null) {
                return returnList;
            }
            /**
             * 遍历所有员工
             * */
            for (ChainEmployee chainEmployee : rspBody.getData().getRows()) {
                if (CollectionUtils.isEmpty(chainEmployee.getClinicInfos())) {
                    continue;
                }
                EmployeeNeedToPushOrganList dto = new EmployeeNeedToPushOrganList();
                dto.setEmployeeId(chainEmployee.getId());

                chainEmployee.getClinicInfos().stream().filter(employeeClinicInfo -> clinicIdToOrgan.containsKey(employeeClinicInfo.getClinicId()))
                        .filter(employeeClinicInfo ->
                                employeeClinicInfo.getRoleId() == EmployeeClinicInfo.RoleId.ADMIN  //系统管理元
                                        || (employeeClinicInfo.getRoleId() == EmployeeClinicInfo.RoleId.NORMAL
                                        && !StringUtils.isEmpty(employeeClinicInfo.getModuleIds())
                                        && Arrays.stream(employeeClinicInfo.getModuleIds().split(",")).anyMatch(moduleId -> moduleId != null && moduleId.compareTo("8") == 0)))
                        .forEach(employeeClinicInfo -> {
                            dto.getHasStockPrivOrganList().add(clinicIdToOrgan.get(employeeClinicInfo.getClinicId()));
                        });
                if (dto.getHasStockPrivOrganList().size() > 0) {
                    returnList.add(dto);
                }
            }
        } catch (Exception e) {
            log.error("findAffectClinicEmployees error:", e);
        }
        log.info("findAffectClinicEmployees returnList:{}", returnList);
        return returnList;
    }

    public List<ClinicDeviceRoomView> listRoomsByIds(String chainId, List<Long> roomIds, int withDeleted) {
        List<ClinicDeviceRoomView> deviceRoomViews = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(roomIds)) {
            return deviceRoomViews;
        }
        ClinicDeviceRoomQueryByIdsReq roomQueryByIdsReq = new ClinicDeviceRoomQueryByIdsReq();
        roomQueryByIdsReq.setChainId(chainId);
        roomQueryByIdsReq.setClinicId(null);
        roomQueryByIdsReq.setRoomIds(roomIds.stream().map(roomId -> Objects.toString(roomId, null)).collect(Collectors.toList()));
        roomQueryByIdsReq.setWithDeleted(withDeleted);

        return Optional.ofNullable(
                FeignClientRpcTemplate.dealRpcClientMethod(
                        "listRoomsByIds",
                        () -> abcCisScClinicFeignClient.listRoomsByIds(roomQueryByIdsReq),
                        roomQueryByIdsReq
                )
        ).map(AbcListPage::getRows).orElse(deviceRoomViews);
    }

    public BaseSuccessRsp deleteRoomById(String deviceRoomId, String chainId, String clinicId, String operatorId) {
        ClinicDeviceRoomDeleteReq req = new ClinicDeviceRoomDeleteReq();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setOperatorId(operatorId);
        return FeignClientRpcTemplate.dealRpcClientMethod(
                "deleteRoom",
                () -> abcCisScClinicFeignClient.deleteRoom(req, deviceRoomId),
                req, deviceRoomId
        );
    }

    public Map<String, Department> getDepartmentMapByIds(List<String> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return new HashMap<>();
        }
        QueryDepartmentByIds req = new QueryDepartmentByIds();
        req.setIds(departmentIds);
        try {
            AbcServiceResponseBody<AbcListPage<Department>> response = abcCisScClinicFeignClient.queryDepartmentsByIds(req);
            if (response != null && response.getData() != null && response.getData().getRows() != null) {
                return response.getData().getRows().stream().collect(Collectors.toMap(Department::getId, Function.identity(), (a, b) -> a));
            }
        } catch (Exception e) {
            log.info("queryDepartmentsByIds error: ", e);
        }
        return new HashMap<>();
    }

    public ClinicPurchaseItemView getClinicPurchaseItem(String clinicId, String purchaseItemKey) {
        return FeignClientRpcTemplate.dealRpcClientMethod(
                "getClinicPurchaseItem",
                true,
                () -> abcCisScClinicFeignClient.getClinicPurchaseItem(clinicId, purchaseItemKey),
                clinicId, purchaseItemKey
        );
    }

    public List<Department> queryDepartmentsByChain(String chainId) {
        return Optional.ofNullable(
                FeignClientRpcTemplate.dealRpcClientMethod(
                        "queryDepartmentsByChain",
                        () -> abcCisScClinicFeignClient.queryDepartmentsByChain(chainId, null, null),
                        chainId
                )
        ).map(AbcListPage::getRows).orElseGet(Collections::emptyList);
    }

    public Map<String, Department> getDepartmentMapByChainId(String chainId) {
        List<Department> list = chainIdToDepartments.getIfPresent(chainId);
        if (list == null) {
            list = queryDepartmentsByChain(chainId);
            chainIdToDepartments.put(chainId, list);
        }
        return Optional.ofNullable(list)
                .orElseGet(Collections::emptyList)
                .stream().collect(Collectors.toMap(DepartmentBase::getId, Function.identity(), (a, b) -> a));
    }

    /**
     * 清理每个机器的内存
     */
    public void clearConfigCache(ClearGoodsConfigCacheMessage configCacheMessage) {
        if (configCacheMessage == null || StringUtils.isEmpty(configCacheMessage.getChainId())) {
            return;
        }
        List<String> clinicIdList = getSubClinicIdList(configCacheMessage.getChainId());
        clinicIdList.add(configCacheMessage.getChainId());
        clinicIdToClinicConfig.invalidateAll(clinicIdList);
    }

    public List<String> querySubClinicIdList(String chainId) {
        try {
            AbcServiceResponseBody<ChainOrgan> rspBody = abcCisScClinicFeignClient.getChainOrgan(chainId);
            if (rspBody != null && rspBody.getData() != null && !CollectionUtils.isEmpty(rspBody.getData().getChildren())) {
                return rspBody.getData().getChildren().stream().map(Organ::getId).distinct().collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("getClinicDepartmentByIds error:", e);
            return new ArrayList<>();
        }
        return new ArrayList<>();
    }

    public List<Organ> querySubClinicList(String chainId) {
        try {
            AbcServiceResponseBody<ChainOrgan> rspBody = abcCisScClinicFeignClient.getChainOrgan(chainId);
            if (rspBody != null && rspBody.getData() != null && !CollectionUtils.isEmpty(rspBody.getData().getChildren())) {
                return rspBody.getData().getChildren();
            }
        } catch (Exception e) {
            log.error("getClinicDepartmentByIds error:", e);
            return new ArrayList<>();
        }
        return new ArrayList<>();
    }

    /**
     * 绑定设备
     */
    public DeviceBindRsp bindDevice(ExaminationDeviceModel deviceModel, ExaminationDevice device) {
        if (!ExamDeviceConst.SupplierId.isCloudSupplier(deviceModel.getSupplierId())) {
            return null;
        }
        int goodsSubType = device.getGoodsSubType();
        Integer deviceType = null;
        if (goodsSubType == GoodsConst.ExaminationSubType.ASSAY) {
            deviceType = DeviceConst.DeviceType.EXAMINATION;
        } else if (goodsSubType == GoodsConst.ExaminationSubType.INSPECTION) {
            deviceType = DeviceConst.DeviceType.INSPECTION;
        }
        if (deviceType == null) {
            throw new ParamRequiredException("deviceType");
        }
        DeviceBindRpcReq deviceBindRpcReq = new DeviceBindRpcReq();
        deviceBindRpcReq.setChainId(device.getChainId());
        deviceBindRpcReq.setClinicId(device.getClinicId());
        deviceBindRpcReq.setOperatorId(device.getCreatedBy());
        deviceBindRpcReq.setDeviceId(device.getShortId());
        deviceBindRpcReq.setDeviceType(deviceType);

        return FeignClientRpcTemplate.dealRpcClientMethod(
                "bindDevice",
                true,
                () -> abcCisScClinicFeignClient.bindDevice(deviceBindRpcReq),
                deviceBindRpcReq
        );
    }

    public List<CooperationClinicView> listCooperationClinicByClinic(String clinicId, Integer type, int businessType) {
        return Optional.ofNullable(
                FeignClientRpcTemplate.dealRpcClientMethod(
                        "listCooperationClinicByClinic",
                        () -> abcCisScClinicFeignClient.listCooperationClinicByClinic(clinicId, type, businessType),
                        clinicId, type, businessType
                )
        ).map(AbcListPage::getRows).orElseGet(Collections::emptyList);
    }

    public List<EmployeeBasic> queryEmployeeByModules(String chainId, String clinicId, List<String> moduleIds) {
        try {
            QueryEmployeeByModuleReq req = new QueryEmployeeByModuleReq();
            req.setChainId(chainId);
            req.setClinicId(clinicId);
            req.setModuleIds(moduleIds);
            AbcListPage<EmployeeBasic> listPage = FeignClientRpcTemplate.dealRpcClientMethod("listEmployeeByModule",
                    () -> abcCisScClinicFeignClient.listEmployeeByModule(req), req);
            if (listPage == null || listPage.getRows().isEmpty()) {
                return new ArrayList<>();
            }
            return listPage.getRows();
        } catch (Exception e) {
            log.error("listEmployeeByModule error:", e);
            return new ArrayList<>();
        }
    }


    /**
     * 是否合作中心门店
     *
     * @param clinicId 诊所 ID
     * @return boolean
     */
    public boolean isCoopCenterClinic(String clinicId) {
        Integer flag = COOP_CENTER_CLINIC_CACHE.get(
                clinicId,
                key -> {
                    ClinicPurchaseItemView purchaseItemView = getClinicPurchaseItem(clinicId, ClinicPurchaseItemKey.EXAM_CENTER_ORGAN);
                    // 不判断是否过期
                    return purchaseItemView != null && purchaseItemView.getIsPurchased() == YesOrNo.YES ? YesOrNo.YES : YesOrNo.NO;
                }
        );
        return Objects.equals(flag, YesOrNo.YES);
    }

    /**
     * 是否合作门店
     *
     * @param clinicId 诊所 ID
     * @return boolean
     */
    public boolean isCoopClinic(String clinicId) {
        Integer flag = COOP_CLINIC_CACHE.get(
                clinicId,
                key -> {
                    ClinicPurchaseItemView purchaseItemView = getClinicPurchaseItem(clinicId, ClinicPurchaseItemKey.EXAM_COOPERATE_ORGAN);
                    // 不判断是否过期
                    return purchaseItemView != null && purchaseItemView.getIsPurchased() == YesOrNo.YES ? YesOrNo.YES : YesOrNo.NO;
                }
        );
        return Objects.equals(flag, YesOrNo.YES);
    }

    public String getOrganRegionId(String clinicId) {
        Organ organ = getOrgan(clinicId);
        return organ != null ?
                GoodsUtils.nullToEmpty(organ.getAddressProvinceId()) + "_" + GoodsUtils.nullToEmpty(organ.getAddressCityId())
                        + "_" + GoodsUtils.nullToEmpty(organ.getAddressDistrictId()) : null;
    }

}
