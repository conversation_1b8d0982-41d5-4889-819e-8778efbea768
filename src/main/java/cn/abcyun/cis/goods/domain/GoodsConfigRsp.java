/*
 * Copyright (c) 2019.
 */

package cn.abcyun.cis.goods.domain;

import cn.abcyun.cis.goods.vo.frontend.pharmacy.GoodsPharmacyRuleView;
import cn.abcyun.cis.goods.vo.frontend.pharmacy.ToClientGoodsPharmacyView;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

;


/**
 * 吐给前端的协议，历史原因，协议兼容
 */

@Data
public class GoodsConfigRsp {

    /**
     * 总部的Id
     */
    @ApiModelProperty("连锁ID")
    private String chainId;
    /**
     * 总部门店的 诊所类型
     * add 2022.06.21
     */
    @ApiModelProperty("总部门店的 诊所类型")
    private int nodeType;
    /**
     * 总部门店的viewMode
     * add 2022.06.21
     */
    @ApiModelProperty("总部门店的viewMode")
    private int viewMode;
    /**
     * 总部门店的hisType
     * add 2022.06.21
     */
    @ApiModelProperty("总部门店的hisType")
    private int hisType;
    /**
     * 【门店视图,门店的属性,不是连锁的】 自定义税率 修改如果为null 表示这里不做修改
     */
    @ApiModelProperty("【门店视图,门店的属性,不是连锁的】 自定义税率 修改如果为null 表示这里不做修改")
    private List<GoodsConfigInOutTaxItem> inOutTaxList;

    /**
     * 【门店视图,门店的属性,不是连锁的】 子店定价 修改如果为null表示这里不做修改
     */
    @ApiModelProperty("【门店视图,门店的属性,不是连锁的】 子店定价 修改如果为null表示这里不做修改")
    private GoodsConfigSubClinicPrice subClinicPrice;

    @ApiModelProperty(value = "子店商品管理权限")
    private GoodsConfigSubClinicArchive subClinicArchive;

    /**
     * 【总部才有的配置，总部配置会控制门店】连锁库存开关 修改如果为null表示这部分不做修改
     */
    @ApiModelProperty("总部才有的配置，总部配置会控制门店】连锁库存开关 修改如果为null表示这部分不做修改")
    private GoodsConfigChainReview chainReview;

    @ApiModelProperty("总部配置，总部配置的库存告警")
    private GoodsChainWarnCfgView chainWarnCfgView;
    /**
     * 【门店视图,门店的属性,不是连锁的】 门店的库存商品的库存配置 单店+连锁子店会返回
     */
    @ApiModelProperty("【门店视图,门店的属性,不是连锁的】 门店的库存商品的库存配置 单店+连锁子店会返回")
    private GoodsClinicConfigView stockGoodsConfig;
    @ApiModelProperty("【门店视图,门店的属性,不是连锁的】 追溯码配置")
    private GoodsClinicTraceCodeConfigView traceCodeConfig;
    /**
     * 后台手动设置
     * 目前占2位 001库存模块走后段搜索)  010门诊搜索搜走后端搜索
     * 前端使用
     */
    private int clinicExternalFlag;

    /**
     * 【门店视图,门店的属性,不是连锁的】 药房列表
     */
    @ApiModelProperty("【门店视图,门店的属性,不是连锁的】 药房列表")
    private List<ToClientGoodsPharmacyView> pharmacyList;

    @ApiModelProperty(value = "下达规则列表")
    private List<GoodsPharmacyRuleView> pharmacyRuleList;
    /**
     * 【门店视图,门店的属性,不是连锁的】 连锁只读不会修改的配置
     */
    @ApiModelProperty("【门店视图,门店的属性,不是连锁的】 连锁只读不会修改的配置")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private GoodsReadOnlyConfig readOnlyConfig;

    /**
     * 门店锁库配置
     */
    @ApiModelProperty("锁库配置")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<LockConfig> lockConfigs;

    /**
     * ABC普通模板门店ChainId
     */
    @ApiModelProperty("ABC普通模板门店ChainId")
    private String normalChainId;
    /**
     * ABC模版门店(牙科科)ChainId
     */
    @ApiModelProperty("ABC模版门店(牙科科)ChainId")
    private String dentistryChainId;
    /**
     * ABC普模版门店(眼科)ChainId
     */
    @ApiModelProperty(" ABC普模版门店(眼科)ChainId")
    private String ophthalmologyChainId;
    /**
     * 体检模版门店
     */
    @ApiModelProperty("ABC模版门店(体检)ChainId")
    private String physicalExaminationChainId;
    /***
     * 【门店视图,门店的属性,不是连锁的】 第一位本地药房是否要算goodsstat的汇总,第3位待检代配药房是否需要汇总
     * */
    @ApiModelProperty("【门店视图,门店的属性,不是连锁的】 第一位本地药房是否要算goodsstat的汇总,第3位待检代配药房是否需要汇总")
    private int needSummeryGoodsStat;
    /**
     * 【门店视图,门店的属性,不是连锁的】 本地药房开启标记 0 未开启药房  10 开启单药房  20 开启多药房
     */
    @ApiModelProperty("【门店视图,门店的属性,不是连锁的】 本地药房开启标记 0 未开启药房  10 开启单药房  20 开启多药房")
    private int openPharmacyFlag;
    @ApiModelProperty("【门店视图,门店的属性,不是连锁的】 代煎代配药房开启标记 0 未开启药房  10 开启单药房  20 开启多药房")
    private int virtualOpenPharmacyFlag;
    @ApiModelProperty("【门店视图,门店的属性,不是连锁的】 业务类型")
    private int busSupportFlag;
    /***
     * 发一个锁库请求出去
     * 1.2024.05 产品的进价加成策略是，可以给单个goods设置
     * 2.所以scGoods会做一个放大策略，只要有一个goods开了进价加成 ，
     *      整个连锁所有门店都锁库，而且进行按批次进行锁定
     * 3.操作界面里面的 “批次锁库” = 按锁定批次库存 + 锁定的批次不能换
     *                非批次锁定 = 按i次锁定库存 + 锁定的批次可以换
     * 4.“批次锁定”：门店真的开了 批次锁定设置 + 进价加成的药品
     *
     * 值为 1表示要发起锁库请求
     * */
    @ApiModelProperty("门店配置 0 不用发起锁库，1是需要发起锁库" +
            " （具体是要发起锁goods还是锁批次 要看 lockConfigs）" +
            " 1.锁goods  锁的数量会计入锁库数量，发药那一刻才确定批次" +
            " 2.锁批次   锁的数量会计入锁库数量，锁住那一刻就确定了批次，不能换" +
            " 3.其他     锁的数量不会计入锁库数量,发药那一刻才确定批次 " +
            "" +
            " 进价加成需求：收费单最后都会记录实际发生的批次" +
            "针对1，3 goods提供按库存量推荐扣库批次的接口功能， 底层实现1，3不锁批次，批次的变更反而更麻烦，直接通过传库存量获取预锁批次接口" +
            "")
    private int openLock;


    @ApiModelProperty(value = "供应商必填字段配置")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private GoodsSupplierMustFieldView supplierMustField;

    @ApiModelProperty(value = "药店配置")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private GoodsPharmacyConfigView pharmacyConfig;
}
