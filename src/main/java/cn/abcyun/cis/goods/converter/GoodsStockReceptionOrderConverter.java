package cn.abcyun.cis.goods.converter;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.commons.util.MergeTool;
import cn.abcyun.cis.goods.cache.redis.ChoosePharmacyHelper;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisCache;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisUtils;
import cn.abcyun.cis.goods.domain.ClinicConfig;
import cn.abcyun.cis.goods.domain.GoodsStatManager;
import cn.abcyun.cis.goods.domain.GoodsStockMountTrible;
import cn.abcyun.cis.goods.entity.BasicGoodsStockReception;
import cn.abcyun.cis.goods.entity.GoodsStockReceptionDraft;
import cn.abcyun.cis.goods.entity.GoodsStockReceptionOrderDraft;
import cn.abcyun.cis.goods.model.*;
import cn.abcyun.cis.goods.repository.GoodsStockLockingRepository;
import cn.abcyun.cis.goods.repository.GoodsStockReceptionDraftRepository;
import cn.abcyun.cis.goods.repository.GoodsStockReceptionOrderDraftRepository;
import cn.abcyun.cis.goods.repository.GoodsStockRepository;
import cn.abcyun.cis.goods.service.rpc.CisClinicService;
import cn.abcyun.cis.goods.service.stockreception.GoodsStockReceptionOrderService;
import cn.abcyun.cis.goods.utils.GoodsStockUtils;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.utils.ServiceUtils;
import cn.abcyun.cis.goods.vo.frontend.pharmacy.GoodsPharmacyView;
import cn.abcyun.cis.goods.vo.frontend.reception.CreateGoodsStockReception;
import cn.abcyun.cis.goods.vo.frontend.reception.CreateStockReceptionItemViewReq;
import cn.abcyun.cis.goods.vo.frontend.reception.CreateStockReceptionOrderReq;
import cn.abcyun.cis.goods.vo.frontend.reception.GoodsStockReceptionOrderView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-07-27 22:45
 */
@Slf4j
@Component
public class GoodsStockReceptionOrderConverter {

    @Lazy
    @Autowired
    private GoodsStockReceptionOrderService goodsStockReceptionOrderService;
    @Autowired
    private GoodsStockReceptionOrderDraftRepository goodsStockReceptionOrderDraftRepository;
    @Autowired
    private GoodsStockReceptionDraftRepository goodsStockReceptionDraftRepository;
    @Autowired
    private CisClinicService clinicService;
    @Autowired
    private GoodsRedisUtils goodsRedisUtils;
    @Autowired
    private GoodsStockRepository goodsStockRepository;
    @Autowired
    private GoodsStockLockingRepository goodsStockLockingRepository;

    public GoodsStockReceptionOrder toGoodsStockReceptionOrder(GoodsStockReceptionOrderDraft goodsStockReceptionOrderDraft) {
        if (goodsStockReceptionOrderDraft == null) {
            return null;
        }
        GoodsStockReceptionOrder goodsStockReceptionOrder = new GoodsStockReceptionOrder();
        //goodsStockReceptionOrder.setIsDeleted(goodsStockReceptionOrderDraft.getIsDeleted());
        goodsStockReceptionOrder.setId(goodsStockReceptionOrderDraft.getId());
        goodsStockReceptionOrder.setOriginalReceptionOrderId(goodsStockReceptionOrderDraft.getId());
        goodsStockReceptionOrder.setReceptSourceOrderId(goodsStockReceptionOrderDraft.getReceptSourceOrderId());
        goodsStockReceptionOrder.setChainId(goodsStockReceptionOrderDraft.getChainId());
        goodsStockReceptionOrder.setClinicId(goodsStockReceptionOrderDraft.getClinicId());
        goodsStockReceptionOrder.setReceiveDepartmentId(goodsStockReceptionOrderDraft.getReceiveDepartmentId());
        goodsStockReceptionOrder.setOrderNo(goodsStockReceptionOrderDraft.getOrderNo());
        goodsStockReceptionOrder.setInConfirmBy(goodsStockReceptionOrderDraft.getInConfirmBy());
        goodsStockReceptionOrder.setInConfirmed(goodsStockReceptionOrderDraft.getInConfirmed());
        goodsStockReceptionOrder.setOutConfirmBy(goodsStockReceptionOrderDraft.getOutConfirmBy());
        goodsStockReceptionOrder.setOutConfirmed(goodsStockReceptionOrderDraft.getOutConfirmed());
        goodsStockReceptionOrder.setType(goodsStockReceptionOrderDraft.getType());
        goodsStockReceptionOrder.setComment(goodsStockReceptionOrderDraft.getComment());
        goodsStockReceptionOrder.setStatus(goodsStockReceptionOrderDraft.getStatus());
        goodsStockReceptionOrder.setReviewed(goodsStockReceptionOrderDraft.getReviewed());
        goodsStockReceptionOrder.setReviewBy(goodsStockReceptionOrderDraft.getReviewBy());
        goodsStockReceptionOrder.setKindCount(goodsStockReceptionOrderDraft.getKindCount());
        goodsStockReceptionOrder.setTotalPackageCount(goodsStockReceptionOrderDraft.getTotalPackageCount());
        goodsStockReceptionOrder.setTotalCost(goodsStockReceptionOrderDraft.getTotalCost());
        goodsStockReceptionOrder.setExcludeTaxTotalCostPrice(goodsStockReceptionOrderDraft.getExcludeTaxTotalCostPrice());
        goodsStockReceptionOrder.setOutPharmacyNo(goodsStockReceptionOrderDraft.getOutPharmacyNo());
        goodsStockReceptionOrder.setOutPharmacyType(goodsStockReceptionOrderDraft.getOutPharmacyType());
        goodsStockReceptionOrder.setInPharmacyNo(goodsStockReceptionOrderDraft.getInPharmacyNo());
        goodsStockReceptionOrder.setInPharmacyType(goodsStockReceptionOrderDraft.getInPharmacyType());
        goodsStockReceptionOrder.setToUserId(goodsStockReceptionOrderDraft.getToUserId());
        goodsStockReceptionOrder.setOutUserId(goodsStockReceptionOrderDraft.getOutUserId());
        goodsStockReceptionOrder.setOutDate(goodsStockReceptionOrderDraft.getOutDate());
        goodsStockReceptionOrder.setCreatedBy(goodsStockReceptionOrderDraft.getCreatedBy());
        goodsStockReceptionOrder.setCreated(goodsStockReceptionOrderDraft.getCreated());
        goodsStockReceptionOrder.setLastModifiedBy(goodsStockReceptionOrderDraft.getLastModifiedBy());
        goodsStockReceptionOrder.setLastModified(goodsStockReceptionOrderDraft.getLastModified());
        goodsStockReceptionOrder.setOutPharmacy(goodsStockReceptionOrderDraft.getOutPharmacy());
        goodsStockReceptionOrder.setInPharmacy(goodsStockReceptionOrderDraft.getInPharmacy());
        //goodsStockReceptionOrder.setList(toGoodsStockReceptionList(goodsStockReceptionOrderDraft.getList().stream().map(it -> (GoodsStockReceptionDraft) it).collect(Collectors.toList())));
        goodsStockReceptionOrder.setApplyList(goodsStockReceptionOrderDraft.getApplyList());
        goodsStockReceptionOrder.setCommentList(goodsStockReceptionOrderDraft.getCommentList());
        goodsStockReceptionOrder.setLogs(goodsStockReceptionOrderDraft.getLogs());
        goodsStockReceptionOrder.setReceiveDepartment(goodsStockReceptionOrderDraft.getReceiveDepartment());
        goodsStockReceptionOrder.setGoodsStockOrderChangeDetail(goodsStockReceptionOrderDraft.getGoodsStockOrderChangeDetail());
        goodsStockReceptionOrder.setBatchIdToGoodsBatchMap(goodsStockReceptionOrderDraft.getBatchIdToGoodsBatchMap());
        return goodsStockReceptionOrder;
    }

    public GoodsStockReception toGoodsStockReception(GoodsStockReceptionDraft goodsStockReceptionDraft) {
        if (goodsStockReceptionDraft == null) {
            return null;
        }
        GoodsStockReception goodsStockReception = new GoodsStockReception();
        //goodsStockReception.setIsDeleted(goodsStockReceptionDraft.getIsDeleted());
        goodsStockReception.setId(goodsStockReceptionDraft.getId());
        goodsStockReception.setReceptSourceId(goodsStockReceptionDraft.getReceptSourceId());
        goodsStockReception.setReceptSourceReception(toGoodsStockReception((GoodsStockReceptionDraft) goodsStockReceptionDraft.getReceptSourceReception()));
        goodsStockReception.setChainId(goodsStockReceptionDraft.getChainId());
        goodsStockReception.setClinicId(goodsStockReceptionDraft.getClinicId());
        goodsStockReception.setStatus(goodsStockReceptionDraft.getStatus());
        goodsStockReception.setOrderId(goodsStockReceptionDraft.getOrderId());
        goodsStockReception.setGoodsId(goodsStockReceptionDraft.getGoodsId());
        goodsStockReception.setGoodsSnap(goodsStockReceptionDraft.getGoodsSnap());
        goodsStockReception.setBatchId(goodsStockReceptionDraft.getBatchId());
        goodsStockReception.setPieceNum(goodsStockReceptionDraft.getPieceNum());
        goodsStockReception.setPieceCount(goodsStockReceptionDraft.getPieceCount());
        goodsStockReception.setPiecePrice(goodsStockReceptionDraft.getPiecePrice());
        goodsStockReception.setPackageCount(goodsStockReceptionDraft.getPackageCount());
        goodsStockReception.setApplicationPieceCount(goodsStockReceptionDraft.getApplicationPieceCount());
        goodsStockReception.setApplicationPackageCount(goodsStockReceptionDraft.getApplicationPackageCount());
        goodsStockReception.setPackagePrice(goodsStockReceptionDraft.getPackagePrice());
        goodsStockReception.setPackageCostPrice(goodsStockReceptionDraft.getPackageCostPrice());
        goodsStockReception.setOutPharmacyNo(goodsStockReceptionDraft.getOutPharmacyNo());
        goodsStockReception.setOutPharmacyType(goodsStockReceptionDraft.getOutPharmacyType());
        goodsStockReception.setInPharmacyNo(goodsStockReceptionDraft.getInPharmacyNo());
        goodsStockReception.setInPharmacyType(goodsStockReceptionDraft.getInPharmacyType());
        goodsStockReception.setOutStockBatchId(goodsStockReceptionDraft.getOutStockBatchId());
        goodsStockReception.setInStockBatchId(goodsStockReceptionDraft.getInStockBatchId());
        goodsStockReception.setLockId(goodsStockReceptionDraft.getLockId());
        goodsStockReception.setType(goodsStockReceptionDraft.getType());
        goodsStockReception.setParentId(goodsStockReceptionDraft.getParentId());
        goodsStockReception.setCreatedBy(goodsStockReceptionDraft.getCreatedBy());
        goodsStockReception.setCreated(goodsStockReceptionDraft.getCreated());
        goodsStockReception.setLastModifiedBy(goodsStockReceptionDraft.getLastModifiedBy());
        goodsStockReception.setLastModified(goodsStockReceptionDraft.getLastModified());
        goodsStockReception.setStockInBatchDetailList(goodsStockReceptionDraft.getStockInBatchDetailList());
        goodsStockReception.setGoodsRedisCache(goodsStockReceptionDraft.getGoodsRedisCache());
        goodsStockReception.setStockItemLogField(goodsStockReceptionDraft.getStockItemLogField());
        goodsStockReception.setGoodsStockLogMsgList(goodsStockReceptionDraft.getGoodsStockLogMsgList());
        goodsStockReception.setReceptionOutBatchGoodsStockList(goodsStockReceptionDraft.getReceptionOutBatchGoodsStockList());
        return goodsStockReception;
    }

    public List<GoodsStockReception> toGoodsStockReceptionList(List<GoodsStockReceptionDraft> goodsStockReceptionDraftList) {
        if (CollectionUtils.isEmpty(goodsStockReceptionDraftList)) {
            return new ArrayList<>();
        }
        return goodsStockReceptionDraftList.stream().map(this::toGoodsStockReception).collect(Collectors.toList());
    }

    public GoodsStockReceptionOrderView toGoodsStockReceptionOrderView(GoodsStockReceptionOrderDraft goodsStockReceptionOrderDraft, List<GoodsStockReceptionDraft> goodsStockReceptionDrafts) {

        ClinicConfig clinicConfig = ServiceUtils.getClinicConfig(clinicService, goodsStockReceptionOrderDraft.getClinicId());
        List<GoodsRedisCache> goodsRedisCacheList = new ArrayList<>();
        List<String> uniqGoodsIdList = Optional.ofNullable(goodsStockReceptionDrafts).orElseGet(Collections::emptyList).stream().map(BasicGoodsStockReception::getGoodsId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (Objects.nonNull(clinicConfig)) {
            GoodsPharmacyView inPharmacy = clinicConfig.getClinicPharmacyIncDeletedByNo(goodsStockReceptionOrderDraft.getInPharmacyNo());
            GoodsPharmacyView outPharmacy = clinicConfig.getClinicPharmacyIncDeletedByNo(goodsStockReceptionOrderDraft.getOutPharmacyNo());
            goodsStockReceptionOrderDraft.setInPharmacy(inPharmacy);
            goodsStockReceptionOrderDraft.setOutPharmacy(outPharmacy);
            goodsRedisCacheList.addAll(goodsRedisUtils.getGoodsRedisCache(uniqGoodsIdList,
                    ChoosePharmacyHelper.ofSpecificPharmacy(Optional.ofNullable(inPharmacy).map(GoodsPharmacyView::getType).orElse(GoodsConst.PharmacyType.LOCAL_PHARMACY), Optional.ofNullable(inPharmacy).map(GoodsPharmacyView::getNo).orElse(GoodsUtils.PHARMACY_NO_0)),
                    clinicConfig));
            goodsRedisCacheList.addAll(goodsRedisUtils.getGoodsRedisCache(uniqGoodsIdList,
                    ChoosePharmacyHelper.ofSpecificPharmacy(Optional.ofNullable(outPharmacy).map(GoodsPharmacyView::getType).orElse(GoodsConst.PharmacyType.LOCAL_PHARMACY), Optional.ofNullable(outPharmacy).map(GoodsPharmacyView::getNo).orElse(GoodsUtils.PHARMACY_NO_0)),
                    clinicConfig));
            if (Objects.nonNull(inPharmacy)) {
                Integer inPharmacyNo = inPharmacy.getNo();
                int inPharmacyType = inPharmacy.getType();
                goodsStockReceptionOrderDraft.setInPharmacyNo(inPharmacyNo);
                goodsStockReceptionOrderDraft.setInPharmacyType(inPharmacyType);
            }
            if (Objects.nonNull(outPharmacy)) {
                Integer outPharmacyNo = outPharmacy.getNo();
                int outPharmacyType = outPharmacy.getType();
                goodsStockReceptionOrderDraft.setOutPharmacyNo(outPharmacyNo);
                goodsStockReceptionOrderDraft.setOutPharmacyType(outPharmacyType);
            }
        }
        Map<String, GoodsRedisCache> goodsIdToGoodsRedisCacheMap = ListUtils.toMap(goodsRedisCacheList, GoodsRedisCache::getId);
        GoodsStockReceptionOrder goodsStockReceptionOrder = toGoodsStockReceptionOrder(goodsStockReceptionOrderDraft);
        List<GoodsStockReception> goodsStockReceptions = toGoodsStockReceptionList(ObjectUtils.defaultIfNull(goodsStockReceptionDrafts, Collections.emptyList()));
        for (GoodsStockReception goodsStockReception : goodsStockReceptions) {
            goodsStockReception.setGoodsRedisCache(goodsIdToGoodsRedisCacheMap.get(goodsStockReception.getGoodsId()));
        }
        goodsStockReceptionOrder.setList(goodsStockReceptions);
        List<GoodsStockReception> filterGoodsStockReceptions = goodsStockReceptions.stream().filter(it -> it.getType() == null || it.getType() != GoodsStockOutOrder.OrderType.RECEPTION_IN_OUT_CONFIRM).collect(Collectors.toList());
        //List<Long> batchIds = filterGoodsStockReceptions.stream().filter(it -> CollectionUtils.isNotEmpty(it.getStockInBatchDetailList())).flatMap(it -> it.getStockInBatchDetailList().stream().map(StockInBatchDetail::getBatchId)).distinct().collect(Collectors.toList());
        List<Long> batchIds = filterGoodsStockReceptions.stream().map(GoodsStockReception::getBatchId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, List<GoodsStock>> batchIdToGoodsStocksMap = new HashMap<>();
        Map<Long, List<GoodsStockLocking>> batchIdToGoodsStockLockingListMap = new HashMap<>();
        Map<Long, GoodsBatch> batchIdToGoodsBatchMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(batchIds)) {
            //int pharmacyNo = goodsStockReceptionOrder.getInPharmacyNo() != null ? goodsStockReceptionOrder.getInPharmacyNo() : goodsStockReceptionOrder.getOutPharmacyNo();
            GoodsStatManager.call("Stock_9_Recpt");
            batchIdToGoodsStocksMap.putAll(goodsStockRepository.queryGoodsStockByClinicIdAndBatchIdList(batchIds, goodsStockReceptionOrder.getClinicId(), BigDecimal.ZERO, BigDecimal.ZERO)
                    .stream()/*.filter(it -> it.getPharmacyNo() == pharmacyNo)*/.collect(Collectors.groupingBy(GoodsStock::getBatchId)));
            batchIdToGoodsStockLockingListMap.putAll(goodsStockLockingRepository.findAllByClinicIdAndBatchIdInAndStatusIn(goodsStockReceptionOrder.getClinicId(), batchIds, Arrays.asList(GoodsStockLocking.Status.LOCKING, GoodsStockLocking.Status.LOCKING_USING))
                    .stream()/*.filter(it -> it.getPharmacyNo() == pharmacyNo)*/.collect(Collectors.groupingBy(GoodsStockLocking::getBatchId)));
        }
        batchIdToGoodsStocksMap.forEach((batchId, stockList) -> {
            batchIdToGoodsBatchMap.put(batchId, GoodsBatch.of(stockList, batchIdToGoodsStockLockingListMap.get(batchId)));
        });
        goodsStockReceptionOrder.setBatchIdToGoodsBatchMap(batchIdToGoodsBatchMap);
        goodsStockReceptionOrder.setDraft(true);
        return goodsStockReceptionOrderService.genGoodsStockReceptionOrderView(
                goodsStockReceptionOrder,
                null,
                1

        );
    }

    public GoodsStockReceptionOrderDraft createGoodsStockReceptionOrderDraft(CreateStockReceptionOrderReq clientReq) {
        String headerChainId = clientReq.getHeaderChainId();
        String headerClinicId = clientReq.getHeaderClinicId();
        String headerEmployeeId = clientReq.getHeaderEmployeeId();
        String clientReqClinicId = clientReq.getClinicId();
        String receptionClinicId = StringUtils.isEmpty(clientReqClinicId) ? headerClinicId : clientReqClinicId;
        List<CreateStockReceptionItemViewReq> clientReqList = clientReq.getList();
        List<CreateGoodsStockReception> createGoodsStockReceptions = genCreateGoodsStockReceptions(clientReqList);
        List<String> uniqGoodsIdList = createGoodsStockReceptions.stream().map(CreateGoodsStockReception::getGoodsId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        Instant now = Instant.now();
        GoodsStockReceptionOrderDraft goodsStockReceptionOrderDraft = new GoodsStockReceptionOrderDraft();
        goodsStockReceptionOrderDraft.setIsDeleted(YesOrNo.NO);
        //goodsStockReceptionOrderDraft.setId();
        goodsStockReceptionOrderDraft.setReceptSourceOrderId(clientReq.getReceptSourceOrderId());
        goodsStockReceptionOrderDraft.setChainId(headerChainId);
        goodsStockReceptionOrderDraft.setClinicId(receptionClinicId);
        goodsStockReceptionOrderDraft.setReceiveDepartmentId(clientReq.getReceiveDepartmentId());
        goodsStockReceptionOrderDraft.setOrderNo("");
        goodsStockReceptionOrderDraft.setInConfirmBy(null);
        goodsStockReceptionOrderDraft.setInConfirmed(null);
        goodsStockReceptionOrderDraft.setOutConfirmBy(null);
        goodsStockReceptionOrderDraft.setOutConfirmed(null);
        goodsStockReceptionOrderDraft.setTotalCost(BigDecimal.ZERO);
        goodsStockReceptionOrderDraft.setType(clientReq.getType());
        goodsStockReceptionOrderDraft.addCommentToEnd(headerEmployeeId, clientReq.getComment());
        goodsStockReceptionOrderDraft.setStatus(GoodsStockOutOrder.GoodsStockOutOrderStatus.RECEPTION_OUT_CONFIRM);
        goodsStockReceptionOrderDraft.setReviewed(null);
        goodsStockReceptionOrderDraft.setReviewBy(null);
        goodsStockReceptionOrderDraft.setKindCount((int) Optional.ofNullable(clientReqList).orElseGet(Collections::emptyList).stream().map(CreateStockReceptionItemViewReq::getGoodsId).distinct().count());

        ClinicConfig clinicConfig = ServiceUtils.getClinicConfig(clinicService, receptionClinicId);
        GoodsPharmacyView inPharmacy = clinicConfig.getClinicPharmacyIncDeletedByNo(clientReq.getInPharmacyNo());
        GoodsPharmacyView outPharmacy = clinicConfig.getClinicPharmacyIncDeletedByNo(clientReq.getOutPharmacyNo());
        List<GoodsRedisCache> goodsRedisCacheList = new ArrayList<>();
        goodsRedisCacheList.addAll(goodsRedisUtils.getGoodsRedisCache(uniqGoodsIdList,
                ChoosePharmacyHelper.ofSpecificPharmacy(Optional.ofNullable(inPharmacy).map(GoodsPharmacyView::getType).orElse(GoodsConst.PharmacyType.LOCAL_PHARMACY), Optional.ofNullable(inPharmacy).map(GoodsPharmacyView::getNo).orElse(GoodsUtils.PHARMACY_NO_0)),
                clinicConfig));
        goodsRedisCacheList.addAll(goodsRedisUtils.getGoodsRedisCache(uniqGoodsIdList,
                ChoosePharmacyHelper.ofSpecificPharmacy(Optional.ofNullable(outPharmacy).map(GoodsPharmacyView::getType).orElse(GoodsConst.PharmacyType.LOCAL_PHARMACY), Optional.ofNullable(outPharmacy).map(GoodsPharmacyView::getNo).orElse(GoodsUtils.PHARMACY_NO_0)),
                clinicConfig));
        if (Objects.nonNull(inPharmacy)) {
            Integer inPharmacyNo = inPharmacy.getNo();
            int inPharmacyType = inPharmacy.getType();
            goodsStockReceptionOrderDraft.setInPharmacyNo(inPharmacyNo);
            goodsStockReceptionOrderDraft.setInPharmacyType(inPharmacyType);
        }
        if (Objects.nonNull(outPharmacy)) {
            Integer outPharmacyNo = outPharmacy.getNo();
            int outPharmacyType = outPharmacy.getType();
            goodsStockReceptionOrderDraft.setOutPharmacyNo(outPharmacyNo);
            goodsStockReceptionOrderDraft.setOutPharmacyType(outPharmacyType);
        }
        Map<String, GoodsRedisCache> goodsIdToGoodsRedisCache = ListUtils.toMap(goodsRedisCacheList, GoodsRedisCache::getId);
        goodsStockReceptionOrderDraft.setToUserId(clientReq.getReceiveUserId());
        goodsStockReceptionOrderDraft.setOutUserId(null);
        goodsStockReceptionOrderDraft.setOutDate(null);
        goodsStockReceptionOrderDraft.setCreatedBy(headerEmployeeId);
        goodsStockReceptionOrderDraft.setCreated(now);
        goodsStockReceptionOrderDraft.setLastModifiedBy(headerEmployeeId);
        goodsStockReceptionOrderDraft.setLastModified(now);

        // save
        goodsStockReceptionOrderDraftRepository.saveAndFlush(goodsStockReceptionOrderDraft);
        Long orderDraftId = goodsStockReceptionOrderDraft.getId();
        //goodsStockReceptionOrderDraft.setOutPharmacy();
        //goodsStockReceptionOrderDraft.setInPharmacy();
        List<GoodsStockReceptionDraft> goodsStockReceptionDrafts = createGoodsStockReceptions.stream()
                .map(createReq -> {
                    GoodsStockReceptionDraft goodsStockReceptionDraft = new GoodsStockReceptionDraft();
                    goodsStockReceptionDraft.setIsDeleted(YesOrNo.NO);
                    //goodsStockReceptionDraft.setId();
                    goodsStockReceptionDraft.setReceptSourceId(createReq.getReceptSourceId());
                    //goodsStockReceptionDraft.setReceptSourceReception();
                    goodsStockReceptionDraft.setChainId(headerChainId);
                    goodsStockReceptionDraft.setClinicId(receptionClinicId);
                    goodsStockReceptionDraft.setStatus(GoodsUtils.OrderItemStatus.OK);
                    goodsStockReceptionDraft.setOrderId(orderDraftId);
                    goodsStockReceptionDraft.setGoodsId(createReq.getGoodsId());
                    GoodsRedisCache goods = goodsIdToGoodsRedisCache.get(createReq.getGoodsId());
                    if (Objects.nonNull(goods)) {
                        goodsStockReceptionDraft.setGoodsSnap(GoodsUtils.genGoodsDbSnap(goods));
                        goodsStockReceptionDraft.setPieceNum(goods.getPieceNum());
                        goodsStockReceptionDraft.setPiecePrice(goods.getPiecePrice());
                        goodsStockReceptionDraft.setPackagePrice(goods.getPackagePrice()!= null?goods.getPackagePrice():BigDecimal.ZERO);
                        goodsStockReceptionDraft.setPackageCostPrice(goods.getPackageCostPrice());
                        BigDecimal pieceCount = GoodsStockUtils.toPieceUnit(createReq.getApplicationPieceCount(), createReq.getApplicationPackageCount(), goods.getPieceNum());
                        GoodsStockMountTrible standardStockCount = GoodsStockUtils.toPiecePackageUnit(pieceCount, goods.getPieceNum(), goods.getType(), goods.getSubType());
                        goodsStockReceptionDraft.setPackageCount(standardStockCount.getPackageCount());
                        goodsStockReceptionDraft.setPieceCount(standardStockCount.getPieceCount());
                        goodsStockReceptionDraft.setApplicationPackageCount(standardStockCount.getPackageCount());
                        goodsStockReceptionDraft.setApplicationPieceCount(standardStockCount.getPieceCount());
                    }

                    goodsStockReceptionDraft.setBatchId(createReq.getBatchId());
                    goodsStockReceptionDraft.setOutPharmacyNo(goodsStockReceptionOrderDraft.getOutPharmacyNo());
                    goodsStockReceptionDraft.setOutPharmacyType(goodsStockReceptionOrderDraft.getOutPharmacyType());
                    goodsStockReceptionDraft.setInPharmacyNo(goodsStockReceptionOrderDraft.getInPharmacyNo());
                    goodsStockReceptionDraft.setInPharmacyType(goodsStockReceptionOrderDraft.getInPharmacyType());
                    if (Arrays.asList(GoodsStockOutOrder.OrderType.RECEPTION_OUT, GoodsStockOutOrder.OrderType.RECEPTION_BACK_OUT).contains(clientReq.getType())) {
                        goodsStockReceptionDraft.setOutStockBatchId(Objects.toString(createReq.getBatchId(), null));
                    }
                    if (Arrays.asList(GoodsStockOutOrder.OrderType.RECEPTION_IN, GoodsStockOutOrder.OrderType.RECEPTION_BACK_IN).contains(clientReq.getType())) {
                        goodsStockReceptionDraft.setInStockBatchId(Objects.toString(createReq.getBatchId(), null));
                    }
                    goodsStockReceptionDraft.setLockId(null);
                    goodsStockReceptionDraft.setType(goodsStockReceptionOrderDraft.getType());
                    goodsStockReceptionDraft.setParentId(createReq.getParentId());
                    goodsStockReceptionDraft.setCreatedBy(headerEmployeeId);
                    goodsStockReceptionDraft.setCreated(now);
                    goodsStockReceptionDraft.setLastModifiedBy(headerEmployeeId);
                    goodsStockReceptionDraft.setLastModified(now);
                    //goodsStockReceptionDraft.setStockInBatchDetailList();
                    //goodsStockReceptionDraft.setGoodsRedisCache();
                    //goodsStockReceptionDraft.setStockItemLogField();
                    //goodsStockReceptionDraft.setGoodsStockLogMsgList();
                    //goodsStockReceptionDraft.setReceptionOutBatchGoodsStockList();
                    return goodsStockReceptionDraft;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(goodsStockReceptionDrafts)) {
            goodsStockReceptionDraftRepository.saveAll(goodsStockReceptionDrafts);
            BigDecimal totalPackageCount = BigDecimal.ZERO;
            BigDecimal totalCost = BigDecimal.ZERO;
            BigDecimal excludeTaxTotalCostPrice = BigDecimal.ZERO;
            for (GoodsStockReceptionDraft goodsStockReceptionDraft : goodsStockReceptionDrafts) {
                totalPackageCount = MathUtils.wrapBigDecimalAdd(totalPackageCount, goodsStockReceptionDraft.getTotalPackageCount());
                totalCost = MathUtils.wrapBigDecimalAdd(totalCost, goodsStockReceptionDraft.getTotalCost());
                excludeTaxTotalCostPrice = MathUtils.wrapBigDecimalAdd(excludeTaxTotalCostPrice, goodsStockReceptionDraft.getExcludeTaxTotalCostPrice(clinicConfig.getCostPrecision()));
            }
            goodsStockReceptionOrderDraft.setList(new ArrayList<>(goodsStockReceptionDrafts));
            goodsStockReceptionOrderDraft.setTotalPackageCount(totalPackageCount);
            goodsStockReceptionOrderDraft.setTotalCost(totalCost);
            goodsStockReceptionOrderDraft.setExcludeTaxTotalCostPrice(excludeTaxTotalCostPrice);
            goodsStockReceptionOrderDraftRepository.save(goodsStockReceptionOrderDraft);
        }
        //goodsStockReceptionOrderDraft.setApplyList();
        //goodsStockReceptionOrderDraft.setCommentList();
        //goodsStockReceptionOrderDraft.setLogs();
        //goodsStockReceptionOrderDraft.setReceiveDepartment();
        //goodsStockReceptionOrderDraft.setGoodsStockOrderChangeDetail();
        //goodsStockReceptionOrderDraft.setBatchIdToGoodsBatchMap();
        return goodsStockReceptionOrderDraft;
    }

    public void copyPropertiesGoodsStockReceptionOrderDraft(GoodsStockReceptionOrderDraft goodsStockReceptionOrderDraft, CreateStockReceptionOrderReq clientReq) {
        String headerChainId = clientReq.getHeaderChainId();
        String headerClinicId = clientReq.getHeaderClinicId();
        String headerEmployeeId = clientReq.getHeaderEmployeeId();
        String clientReqClinicId = clientReq.getClinicId();
        String receptionClinicId = StringUtils.isEmpty(clientReqClinicId) ? headerClinicId : clientReqClinicId;
        List<CreateStockReceptionItemViewReq> clientReqList = clientReq.getList();
        List<CreateGoodsStockReception> createGoodsStockReceptions = genCreateGoodsStockReceptions(clientReqList);
        List<String> uniqGoodsIdList = createGoodsStockReceptions.stream().map(CreateGoodsStockReception::getGoodsId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        Instant now = Instant.now();
        //GoodsStockReceptionOrderDraft goodsStockReceptionOrderDraft = new GoodsStockReceptionOrderDraft();
        goodsStockReceptionOrderDraft.setIsDeleted(YesOrNo.NO);
        //goodsStockReceptionOrderDraft.setId();
        goodsStockReceptionOrderDraft.setReceptSourceOrderId(clientReq.getReceptSourceOrderId());
        goodsStockReceptionOrderDraft.setChainId(headerChainId);
        goodsStockReceptionOrderDraft.setClinicId(receptionClinicId);
        goodsStockReceptionOrderDraft.setReceiveDepartmentId(clientReq.getReceiveDepartmentId());
        goodsStockReceptionOrderDraft.setOrderNo("");
        goodsStockReceptionOrderDraft.setInConfirmBy(null);
        goodsStockReceptionOrderDraft.setInConfirmed(null);
        goodsStockReceptionOrderDraft.setOutConfirmBy(null);
        goodsStockReceptionOrderDraft.setOutConfirmed(null);
        goodsStockReceptionOrderDraft.setType(clientReq.getType());
        goodsStockReceptionOrderDraft.addCommentToEnd(headerEmployeeId, clientReq.getComment());
        goodsStockReceptionOrderDraft.setStatus(GoodsStockOutOrder.GoodsStockOutOrderStatus.RECEPTION_OUT_CONFIRM);
        goodsStockReceptionOrderDraft.setReviewed(null);
        goodsStockReceptionOrderDraft.setReviewBy(null);
        goodsStockReceptionOrderDraft.setKindCount((int) Optional.ofNullable(clientReqList).orElseGet(Collections::emptyList).stream().map(CreateStockReceptionItemViewReq::getGoodsId).distinct().count());

        ClinicConfig clinicConfig = ServiceUtils.getClinicConfig(clinicService, receptionClinicId);
        GoodsPharmacyView inPharmacy = clinicConfig.getClinicPharmacyIncDeletedByNo(clientReq.getInPharmacyNo());
        GoodsPharmacyView outPharmacy = clinicConfig.getClinicPharmacyIncDeletedByNo(clientReq.getOutPharmacyNo());
        List<GoodsRedisCache> goodsRedisCacheList = new ArrayList<>();
        goodsRedisCacheList.addAll(goodsRedisUtils.getGoodsRedisCache(uniqGoodsIdList,
                ChoosePharmacyHelper.ofSpecificPharmacy(Optional.ofNullable(inPharmacy).map(GoodsPharmacyView::getType).orElse(GoodsConst.PharmacyType.LOCAL_PHARMACY), Optional.ofNullable(inPharmacy).map(GoodsPharmacyView::getNo).orElse(GoodsUtils.PHARMACY_NO_0)),
                clinicConfig));
        goodsRedisCacheList.addAll(goodsRedisUtils.getGoodsRedisCache(uniqGoodsIdList,
                ChoosePharmacyHelper.ofSpecificPharmacy(Optional.ofNullable(outPharmacy).map(GoodsPharmacyView::getType).orElse(GoodsConst.PharmacyType.LOCAL_PHARMACY), Optional.ofNullable(outPharmacy).map(GoodsPharmacyView::getNo).orElse(GoodsUtils.PHARMACY_NO_0)),
                clinicConfig));
        if (Objects.nonNull(inPharmacy)) {
            Integer inPharmacyNo = inPharmacy.getNo();
            int inPharmacyType = inPharmacy.getType();
            goodsStockReceptionOrderDraft.setInPharmacyNo(inPharmacyNo);
            goodsStockReceptionOrderDraft.setInPharmacyType(inPharmacyType);
        }
        if (Objects.nonNull(outPharmacy)) {
            Integer outPharmacyNo = outPharmacy.getNo();
            int outPharmacyType = outPharmacy.getType();
            goodsStockReceptionOrderDraft.setOutPharmacyNo(outPharmacyNo);
            goodsStockReceptionOrderDraft.setOutPharmacyType(outPharmacyType);
        }
        Map<String, GoodsRedisCache> goodsIdToGoodsRedisCache = ListUtils.toMap(goodsRedisCacheList, GoodsRedisCache::getId);
        goodsStockReceptionOrderDraft.setToUserId(clientReq.getReceiveUserId());
        goodsStockReceptionOrderDraft.setOutUserId(null);
        goodsStockReceptionOrderDraft.setOutDate(null);
        //goodsStockReceptionOrderDraft.setCreatedBy(headerEmployeeId);
        //goodsStockReceptionOrderDraft.setCreated(now);
        goodsStockReceptionOrderDraft.setLastModifiedBy(headerEmployeeId);
        goodsStockReceptionOrderDraft.setLastModified(now);

        // save
        //goodsStockReceptionOrderDraftRepository.saveAndFlush(goodsStockReceptionOrderDraft);
        Long orderDraftId = goodsStockReceptionOrderDraft.getId();
        //goodsStockReceptionOrderDraft.setOutPharmacy();
        //goodsStockReceptionOrderDraft.setInPharmacy();

        List<GoodsStockReceptionDraft> goodsStockReceptionDrafts = goodsStockReceptionDraftRepository.findAllByOrderId(goodsStockReceptionOrderDraft.getId());
        MergeTool<CreateGoodsStockReception, GoodsStockReceptionDraft> mergeTool = new MergeTool<>();
        mergeTool.setSrc(createGoodsStockReceptions);
        mergeTool.setDst(goodsStockReceptionDrafts);
        mergeTool.setIsEqualKeyFunc((createReq, draft) -> Objects.equals(createReq.getUniqueKey(), draft.getUniqueKey()));
        mergeTool.setUpdateFunc((createReq, goodsStockReceptionDraft) -> {
            //GoodsStockReceptionDraft goodsStockReceptionDraft = new GoodsStockReceptionDraft();
            //goodsStockReceptionDraft.setIsDeleted(YesOrNo.NO);
            //goodsStockReceptionDraft.setId();
            goodsStockReceptionDraft.setReceptSourceId(createReq.getReceptSourceId());
            //goodsStockReceptionDraft.setReceptSourceReception();
            goodsStockReceptionDraft.setChainId(headerChainId);
            goodsStockReceptionDraft.setClinicId(receptionClinicId);
            goodsStockReceptionDraft.setStatus(GoodsUtils.OrderItemStatus.OK);
            goodsStockReceptionDraft.setOrderId(orderDraftId);
            goodsStockReceptionDraft.setGoodsId(createReq.getGoodsId());
            GoodsRedisCache goods = goodsIdToGoodsRedisCache.get(createReq.getGoodsId());
            if (Objects.nonNull(goods)) {
                goodsStockReceptionDraft.setGoodsSnap(GoodsUtils.genGoodsDbSnap(goods));
                goodsStockReceptionDraft.setPieceNum(goods.getPieceNum());
                goodsStockReceptionDraft.setPiecePrice(goods.getPiecePrice());
                goodsStockReceptionDraft.setPackagePrice(goods.getPackagePrice()!= null?goods.getPackagePrice():BigDecimal.ZERO);
                goodsStockReceptionDraft.setPackageCostPrice(goods.getPackageCostPrice());
                BigDecimal pieceCount = GoodsStockUtils.toPieceUnit(createReq.getApplicationPieceCount(), createReq.getApplicationPackageCount(), goods.getPieceNum());
                GoodsStockMountTrible standardStockCount = GoodsStockUtils.toPiecePackageUnit(pieceCount, goods.getPieceNum(), goods.getType(), goods.getSubType());
                goodsStockReceptionDraft.setPackageCount(standardStockCount.getPackageCount());
                goodsStockReceptionDraft.setPieceCount(standardStockCount.getPieceCount());
                goodsStockReceptionDraft.setApplicationPackageCount(standardStockCount.getPackageCount());
                goodsStockReceptionDraft.setApplicationPieceCount(standardStockCount.getPieceCount());
            }

            goodsStockReceptionDraft.setBatchId(createReq.getBatchId());
            goodsStockReceptionDraft.setOutPharmacyNo(goodsStockReceptionOrderDraft.getOutPharmacyNo());
            goodsStockReceptionDraft.setOutPharmacyType(goodsStockReceptionOrderDraft.getOutPharmacyType());
            goodsStockReceptionDraft.setInPharmacyNo(goodsStockReceptionOrderDraft.getInPharmacyNo());
            goodsStockReceptionDraft.setInPharmacyType(goodsStockReceptionOrderDraft.getInPharmacyType());
            if (Arrays.asList(GoodsStockOutOrder.OrderType.RECEPTION_OUT, GoodsStockOutOrder.OrderType.RECEPTION_BACK_OUT).contains(clientReq.getType())) {
                goodsStockReceptionDraft.setOutStockBatchId(Objects.toString(createReq.getBatchId(), null));
            }
            if (Arrays.asList(GoodsStockOutOrder.OrderType.RECEPTION_IN, GoodsStockOutOrder.OrderType.RECEPTION_BACK_IN).contains(clientReq.getType())) {
                goodsStockReceptionDraft.setInStockBatchId(Objects.toString(createReq.getBatchId(), null));
            }
            goodsStockReceptionDraft.setLockId(null);
            goodsStockReceptionDraft.setType(goodsStockReceptionOrderDraft.getType());
            goodsStockReceptionDraft.setParentId(createReq.getParentId());
            //goodsStockReceptionDraft.setCreatedBy(headerEmployeeId);
            //goodsStockReceptionDraft.setCreated(now);
            goodsStockReceptionDraft.setLastModifiedBy(headerEmployeeId);
            goodsStockReceptionDraft.setLastModified(now);
            //goodsStockReceptionDraft.setStockInBatchDetailList();
            //goodsStockReceptionDraft.setGoodsRedisCache();
            //goodsStockReceptionDraft.setStockItemLogField();
            //goodsStockReceptionDraft.setGoodsStockLogMsgList();
            //goodsStockReceptionDraft.setReceptionOutBatchGoodsStockList();
        });
        mergeTool.setInsertFunc(createReq -> {
            GoodsStockReceptionDraft goodsStockReceptionDraft = new GoodsStockReceptionDraft();
            goodsStockReceptionDraft.setIsDeleted(YesOrNo.NO);
            //goodsStockReceptionDraft.setId();
            goodsStockReceptionDraft.setReceptSourceId(createReq.getReceptSourceId());
            //goodsStockReceptionDraft.setReceptSourceReception();
            goodsStockReceptionDraft.setChainId(headerChainId);
            goodsStockReceptionDraft.setClinicId(receptionClinicId);
            goodsStockReceptionDraft.setStatus(GoodsUtils.OrderItemStatus.OK);
            goodsStockReceptionDraft.setOrderId(orderDraftId);
            goodsStockReceptionDraft.setGoodsId(createReq.getGoodsId());
            GoodsRedisCache goods = goodsIdToGoodsRedisCache.get(createReq.getGoodsId());
            if (Objects.nonNull(goods)) {
                goodsStockReceptionDraft.setGoodsSnap(GoodsUtils.genGoodsDbSnap(goods));
                goodsStockReceptionDraft.setPieceNum(goods.getPieceNum());
                goodsStockReceptionDraft.setPiecePrice(goods.getPiecePrice());
                goodsStockReceptionDraft.setPackagePrice(goods.getPackagePrice()!= null?goods.getPackagePrice():BigDecimal.ZERO);
                goodsStockReceptionDraft.setPackageCostPrice(goods.getPackageCostPrice());
                BigDecimal pieceCount = GoodsStockUtils.toPieceUnit(createReq.getApplicationPieceCount(), createReq.getApplicationPackageCount(), goods.getPieceNum());
                GoodsStockMountTrible standardStockCount = GoodsStockUtils.toPiecePackageUnit(pieceCount, goods.getPieceNum(), goods.getType(), goods.getSubType());
                goodsStockReceptionDraft.setPackageCount(standardStockCount.getPackageCount());
                goodsStockReceptionDraft.setPieceCount(standardStockCount.getPieceCount());
                goodsStockReceptionDraft.setApplicationPackageCount(standardStockCount.getPackageCount());
                goodsStockReceptionDraft.setApplicationPieceCount(standardStockCount.getPieceCount());
            }

            goodsStockReceptionDraft.setBatchId(createReq.getBatchId());
            goodsStockReceptionDraft.setOutPharmacyNo(goodsStockReceptionOrderDraft.getOutPharmacyNo());
            goodsStockReceptionDraft.setOutPharmacyType(goodsStockReceptionOrderDraft.getOutPharmacyType());
            goodsStockReceptionDraft.setInPharmacyNo(goodsStockReceptionOrderDraft.getInPharmacyNo());
            goodsStockReceptionDraft.setInPharmacyType(goodsStockReceptionOrderDraft.getInPharmacyType());
            if (Arrays.asList(GoodsStockOutOrder.OrderType.RECEPTION_OUT, GoodsStockOutOrder.OrderType.RECEPTION_BACK_OUT).contains(clientReq.getType())) {
                goodsStockReceptionDraft.setOutStockBatchId(Objects.toString(createReq.getBatchId(), null));
            }
            if (Arrays.asList(GoodsStockOutOrder.OrderType.RECEPTION_IN, GoodsStockOutOrder.OrderType.RECEPTION_BACK_IN).contains(clientReq.getType())) {
                goodsStockReceptionDraft.setInStockBatchId(Objects.toString(createReq.getBatchId(), null));
            }
            goodsStockReceptionDraft.setInStockBatchId(null);
            goodsStockReceptionDraft.setLockId(null);
            goodsStockReceptionDraft.setType(goodsStockReceptionOrderDraft.getType());
            goodsStockReceptionDraft.setParentId(createReq.getParentId());
            goodsStockReceptionDraft.setCreatedBy(headerEmployeeId);
            goodsStockReceptionDraft.setCreated(now);
            goodsStockReceptionDraft.setLastModifiedBy(headerEmployeeId);
            goodsStockReceptionDraft.setLastModified(now);
            //goodsStockReceptionDraft.setStockInBatchDetailList();
            //goodsStockReceptionDraft.setGoodsRedisCache();
            //goodsStockReceptionDraft.setStockItemLogField();
            //goodsStockReceptionDraft.setGoodsStockLogMsgList();
            //goodsStockReceptionDraft.setReceptionOutBatchGoodsStockList();
            return goodsStockReceptionDraft;
        });
        mergeTool.setDeleteFunc(goodsStockReceptionDraft -> {
            goodsStockReceptionDraft.setIsDeleted(YesOrNo.YES);
            goodsStockReceptionDraft.setLastModifiedBy(headerEmployeeId);
            goodsStockReceptionDraft.setLastModified(now);
            return true;
        });
        mergeTool.doMerge();
        goodsStockReceptionDraftRepository.saveAll(goodsStockReceptionDrafts);
        BigDecimal totalPackageCount = BigDecimal.ZERO;
        BigDecimal includeTaxTotalCostPrice = BigDecimal.ZERO;
        BigDecimal excludeTaxTotalCostPrice = BigDecimal.ZERO;
        for (GoodsStockReceptionDraft goodsStockReceptionDraft : goodsStockReceptionDrafts) {
            totalPackageCount = MathUtils.wrapBigDecimalAdd(totalPackageCount, goodsStockReceptionDraft.getTotalPackageCount());
            includeTaxTotalCostPrice = MathUtils.wrapBigDecimalAdd(includeTaxTotalCostPrice, goodsStockReceptionDraft.getTotalCost());
            excludeTaxTotalCostPrice = MathUtils.wrapBigDecimalAdd(excludeTaxTotalCostPrice, goodsStockReceptionDraft.getExcludeTaxTotalCostPrice(clinicConfig.getCostPrecision()));
        }
        goodsStockReceptionOrderDraft.setList(new ArrayList<>(goodsStockReceptionDrafts));
        goodsStockReceptionOrderDraft.setTotalPackageCount(totalPackageCount);
        goodsStockReceptionOrderDraft.setTotalCost(includeTaxTotalCostPrice);
        goodsStockReceptionOrderDraft.setExcludeTaxTotalCostPrice(excludeTaxTotalCostPrice);
        goodsStockReceptionOrderDraftRepository.save(goodsStockReceptionOrderDraft);
        //goodsStockReceptionOrderDraft.setApplyList();
        //goodsStockReceptionOrderDraft.setCommentList();
        //goodsStockReceptionOrderDraft.setLogs();
        //goodsStockReceptionOrderDraft.setReceiveDepartment();
        //goodsStockReceptionOrderDraft.setGoodsStockOrderChangeDetail();
        //goodsStockReceptionOrderDraft.setBatchIdToGoodsBatchMap();
    }

    private List<CreateGoodsStockReception> genCreateGoodsStockReceptions(List<CreateStockReceptionItemViewReq> clientReqList) {
        return Optional.ofNullable(clientReqList).orElseGet(Collections::emptyList).stream()
                .flatMap(itemReq -> {
                    String goodsId = itemReq.getGoodsId();
                    return Optional.ofNullable(itemReq.getBatches()).orElseGet(Collections::emptyList).stream()
                            .map(batchReq -> {
                                CreateGoodsStockReception createReq = new CreateGoodsStockReception();
                                createReq.setId(batchReq.getId());
                                createReq.setReceptSourceId(batchReq.getReceptSourceId());
                                //createReq.setReceptSourceReception();
                                createReq.setParentId(batchReq.getParentId());
                                createReq.setGoodsId(goodsId);
                                createReq.setBatchId(batchReq.getBatchId());
                                createReq.setApplicationPieceCount(batchReq.getApplicationPieceCount());
                                createReq.setApplicationPackageCount(batchReq.getApplicationPackageCount());
                                createReq.setPieceCount(batchReq.getPieceCount());
                                createReq.setPackageCount(batchReq.getPackageCount());
                                createReq.setIndex(batchReq.getIndex());
                                return createReq;
                            });
                }).collect(Collectors.toMap(CreateGoodsStockReception::getUniqueKey, Function.identity(), (a, b) -> a))
                .values()
                .stream()
                .sorted(Comparator.comparing(CreateGoodsStockReception::getIndex))
                .collect(Collectors.toList());
    }
}
