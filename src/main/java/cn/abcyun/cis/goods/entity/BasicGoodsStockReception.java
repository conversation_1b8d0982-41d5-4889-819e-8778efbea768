package cn.abcyun.cis.goods.entity;

import cn.abcyun.bis.rpc.sdk.cis.model.message.goodslog.GoodsStockLogMsg;
import cn.abcyun.cis.goods.cache.redis.GoodsRedisCache;
import cn.abcyun.cis.goods.model.*;
import cn.abcyun.cis.goods.service.hisversion.GoodsHisVersionManager;
import cn.abcyun.cis.goods.utils.GoodsStockUtils;
import cn.abcyun.cis.goods.utils.GoodsUtils;
import cn.abcyun.cis.goods.vo.frontend.stockin.StockItemLogField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 领用申请单表
 * 为了复用老的领用出库
 */
@Data
@Accessors(chain = true)
@MappedSuperclass
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class BasicGoodsStockReception {
    public static final Logger sLogger = LoggerFactory.getLogger(BasicGoodsStockReception.class);
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    //新加
    private Long receptSourceId;                   // 领用退回出库/领用退回入库 指定要退回的领用单ID，需要原领处方确认

    @Transient
    private BasicGoodsStockReception receptSourceReception;
    private String chainId;                        // 连锁id
    private String clinicId;                       // 领用门店ID
    private int status;                            // 1正常；99删除
    private Long orderId;                          // 调拨单id
    private String goodsId;                        // 商品id
    @Type(type = "json")
    @Column(name = "goods", columnDefinition = "json")
    private GoodsSnapV3 goodsSnap;
    private Long batchId;                       // 如果指定批次领用，
    private int pieceNum;                         // 制剂数量
    private BigDecimal pieceCount = BigDecimal.ZERO;                 // 实际领用 制剂调拨量 调入调出都是+
    private BigDecimal piecePrice = BigDecimal.ZERO;
    private BigDecimal packageCount = BigDecimal.ZERO;               // 实际领用 整包装调拨量制剂调拨量 调入调出都是+
    private BigDecimal applicationPieceCount = BigDecimal.ZERO;      // 申请小单位数量
    private BigDecimal applicationPackageCount = BigDecimal.ZERO;    // 申请大单位数量
    private BigDecimal packagePrice = BigDecimal.ZERO;
    private BigDecimal packageCostPrice;           // 整包平均成本价
    private BigDecimal totalCost = BigDecimal.ZERO;
    @Column(name = "pharmacy_no")
    private Integer outPharmacyNo;                     // 调拨源[虚拟库存多药房]所属药房的ID
    @Column(name = "pharmacy_type")
    private Integer outPharmacyType;                   // 调拨源[冗余一下]药房类型 0 实体药房 1 空中药房 2 虚拟药房
    private Integer inPharmacyNo;                      // 调拨到[虚拟库存多药房]所属药房的ID
    private Integer inPharmacyType;                    // 调拨到[冗余一下]药房类型 0 实体药房 1 空中药房 2 虚拟药房

    private String outStockBatchId = "";                // 领出方进销存batchId
    private String inStockBatchId = "";                 // 领 入方进销存batchId
    private Long lockId;                           // 出库方锁库ID
    private Integer type;
    private Long parentId; // 原itemId，由哪个itemId指定的批次


    @JsonIgnore
    @Column(name = "created_user_id")
    private String createdBy = "";
    @JsonIgnore
    @Column(name = "created_date")
    private Instant created;
    @JsonIgnore
    @Column(name = "last_modified_user_id")
    private String lastModifiedBy = "";
    @JsonIgnore
    @Column(name = "last_modified_date")
    private Instant lastModified;

    /**
     *
     * 领用的详细信息，方便算退货出库数量
     * */
    @Type(type = "json")
    @Column(name = "stock_in_batch_detail_list", columnDefinition = "json")
    private List<StockInBatchDetail> stockInBatchDetailList;

    /**
     * 已退
     * */
    @JsonIgnore
    public BigDecimal getTotalReturnYetPieceCount() {
        if (CollectionUtils.isEmpty(stockInBatchDetailList)) {
            return BigDecimal.ZERO;
        }
        if(type != null && type == GoodsStockOutOrder.OrderType.RECEPTION_OUT && inPharmacyNo == null){
            return stockInBatchDetailList.stream().filter(it -> it.getTotalPieceCount() != null && it.getTotalPieceCount().compareTo(BigDecimal.ZERO) > 0)
                    .map(StockInBatchDetail::getTotalPieceCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return stockInBatchDetailList.stream().filter(it -> it.getTotalPieceCount() != null && it.getTotalPieceCount().compareTo(BigDecimal.ZERO) < 0)
                .map(StockInBatchDetail::getTotalPieceCount).reduce(BigDecimal.ZERO, BigDecimal::add).abs();
    }

    @JsonIgnore
    public Set<Long> getBatchIdList() {
        if (CollectionUtils.isEmpty(stockInBatchDetailList)) {
            return null;
        }
        return stockInBatchDetailList.stream().map(StockInBatchDetail::getBatchId).collect(Collectors.toSet());
    }

    @JsonIgnore
    public BigDecimal getBatchCanReturnPieceCount(Long batchId) {
        if (CollectionUtils.isEmpty(stockInBatchDetailList)) {
            return BigDecimal.ZERO;
        }
        //全部加起来就是当前可以退的数量
        return stockInBatchDetailList.stream().filter(it -> it.getBatchId().compareTo(batchId) == 0  && it.getTotalPieceCount() != null)
                .map(StockInBatchDetail::getTotalPieceCount).reduce(BigDecimal.ZERO, BigDecimal::add).abs();

    }
    /**
     * 可退
     * */
    @JsonIgnore
    public BigDecimal getTotalCanReturnPieceCount(Map<Long, GoodsBatch> batchIdToGoodsBatch) {
        if (CollectionUtils.isEmpty(stockInBatchDetailList)) {
            return BigDecimal.ZERO;
        }
        Map<Long ,BigDecimal> batchIdToCanReturnPieceCount = stockInBatchDetailList
                .stream()
                .collect(Collectors.groupingBy(
                        StockInBatchDetail::getBatchId, //按批次分组
                        Collectors.reducing(BigDecimal.ZERO, StockInBatchDetail::getTotalPieceCount, BigDecimal::add)));
        final BigDecimal[] totalCanReturnPieceCount = {BigDecimal.ZERO};
        batchIdToCanReturnPieceCount.forEach((batchId,canReturnPieceCount)->{
            //没有领入 只看出了多少，还剩余多少
            if(inPharmacyNo == null){
                totalCanReturnPieceCount[0] = totalCanReturnPieceCount[0].add(canReturnPieceCount);
                return;
            }
            GoodsBatch goodsBatch = batchIdToGoodsBatch.get(batchId);
            if (goodsBatch == null) {
                return;
            }
            totalCanReturnPieceCount[0] = totalCanReturnPieceCount[0].add(canReturnPieceCount.min(goodsBatch.getTotalStockPieceCount(null)));
        });
        return totalCanReturnPieceCount[0].abs();
    }

    /**
     * 添加领用详细批次信息
     */
    public void addStockInBatchDetail(StockInBatchDetail stockInBatchDetail) {
        if (stockInBatchDetailList == null) {
            stockInBatchDetailList = Lists.newArrayList();
        }
        stockInBatchDetailList.add(stockInBatchDetail);
    }
    @Transient
    private GoodsRedisCache goodsRedisCache;
    /**
     * 通过这个成员变量来控制，是否需要写字段的变更log。
     * 需要写变更log的字段需要复写set方法
     */
    @JsonIgnore
    @Transient
    private StockItemLogField stockItemLogField = null;

    public BigDecimal getPackageCostPrice() {
        if (packageCostPrice != null) {
            return packageCostPrice;
        }

        if (goodsRedisCache != null) {
            return goodsRedisCache.getPackageCostPrice();
        }
        return null;
    }

    /**
     * 初始化记录是否需要记录变更
     */
    public void logNeedRecordOrderChange(GoodsSnapV3 goodsSnap, String action, String mainKey) {
        if (stockItemLogField == null) {
            this.stockItemLogField = new StockItemLogField();
            stockItemLogField.setGoods(goodsSnap);
            stockItemLogField.setAction(action);
            stockItemLogField.setMainKey(mainKey);
        }
    }

    /**
     * 如果没有字段变化 返回null
     * 只能再最后使用的地方调用
     */
    public StockItemLogField getStockItemLogField() {
        if (stockItemLogField == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(stockItemLogField.getFields())) {
            return null;
        }
        return stockItemLogField;
    }

    public void logAddField() {
        if (stockItemLogField != null) {
            stockItemLogField.getFields().clear();
            stockItemLogField.addChangedField("packageCount", GoodsUtils.nullToZero(packageCount), null);
            stockItemLogField.addChangedField("pieceCount", GoodsUtils.nullToZero(pieceCount), null);
            stockItemLogField.setOriginCount(packageCount, pieceCount);
        }
    }

    public void logDeleteField() {
        if (stockItemLogField != null) {
            stockItemLogField.getFields().clear();
            stockItemLogField.addChangedField("packageCount", GoodsUtils.nullToZero(packageCount), null);
            stockItemLogField.addChangedField("pieceCount", GoodsUtils.nullToZero(pieceCount), null);
            stockItemLogField.setOriginCount(packageCount, pieceCount);
        }
    }

    @JsonIgnore
    public BigDecimal getTotalPackageCount() {
        return GoodsStockUtils.toBigChangePackageUnit_scale10(pieceCount, packageCount, getPieceNum());
    }

    @JsonIgnore
    public BigDecimal getTotalPieceCount() {
        return GoodsStockUtils.toPieceUnit(pieceCount, packageCount, getPieceNum());
    }

    @JsonIgnore
    public BigDecimal getTotalApplicationPieceCount() {
        return GoodsStockUtils.toPieceUnit(pieceCount, packageCount, getPieceNum());
    }


    /**
     * 工具函数，获取不含税的总的成本(入库时的)
     */
    @JsonIgnore
    public BigDecimal getExcludeTaxTotalCostPrice(int costPricePrecision) {
        BigDecimal inTaxRat = goodsSnap != null && goodsSnap.getInTaxRat() != null ? goodsSnap.getInTaxRat() : BigDecimal.ZERO;
        return GoodsStockUtils.totalCostExcludeTaxRat(totalCost, inTaxRat);
    }

    /**
     * 调拨 入库方的进销存
     * 用于发openApi消息出去
     * 以后订单要发送进销存详细批次都可以走这个结构通知出去
     */
    @Transient
    @JsonIgnore
    private List<GoodsStockLogMsg> goodsStockLogMsgList;

    /**
     * 如果指定批次调拨，是指定调拨的GoodsStock，否则为null
     */
    @Transient
    @JsonIgnore
    private List<GoodsStock> receptionOutBatchGoodsStockList;

    @JsonIgnore
    public GoodsSnapV3 getAssembleGoodsSnap(GoodsHisVersionManager hisManager) {
        /**
         * 要注意 goodsSnap 实在找不到对应的版本 ，会被修改装载成 当前的完整的goodsSnap
         * https://www.tapd.cn/43780818/bugtrace/bugs/view?bug_id=1143780818001061301
         * */
        if(goodsSnap!= null && goodsSnap.getGoodsVersion() == null){
            return goodsSnap;//最老历史数据兼容
        }

        GoodsSnapV3 copySnap = GoodsUtils.deepCopyDbGoodsSnap(goodsSnap, goodsId, hisManager.getClinicConfig());
        return hisManager.getGoodsHistoryVersion(copySnap, goodsId);
    }

    @JsonIgnore
    public String getUniqueKey() {
        return goodsId + "_" + batchId;
    }
}