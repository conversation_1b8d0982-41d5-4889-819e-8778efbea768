package cn.abcyun.cis.goods.mapper;

import cn.abcyun.cis.goods.dto.list.PriceOrderDtoCount;
import cn.abcyun.cis.goods.model.GoodsModifyPriceOrder;
import cn.abcyun.cis.goods.vo.frontend.goodsinfo.price.GetModifyPriceListReq;
import org.apache.ibatis.annotations.Mapper;

import java.time.Instant;
import java.util.List;

@Mapper
public interface GoodsPriceMapper {

    /**
     * 通过trimmedCadn 找中药goods信息
     */
    List<GoodsModifyPriceOrder> getModifyPriceOrderList(GetModifyPriceListReq req);

    PriceOrderDtoCount countModifyPriceOrderList(GetModifyPriceListReq req);

    /**
     * 查找待生效改价订单
     */
    List<Long> getSchedulexModifyPriceOrderListList(Instant dateNow);
}
