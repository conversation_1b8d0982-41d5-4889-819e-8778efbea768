package cn.abcyun.cis.goods.mapper;

import cn.abcyun.cis.goods.model.GoodsShortIdGapGroupInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface GoodsShortIdMapper {

    /**
     * 查询shortId gap group信息
     * 商品编码和行信息间隔分组信息
     * eg:
     * gen_id   row_number   gap(gen_id - row_number)
     * 12        12                0
     * 13        13                0
     * 15        14                1
     * 16        15                1
     * 18        16                2
     * 19        17                2
     * 会被分成3组，0~[12,13], 1~[15,16], 2~[18,19]
     */
    List<GoodsShortIdGapGroupInfo> findShortIdGapGroupInfo(@Param("clinicId") String clinicId,
                                                           @Param("shortIdType") int shortIdType,
                                                           @Param("startShortId") BigDecimal startShortId);
}
