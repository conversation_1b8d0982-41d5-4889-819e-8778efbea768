package cn.abcyun.cis.goods.amqp;

import cn.abcyun.bis.rpc.sdk.cis.model.message.goodslog.GoodsTraceCodeSupervisionOrderMsg;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@Slf4j
public class SupervisionLogMQProducer {

    @Autowired
    private RocketMQTemplate rocketMqTemplate;

    /**
     * 消息topic
     */
    @Value("${rocketmq.topic.cis-goods.name}")
    private String cisGoodsTopicName;


    /**
     * RocketMq消息
     */
    @Value("${rocketmq.tag.cis-goods.trace-code-supervision-log}")
    private String traceCodeSupervisionLogTag;//Goods新建或修改的消息



    private String getDestination(String topic, String tag) {
        if (StringUtils.isEmpty(topic)) {
            throw new ParamNotValidException("topic is null");
        }
        if (StringUtils.isEmpty(tag)) {
            return topic;
        }
        return String.format("%s:%s", topic, tag);
    }
}
