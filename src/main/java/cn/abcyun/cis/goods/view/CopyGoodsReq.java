package cn.abcyun.cis.goods.view;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 复制goods要求
 *
 * <AUTHOR>
 * @date 2023-10-26 16:47
 */
@Data
public class CopyGoodsReq {

    @Valid
    @NotEmpty(message = "items不能为空")
    private List<CopyGoodsItemReq> items;

    @Data
    public static class CopyGoodsItemReq {

        @NotBlank(message = "goodsId不能为空")
        private String goodsId;

        @NotNull(message = "goodsType不能为空")
        private Integer goodsType;

        @NotNull(message = "goodsSubType不能为空")
        private Integer goodsSubType;
    }
}
