package cn.abcyun.cis.goods.model.b2b;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.Instant;

/**
 * 仁和多采购车
 * https://www.tapd.cn/22044681/prong/stories/view/1122044681001002819?url_cache_key=c1a7057ea78b29cea39fd07b4b40b7c5&action_entry_type=stories
 * */
@Entity
@Table(name = "v2_goods_mall_purchase_plan_list")
@Data
public class GoodsPurchaseCarList {
    @Id
    private Long id;

    private String chainId;// '总店id
    private String clinicId;//子店id
    /**
     * 采购计划的流量
     * */
    @Length(max=32)
    private String name;//'采购的诊所商品id
    /**
     * 采购计划的编号，从0开始增加
     * */
    private int purchasePlanIdx;

    private short isDeleted  = 0;//'标记删除',
    private Instant created;//                  timestamp   default CURRENT_TIMESTAMP not null comment '加入购物计划时间',
    private String createdBy;//               varchar(32) default ''                not null comment '添加购物车的员工id',
    private String lastModifiedBy;//        varchar(32) default ''                not null comment '最后修改员工id',
    private Instant lastModified;//           timestamp   default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '最后修改时间',

}
