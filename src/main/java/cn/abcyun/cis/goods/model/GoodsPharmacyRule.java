package cn.abcyun.cis.goods.model;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.cis.goods.utils.AbcIdUtils;
import cn.abcyun.cis.goods.utils.UserFillUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-05-09 15:21:13
 */
@Data
@Entity
@Table(name = "v2_goods_pharmacy_rule")
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class GoodsPharmacyRule {
    /**
     * 实例化一条内置的下达规则
     */
    public static GoodsPharmacyRule ofInner(String ChainId,
                                            String clinicId,
                                            int sceneType,
                                            int isAllDept,
                                            int isAllWardArea,
                                            int isAllProcess,
                                            int sort,
                                            List<Integer> typeIdList
    ) {
        GoodsPharmacyRule rule = new GoodsPharmacyRule();
        rule.setId(AbcIdUtils.getUUIDLong());
        rule.setChainId(ChainId);
        rule.setClinicId(clinicId);
        rule.setInnerFlag(YesOrNo.YES);
        rule.setGoodsType(new GoodsType());
        for (Integer typeId : typeIdList) {
            rule.getGoodsType().getTypeIdList().add(new GoodsTypeIdItem(typeId));
        }
        if (isAllDept == YesOrNo.YES) {
            rule.setDepartment(new ArrayList<>());
        }
        if (isAllWardArea == YesOrNo.YES) {
            rule.setWardArea(new ArrayList<>());
        }
        rule.setSceneType(sceneType);
        if (isAllProcess == YesOrNo.YES) {
            rule.setRuleProcessInfo(new RuleProcessInfo());
        }
        rule.setSort(sort);
        rule.setStatus(1);
        UserFillUtils.fillCreatedBy(rule, UserFillUtils.DEFUALT_ID);
        return rule;
    }

    @Id
    private Long id;
    private String chainId;
    private String clinicId;
    /**
     * 默认规则
     */
    private int innerFlag;

    @Type(type = "json")
    @Column(columnDefinition = "json")
    private GoodsType goodsType;

    /**
     * 加工方式
     */
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private RuleProcessInfo ruleProcessInfo = null;

    /**
     * 科室
     */
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private List<String> department;

    /**
     * 病区
     */
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private List<String> wardArea;

    /**
     * 就诊类型 位运算,1=门诊 2=住院 3=全部
     */
    private int sceneType;
    private int pharmacyType;
    private int pharmacyNo;
    /**
     * 状态 1=启用 0=停用
     */
    private int status;
    /**
     * 排序 降序列
     */
    private int sort;

    private int isDeleted;
    private String createdBy;
    private Instant created;
    private String lastModifiedBy;
    private Instant lastModified;

    @Data
    public static class GoodsType {
        @ApiModelProperty("是否全部, 0=否 1=是")
        private int allFlag;
        private List<GoodsTypeIdItem> typeIdList = new ArrayList<>();
    }

    /**
     * 加工方式
     */
    @Data
    public static class RuleProcessInfo {
        @ApiModelProperty("加工方式")
        private List<ProcessInfo> processInfos = null;
    }

    @Data
    public static class ProcessInfo {
        private int id;
        @ApiModelProperty("加工方式")
        private int type;
        @ApiModelProperty("加工方式 如果为null表示父类(charge 那边 父类用 0和子类的0 区分不开)")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer subType;
        /**
         * 这个字段不写入DB，只是协议层输出
         */
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @ApiModelProperty("加工方式名字")
        private String name;
        /**
         * children
         */
        @ApiModelProperty("1 null表示无孩子， 用type和subType进行匹配" +
                "2[]表示大类被全选了，用type进行匹配" +
                "3非空具体的列表,用列表里面的type和subType进行匹配 ")
        private List<ProcessInfo> children;
    }

    @Data
    public static class GoodsTypeIdItem {
        public GoodsTypeIdItem() {
        }

        public GoodsTypeIdItem(int typeId) {
            this.typeId = typeId;
        }

        private int typeId;
        //未选二级分类如何表示 -1TypeId
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private List<Long> customTypeIdList;

    }

    public static final int SCENE_TYPE_OUTPATIENT = 0x1;
    public static final int SCENE_TYPE_HOSPITAL = 0x02;

}