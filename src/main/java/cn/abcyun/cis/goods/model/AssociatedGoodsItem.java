package cn.abcyun.cis.goods.model;

import cn.abcyun.cis.goods.cache.redis.GoodsRedisCache;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 关联项目
 * 存入到db里面的结构
 *
 * <AUTHOR>
 * @Date 2021/11/8 4:38 下午
 */
@Data
public class AssociatedGoodsItem {
    /**
     * 使用规则类型
     */
    private int usageRuleType;
    /**
     * 商品id
     */
    private String goodsId;

    /**
     * goodsId对应的GoodsRedis信息
     * */
    @JsonIgnore
    private GoodsRedisCache goodsRedisCache;
    /**
     * 使用单位
     */
    private String unit;
    /**
     * 使用量
     */
    private BigDecimal unitCount;
    /**
     * 使用方法 每组每天 每组每次
     */
    private String freq;
    /**
     * 区分不同组
     */
    private String groupId;
    /**
     * 组内 顺序
     */
    private String groupNo;
    /**
     * 规则顺序
     */
    private int sort;
    /**
     * 是否选中
     */
    private int isUsed;
    /**
     * 是否使用拆零
     */
    private int useDismounting;

    public static class UsageRuleType {
        /**
         * 按天
         */
        public static final int DAYS = 0;
        /**
         * 按组
         */
        public static final int GROUP_FIXED = 1;
        /**
         * 阶梯按组（区分首组和加组）
         */
        public static final int GROUP_STEP = 2;
        /**
         * 只有1次
         */
        public static final int ONLY_ONCE = 3;
        /**
         * 按次
         */
        public static final int TIMES = 4;
    }
}
