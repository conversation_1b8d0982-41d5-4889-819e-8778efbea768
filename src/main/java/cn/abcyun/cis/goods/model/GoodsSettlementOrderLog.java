package cn.abcyun.cis.goods.model;

import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import org.hibernate.annotations.TypeDef;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import java.time.Instant;

@Entity
@Table(name = "v2_goods_settlement_log")
@Data
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class GoodsSettlementOrderLog {

    /**
     * 入库单的日志action常量
     */
    public static final String ACTION_CREATE = "创建";
    public static final String ACTION_REVIEW_PASS = "审核通过";
    public static final String ACTION_REVIEW_PASS_PHARMACY = "审批完成";
    public static final String ACTION_REVIEW_REJECT = "审核不通过";
    public static final String ACTION_REVIEW_REJECT_PHARMACY = "审批驳回";
    public static final String ACTION_WITHDRAW = "撤销";
    public static final String ACTION_UPDATE = "修改";
    public static final String ACTION_DELETE = "删除";

    public String displayAction(String rejectReason) {
        if (StringUtils.isEmpty(action)) {
            return "";
        }
        if (action.compareTo(ACTION_CREATE) == 0) {
            return "创建结算单";
        }
        if (action.compareTo(ACTION_REVIEW_PASS) == 0) {
            return "审核结算单 审核通过";
        }
        if (action.compareTo(ACTION_REVIEW_REJECT) == 0) {
            if (!StringUtils.isEmpty(rejectReason)) {
                return "审核结算单 审核不通过 原因:" + rejectReason;
            } else {
                return "审核结算单 审核不通过";
            }
        }
        if (action.compareTo(ACTION_WITHDRAW) == 0) {
            return "撤销结算单";
        }
        if (action.compareTo(ACTION_UPDATE) == 0) {
            return "修改结算单";
        }
        if (action.compareTo(ACTION_REVIEW_PASS_PHARMACY) == 0) {
            return "审批完成";
        }
        if (action.compareTo(ACTION_REVIEW_REJECT_PHARMACY) == 0) {
            if (!StringUtils.isEmpty(rejectReason)) {
                return "审批驳回 原因:" + rejectReason;
            } else {
                return "审批驳回";
            }
        }
        if (action.compareTo(ACTION_DELETE) == 0) {
            return "删除草稿";
        }
        return "";
    }


    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 可读盘点单号
     */
    private Long orderId;

    private String action;

    @Column(name = "created_user_id")
    private String createdBy = "";

    @Column(name = "created_date")
    private Instant created;

}
