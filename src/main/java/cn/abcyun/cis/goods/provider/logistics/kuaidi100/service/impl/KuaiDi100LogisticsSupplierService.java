package cn.abcyun.cis.goods.provider.logistics.kuaidi100.service.impl;

import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.core.util.SpringUtils;
import cn.abcyun.cis.goods.consts.Logistics;
import cn.abcyun.cis.goods.entity.GoodsStockOrderLogistics;
import cn.abcyun.cis.goods.provider.logistics.kuaidi100.common.KuaiDi100Const;
import cn.abcyun.cis.goods.provider.logistics.kuaidi100.conf.KuaiDi100Config;
import cn.abcyun.cis.goods.provider.logistics.kuaidi100.model.QueryTraceParam;
import cn.abcyun.cis.goods.provider.logistics.kuaidi100.model.SubscribeParam;
import cn.abcyun.cis.goods.provider.logistics.kuaidi100.model.SubscribeParameters;
import cn.abcyun.cis.goods.provider.logistics.kuaidi100.req.QueryTraceReq;
import cn.abcyun.cis.goods.provider.logistics.kuaidi100.req.SubscribeReq;
import cn.abcyun.cis.goods.provider.logistics.kuaidi100.rsp.QueryTraceRsp;
import cn.abcyun.cis.goods.provider.logistics.kuaidi100.rsp.SubscribeRsp;
import cn.abcyun.cis.goods.provider.logistics.kuaidi100.service.LogisticsSupplierService;
import cn.abcyun.cis.goods.provider.logistics.kuaidi100.utils.SignUtils;
import cn.abcyun.cis.goods.provider.logistics.rsp.LogisticsTraceRsp;
import com.netflix.hystrix.HystrixCircuitBreaker;
import com.netflix.hystrix.HystrixCommandKey;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import com.netflix.hystrix.contrib.javanica.conf.HystrixPropertiesManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;

/**
 * 快递100对接
 */
@Slf4j
@Service
public class KuaiDi100LogisticsSupplierService extends LogisticsSupplierService {


    @Autowired
    private KuaiDi100Config kuaiDi100Config;

    @Value("${abc.domain}")
    private String domain;

    public static final String CHARSET_DEFAULT = "utf-8";

    public KuaiDi100LogisticsSupplierService() {
        super(Logistics.SupplierId.KUAI_DI_100);
    }

    @Override
    public LogisticsTraceRsp queryLogisticsTrace(GoodsStockOrderLogistics logistics) {
        LogisticsTraceRsp logisticsTraceRsp = new LogisticsTraceRsp();
        String logisticsNo = logistics.getLogisticsNo();
        String logisticsMobile = logistics.getLogisticsMobile();
        Long logisticsCompanyId = logistics.getLogisticsCompanyId();
        if (StringUtils.isBlank(logisticsNo) || StringUtils.isBlank(logisticsMobile) || Objects.isNull(logisticsCompanyId)) {
            return logisticsTraceRsp;
        }
        QueryTraceRsp queryTraceRsp = queryTrace(
                new QueryTraceParam()
                        .setCom(KuaiDi100Const.Company.getLogisticsCompanyNameById(logisticsCompanyId))
                        .setPhone(logisticsMobile)
                        .setNum(logisticsNo)
        );
        if (queryTraceRsp != null) {
            logisticsTraceRsp.setRows(queryTraceRsp.getData());
        }
        return logisticsTraceRsp;
    }


    /**
     * 查询物流trace
     *
     * @param queryTraceParam
     * @return
     */
    public QueryTraceRsp queryTrace(QueryTraceParam queryTraceParam) {

        QueryTraceReq req = new QueryTraceReq();
        req.setCustomer(kuaiDi100Config.getCustomer())
                .setParam(JsonUtils.dump(queryTraceParam))
                .setSign(SignUtils.querySign(req.getParam(), kuaiDi100Config.getKey(), kuaiDi100Config.getCustomer()));

        QueryTraceRsp rsp = SpringUtils.getBean(KuaiDi100LogisticsSupplierService.class).doPost(kuaiDi100Config.getQueryUrl(), req, QueryTraceRsp.class);

        if (rsp == null || !Objects.equals("200", rsp.getStatus())) {
            return null;
//            throw new OrderServiceException(OrderServiceError.MALL_ORDER_KUAIDI_100_QUERY_FAIL);
        }

        return rsp;
    }


    /**
     * 订阅快递信息
     *
     * @param companyCode 快递100的公司编码
     * @param logisticsNo 快递单号
     * @param callbackUrl 订阅回调地址
     * @return
     */
    public SubscribeRsp subscribe(String companyCode, String logisticsNo, String callbackUrl, String phone) {
        SubscribeReq subscribeReq = new SubscribeReq();
        SubscribeParam subscribeParam = new SubscribeParam();
        subscribeParam.setCompany(companyCode)
                .setKey(kuaiDi100Config.getKey())
                .setNumber(logisticsNo)
                .setParameters(new SubscribeParameters()
                        .setAutoCom("1")
                        .setCallbackurl(callbackUrl)
                        .setPhone(phone)
                );

        subscribeReq.setParam(JsonUtils.dump(subscribeParam));

        SubscribeRsp rsp = SpringUtils.getBean(KuaiDi100LogisticsSupplierService.class).doPost(kuaiDi100Config.getSubscribeUrl(), subscribeReq, SubscribeRsp.class);

        if (rsp == null || !rsp.isResult()) {
            return null;
        }

        return rsp;
    }


    /**
     * post请求  编码格式默认UTF-8
     *
     * @param url 请求url
     * @return
     */
    @HystrixCommand(
            fallbackMethod = "doPostFallback",
            commandKey = "doPostCommandKey",
            commandProperties = {
                    @HystrixProperty(name = HystrixPropertiesManager.EXECUTION_ISOLATION_THREAD_TIMEOUT_IN_MILLISECONDS, value = "5000"),// 超时时间「默认1s」
                    @HystrixProperty(name = HystrixPropertiesManager.EXECUTION_ISOLATION_STRATEGY, value = "THREAD"),// 线程隔离
                    @HystrixProperty(name = HystrixPropertiesManager.METRICS_ROLLING_STATS_TIME_IN_MILLISECONDS, value = "10000"),// 滑动窗口的时间「默认10s」
                    @HystrixProperty(name = HystrixPropertiesManager.METRICS_ROLLING_STATS_NUM_BUCKETS, value = "10"),// 滑动窗口划分的桶数「默认10」
                    @HystrixProperty(name = HystrixPropertiesManager.CIRCUIT_BREAKER_REQUEST_VOLUME_THRESHOLD, value = "20"),// 触发断路器的最小请求数(滑动窗口期内)「默认20」
                    @HystrixProperty(name = HystrixPropertiesManager.CIRCUIT_BREAKER_ERROR_THRESHOLD_PERCENTAGE, value = "50"),// 触发断路器的失败率(滑动窗口期内)「默认50%」
                    @HystrixProperty(name = HystrixPropertiesManager.CIRCUIT_BREAKER_SLEEP_WINDOW_IN_MILLISECONDS, value = "5000") // 断路器打开后，多久尝试恢复「默认5s」
            }
    )
    public <T> T doPost(String url, Object reqBody, Class<T> rspClazz) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        CloseableHttpResponse resp = null;
        T rsp = null;
        try {
            Map<String, String> params = objectToMap(reqBody);
            HttpPost httpPost = new HttpPost(url);
            if (params != null && params.size() > 0) {
                List<NameValuePair> list = new ArrayList<>();
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    list.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }
                httpPost.setEntity(new UrlEncodedFormEntity(list, CHARSET_DEFAULT));
                log.info("reqEntity: {}", JsonUtils.dump(list));
            }
            log.info("url: {}", url);
            resp = httpClient.execute(httpPost);
            String body = EntityUtils.toString(resp.getEntity(), CHARSET_DEFAULT);
            log.info("body: {}", JsonUtils.dump(body));
            rsp = JsonUtils.readValue(body, rspClazz);
            log.info("rsp: {}", JsonUtils.dump(rsp));
        } catch (Exception e) {
            log.error("doPost error", e);
            throw new RuntimeException(e);
        } finally {
            if (null != resp) {
                try {
                    resp.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return rsp;
    }

    public <T> T doPostFallback(String url, Object reqBody, Class<T> rspClazz, Throwable e) {
        HystrixCircuitBreaker hystrixCircuitBreaker = HystrixCircuitBreaker.Factory.getInstance(HystrixCommandKey.Factory.asKey("doPostCommandKey"));
        log.error("hystrixCircuitBreaker.isOpen: {}. doPostFallback error, url: {}, reqBody: {}", hystrixCircuitBreaker.isOpen(), url, JsonUtils.dump(reqBody), e);
        return null;
    }

    /**
     * 将Object对象里面的属性和值转化成Map对象
     *
     * @param obj
     * @return
     * @throws IllegalAccessException
     */
    public static Map<String, String> objectToMap(Object obj) throws IllegalAccessException {
        if (obj == null) {
            return null;
        }
        Map<String, String> map = new HashMap<>();
        List<Field> allField = getAllField(obj);
        for (Field field : allField) {
            field.setAccessible(true);
            String fieldName = field.getName();
            String fieldValue = "";
            if (field.getType() == String.class || field.getType() == Integer.class || field.getType() == int.class) {
                fieldValue = field.get(obj) == null ? "" : field.get(obj).toString();
            }
            map.put(fieldName, fieldValue);
        }
        return map;
    }

    private static List<Field> getAllField(Object obj) {
        List<Field> fieldList = new ArrayList<Field>();
        Class<?> clazz = obj.getClass();
        while (clazz != null) {
            fieldList.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        return fieldList;

    }
}
