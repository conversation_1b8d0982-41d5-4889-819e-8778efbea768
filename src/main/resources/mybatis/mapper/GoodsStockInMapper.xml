<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.abcyun.cis.goods.mapper.GoodsStockInMapper">

    <!-- 表链接里面的条件和where里面条件的区别 表链接里面的条件影响生成临时表的函数 where里面的条件影响 临时表返回最终结果的函数-->
    <sql id="getStockInOrderListInnerJoinWhereClause">
        v2_goods_stock_in_order AS a
        <if test="settlementStatus !=null and settlementStatus != 0">
            LEFT JOIN v2_goods_settlement_order as c ON a.settlement_order_id = c.id AND a.chain_id = #{chainId} and c.is_deleted = 0
        </if>
        <if test="_parameter.containsKey('isChineseMedicine') and isChineseMedicine != null and isChineseMedicine == true  ">
            INNER JOIN v2_goods_stock_in t2 ON a.id = t2.order_id AND a.chain_id = t2.chain_id
            INNER JOIN v2_goods t3 ON t2.goods_id = t3.id AND t3.organ_id = t2.chain_id
        </if>

    </sql>
    <sql id="getStockInOrderListInnerJoinWhereClauseCount">
        v2_goods_stock_in_order AS a
        <if test="settlementStatus !=null and settlementStatus != 0">
            LEFT JOIN v2_goods_settlement_order as c ON a.settlement_order_id = c.id AND a.chain_id = #{chainId} and c.is_deleted = 0
        </if>
        <if test="_parameter.containsKey('isChineseMedicine') and isChineseMedicine != null and isChineseMedicine == true  ">
            INNER JOIN v2_goods_stock_in t2 ON a.id = t2.order_id AND a.chain_id = t2.chain_id
            INNER JOIN v2_goods t3 ON t2.goods_id = t3.id AND t3.organ_id = t2.chain_id
        </if>
    </sql>
    <sql id="getStockInOrderListWhereClause">
        a.chain_id = #{chainId}

        <if test="_parameter.containsKey('isChineseMedicine') and isChineseMedicine != null and isChineseMedicine == true  ">
         and t3.type=1 and t3.sub_type = 2 and t2.status = 1
        </if>
        <!-- 张仲景修改入库单不要在列表那里展示出来 -->
        <if test="isFixedOrderNeedReview == 1  ">
            and ( a.import_flag != 2 or a.import_flag is null)
        </if>
        <if test="withGoodsId !=null and withGoodsId !=''  ">
            AND a.id in (SELECT b.order_id from v2_goods_stock_in as b WHERE b.goods_id = #{withGoodsId} AND b.status = 1)
        </if>
        <if test="keyword !=null and keyword !=''  ">
            and  a.order_no = #{keyword}
        </if>
        <if test="outOrderNo!=null and outOrderNo !=''  ">
            and a.out_order_no = #{outOrderNo}
        </if>
        <if test="clinicId != null   and clinicId !='' ">
            and a.to_organ_id = #{clinicId}
        </if>
        <if test="status != null and settlementStatus == null">
            and a.status = #{status}
        </if>
        <if test="pharmacyType != null">
            AND a.pharmacy_type = #{pharmacyType}
        </if>
        <if test="defaultTypes != null and defaultTypes.size() > 0">
            AND a.type in <foreach collection="defaultTypes" item="defaultType" open="(" separator="," close=")">#{defaultType}</foreach>
        </if>
        <if test="pharmacyNo != null">
            AND a.pharmacy_no = #{pharmacyNo}
        </if>
        <if test="dateField != null  ">
            <if test="dateField == 'lastModified'.toString()  ">
                <choose>
                    <when test="beginDate != null and  endDate != null ">
                        and a.last_modified_date &gt; #{beginDate} and a.last_modified_date &lt; #{endDate}
                    </when>
                    <when test="beginDate != null and  endDate == null ">
                        and a.last_modified_date &gt; #{beginDate}
                    </when>
                    <when test="beginDate == null and  endDate != null ">
                        and a.last_modified_date &lt; #{endDate}
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test="dateField == 'inDate'.toString()  ">
                <choose>
                    <when test="beginDate != null and  endDate != null ">
                        and ((a.in_date &gt; #{beginDate} and a.in_date &lt; #{endDate})
                            <if test="includeToday == 1">
                            OR a.status = 1
                            </if>
                        )
                    </when>
                    <when test="beginDate != null and  endDate == null ">
                        and a.in_date &gt; #{beginDate}
                    </when>
                    <when test="beginDate == null and  endDate != null ">
                        and a.in_date &lt; #{endDate}
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test="dateField == 'createdDate'.toString()">
                <choose>
                    <when test="beginDate != null and  endDate != null ">
                        and a.created_date &gt; #{beginDate} and a.created_date &lt; #{endDate}
                    </when>
                    <when test="beginDate != null and  endDate == null ">
                        and a.created_date &gt; #{beginDate}
                    </when>
                    <when test="beginDate == null and  endDate != null ">
                        and a.created_date &lt; #{endDate}
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
        </if>

        <if test="supplierId !=null and supplierId !='' ">
            AND a.supplier_id = #{supplierId}
        </if>

        <!-- 这个接口实现了太多逻辑 ：1.listCount设置后，其他两个状态都失效 2.settlementStatus设置 status失效  -->
        <choose>
            <when test="listCount !=null and listCount  &gt; 0 ">
                AND (a.status = 2
                <if test="includeToday == 1">
                    OR a.status = 1
                </if>
                )
            </when>
            <otherwise>
                <!--前端业务逻辑保证了  settlementStatus 和 status 不可能同时设置-->
                <if test="settlementStatus !=null ">
                    <choose>
                        <when test="settlementStatus == 0">
                            and a.status =2
                            AND a.supplier_id != '00000000000000000000000000000000'
                            and a.settlement_order_id is null
                            and ifnull(a.business_settle_status, 0) = 0
                        </when>
                        <when test="settlementStatus == 1">
                            AND a.status =2
                            and a.business_settle_status &amp; 1 > 0
                        </when>
                        <when test="settlementStatus == 2">
                            AND a.status =2
                            and if(a.settlement_order_id is not null,  c.status = 0, a.business_settle_status &amp; 2 > 0)
                        </when>
                        <when test="settlementStatus == 4">
                            AND a.status =2
                            and if(a.settlement_order_id is not null,  c.status = 1, a.business_settle_status &amp; 4 > 0)
                        </when>
                        <otherwise>
                            <if test="status !=null and status &gt;= 0 ">
                                AND a.status = #{status}
                            </if>
                        </otherwise>
                    </choose>
                </if>
            </otherwise>
        </choose>
        <if test="stockInBy != null and stockInBy != ''">
            AND a.stock_in_by = #{stockInBy}
        </if>
        <if test="sourceType != null and sourceType.size() > 0">
            AND a.source_type in
            <foreach collection="sourceType" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="getStockInOrderList" resultType="cn.abcyun.cis.goods.dto.stockin.GoodsStockInOrderDto">

        SELECT
        distinct a.id as id,
        a.order_no as orderNo,
        a.out_order_no as outOrderNo,
        a.supplier_id as supplierId,
        a.settlement_order_id as settlementOrderId,
        a.to_organ_id as toOrganId,
        a.comment as comment,
        a.type as type,
        a.is_confirm as isConfirm,
        a.is_review as isReview,
        a.settlement_date as settlementDate,
        a.confirm_user_id as confirmUserId,
        a.confirm_date as confirmDate,
        a.in_date as inDate,
        a.review_user_id as reviewUserId,
        a.review_date as reviewDate,
        a.kind_count as kindCount,
        a.status as status,
        a.sum as totalInOrderCount,<!--入库单上所有useCount的简单累加-->
        a.count*IF(a.type in(10 ,11, 12, 13) ,-1,1) as totalPackageCount,<!--useCount转成packageCount后的累加-->
        a.amount*IF(a.type in(10 ,11, 12, 13) ,-1,1) as totalInOrderTotalCostPrice,<!--入库单上userTotalCostPrice的累加-->
        a.amount_excluding_tax*IF(a.type in(10 ,11, 12, 13) ,-1,1) as totalInOrderTotalCostPriceExcludingTax,
        a.created_user_id as createdBy,
        a.created_date as created,
        a.last_modified_user_id as lastModifiedBy,
        a.last_modified_date as lastModified,
        a.mall_order_id as mallOrderId,
        a.settlement_order_id as settlementId
        <if test="settlementStatus !=null and settlementStatus != 0">
            ,c.status as settlementStaus
        </if>
        ,a.business_settle_status as businessSettleStatus
        ,a.pharmacy_no as pharmacyNo
        ,a.pharmacy_type as pharmacyType
        ,a.stock_in_by as stockInBy
        ,a.related_orders as relatedOrdersStr,
        a.gsp_inst_id as gspInstId,
        a.source_type as sourceType,
        a.external_flag as externalFlag,
        a.extend_data as extendData
        FROM
        <include refid="getStockInOrderListInnerJoinWhereClause"></include>
        WHERE
        <include refid="getStockInOrderListWhereClause"></include>
        <choose>
            <when test="isPharmacy != null and isPharmacy == true">
                order by a.status, case when a.status = 1 then a.created_date end desc, a.in_date desc, a.created_date desc
            </when>
            <otherwise>
                ORDER by a.created_date desc
            </otherwise>
        </choose>
        <if test="offset != null and offset gt -1 and limit != null and limit gt -1">LIMIT #{offset}, #{limit}</if>
    </select>

    <select id="countStockInOrderList" resultType="cn.abcyun.cis.goods.dto.stockin.GoodsStockInOrderDtoCount">
        SELECT

        count(distinct a.id) as inOrderCount,
        SUM(IF(a.status = 1,1, 0)) as inTodoCount,
        SUM(IF(a.status = 0 ,1, 0)) as reviewTodoCount,
        SUM(IF(a.type  in(10 ,11, 12, 13) ,-1,1) * a.amount) as sumAmount,
        SUM(IF(a.type  in(10 ,11, 12, 13) ,-1,1) * a.sum) as sumCount,
        SUM(IF(a.type in(0,1,2,3,4,5,7,20,21,22),a.amount, 0)) as inSumAmount,
        SUM(IF(a.type in(0,1,2,3,4,5,7, 20, 21,22) ,a.sum, 0)) as inSumCount,
        SUM(IF(a.type in(10 ,11, 12, 13),a.amount, 0)) as returnOutInSumAmount,
        SUM(IF(a.type in(10 ,11, 12, 13) ,a.sum, 0)) as returnOutInSumCount
        FROM
        <include refid="getStockInOrderListInnerJoinWhereClauseCount"></include>
        WHERE
        <include refid="getStockInOrderListWhereClause"></include>
    </select>

    <select id="countTodoStockInOrderList" resultType="cn.abcyun.cis.goods.dto.stockin.GoodsStockInOrderDtoCount">
        SELECT
        SUM(IF(a.status = 1,1, 0)) as inTodoCount
        FROM
        <include refid="getStockInOrderListInnerJoinWhereClauseCount"></include>
        <where>
            a.chain_id = #{chainId}
            <if test="withGoodsId !=null and withGoodsId !=''  ">
                AND a.id in (SELECT b.order_id from v2_goods_stock_in as b WHERE b.goods_id = #{withGoodsId} AND b.status =
                1)
            </if>
            <if test="keyword !=null and keyword !=''  ">
                and  a.order_no = #{keyword}
            </if>
            <if test="outOrderNo!=null and outOrderNo !=''  ">
                and a.out_order_no = #{outOrderNo}
            </if>
            <if test="clinicId != null   and clinicId !='' ">
                and a.to_organ_id = #{clinicId}
            </if>
            <if test="pharmacyType != null">
                AND a.pharmacy_type = #{pharmacyType}
            </if>
            <if test="defaultTypes != null and defaultTypes.size() > 0">
                AND a.type in <foreach collection="defaultTypes" item="defaultType" open="(" separator="," close=")">#{defaultType}</foreach>
            </if>
            <if test="pharmacyNo != null">
                AND a.pharmacy_no = #{pharmacyNo}
            </if>
            <if test="dateField != null  ">
                <if test="dateField == 'lastModified'.toString()  ">
                    <choose>
                        <when test="beginDate != null and  endDate != null ">
                            and a.last_modified_date &gt; #{beginDate} and a.last_modified_date &lt; #{endDate}
                        </when>
                        <when test="beginDate != null and  endDate == null ">
                            and a.last_modified_date &gt; #{beginDate}
                        </when>
                        <when test="beginDate == null and  endDate != null ">
                            and a.last_modified_date &lt; #{endDate}
                        </when>
                        <otherwise>
                        </otherwise>
                    </choose>
                </if>
                <if test="dateField == 'inDate'.toString()  ">
                    <choose>
                        <when test="beginDate != null and  endDate != null ">
                            and ((a.in_date &gt; #{beginDate} and a.in_date &lt; #{endDate})
                                <if test="includeToday == 1">
                                OR a.status = 1
                                </if>
                            )
                        </when>
                        <when test="beginDate != null and  endDate == null ">
                            and a.in_date &gt; #{beginDate}
                        </when>
                        <when test="beginDate == null and  endDate != null ">
                            and a.in_date &lt; #{endDate}
                        </when>
                        <otherwise>
                        </otherwise>
                    </choose>
                </if>
                <if test="dateField == 'createdDate'.toString()">
                    <choose>
                        <when test="beginDate != null and  endDate != null ">
                            and a.created_date &gt; #{beginDate} and a.created_date &lt; #{endDate}
                        </when>
                        <when test="beginDate != null and  endDate == null ">
                            and a.created_date &gt; #{beginDate}
                        </when>
                        <when test="beginDate == null and  endDate != null ">
                            and a.created_date &lt; #{endDate}
                        </when>
                        <otherwise>
                        </otherwise>
                    </choose>
                </if>
            </if>

            <if test="supplierId !=null and supplierId !='' ">
                AND a.supplier_id = #{supplierId}
            </if>

            <!-- 这个接口实现了太多逻辑 ：1.listCount设置后，其他两个状态都失效 2.settlementStatus设置 status失效  -->
            <choose>
                <when test="listCount !=null and listCount  &gt; 0 ">
                    AND (a.status = 2
                    <if test="includeToday == 1">
                        OR a.status = 1
                    </if>)
                </when>
                <otherwise>
                    <!--前端业务逻辑保证了  settlementStatus 和 status 不可能同时设置-->
                    <if test="settlementStatus !=null ">
                        <choose>
                            <when test="settlementStatus == 0">
                                and a.status =2
                                AND a.supplier_id != '00000000000000000000000000000000'
                                and a.settlement_order_id is null
                                and ifnull(a.business_settle_status, 0) = 0
                            </when>
                            <when test="settlementStatus == 1">
                                AND a.status =2
                                and a.business_settle_status &amp; 1 > 0
                            </when>
                            <when test="settlementStatus == 2">
                                AND a.status =2
                                and if(a.settlement_order_id is not null,  c.status = 0, a.business_settle_status &amp; 2 > 0)
                            </when>
                            <when test="settlementStatus == 4">
                                AND a.status =2
                                and if(a.settlement_order_id is not null,  c.status = 1, a.business_settle_status &amp; 4 > 0)
                            </when>
                            <otherwise>
                                <if test="status !=null and status &gt;= 0 ">
                                    AND a.status = #{status}
                                </if>
                            </otherwise>
                        </choose>
                    </if>
                </otherwise>
            </choose>
            <if test="stockInBy != null and stockInBy != ''">
                AND a.stock_in_by = #{stockInBy}
            </if>
            <if test="sourceType != null and sourceType.size() > 0">
                AND a.source_type in
                <foreach collection="sourceType" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <sql id="findInOrderItemWithReturnLeftColumns">
        a.id as id,
                a.original_in_id as originalInId,
                a.original_in_order_id as originalInOrderId,
                a.batch_no as batchNo,
                a.expiry_date as expiryDate,
                a.production_date as productionDate,
                a.goods_id as goodsId,
                a.goods as goods,
                a.order_id as orderId,
                a.piece_num as pieceNum,
                a.last_modified_date as lastModified,
                a.use_count as inOrderCount,
                a.use_unit as inOrderUnit,
                a.use_unit_cost_price as inOrderUnitCostPrice,
                a.use_total_cost_price as inOrderTotalCostPrice,
                a.total_cost as totalCost,
                a.package_cost_price as packageCostPrice,
                a.package_count as packageCount,
                a.piece_count as pieceCount,
                a.pharmacy_no as pharmacyNo,
                a.return_count as canReturnInOrderCount,
                a.return_package_count as yetReturnInOrderPackageCount,
                a.return_piece_count  as yetReturnInOrderPieceCount,
                a.last_modified_date as lastModified,
                a.original_expiry_date as originalExpiryDate

    </sql>

    <select id="findBigInOrderTopNItemWithReturnLeft"
            resultType="cn.abcyun.cis.goods.dto.stockin.GoodsStockInOrderItemDto">
        SELECT
        <include refid="findInOrderItemWithReturnLeftColumns"></include>
        FROM v2_goods_stock_in as a
        WHERE a.order_id = #{orderId}
        LIMIT 0,#{topN}
    </select>
    <select id="findBigInOrderTopNItemWithReturnLeft1"
            resultType="cn.abcyun.cis.goods.dto.stockin.GoodsStockInOrderItemDto">
        SELECT
        <include refid="findInOrderItemWithReturnLeftColumns"></include>
        FROM v2_goods_stock_in as a
        WHERE a.chain_id = #{chainId} and a.original_in_id  in
        <foreach collection="inOrderItemIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findInOrderItemWithReturnLeft" resultType="cn.abcyun.cis.goods.dto.stockin.GoodsStockInOrderItemDto">
        SELECT
        <include refid="findInOrderItemWithReturnLeftColumns"></include>
        FROM v2_goods_stock_in as a
        WHERE a.chain_id = #{chainId} and a.original_in_order_id IN
        <foreach collection="inOrderIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findInOrderItemGoodsIdWithReturnLeft"
            resultType="cn.abcyun.cis.goods.dto.stockin.GoodsStockInOrderItemDto">
        SELECT
        <include refid="findInOrderItemWithReturnLeftColumns"></include>
        FROM v2_goods_stock_in as a
        WHERE a.goods_id = #{goodsId} AND a.original_in_order_id IN
        <foreach collection="inOrderIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findInOrderMinCreatedDate" resultType="java.time.Instant">
        SELECT
        min(created_date) as created
        FROM v2_goods_stock_in_order
        WHERE chain_id=#{chainId}
        <if test="clinicId != null  and clinicId != '' ">
            AND to_organ_id = #{clinicId}
        </if>
    </select>
    <select id="findCheckOrderMinCreatedDate" resultType="java.time.Instant">
        SELECT
        min(created_date) as created
        FROM v2_goods_stock_check_order
        WHERE chain_id=#{chainId}
        <if test="clinicId != null  and clinicId != '' ">
            AND organ_id = #{clinicId}
        </if>
    </select>
    <select id="findOutOrderMinCreatedDate" resultType="java.time.Instant">
        SELECT
        min(created_date) as created
        FROM v2_goods_stock_out_order
        WHERE chain_id=#{chainId}
        <if test="clinicId != null  and clinicId != '' ">
            AND from_organ_id = #{clinicId}
        </if>
        <if test="typeList != null">
                and type in
            <foreach collection="typeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="findTransOrderMinCreatedDate" resultType="java.time.Instant">
        SELECT min(created_date) as created
        FROM v2_goods_stock_trans_order
        WHERE chain_id = #{chainId}
    </select>
    <select id="findPurchaseOrderMinCreatedDate" resultType="java.time.Instant">
        SELECT
        min(created_date) as created
        FROM v2_goods_purchase_order
        WHERE chain_id=#{chainId}
        <if test="clinicId != null  and clinicId != '' ">
            AND applicant_organ_id = #{clinicId}
        </if>
    </select>
    <!--
        计算某个供应商要算销量的goodsId列表，从goodsstocklog表里面 算是否有发药退药action 销量太低。
        这里使用了 通过判断goodsStock 的最近修改时间比 算销量起始时间大的goodsId作为可能有销量的goods列表
     -->
    <select id="findCalculateSellCountGoodsIdList" resultType="java.lang.String">
        SELECT distinct goods_id
        FROM v2_goods_stock as a
        WHERE a.chain_id = #{chainId}
          and a.organ_id = #{clinicId}
          and a.supplier_id = #{supplierId}
          and a.last_modified_date >= #{beginDate}
    </select>

    <select id="findNeedSyncToInOrderOrderIdList" resultType="java.lang.Long">
        SELECT id
        FROM v2_goods_stock_out_order as a
        WHERE a.chain_id = #{chainId}
          and a.type = 3
    </select>
    <sql id="queryGoodsStockInItemWhereClause">
        orders.chain_id = #{headerChainId}
        <if test="clinicId != null and clinicId != ''">
            and orders.to_organ_id = #{clinicId}
        </if>
        <if test="supplierId != null and supplierId != ''">
            and orders.supplier_id = #{supplierId}
        </if>
        <if test="orderNo != null and orderNo != ''">
            and orders.order_no = #{orderNo}
        </if>
        <if test=" createBeginTime != null">
            and orders.in_date >= #{createBeginTime}
        </if>
        <if test=" createEndTime != null">
            and orders.in_date &lt;= #{createEndTime}
        </if>
        <if test="goodsId != null and goodsId != ''">
            and stock.goods_id = #{goodsId}
        </if>
    </sql>

    <select id="queryGoodsStockInGspItem" resultType="cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsStockInOrderRsp">
        select orders.id as orderId,
        stock.id as id,
        orders.order_no as orderNo,
        orders.supplier_id as supplierId,
        orders.to_organ_id as clinicId,
        orders.comment,
        orders.type,
        orders.status,
        orders.last_modified_user_id as OpUserId,
        orders.in_date as opDate,
        stock.package_count as packageCount,
        stock.piece_count as pieceCount,
        stock.expiry_date as expiryDate,
        stock.production_date as productionDate,
        stock.batch_no as batchNo,
        stock.batch_id as batchId,
        stock.expiry_date as expiryDate,
        stock.goods_id as goodsId,
        stock.use_unit_cost_price as packagePrice,
        stock.use_total_cost_price as amount,
        stock.use_unit as inOrderUnit
        from v2_goods_stock_in as stock
        inner join v2_goods_stock_in_order as orders on stock.order_id = orders.id and stock.status = 1
        and orders.chain_id = #{headerChainId}
        inner join v2_goods as goods on goods.id = stock.goods_id and goods.organ_id = stock.chain_id
                                            <!-- 1 处方药 -->
        <if test="type == 0">
            and goods.otc_type = 1
        </if>
        <if test="type == 1">
            and goods.danger_ingredient >= 32
        </if>
        WHERE
        <include refid="queryGoodsStockInItemWhereClause"></include>
        order by orders.created_date desc
        <if test="offset != null and offset gt -1 and limit != null and limit gt -1">LIMIT #{offset}, #{limit}</if>
    </select>


    <select id="countGoodsStockInGspItem" resultType="java.lang.Integer">
        select count(orders.id)
        from v2_goods_stock_in stock
        inner join v2_goods_stock_in_order orders on stock.order_id = orders.id and stock.status = 1
        and orders.chain_id = #{headerChainId}
        inner join v2_goods as goods on goods.id = stock.goods_id and goods.organ_id = stock.chain_id
        <!-- 1 处方药 -->
        <if test="type == 0">
            and goods.otc_type = 1
        </if>
        <if test="type == 1">
            and goods.danger_ingredient >= 32
        </if>
        WHERE
        <include refid="queryGoodsStockInItemWhereClause"></include>
    </select>

    <select id="queryDeliveryGoodsByGoodsPurchaseOrder"
            resultType="cn.abcyun.cis.goods.dto.stockin.GoodsStockInReceivedCount">
        select b.goods_id                   as goodsId,
               b.piece_num                  as pieceNum,
               sum(IF(a.status == 20, b.delivery_package_count,-1*b.delivery_package_count)) as deliveryPackageCount,
               sum(IF(a.status == 20, b.delivery_piece_count,-1*b.delivery_piece_count))   as deliveryPieceCount
        from v2_goods_stock_delivery_order a
                 inner join v2_goods_stock_in b on (a.id = b.purchase_order_id)
        where a.clinic_id = #{clinicId}
          and b.purchase_order_id = #{claimOrderId}
          and a.status in ( 20,30)
          and a.is_deleted = 0
        group by goods_id;
    </select>
    <select id="queryReceivedGoodsByGoodsPurchaseOrder"
            resultType="cn.abcyun.cis.goods.dto.stockin.GoodsStockInReceivedCount">
        select b.goods_id                   as goodsId,
               b.piece_num                  as pieceNum,
               sum(b.receive_package_count) as receivedPackageCount,
               sum(b.receive_piece_count)   as receivedPieceCount
        from v2_goods_stock_receive_order a
                 inner join v2_goods_stock_in b on (a.id = b.receive_order_id)
        where a.clinic_id = #{clinicId}
          and b.purchase_order_id = #{purchaseOrderId}
          and a.status = 20
          and a.is_deleted = 0
        group by goods_id;
    </select>

    <select id="getStockInOrderListForImport" resultType="Long">
        select distinct a.id from v2_goods_stock_in_order a
        where a.chain_id = #{chainId} and a.to_organ_id = #{clinicId} and a.type in (3, 20, 21)
        <if test="supplierId != null and supplierId != ''">
            and a.supplier_id = #{supplierId}
        </if>
        <if test="withGoodsId != null and withGoodsId != ''">
            and a.id in (select b.order_id from v2_goods_stock_in b where b.goods_id = #{withGoodsId} and b.status = 1)
        </if>
        <if test="beginDate != null and endDate != null">
            and a.created_date > #{beginDate} and a.created_date &lt; #{endDate}
        </if>
        order by a.created_date desc
    </select>

    <select id="findInOrderItemWithReturnLeftByGoodsList" resultType="cn.abcyun.cis.goods.dto.stockin.GoodsStockInOrderItemDto">
        SELECT
        <include refid="findInOrderItemWithReturnLeftColumns"></include>
        FROM v2_goods_stock_in as a
        WHERE a.chain_id = #{chainId} and a.goods_id IN
        <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="countImportGoodsStockIn" resultType="int">
        select count(1)
        from v2_goods a
                 join v2_goods_stock_in_order b on a.organ_id = b.chain_id
                 join v2_goods_stock_in c on a.id = c.goods_id and b.id = c.order_id
        where a.organ_id = #{chainId}
          and b.to_organ_id = #{clinicId}
          and b.type in (3, 20, 21)
        <if test="keyword != null and keyword != ''">
            and (a.name like CONCAT(#{keyword}, '%') or a.medicine_cadn like CONCAT(#{keyword}, '%')
            or a.py like CONCAT(#{keyword}, '%')
            <if test="keyword.length() >= 3">
                or a.bar_code = #{keyword} or a.short_id = #{keyword}
                or a.py like CONCAT('%', #{keyword}, '%')
            </if>
            )
        </if>

    </select>

    <select id="queryImportGoodsStockIn" resultType="cn.abcyun.cis.goods.dto.stockin.GoodsStockInOrderItemDto">
        select
            c.id as id,
            c.batch_no as batchNo,
            c.expiry_date as expiryDate,
            c.production_date as productionDate,
            c.goods_id as goodsId,
            c.goods as goods,
            c.order_id as orderId,
            c.piece_num as pieceNum,
            c.last_modified_date as lastModified,
            c.use_count as inOrderCount,
            c.use_unit as inOrderUnit,
            c.use_unit_cost_price as inOrderUnitCostPrice,
            c.use_total_cost_price as inOrderTotalCostPrice,
            c.package_cost_price as packageCostPrice,
            c.package_count as packageCount,
            c.piece_count as pieceCount,
            c.pharmacy_no as pharmacyNo,
            c.return_count as canReturnInOrderCount,
            c.return_package_count as yetReturnInOrderPackageCount,
            c.return_piece_count  as yetReturnInOrderPieceCount,
            c.last_modified_date as lastModified,
            c.original_expiry_date as originalExpiryDate
        from v2_goods a
                 join v2_goods_stock_in_order b on a.organ_id = b.chain_id
                 join v2_goods_stock_in c on a.id = c.goods_id and b.id = c.order_id
        where a.organ_id = #{chainId}
          and b.to_organ_id = #{clinicId}
          and b.type in (3, 20, 21)
        <if test="keyword != null and keyword != ''">
            and (a.name like concat(#{keyword}, '%') or a.medicine_cadn like concat(#{keyword}, '%')
            or a.py like concat(#{keyword}, '%')
            <if test="keyword.length() >= 3">
                or a.bar_code = #{keyword} or a.short_id = #{keyword}
                or a.py like concat('%', #{keyword}, '%')
            </if>
            )
        </if>
        order by b.created_date desc
        <if test="offset != null and offset gt -1 and limit != null and limit gt -1">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="queryGoodsIdsByInOrderId" resultType="java.lang.String">
        select distinct goods_id
        from v2_goods_stock_in
        where order_id = #{inOrderId}
          and chain_id = #{chainId}
          and status != 99
    </select>

    <select id="getGoodsStockInOrderListOfDelivery" resultType="cn.abcyun.cis.goods.dto.stockin.GoodsStockInOrderDto">
        SELECT
        a.id as id,
        a.order_no as orderNo,
        a.out_order_no as outOrderNo,
        a.supplier_id as supplierId,
        a.to_organ_id as toOrganId,
        a.comment as comment,
        a.type as type,
        a.external_flag as externalFlag,
        a.is_confirm as isConfirm,
        a.is_review as isReview,
        a.confirm_date as confirmDate,
        a.in_date as inDate,
        a.review_user_id as reviewUserId,
        a.review_date as reviewDate,
        a.kind_count as kindCount,
        a.status as status,
        a.sum as totalInOrderCount,<!--入库单上所有useCount的简单累加-->
        a.count*IF(a.type in(10 ,11) ,-1,1) as totalPackageCount,<!--useCount转成packageCount后的累加-->
        a.amount*IF(a.type in(10 ,11) ,-1,1) as totalInOrderTotalCostPrice,<!--入库单上userTotalCostPrice的累加-->
        a.amount_excluding_tax*IF(a.type in(10 ,11) ,-1,1) as totalInOrderTotalCostPriceExcludingTax,
        a.created_user_id as createdBy,
        a.created_date as created,
        a.last_modified_user_id as lastModifiedBy,
        a.last_modified_date as lastModified,
        a.mall_order_id as mallOrderId,
        a.settlement_order_id as settlementId
        ,a.pharmacy_no as pharmacyNo
        ,a.pharmacy_type as pharmacyType
        ,a.stock_in_by as stockInBy
        ,a.related_orders as relatedOrdersStr,
        a.gsp_inst_id as gspInstId,
        a.source_type as sourceType,
        a.claim_clinic_id as claimClinicId
    from v2_goods_stock_in_order AS a
        <if test="withGoodsId != null and withGoodsId != ''">
            inner join v2_goods_stock_in b on a.chain_id = b.chain_id and a.id = b.order_id and b.status = 1 and b.goods_id = #{withGoodsId}
        </if>
    where
        a.chain_id = #{headerChainId} and a.to_organ_id = #{headerClinicId} and a.status = 2 and a.type in (4, 5)
        <if test="claimClinicId != null and claimClinicId != ''">
            and a.claim_clinic_id = #{claimClinicId}
        </if>
        <if test="keyword != null and keyword != ''">
            and a.order_no = #{keyword}
        </if>
        <if test="beginInstantDate != null and endInstantDate != null ">
            and a.created_date &gt;= #{beginInstantDate} and a.created_date &lt;= #{endInstantDate}
        </if>
        ORDER by a.created_date desc
        limit #{offset}, #{limit}
    </select>

    <select id="countGoodsStockInOrderListOfDelivery" resultType="cn.abcyun.cis.goods.dto.stockin.GoodsStockInOrderDtoCount">
        SELECT
            count(*) as inOrderCount
        from v2_goods_stock_in_order AS a
        <if test="withGoodsId != null and withGoodsId != ''">
            inner join v2_goods_stock_in b on a.chain_id = b.chain_id and a.id = b.order_id and b.status = 1 and b.goods_id = #{withGoodsId}
        </if>
        where
            a.chain_id = #{headerChainId} and a.to_organ_id = #{headerClinicId} and a.status = 2 and a.type in (4, 5)
            <if test="claimClinicId != null and claimClinicId != ''">
                and a.claim_clinic_id = #{claimClinicId}
            </if>
            <if test="keyword != null and keyword != ''">
                and a.order_no = #{keyword}
            </if>
            <if test="beginInstantDate != null and endInstantDate != null ">
                and a.created_date &gt;= #{beginInstantDate} and a.created_date &lt;= #{endInstantDate}
            </if>
    </select>

    <select id="batchGoodsStockInItems" resultType="cn.abcyun.cis.goods.dto.stockin.BatchGoodsStockInDto">
        select
            id id,
            goods_id goodsId,
            order_id orderId
        from v2_goods_stock_in
        where chain_id = #{chainId} and status = 1
        and order_id in
        <foreach collection="inOrderList" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        order by order_id, id
        limit #{offset}, #{limit}
    </select>

    <select id="countBatchGoodsStockInItems" resultType="int">
        select count(1)
        from v2_goods_stock_in
        where chain_id = #{chainId} and status = 1
        and order_id in
        <foreach collection="inOrderList" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>
</mapper>