<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.abcyun.cis.goods.mapper.GoodsTraceCodeMapper">
    <sql id="sumStockTraceableCodeAction">
        action = 500 or action = 600 or action = 620 or action = 900 or action = 950 or action = 1000 or action = 200
        or action = 530 or action = 630 or action = 610 or action = 510
    </sql>

    <select id = "getTraceCodeUsedCount" resultType="cn.abcyun.cis.goods.dto.stock.GoodsTraceCodeUsedCountDto">
        select goods_id                  as goodsId,
               no,
               sum(if(<include refid="sumStockTraceableCodeAction"/>, change_piece_count, 0))   as changePieceCount,
               sum(if(<include refid="sumStockTraceableCodeAction"/>, change_package_count, 0)) as changePackageCount,
               sum(if(action = 100 or action = 110, change_piece_count, 0))   as stockChangePieceCount,
               sum(if(action = 100 or action = 110, change_package_count, 0)) as stockChangePackageCount,
               max(if(action = 900 or action = 950 or action = 1000, created, null)) as stockOutTime
        from v3_goods_stock_traceable_code_log where clinic_id = #{clinicId}
        <if test="noList != null and noList.size() > 0">
            AND no in
            <foreach collection="noList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and is_deleted = 0
        group by goods_id, no
    </select>

    <select id="queryTraceableCodeRelation" resultType="cn.abcyun.cis.goods.model.GoodsDrugIdentificationCodeRelation">
        select *
        from v2_goods_identification_code_relation
        where chain_id = #{chainId}
        <if test="clinicId != null and clinicId != ''">
            and clinic_id = #{clinicId}
        </if>
        <if test="drugIdentificationCodeKeys != null and drugIdentificationCodeKeys.size() != 0">
            and (goods_id, drug_identification_code) in
            <foreach collection="drugIdentificationCodeKeys" item="item" open="((" close="))" separator="),(">
                #{item.goodsId}, #{item.drugIdentificationCode}
            </foreach>
        </if>
    </select>

    <select id="queryTraceableCode" resultType="cn.abcyun.cis.goods.model.GoodsStockTraceableCode">
        select *, max(piece_count) as piece_count, max(last_modified) as last_modified
        from v2_goods_stock_traceable_code
        where clinic_id = #{clinicId}
          and goods_id = #{goodsId}
          <choose>
              <when test="no != null and no != ''">
                  and no like concat('%', #{no}, '%')
              </when>
              <otherwise>
                  and stock_out_time is not null
              </otherwise>
          </choose>
          and ((use_piece_count is null or use_piece_count > 0) or  (flag &amp; 4) > 0)
          and used >= 0
          and is_deleted = 0
          and (flag &amp; 1) > 0
        group by no
        order by if(piece_count > 0, 0, 1) asc, last_modified desc
        limit #{offset}, #{limit}
    </select>

    <select id="queryTraceableCodeCount" resultType="int">
        select count(distinct no)
        from v2_goods_stock_traceable_code
        where clinic_id = #{clinicId}
          and goods_id = #{goodsId}
          and used >= 0
          and (flag &amp; 1) > 0
        <choose>
            <when test="no != null and no != ''">
                and no like concat('%', #{no}, '%')
            </when>
            <otherwise>
                and stock_out_time is not null
            </otherwise>
        </choose>
        and (use_piece_count is null or use_piece_count > 0)
        and is_deleted = 0
    </select>
    <update id="updateTraceCodeRecentUsed">
        update  v2_goods_identification_code_relation
        set last_modified =now()
        where  id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
        #{item}
            </foreach>

    </update>

    <select id="queryTraceableCodeV2" resultType="cn.abcyun.cis.goods.model.GoodsStockTraceableCode">
          select * from  v2_goods_stock_traceable_code
             where chain_id = #{headerChainId}
             AND is_deleted = 0
             AND used <![CDATA[>=]]> 0
              <include refid="v2Condition"/>
             <if test="status !=null">
               <choose>
                   <when test="status==0">
                       and used in (0,2)
                   </when>
                   <otherwise>
                       and used = 1
                   </otherwise>
               </choose>
            </if>
             order by last_modified desc
             limit #{offset}, #{limit}
    </select>

    <select id="queryTraceableCodeCountV2" resultType="cn.abcyun.cis.goods.vo.frontend.GoodsStockTraceCodeRsp">
        select count(1) as total,
              sum(if(used = 1, 0, 1)) as inStock,
              sum(if(used = 1, 1, 0)) as outStock
               from  v2_goods_stock_traceable_code
        where chain_id = #{headerChainId}
        AND is_deleted = 0
        AND used <![CDATA[>=]]> 0
        <include refid="v2Condition"/>
    </select>

    <sql id="v2Condition">
        <if test="no !=null and no!= ''">
             and no = #{no}
        </if>
        <if test="goodsId !=null and goodsId!= ''">
            and goods_Id = #{goodsId}
        </if>
        <if test="clinicId != null and clinicId !=''">
            and clinic_id = #{clinicId}
        </if>
    </sql>

    <select id="queryTraceableCodeByIdentificationCodeNo" resultType="cn.abcyun.cis.goods.model.GoodsStockTraceableCode">
        select no, ali_package_level, ali_piece_num, ali_piece_unit, ali_package_unit
        from (
            select ROW_NUMBER() over (
                       partition by substr(no, 1, #{codePrefixLen})
                       order by created desc
                   ) as rk,
                   no,
                   ali_package_level,
                   ali_piece_num,
                   ali_piece_unit,
                   ali_package_unit
            from v2_goods_stock_traceable_code
            where chain_id = #{chainId}
              and clinic_id = #{clinicId}
              and goods_id = #{goodsId}
              and piece_count > 0
              and ali_piece_num is not null
              and
              <foreach collection="codes" item="code" open="(" close=")" separator=" or " >
                  no like CONCAT(#{code}, '%')
              </foreach>
        ) t
        where rk = 1
    </select>
</mapper>
