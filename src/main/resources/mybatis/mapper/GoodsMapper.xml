<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.abcyun.cis.goods.mapper.GoodsMapper">

    <resultMap type="cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem" id="GoodsItemMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="shortId" column="short_id" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="py" column="py" jdbcType="VARCHAR"/>
        <result property="organId" column="organ_id" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="subType" column="sub_type" jdbcType="INTEGER"/>
        <result property="barCode" column="bar_code" jdbcType="VARCHAR"/>
        <result property="manufacturer" column="manufacturer" jdbcType="VARCHAR"/>
        <result property="manufacturerFull" column="manufacturer_full" jdbcType="VARCHAR"/>
        <result property="pieceNum" column="piece_num" jdbcType="INTEGER"/>
        <result property="pieceUnit" column="piece_unit" jdbcType="VARCHAR"/>
        <result property="packageUnit" column="package_unit" jdbcType="VARCHAR"/>
        <result property="dismounting" column="dismounting" jdbcType="INTEGER"/>
        <result property="medicineCadn" column="medicine_cadn" jdbcType="VARCHAR"/>
        <result property="medicineNmpn" column="medicine_nmpn" jdbcType="VARCHAR"/>
        <result property="medicineDosageNum" column="medicine_dosage_num" jdbcType="DECIMAL"/>
        <result property="medicineDosageUnit" column="medicine_dosage_unit" jdbcType="VARCHAR"/>
        <result property="medicineDosageForm" column="medicine_dosage_form" jdbcType="VARCHAR"/>
        <result property="materialSpec" column="material_spec" jdbcType="VARCHAR"/>
        <result property="cMSpec" column="c_m_spec" jdbcType="VARCHAR"/>
        <result property="extendSpec" column="extend_spec" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="isSell" column="is_sell" jdbcType="INTEGER"/>
        <result property="disable" column="disable" jdbcType="INTEGER"/>
        <result property="piecePrice" column="piece_price" jdbcType="DECIMAL"/>
        <result property="packagePrice" column="package_price" jdbcType="DECIMAL"/>
        <result property="packageCostPrice" column="package_cost_price" jdbcType="DECIMAL"/>
        <result property="origin" column="origin" jdbcType="VARCHAR"/>
        <result property="certificateName" column="certificate_name" jdbcType="VARCHAR"/>
        <result property="certificateNo" column="certificate_no" jdbcType="VARCHAR"/>
        <result property="inTaxRat" column="in_tax_rat" jdbcType="DECIMAL"/>
        <result property="outTaxRat" column="out_tax_rat" jdbcType="DECIMAL"/>
        <result property="createdUserId" column="created_user_id" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="DATE"/>
        <result property="lastModifiedUserId" column="last_modified_user_id" jdbcType="VARCHAR"/>
        <result property="lastModifiedDate" column="last_modified_date" jdbcType="DATE"/>
        <result property="needExecutive" column="need_executive" jdbcType="INTEGER"/>
        <result property="typeId" column="type_id" jdbcType="INTEGER"/>
        <result property="customTypeId" column="custom_type_id" jdbcType="INTEGER"/>
        <result property="combineType" column="combine_type" jdbcType="INTEGER"/>
        <result property="bizExtensions" column="biz_extensions" jdbcType="VARCHAR"
                typeHandler="cn.abcyun.cis.goods.handler.JsonNodeTypeHandler"/>
        <result property="enName" column="en_name" jdbcType="VARCHAR"/>
        <result property="defaultInOutTax" column="default_in_out_tax" jdbcType="INTEGER"/>
        <result property="goodsVersion" column="goods_version" jdbcType="INTEGER"/>
        <result property="bizRelevantId" column="biz_relevant_id" jdbcType="VARCHAR"/>
        <result property="componentContentNum" column="component_content_num" jdbcType="DECIMAL"/>
        <result property="componentContentUnit" column="component_content_unit" jdbcType="VARCHAR"/>
        <result property="specType" column="spec_type" jdbcType="INTEGER"/>
        <result property="deviceType" column="device_type" jdbcType="INTEGER"/>
        <result property="processPrice" column="process_price" jdbcType="DECIMAL"/>
        <result property="feeComposeType" column="fee_compose_type" jdbcType="INTEGER"/>
        <result property="feeTypeId" column="fee_type_id" jdbcType="BIGINT"/>
        <result property="inspectionSite" column="inspection_site" jdbcType="INTEGER"/>
    </resultMap>

    <select id="findExamExistGoods" resultType="cn.abcyun.cis.goods.dto.ExamGoods">
        SELECT
        id ,
        name,
        package_unit as packageUnit,
        en_name as enName,
        biz_relevant_id as bizRelevantId,
        medicine_cadn as medicineCadn,
        status ,
        disable ,
        type ,
        sub_type as subType
        FROM v2_goods
        WHERE organ_id =#{chainId} AND status = 1 AND type = 3 AND sub_type= 1
        <if test="deviceModelId != null and deviceModelId != '' ">AND biz_relevant_id = #{deviceModelId}</if>
        <if test="composeGoodsName != ''  and  enName != '' ">AND ( name = #{composeGoodsName} OR en_name = #{enName}
            )
        </if>
        <if test="composeGoodsName != ''  and  enName == '' ">AND name = #{composeGoodsName}</if>
        <if test="composeGoodsName == ''  and  enName !='' ">AND en_name = #{enName}</if>
    </select>
    <select id="findEyeInspectExistGoods" resultType="cn.abcyun.cis.goods.dto.ExamGoods">
        SELECT
        id ,
        name,
        package_unit as packageUnit,
        biz_relevant_id as bizRelevantId,
        medicine_cadn as medicineCadn,
        status ,
        disable ,
        type ,
        sub_type as subType
        FROM v2_goods
        WHERE organ_id =#{chainId} AND status = 1 AND type = 3 AND sub_type= 2
        AND name in
        <foreach collection="composeGoodsName" item="item" open="(" separator=" , " close=")">
            #{item}
        </foreach>
    </select>
    <select id="findSurgeryExistGoods" resultType="cn.abcyun.cis.goods.dto.ExamGoods">
        SELECT
        a.id ,
        a.name,
        package_unit as packageUnit,
        a.medicine_cadn as medicineCadn,
        a.status ,
        a.disable ,
        a.type ,
        a.sub_type as subType,
        ifnull(b.shebao_code_national_code, '') shebaoCode,
        ifnull(a.package_unit, '') packageUnit
        FROM v2_goods a
        left join v2_goods_extend b on a.id = b.goods_id and b.organ_id = #{clinicId}
        WHERE a.organ_id =#{chainId} AND a.status = 1 AND a.type = 29
        <if test="innerFlag != null ">AND a.inner_flag = #{innerFlag}</if>
        AND a.name in
        <foreach collection="composeGoodsName" item="item" open="(" separator=" , " close=")">
            #{item}
        </foreach>
    </select>
    <select id="findFeeExistGoods" resultType="cn.abcyun.cis.goods.dto.ExamGoods">
        SELECT
        a.id ,
        a.name,
        package_unit as packageUnit,
        a.medicine_cadn as medicineCadn,
        a.status ,
        a.disable ,
        a.type ,
        a.sub_type as subType,
        ifnull(b.shebao_code_national_code, '') shebaoCode,
        ifnull(b.shebao_code_national_code_id, '') shebaoCodeNationalCodeId,
        b.shebao_pay_mode as shebaoPayMode,
        ifnull(b.shebao_code_national_code_id, '') shebaoCodeNationalCodeId,
        JSON_UNQUOTE(JSON_EXTRACT(shebao_extend_info, '$.nanJingCenterCode') ) centerCode,
        b.shebao_limit_price_rule_type as shebaoLimitPriceRuleType,
        b.shebao_limit_price_rule as shebaoLimitPriceRuleStr,
        ifnull(a.package_unit, '') packageUnit
        FROM v2_goods a
        left join v2_goods_extend b on a.id = b.goods_id and b.organ_id = #{clinicId}
        WHERE a.organ_id =#{chainId} AND a.status = 1 AND a.type = 19
        <if test="innerFlag != null ">AND a.inner_flag = #{innerFlag}</if>
        AND a.name in
        <foreach collection="composeGoodsName" item="item" open="(" separator=" , " close=")">
            #{item}
        </foreach>
    </select>
    <select id="findFeeExistGoodsByCode" resultType="cn.abcyun.cis.goods.dto.ExamGoods">
        SELECT
        a.id ,
        a.name,
        a.package_unit as packageUnit,
        a.medicine_cadn as medicineCadn,
        a.status ,
        a.disable ,
        a.type ,
        a.sub_type as subType,
        ifnull(b.shebao_code_national_code, '') shebaoCode,
        ifnull(b.shebao_code_national_code_id, '') shebaoCodeNationalCodeId,
        b.shebao_pay_mode as shebaoPayMode,
        b.shebao_limit_price_rule_type as shebaoLimitPriceRuleType,
        b.shebao_limit_price_rule as shebaoLimitPriceRuleStr,
        ifnull(b.shebao_code_national_code_id, '') shebaoCodeNationalCodeId,
        JSON_UNQUOTE(JSON_EXTRACT(shebao_extend_info, '$.nanJingCenterCode')) centerCode,
        ifnull(a.package_unit, '') packageUnit
        FROM v2_goods a
        join v2_goods_extend b on a.id = b.goods_id and b.organ_id = #{clinicId} and a.organ_id = b.chain_id
        WHERE a.organ_id =#{chainId} AND a.status = 1 AND a.type = 19
        <if test="innerFlag != null ">AND a.inner_flag = #{innerFlag}</if>
        AND b.shebao_code_national_code in
        <foreach collection="shebaoCodes" item="item" open="(" separator=" , " close=")">
            #{item}
        </foreach>
    </select>
    <select id="findAllFeeExistGoods" resultType="cn.abcyun.cis.goods.dto.ExamGoods">
        SELECT
        a.id ,
        a.name,
        package_unit as packageUnit,
        a.medicine_cadn as medicineCadn,
        a.status ,
        a.disable ,
        a.type ,
        a.sub_type as subType,
        ifnull(b.shebao_code_national_code, '') shebaoCode,
        b.organ_id as clinicId,
        ifnull(a.package_unit, '') packageUnit
        FROM v2_goods a
        left join v2_goods_extend b on a.id = b.goods_id and b.chain_id = a.organ_id
        WHERE a.organ_id =#{chainId} AND a.status = 1 AND a.type = 19
        <if test="innerFlag != null ">AND a.inner_flag = #{innerFlag}</if>
    </select>
    <!-- 2025.06 药君堂存在很多同名的中药，这里通过goodsStat来限定下门店有的药品 ,因为goodsStat里面是多药房，药房号要存进来-->
    <select id="findChineseMedcineByEqual" resultType="cn.abcyun.cis.goods.dto.FindChineseGoods">
        SELECT
        g.id ,
        g.name,
        g.medicine_cadn as medicineCadn,
        g.piece_unit as pieceUnit,
        g.status ,
        g.disable
        FROM v2_goods as g inner join v2_goods_stat as s on
        g.id = s.goods_id
        AND g.organ_id = s.chain_id
        AND s.clinic_id = #{clinicId}
        AND s.pharmacy_no = #{pharmacyNo}
        <if test="typeIdList != null and typeIdList.size() > 0  ">
            AND g.type_id IN
            <foreach collection="typeIdList" item="item" open="(" separator=" , " close=")">#{item}</foreach>
        </if>
        WHERE g.organ_id =#{chainId} AND g.status = 1
        AND g.medicine_cadn IN
        <foreach collection="cadnList" item="item" open="(" separator=" , " close=")">#{item}</foreach>
    </select>
    <select id="findChineseMedcineCadnByEqual" resultType="cn.abcyun.cis.goods.dto.FindChineseGoods">
        SELECT
        id ,
        name,
        medicine_cadn as medicineCadn,
        piece_unit as pieceUnit,
        status ,
        disable
        FROM v2_goods
        WHERE organ_id = #{chainId} AND status = 1
        AND type_id IN
        <foreach collection="typeIdList" item="item" open="(" separator=" , " close=")">#{item}</foreach>
        AND medicine_cadn IN
        <foreach collection="cadnList" item="item" open="(" separator=" , " close=")">#{item}</foreach>
    </select>
    <select id="findChineseNameByEqual" resultType="cn.abcyun.cis.goods.dto.FindChineseGoods">
        SELECT
        id ,
        name,
        medicine_cadn as medicineCadn,
        piece_unit as pieceUnit,
        status ,
        disable
        FROM v2_goods
        WHERE organ_id =#{chainId} AND status = 1
        AND type_id IN
        <foreach collection="typeIdList" item="item" open="(" separator=" , " close=")">#{item}</foreach>
        AND name IN
        <foreach collection="cadnList" item="item" open="(" separator=" , " close=")">#{item}</foreach>
    </select>
    <select id="findChineseMedcineByTrimedCadnContains" resultType="cn.abcyun.cis.goods.dto.FindChineseGoods">
        SELECT
        g.id ,
        g.name,
        g.medicine_cadn as medicineCadn,
        g.piece_unit as pieceUnit,
        g.status ,
        g.disable
        FROM v2_goods as g inner join v2_goods_stat as s on
        g.id = s.goods_id
        AND g.organ_id = s.chain_id
        AND s.clinic_id = #{clinicId}
        AND s.pharmacy_no = #{pharmacyNo}
        <if test="typeIdList != null and typeIdList.size() > 0  ">
            AND g.type_id IN
            <foreach collection="typeIdList" item="item" open="(" separator=" , " close=")">#{item}</foreach>
        </if>
        WHERE g.organ_id =#{chainId} AND g.status = 1
        AND
        <foreach collection="cadnList" item="item" open="  (" separator=" OR " close=")">
            g.medicine_cadn LIKE CONCAT('%',#{item},'%')
        </foreach>
    </select>


    <select id="getSupplierByStockInIdList" resultType="cn.abcyun.cis.goods.dto.GoodsSupplierDto">
        SELECT a.id as keyId,
        c.id as id,
        c.name as name,
        c.created as created
        FROM v2_goods_stock_in a
        LEFT JOIN v2_goods_stock_in_order b
        ON a.order_id = b.id AND b.chain_id = #{chainId}
        LEFT JOIN v2_goods_supplier c
        ON b.supplier_id = c.id AND c.chain_id = #{chainId}
        WHERE a.id IN
        <foreach collection="stockInIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getSupplierByStockInOrderIdList" resultType="cn.abcyun.cis.goods.dto.GoodsSupplierDto">

        SELECT a.id as keyId,
        c.id as id,
        c.name as name,
        c.created as created
        FROM v2_goods_stock_in_order a
        LEFT JOIN v2_goods_supplier c
        ON a.supplier_id = c.id AND c.chain_id = #{chainId} AND a.chain_id = #{chainId}
        WHERE a.id IN
        <foreach collection="stockInOrderIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="lastTimeChangePieceNum" resultType="java.time.Instant">
        SELECT min(a.created_date) as lastTime
        FROM v2_goods_log as a
                 INNER JOIN v2_goods as b
                            ON b.id = a.goods_id
                                AND b.piece_num = a.piece_num
        WHERE a.goods_id = #{goodsId}
          AND a.created_date > ifnull((SELECT MAX(c.created_date) as lastPackagingTime
                                       FROM v2_goods_log as c
                                                INNER JOIN v2_goods as d
                                                           ON d.id = c.goods_id
                                                               AND d.piece_num &lt;&gt; c.piece_num
                                       WHERE c.goods_id = #{goodsId}), 0)
    </select>
    <select id="lastOrderCheckTime" resultType="cn.abcyun.cis.goods.dto.ClinicLastCheckTimeDto">
        SELECT
        b.organ_id as clinicId,
        MAX(a.created_date) AS checkTime
        FROM v2_goods_stock_check AS a
        INNER JOIN v2_goods_stock_check_order AS b
        ON b.id = a.order_id
        AND b.organ_id IN
        <foreach collection="clinicIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        WHERE
        a.goods_id = #{goodsId}
        GROUP By b.organ_id
    </select>


    <select id="findAffectClinicEmployees" resultType="cn.abcyun.cis.goods.dto.EmployeeNeedToPushOrganList">
        SELECT
        a.employeeId,
        group_concat(a.clinicName) as clinicNames
        FROM (
        SELECT DISTINCT
        a.employee_id as employeeId,
        ifnull(b.short_name, b.name) as clinicName
        FROM clinic_employee as a
        INNER JOIN organ as b
        ON b.id = a.clinic_id
        AND b.id IN
        <foreach collection="clinicIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        WHERE
        (a.role_id = 1
        OR (
        a.role_id = 2
        AND a.module_ids regexp '(^|,)8(,|$)' ))
        AND a.status &lt; 90
        ) AS a
        GROUP BY employeeId
    </select>


    <!--
         凌晨同步类型  库存类型(1,2,7,24)
                     项目类型(3,4 检查检验只是为了兼容)
                     费用类型(19 项目类型会映射多个费用，每个费用对不同码)
         1.三合一过后，对码都对到费用项上
         2.收费项的码如何映射到项目上？  要按类型建社保变更单？
         3.多个收费项的码如何映射到项目上？
    -->
    <select id="findQuerySheBaoStatusGoodsId" resultType="cn.abcyun.cis.goods.dto.list.QueryGoodsSheBaoMatchStatusDto">
        SELECT
        id as goodsId,
        type as goodsType,
        name as name,
        sub_type as goodsSubType,
        type_id as goodsTypeId,
        medicine_cadn as medicineCadn,
        medicine_nmpn as medicineNmpn,
        medicine_dosage_num as medicineDosageNum,
        medicine_dosage_form as medicineDosageForm,
        medicine_dosage_unit as medicineDosageUnit,
        piece_num as pieceNum,
        piece_unit as pieceUnit,
        package_unit as packageUnit,
        c_m_spec as goodsCMSpec,
        extend_spec as extendSpec,
        manufacturer as manufacturer,
        manufacturer_full as manufacturerFull,
        fee_compose_type as feeComposeType ,
        compose_flag as composeFlag
        FROM v2_goods
        <!-- 21 护理 25 会诊 -->
        WHERE type in (1,2,7,24,3,4,19,21,25) and organ_id = #{chainId} and status &lt; 90
        <if test="offset != null and offset gt -1 and limit != null and limit gt -1">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="fixLoadChineseGoodsByAliasNames" resultType="cn.abcyun.cis.goods.dto.FixLoadChineneGoodsAliasDto">
        SELECT
        id as goodsId,
        name as name,
        medicine_cadn as medicineCadn
        FROM v2_goods
        WHERE organ_id =#{chainId} AND type = 1 and sub_type = 2 and status = 1 and disable = 0
        <if test="cMSpec !=null and cMSpec != '' ">AND c_m_spec = #{cMSpec}</if>
        AND (
        <foreach item="item" index="index" collection="aliasNameList" open=" (" separator=" )OR(  " close=" )">
            medicine_cadn =#{item} OR medicine_cadn LIKE CONCAT('%',#{item},'%')
        </foreach>
        )

    </select>
    <select id="findGoodsComposeList" resultType="cn.abcyun.cis.goods.dto.goods.ComposeDto">
        SELECT clinic_id as clinicId, goods_id as goodsId, parent_goods_id as parentGoodsId,compose_type as composeType
        FROM v2_goods_compose_opt as a
        WHERE a.chain_id = #{chainId}
        and a.compose_type = 10
        AND a.goods_id IN
        <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
    <select id="findGoodsNestEsMessage" resultType="cn.abcyun.cis.goods.amqp.upsertes.ESNestedGoods">
        SELECT
        id as goodsId,
        short_id as shortId,
        status,
        name,
        short_id as shortIdFuzzy,
        name as nameFuzzy,
        medicine_cadn as medicineCadnFuzzy,
        en_name as enNameFuzzy,
        organ_id as chainId,
        type as type,
        sub_type as subType,
        c_m_spec as CMSpec,
        bar_code as barCode,
        manufacturer,
        medicine_cadn as medicineCadn,
        medicine_nmpn as medicineNmpn,
        is_sell as isSell,
        type_id as typeId,
        custom_type_id as customTypeId,
        combine_type as combineType,
        inorder_config as inorderConfig,
        sell_config as sellConfig,
        disable,
        en_name as enName
        FROM v2_goods as a
        WHERE a.organ_id = #{chainId}
        AND a.id IN
        <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
    <!-- GoodsExtend里面的同步标记位只是告知是否需要同步，有可能那条记录不在需要同步过去了-->
    <select id="findGoodsNestEsExtendMessage" resultType="cn.abcyun.cis.goods.amqp.upsertes.ESNestedGoodsExtend">
        SELECT
        goods_id as goodsId,
        organ_id as clinicId,
        inorder_config as inorderConfig,
        sell_config as sellConfig,
        disable
        FROM v2_goods_extend as a
        WHERE
        a.chain_id = #{chainId}
        AND a.goods_id IN
        <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND ( a.disable != 0 OR a.inorder_config != 0 OR a.sell_config !=0)
    </select>
    <!-- GoodsExtend里面的同步标记位只是告知是否需要同步，有可能那条记录不在需要同步过去了-->
    <!--
     1.这里需要把非聚合的写到ES里面
     -->
    <select id="findGoodsNestEsStatMessage" resultType="cn.abcyun.cis.goods.amqp.upsertes.ESNestedGoodsStat">
        SELECT
        goods_id as goodsId,
        clinic_id as clinicId,
        es_inorder as esInorder,
        es_hasstock as esHasstock,
        pharmacy_no as pharmacyNo
        FROM v2_goods_stat as a
        WHERE
        a.chain_id = #{chainId}
        AND a.goods_id IN
        <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND a.clinic_id != ''
        AND a.pharmacy_no != -1
    </select>
    <!-- GoodsExtend里面的同步标记位只是告知是否需要同步，有可能那条记录不在需要同步过去了-->
    <select id="findGoodsNestEsDiseaseMessage" resultType="cn.abcyun.cis.goods.amqp.upsertes.ESNestedGoodsDiseaseDto">
        SELECT
        goods_id as goodsId,
        disease_usage_info as disease,
        count as count
        FROM v2_goods_disease_usage_info as a
        WHERE
        a.chain_id = #{chainId}
        AND a.goods_id IN
        <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findGoodsNestEsTagMessage" resultType="cn.abcyun.cis.goods.amqp.upsertes.ESNestedGoodsTag">
        SELECT
        clinic_id as clinicId,
        tag_id as tagId
        FROM v2_goods_tag as a
        WHERE
        a.chain_id = #{chainId}
        AND a.goods_id IN
        <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND a.is_deleted = 0
    </select>

    <select id="getNeedUpdateGoodsInOutTaxRatGoodsIdList" resultType="java.lang.String">
        Select id
        from v2_goods
        WHERE organ_id = #{chainId}
          and default_in_out_tax = 1
          and type_id = #{typeId}
          and status = 1
          and (in_tax_rat is null or in_tax_rat != #{inTaxRat} or out_tax_rat is null or out_tax_rat != #{outTaxRat})
    </select>
    <update id="updateGoodsStockPieceNum">
        UPDATE v2_goods_stock
        set piece_num = #{pieceNum}
        WHERE chain_id = #{chainId}
          AND goods_id = #{goodsId}
          and (piece_count > 0 or package_count > 0)
    </update>
    <update id="updateGoodsStockInPieceNum">
        UPDATE v2_goods_stock_in
        set piece_num = #{pieceNum}
        WHERE chain_id = #{chainId}
          AND goods_id = #{goodsId}
    </update>
    <update id="updateGoodsInOutTaxRat">
        UPDATE v2_goods
        SET in_tax_rat = #{inTaxRat},out_tax_rat = #{outTaxRat}
        WHERE organ_id =#{chainId} and default_in_out_tax = 1 and type_id = #{typeId} and status = 1
        AND id IN
        <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="updateSpuGoodsInOutTaxRat">
        UPDATE v2_goods_spu
        SET in_tax_rat  = #{inTaxRat},
            out_tax_rat = #{outTaxRat}
        WHERE chain_id = #{chainId}
          and default_in_out_tax = 1
          and type = #{type}
          and sub_type = #{subType}
          and status = 1
    </update>
    <!--    <delete id="deleteGoodsSubPrice">-->
    <!--        DELETE-->
    <!--        FROM v2_goods_price_opt-->
    <!--        WHERE clinic_id = #{clinicId}-->
    <!--        <if test="isAbcPharmacy==1 ">-->
    <!--            and individual_pricing_type is null or individual_pricing_type = 0-->
    <!--        </if>-->

    <!--        <if test="cleanPriceMakeUp==1 ">-->
    <!--            and price_type = 3-->
    <!--        </if>-->

    <!--        limit 200-->
    <!--    </delete>-->
    <update id="updateGoodsComposeSubPrice">
        update v2_goods_compose_price_opt
        set is_deleted       = 1,
            last_modified_by = #{employeeId},
            last_modified    = now()
        WHERE chain_id = #{chainId}
          and organ_id = #{clinicId}
          and is_deleted = 0 limit 200;
    </update>
    <update id="updateGoodsStatSubPrice">
        UPDATE v2_goods_stat stat inner join v2_goods g
        on stat.goods_id = g.id and stat.chain_id = g.organ_id and
            g.organ_id = #{chainId} and stat.clinic_id = #{clinicId} and
            g.type in (1, 2, 7, 24)
            SET stat.piece_price = g.piece_price, stat.package_price = g.package_price, stat.price_type = g.price_type, stat.price_makeup_percent = g.price_makeup_percent
        WHERE g.organ_id = #{chainId};
    </update>
    <update id="updateGoodsExtendFeeComposeType">
        update v2_goods_extend
        set fee_compose_type = null,
            last_modified    = now(),
            last_modified_by = #{employeeId}
        where chain_id = #{chainId}
          and organ_id = #{clinicId}
          and fee_compose_type is not null limit 200;
    </update>
    <update id="updateGoodsSubComposeOpt">
        update v2_goods_compose_opt
        set is_deleted            = 1,
            last_modified_date    = now(),
            last_modified_user_id = #{employeeId}
        where chain_id = #{chainId}
          and clinic_id = #{clinicId}
          and is_deleted = 0
          and compose_type = 10 limit 200;
    </update>
    <update id="updateGoodsSubComposePriceOpt">
        update v2_goods_compose_price_opt
        set is_deleted       = 1,
            last_modified    = now(),
            last_modified_by = #{employeeId}
        where chain_id = #{chainId}
          and organ_id = #{clinicId}
          and compose_type = 10
    </update>
    <update id="updateGoodsStatHasModifyPriceOrder">
        update v2_goods_stat as a left join v2_goods_modify_price_order_item as b
        on a.chain_id = b.chain_id
        and a.goods_id = b.goods_id
        and a.chain_id = #{chainId}
        and b.status = 0
        and b.is_deleted = 0
        set a.waiting_effect_update_price_item_id = b.id
        where a.chain_id = #{chainId}
        <if test="goodsIdList != null and goodsIdList.size() > 0  ">
            AND a.goods_id IN
            <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="clinicIdList != null and clinicIdList.size() > 0  ">
            AND a.clinic_id IN
            <foreach collection="clinicIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
    <select id="loadChainGoodsIdList" resultType="java.lang.String">
        SELECT id
        from v2_goods
        WHERE organ_id = #{chainId}
          and status = 1
        ORDER By id
            LIMIT #{offset}, #{limit};
    </select>
    <select id="loadChainStockGoodsIdList" resultType="java.lang.String">
        SELECT id
        from v2_goods
        WHERE organ_id = #{chainId}
          and status = 1
          and type in (1, 2, 7, 24)
            LIMIT #{offset}
            , #{limit};
    </select>
    <select id="loadChainAllGoodsIdList" resultType="java.lang.String">
        SELECT id
        from v2_goods
        WHERE organ_id = #{chainId}
          and status = 1
    </select>
    <select id="loadChainAllSpuGoodsIdList" resultType="java.lang.Long">
        SELECT id
        from v2_goods_spu
        WHERE chain_id = #{chainId}
    </select>
    <update id="updateClinicDispenseMachine">
        update v2_goods_extend extend inner join v2_goods as g
        on extend.chain_id = g.organ_id and
            extend.organ_id = #{clinicId} and
            g.status = 1 and g.type_id = 15 and
            extend.smart_dispense = 1 and g.organ_id = #{chainId}
            set smart_dispense_machine_no = #{no}
        where extend.chain_id = #{chainId};
    </update>
    <select id="loadHisGoodsVersionList" resultType="cn.abcyun.cis.goods.model.GoodsHistoryVersion">
        SELECT id            as id,
               chain_id      as chainId,
               goods_id      as goodsId,
               goods_version as goodsVersion,
               goods         as goodsStr
        FROM v2_goods_history_version as a
        WHERE chain_id = #{chainId}
          AND ${dbOrderByString}
    </select>
    <select id="findShebaoReportGoodsList" resultType="cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem">
        SELECT
        b.id as id,
        b.type as type,
        b.sub_type as subType,
        b.c_m_spec as cMSpec,
        b.type_id as typeId,
        b.custom_type_id as customTypeId,
        b.short_id as shortId,
        b.name as name,
        b.medicine_cadn as medicineCadn,
        b.extend_spec as extendSpec,
        b.disable as disable,
        b.dismounting as dismounting,
        b.is_sell as isSell,
        b.combine_type as combineType,
        b.status as status,

        b.piece_unit as pieceUnit,
        b.package_unit as packageUnit,
        b.piece_price as piecePrice,
        b.package_price as packagePrice,
        b.piece_price as piecePrice,
        b.package_cost_price as packageCostPrice,

        b.piece_num as pieceNum,
        b.bar_code as barCode,
        b.manufacturer as manufacturer,
        b.manufacturer_full as manufacturerFull,
        b.medicine_nmpn as medicineNmpn,
        b.material_spec as materialSpec,
        b.medicine_dosage_num as medicineDosageNum,
        b.medicine_dosage_unit as medicineDosageUnit,
        b.medicine_dosage_form as medicineDosageForm,
        b.created_date as createdDate,
        b.last_modified_date as lastModifiedDate,
        b.dosage_form_type as dosageFormType
        from v2_goods as b
        WHERE b.organ_id = #{chainId}
        <if test="includeDeleted == 0">
            AND b.status = 1
        </if>
        <if test="typeList != null and typeList.size() > 0  ">
            AND b.type IN
            <foreach collection="typeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dateField != null  ">
            <if test="dateField == 'createdDate'.toString()  ">
                <choose>
                    <when test="beginDate != null and  endDate != null ">
                        and b.created_date &gt; #{beginDate} and b.created_date &lt; #{endDate}
                    </when>
                    <when test="beginDate != null and  endDate == null ">
                        and b.created_date &gt; #{beginDate}
                    </when>
                    <when test="beginDate == null and  endDate != null ">
                        and b.created_date &lt; #{endDate}
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test="dateField == 'lastModified'.toString()  ">
                <choose>
                    <when test="beginDate != null and  endDate != null ">
                        and b.last_modified_date &gt; #{beginDate} and b.last_modified_date &lt; #{endDate}
                    </when>
                    <when test="beginDate != null and  endDate == null ">
                        and b.last_modified_date &gt; #{beginDate}
                    </when>
                    <when test="beginDate == null and  endDate != null ">
                        and b.last_modified_date &lt; #{endDate}
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
        </if>
        <if test="typeIds != null and typeIds.size() > 0  ">
            AND b.type_id IN
            <foreach collection="typeIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="keyword != null and keyword !=''">
            AND (b.name LIKE CONCAT('%', #{keyword}, '%')
            OR b.py LIKE CONCAT('%', #{keyword}, '%')
            OR b.medicine_cadn LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER By b.id
        LIMIT #{offset}, #{limit};
    </select>
    <select id="countShebaoReportGoodsList" resultType="java.lang.Integer">
        SELECT
        count(*)
        from v2_goods as b
        WHERE b.organ_id = #{chainId}
        <if test="includeDeleted == 0">
            AND b.status = 1
        </if>
        <if test="typeList != null and typeList.size() > 0  ">
            AND b.type IN
            <foreach collection="typeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dateField != null  ">
            <if test="dateField == 'createdDate'.toString()  ">
                <choose>
                    <when test="beginDate != null and  endDate != null ">
                        and b.created_date &gt; #{beginDate} and b.created_date &lt; #{endDate}
                    </when>
                    <when test="beginDate != null and  endDate == null ">
                        and b.created_date &gt; #{beginDate}
                    </when>
                    <when test="beginDate == null and  endDate != null ">
                        and b.created_date &lt; #{endDate}
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test="dateField == 'lastModified'.toString()  ">
                <choose>
                    <when test="beginDate != null and  endDate != null ">
                        and b.last_modified_date &gt; #{beginDate} and b.last_modified_date &lt; #{endDate}
                    </when>
                    <when test="beginDate != null and  endDate == null ">
                        and b.last_modified_date &gt; #{beginDate}
                    </when>
                    <when test="beginDate == null and  endDate != null ">
                        and b.last_modified_date &lt; #{endDate}
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
        </if>
    </select>
    <!-- 按最近供应商改价格  按供应商找到最后一个入库单，拿到这个入库单的goodsID 再按类型过滤 找到最后供应商的入库单ID-->
    <select id="findTheSupplerLastInOrderId" resultType="java.lang.Long">
        select id from v2_goods_stock_in_order
        where chain_id=#{chainId}
        <if test="clinicId != null and clinicId != '' ">
            AND to_organ_id = #{clinicId}
        </if>
        and supplier_id=#{supplierId} order by id desc limit 1
    </select>
    <!-- 为了不至于sql太复杂，子店的禁用在外部加载goods的时候再检查，子店只加载-->
    <select id="findBatchUpdatePriceGoodsIdList" resultType="java.lang.String">
        SELECT distinct(b.goods_id) from
        v2_goods_stat as b
        <if test=" (customTypeIdList != null and customTypeIdList.size() > 0) or (otherCustomTypeTypeIdList != null and otherCustomTypeTypeIdList.size() > 0)">
            inner join v2_goods as a on a.organ_id = b.chain_id and a.id = b.goods_id and a.organ_id = #{chainId}
        </if>
        WHERE
        b.chain_id = #{chainId} and b.is_sell = 1
        <choose>
            <when test="clinicId !=null and  clinicId != '' ">and b.clinic_id =#{clinicId}</when>
            <otherwise>and b.clinic_id = ''</otherwise>
        </choose>
        <if test="supplierNames !=null and supplierNames.size() > 0">
            and b.last_stock_in_order_supplier in
            <foreach collection="supplierNames" item="supplierName" open="(" separator="," close=")">#{supplierName}
            </foreach>
        </if>
        <if test="priceType !=null  ">and b.price_type = #{priceType}</if>
        <choose>
            <when test="pharmacyType !=null and  pharmacyType == 2 ">and b.pharmacy_type = #{pharmacyType}</when>
            <otherwise>and b.pharmacy_type = 0 and b.pharmacy_no = #{pharmacyNo}</otherwise>
        </choose>
        <if test="sheBaoOverPrice != null and sheBaoOverPrice.size() > 0">
            AND
            <foreach collection="sheBaoOverPrice" item="item" open="(" separator=" OR " close=")">
                <choose>
                    <when test="item == 1">
                        IFNULL(b.package_price &gt; b.shebao_code_national_current_price_limited,0)
                    </when>
                    <when test="item == 0">
                        IFNULL(b.package_price &lt; b.shebao_code_national_current_price_limited,0)
                    </when>
                </choose>
            </foreach>
        </if>

        <choose>
            <when test="(typeIdList != null and typeIdList.size() > 0) or  (customTypeIdList != null and customTypeIdList.size() > 0) or (otherCustomTypeTypeIdList != null and otherCustomTypeTypeIdList.size() > 0)">
                AND (
                <trim prefixOverrides="OR">
                    <if test="(typeIdList != null and typeIdList.size() > 0)">
                        OR b.type_id IN
                        <foreach collection="typeIdList" item="item" open="(" separator="," close=")">#{item}</foreach>
                    </if>
                    <if test="(customTypeIdList != null and customTypeIdList.size() > 0)">
                        OR a.custom_type_id IN
                        <foreach collection="customTypeIdList" item="item" open="(" separator="," close=")">#{item}
                        </foreach>
                    </if>
                    <if test="(otherCustomTypeTypeIdList != null and otherCustomTypeTypeIdList.size() > 0)">
                        OR (
                        <trim prefixOverrides="OR">
                            <foreach collection="otherCustomTypeTypeIdList" item="item" open="" separator=" " close="">
                                OR (b.type_id = #{item} and a.custom_type_id is null )
                            </foreach>
                        </trim>
                        )
                    </if>
                </trim>
                )
            </when>
            <otherwise>
                and b.goods_type in(1,2,7,24)
            </otherwise>
        </choose>
    </select>
    <!-- 只加载部分字段-->
    <select id="findGoodsListToStockIn" resultType="cn.abcyun.cis.goods.model.Goods">
        SELECT
        a.id,
        a.short_id,
        a.status,
        a.name,
        a.type,
        a.sub_type,
        a.bar_code,
        a.manufacturer,
        a.manufacturer_full,
        a.piece_num,
        a.piece_unit,
        a.package_unit,
        a.dismounting,
        a.medicine_cadn,
        a.medicine_nmpn,
        a.medicine_dosage_num,
        a.medicine_dosage_unit,
        a.material_spec,
        a.c_m_spec,
        a.extend_spec,
        a.piece_price,
        a.package_price,
        a.package_cost_price,
        a.disable,
        a.type_id,
        a.custom_type_id,
        a.inorder_config,
        a.sell_config,
        a.spec_type,
        a.component_content_num,
        a.component_content_unit
        FROM
        v2_goods as a
        WHERE a.organ_id = #{chainId} and status = 1 and disable = 0 and inorder_config = 0
        <choose>
            <when test="(typeIdList != null and typeIdList.size() > 0) or  (customTypeIdList != null and customTypeIdList.size() > 0) or (otherCustomTypeTypeIdList != null and otherCustomTypeTypeIdList.size() > 0)">
                AND (
                <trim prefixOverrides="OR">
                    <if test="(typeIdList != null and typeIdList.size() > 0)">
                        OR a.type_id IN
                        <foreach collection="typeIdList" item="item" open="(" separator="," close=")">#{item}</foreach>
                    </if>
                    <if test="(customTypeIdList != null and customTypeIdList.size() > 0)">
                        OR a.custom_type_id IN
                        <foreach collection="customTypeIdList" item="item" open="(" separator="," close=")">#{item}
                        </foreach>
                    </if>
                    <if test="(otherCustomTypeTypeIdList != null and otherCustomTypeTypeIdList.size() > 0)">
                        OR (
                        <trim prefixOverrides="OR">
                            <foreach collection="otherCustomTypeTypeIdList" item="item" open="" separator=" " close="">
                                OR (a.type_id = #{item} and a.custom_type_id is null )
                            </foreach>
                        </trim>
                        )
                    </if>
                </trim>
                )
            </when>
            <otherwise>
                and a.type in (1,2,7,24)
            </otherwise>
        </choose>
    </select>
    <select id="countGoodsListToStockCheck" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        v2_goods_stock as d
        <if test="(typeIdList != null and typeIdList.size() > 0) or  (customTypeIdList != null and customTypeIdList.size() > 0) or (otherCustomTypeTypeIdList != null and otherCustomTypeTypeIdList.size() > 0)">
            inner join v2_goods as a
            on a.id = d.goods_id and d.chain_id = a.organ_id and a.organ_id =#{chainId} and d.pharmacy_type
            =#{pharmacyType} and d.pharmacy_no =#{pharmacyNo}
        </if>
        WHERE d.chain_id = #{chainId} and d.organ_id = #{clinicId}
        <if test="isGroup == 0">
            <choose>
                <when test="activeMonth != null and activeMonth > 0">
                    and (((d.piece_count > 0 or d.package_count > 0)) OR (d.last_modified_date >= DATE_ADD(CURDATE(),
                    INTERVAL -
                    #{activeMonth} MONTH) and d.piece_count = 0 and d.package_count = 0))
                </when>
                <otherwise>
                    and (d.piece_count > 0 or d.package_count > 0)
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="(typeIdList != null and typeIdList.size() > 0) or  (customTypeIdList != null and customTypeIdList.size() > 0) or (otherCustomTypeTypeIdList != null and otherCustomTypeTypeIdList.size() > 0)">
                AND (
                <trim prefixOverrides="OR">
                    <if test="(typeIdList != null and typeIdList.size() > 0)">
                        OR a.type_id IN
                        <foreach collection="typeIdList" item="item" open="(" separator="," close=")">#{item}</foreach>
                    </if>
                    <if test="(customTypeIdList != null and customTypeIdList.size() > 0)">
                        OR a.custom_type_id IN
                        <foreach collection="customTypeIdList" item="item" open="(" separator="," close=")">#{item}
                        </foreach>
                    </if>
                    <if test="(otherCustomTypeTypeIdList != null and otherCustomTypeTypeIdList.size() > 0)">
                        OR (
                        <trim prefixOverrides="OR">
                            <foreach collection="otherCustomTypeTypeIdList" item="item" open="" separator=" " close="">
                                OR (a.type_id = #{item} and a.custom_type_id is null )
                            </foreach>
                        </trim>
                        )
                    </if>
                </trim>
                )
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>
    <!-- 只加载部分字段-->
    <select id="findGoodsListToStockCheck" resultType="cn.abcyun.cis.goods.dto.StockCheckGoodsDto">
        SELECT
        d.goods_id as goodsId,
        d.batch_id as batchId,
        d.batch_no as batchNo,
        d.expiry_date as expiryDate,
        d.package_cost_price as packageCostPrice,
        d.created_date as inDate,
        <if test=" isGroup == 1 ">
            IFNULL(SUM(d.package_count) ,0) as packageCount,
            IFNULL(SUM(d.piece_count) ,0) as pieceCount
        </if>
        <if test=" isGroup == 0 ">
            sum(ifnull(d.package_count, 0)) as packageCount,
            sum(ifnull(d.piece_count, 0)) as pieceCount
        </if>

        FROM
        v2_goods_stock as d
        <if test="(typeIdList != null and typeIdList.size() > 0) or  (customTypeIdList != null and customTypeIdList.size() > 0) or (otherCustomTypeTypeIdList != null and otherCustomTypeTypeIdList.size() > 0)">
            inner join v2_goods as a
            on a.id = d.goods_id and d.chain_id = a.organ_id and a.organ_id =#{chainId} and d.pharmacy_type
            =#{pharmacyType} and d.pharmacy_no =#{pharmacyNo}
        </if>
        <if test="sortPositionAsc != null ">
            left join v2_goods_pharmacy_extend as pe on d.organ_id = pe.clinic_id and d.goods_id = pe.goods_id and
            pe.pharmacy_no = d.pharmacy_no and pe.is_deleted = 0 and pe.pharmacy_no = #{pharmacyNo} and pe.clinic_id
            =#{clinicId}
        </if>
        WHERE d.chain_id = #{chainId} and d.organ_id = #{clinicId}
        <if test="isGroup == 0">
            <choose>
                <when test="activeMonth != null and activeMonth > 0">
                    and (((d.piece_count > 0 or d.package_count > 0)) OR (d.last_modified_date >= DATE_ADD(CURDATE(),
                    INTERVAL -
                    #{activeMonth} MONTH) and d.piece_count = 0 and d.package_count = 0))
                </when>
                <otherwise>
                    and (d.piece_count > 0 or d.package_count > 0)
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="(typeIdList != null and typeIdList.size() > 0) or  (customTypeIdList != null and customTypeIdList.size() > 0) or (otherCustomTypeTypeIdList != null and otherCustomTypeTypeIdList.size() > 0)">
                AND (
                <trim prefixOverrides="OR">
                    <if test="(typeIdList != null and typeIdList.size() > 0)">
                        OR a.type_id IN
                        <foreach collection="typeIdList" item="item" open="(" separator="," close=")">#{item}</foreach>
                    </if>
                    <if test="(customTypeIdList != null and customTypeIdList.size() > 0)">
                        OR a.custom_type_id IN
                        <foreach collection="customTypeIdList" item="item" open="(" separator="," close=")">#{item}
                        </foreach>
                    </if>
                    <if test="(otherCustomTypeTypeIdList != null and otherCustomTypeTypeIdList.size() > 0)">
                        OR (
                        <trim prefixOverrides="OR">
                            <foreach collection="otherCustomTypeTypeIdList" item="item" open="" separator=" " close="">
                                OR (a.type_id = #{item} and a.custom_type_id is null )
                            </foreach>
                        </trim>
                        )
                    </if>
                </trim>
                )
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="isGroup == 1 ">
            <if test="sortPositionAsc != null ">
                group by d.goods_id, pe.position
                <if test="sortPositionAsc == 1 ">
                    <!-- 只有5%的有柜号，但是要求柜号为空排后面，只能先 IF(pe.position is null 1,0) asc排一把，虽然95%都是空转-->
                    order by IF(pe.position is null ,1,0) asc, pe.position asc, d.goods_id desc
                </if>
                <if test="sortPositionAsc == 0 ">
                    <!-- 只有5%的有柜号，但是要求柜号为空排后面，只能先 IF(pe.position is null 1,0) asc排一把，虽然95%都是空转-->
                    order by IF(pe.position is null ,1,0) asc, pe.position desc, d.goods_id desc
                </if>
            </if>
            <if test="sortPositionAsc == null ">
                group by d.goods_id
                order by d.goods_id desc,d.id desc
            </if>

        </if>
        <if test="isGroup == 0 ">
            <if test="sortPositionAsc != null ">
                group by d.goods_id,d.batch_id,pe.position
                <if test="sortPositionAsc == 1 ">
                    <!-- 只有5%的有柜号，但是要求柜号为空排后面，只能先 IF(pe.position is null 1,0) asc排一把，虽然95%都是空转-->
                    order by IF(pe.position is null, 1,0) asc, pe.position asc, d.goods_id desc
                </if>
                <if test="sortPositionAsc == 0 ">
                    <!-- 只有5%的有柜号，但是要求柜号为空排后面，只能先 IF(pe.position is null 1,0) asc排一把，虽然95%都是空转-->
                    order by IF(pe.position is null, 1,0) asc, pe.position desc, d.goods_id desc
                </if>
            </if>
            <if test="sortPositionAsc == null ">
                group by d.goods_id,d.batch_id
                order by d.goods_id desc,d.id desc
            </if>
        </if>
        <if test="offset != null and offset gt -1 and limit != null and limit gt -1">
            LIMIT #{offset}, #{limit}
        </if>
    </select>
    <select id="findGoodsIdByShortId" resultType="cn.abcyun.cis.goods.dto.GoodsShortIdGoodsId">
        SELECT
        g.id as goodsId,
        g.short_id as shortId,
        dismounting as dismounting,
        piece_unit as pieceUnit,
        package_unit as packageUnit,
        package_price as packagePrice,
        piece_price as piecePrice,
        piece_num as pieceNum,
        type as type,
        sub_type as subType,
        type_id as typeId
        FROM v2_goods g
        WHERE g.organ_id = #{chainId} and g.status = 1
        <if test="shortIdList != null and shortIdList.size() > 0">
            AND g.short_id IN
            <foreach collection="shortIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findGoodsIdByImportShortId" resultType="cn.abcyun.cis.goods.dto.GoodsShortIdGoodsId">
        select g.id as goodsId,
        g.short_id as shortId,
        a.short_id as importShortId,
        g.dismounting as dismounting,
        g.piece_unit as pieceUnit,
        g.package_unit as packageUnit,
        g.package_price as packagePrice,
        g.piece_price as piecePrice,
        g.piece_num as pieceNum,
        g.type as type,
        g.sub_type as subType,
        g.type_id as typeId
        from v2_goods_import_shortid_mapping a
        inner join v2_goods g on (a.goods_id = g.id and g.status = 1)
        where a.chain_id = #{chainId}
        <if test="shortIds != null and shortIds.size() != 0">
            AND a.short_id IN
            <foreach collection="shortIds" item="shortId" open="(" separator="," close=")">
                #{shortId}
            </foreach>
        </if>
    </select>

    <select id="findGoodsIdByDrugIdentifyCode" resultType="cn.abcyun.cis.goods.dto.GoodsShortIdGoodsId">
        SELECT
        g.id as goodsId,
        g.drug_identification_code as drugIdentificationCode
        FROM v2_goods g
        WHERE g.organ_id = #{chainId} and g.status = 1
        <if test="identifyCodeList != null and identifyCodeList.size() > 0">
            AND g.drug_identification_code IN
            <foreach collection="identifyCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="findGoodsIdByBarCode" resultType="cn.abcyun.cis.goods.dto.GoodsShortIdGoodsId">
        SELECT
        g.id as goodsId,
        g.bar_code as barCode
        FROM v2_goods g
        WHERE g.organ_id = #{chainId} and g.status = 1
        <if test="barCodeList != null and barCodeList.size() > 0">
            AND g.bar_code IN
            <foreach collection="barCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="countGoodsTypeIdGoodsCount" resultType="cn.abcyun.cis.goods.dto.GoodsTypeIdCount">
        SELECT g.type_id   as typeId,
               count(g.id) as goodsCount
        FROM v2_goods g
        WHERE g.organ_id = #{chainId}
          and g.status = 1
          and g.disable = 0
          and g.inner_flag = 0
        GROUP by g.type_id
    </select>
    <select id="countGoodsCustomTypeIdGoodsCount" resultType="cn.abcyun.cis.goods.dto.GoodsCustomTypeIdCount">
        SELECT g.type_id        as typeId,
               g.custom_type_id as customTypeId,
               count(g.id)      as goodsCount
        FROM v2_goods g
        WHERE g.organ_id = #{chainId}
          and g.status = 1
          and g.disable = 0
          and g.inner_flag = 0
        GROUP by g.type_id, g.custom_type_id
    </select>
    <select id="countOneCustomTypeIdGoodsCount" resultType="java.lang.Integer">
        SELECT count(g.id) as goodsCount
        FROM v2_goods g
        WHERE g.organ_id = #{chainId}
          and g.status = 1
          and g.inner_flag = 0
          and g.custom_type_id = #{customTypeId}
    </select>
    <select id="getTypeIdInGoodsIdList" resultType="java.lang.String">
        Select id from v2_goods
        WHERE organ_id =#{chainId}
        <if test="typeIds != null and typeIds.size() > 0">
            and type_id in
            <foreach collection="typeIds" item="typeId" open="(" separator="," close=")">#{typeId}</foreach>
        </if>
        and status = 1
    </select>
    <select id="findInnerSystemGoodsList" resultType="cn.abcyun.cis.goods.model.InnerSystemGoods">
        SELECT a.id as id,
        a.his_type as hisType,
        a.chain_id as chainId,
        a.goods_id as goodsId,
        a.goods_type as goodsType,
        a.supplement_type as supplementType,
        a.use_count as useCount
        FROM v2_goods_inner_system_goods as a
        INNER JOIN v2_goods_medical_stat as s on a.chain_id = s.clinic_id and a.goods_id = s.goods_id and s.disable= 0
        and s.clinic_id = #{chainId}
        WHERE s.clinic_id =#{chainId} and a.his_type = #{hisType} and a.goods_type =#{goodsType} and
        a.goods_sub_type=#{goodsSubType} and a.is_deleted = 0
        <if test="supplementType != null ">AND a.supplement_type = #{supplementType}</if>
        <if test="deviceModelIds != null and deviceModelIds.size() > 0">
            AND
            <foreach open="(" separator="OR" close=")" collection="deviceModelIds" item="deviceModelId">
                json_contains(s.extend_info, json_quote(#{deviceModelId}), '$.bizRelevantIds')
            </foreach>
        </if>
        <if test="combineType != null ">AND s.combine_type = #{combineType}</if>
        ORDER by a.use_count desc
        LIMIT #{offset}, #{limit};

    </select>
    <select id="countInnerSystemGoodsList" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM v2_goods_inner_system_goods as a
        INNER JOIN v2_goods_medical_stat as s on a.chain_id = s.clinic_id and a.goods_id = s.goods_id and s.disable= 0
        and s.clinic_id = #{chainId}
        WHERE s.clinic_id =#{chainId} and a.his_type = #{hisType} and a.goods_type =#{goodsType} and
        a.goods_sub_type=#{goodsSubType} and a.is_deleted = 0
        <if test="supplementType != null ">AND a.supplement_type = #{supplementType}</if>
        <if test="deviceModelIds != null and deviceModelIds.size() > 0">
            AND
            <foreach open="(" separator="OR" close=")" collection="deviceModelIds" item="deviceModelId">
                json_contains(s.extend_info, json_quote(#{deviceModelId}), '$.bizRelevantIds')
            </foreach>
        </if>
        <if test="combineType != null ">AND s.combine_type = #{combineType}</if>
    </select>
    <select id="findMaxPharmacyNo" resultType="java.lang.Integer">
        SELECT max(no)
        FROM v2_goods_pharmacy as a
        WHERE a.chain_id = #{chainId}
          and a.clinic_id = #{clinicId}
    </select>
    <!-- 统计一个Goods 组成了几个套餐 -->
    <select id="findGoodsComposeCount" resultType="cn.abcyun.cis.goods.dto.FindGoodsComposeCount">
        SELECT
        goods_id as goodsId,
        count(IF(compose_type = 0 , 1 ,0)) as composeCount,
        count(IF(compose_type = 10 , 1 ,0)) as feeComposeCount,
        count(IF(compose_type = 40 , 1 ,0)) as goodsGroupComposeCount
        FROM v2_goods_compose_opt as a
        WHERE a.chain_id =#{chainId} and a.is_deleted = 0
        AND a.goods_id IN
        <foreach collection="goodsIdList" item="item" open="(" separator=" , " close=")">
            #{item}
        </foreach>
        group by a.goods_id
    </select>
    <select id="findGoodsAssociationCount" resultType="cn.abcyun.cis.goods.dto.FindGoodsComposeCount">
        SELECT
        goods_id as goodsId,
        count(IF(type = 0,1, 0)) as associationUsageCount,
        count(IF(type = 1,1, 0)) as associationGoodsCount
        FROM v2_goods_association_goods as a
        WHERE a.chain_id =#{chainId} and a.is_deleted = 0
        AND a.goods_id IN
        <foreach collection="goodsIdList" item="item" open="(" separator=" , " close=")">
            #{item}
        </foreach>
        GROUP BY a.goods_id
    </select>

    <select id="findMinPharmacyRuleSort" resultType="java.lang.Integer">
        SELECT min(sort)
        FROM v2_goods_pharmacy_rule as a
        WHERE a.chain_id = #{chainId}
          and a.clinic_id = #{clinicId}
          and a.inner_flag = 0
          and a.is_deleted = 0
    </select>
    <select id="findAllByOrganIdInAndTypeInAndSubTypeAndBizRelevantIdInAndStatusLessThan" resultMap="GoodsItemMap">
        SELECT a.id,
        a.domain_id,
        a.short_id,
        a.status,
        a.name,
        a.py,
        a.organ_id,
        a.type,
        a.sub_type,
        a.bar_code,
        a.manufacturer,
        a.manufacturer_full,
        a.piece_num,
        a.piece_unit,
        a.package_unit,
        a.dismounting,
        a.medicine_cadn,
        a.medicine_nmpn,
        a.medicine_dosage_num,
        a.medicine_dosage_unit,
        a.medicine_dosage_form,
        a.material_spec,
        a.c_m_spec,
        a.extend_spec,
        a.grade,
        a.grade_id,
        a.remark,
        a.atc,
        a.is_sell,
        a.smart_dispense,
        a.piece_price,
        a.package_price,
        a.package_cost_price,
        a.origin,
        a.certificate_name,
        a.certificate_no,
        a.in_tax_rat,
        a.out_tax_rat,
        a.default_in_out_tax,
        a.created_user_id,
        a.created_date,
        a.last_modified_user_id,
        a.last_modified_date,
        a.disable,
        a.need_executive,
        a.type_id,
        a.custom_type_id,
        a.short_id_n,
        a.combine_type,
        a.en_name,
        a.biz_extensions,
        a.inorder_config,
        a.sell_config,
        a.goods_version,
        a.spec_type,
        a.component_content_num,
        a.component_content_unit,
        a.biz_relevant_id,
        a.shebao_pay_mode,
        a.device_type,
        a.has_association,
        a.spu_goods_id,
        a.compose_flag,
        a.extend_spec_id,
        a.process_price,
        a.inner_flag,
        a.fee_compose_type,
        a.fee_type_id,
        a.inspection_site
        FROM v2_goods AS a
        WHERE sub_type = #{subType}
        <if test="types != null and types.size() > 0">
            AND type IN
            <foreach collection="types" item="type" open="(" separator="," close=")">#{type}</foreach>
        </if>
        <if test="extendSpec != null and extendSpec != ''">
            AND extend_spec = #{extendSpec}
        </if>
        <if test="combineType != null">
            AND combine_type = #{combineType}
        </if>
        <if test="keyword != null and keyword !=''">
            AND (a.name LIKE CONCAT('%', #{keyword}, '%')
            OR a.py LIKE CONCAT('%', #{keyword}, '%')
            OR a.en_name LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        AND organ_id IN
        <foreach collection="chainIds" item="chainId" open="(" separator="," close=")">#{chainId}</foreach>
        <if test="bizRelevantIds != null and bizRelevantIds.size() > 0">
            AND a.biz_relevant_id IN
            <foreach collection="bizRelevantIds" item="bizRelevantId" open="(" separator="," close=")">
                #{bizRelevantId}
            </foreach>
        </if>
        AND a.status &lt; #{status}
        <if test="offset != null and limit != null">
            LIMIT #{offset},#{limit}
        </if>
    </select>
    <select id="findAllByOrganIdInAndTypeAndSubTypeAndCmSpecAndExtendSpecAndStatusLessThan"
            resultType="cn.abcyun.cis.goods.model.Goods">
        SELECT a.id,
        a.domain_id,
        a.short_id,
        a.status,
        a.name,
        a.py,
        a.organ_id,
        a.type,
        a.sub_type,
        a.bar_code,
        a.manufacturer,
        a.manufacturer_full,
        a.piece_num,
        a.piece_unit,
        a.package_unit,
        a.dismounting,
        a.medicine_cadn,
        a.medicine_nmpn,
        a.medicine_dosage_num,
        a.medicine_dosage_unit,
        a.medicine_dosage_form,
        a.material_spec,
        a.c_m_spec,
        a.extend_spec,
        a.grade,
        a.grade_id,
        a.remark,
        a.atc,
        a.is_sell,
        a.smart_dispense,
        a.piece_price,
        a.package_price,
        a.package_cost_price,
        a.origin,
        a.certificate_name,
        a.certificate_no,
        a.in_tax_rat,
        a.out_tax_rat,
        a.default_in_out_tax,
        a.created_user_id,
        a.created_date,
        a.last_modified_user_id,
        a.last_modified_date,
        a.disable,
        a.need_executive,
        a.type_id,
        a.custom_type_id,
        a.short_id_n,
        a.combine_type,
        a.en_name,
        a.biz_extensions,
        a.inorder_config,
        a.sell_config,
        a.goods_version,
        a.spec_type,
        a.component_content_num,
        a.component_content_unit,
        a.biz_relevant_id,
        a.shebao_pay_mode,
        a.device_type,
        a.has_association,
        a.spu_goods_id,
        a.compose_flag,
        a.extend_spec_id,
        a.process_price,
        a.inner_flag,
        a.fee_compose_type,
        a.fee_type_id,
        a.inspection_site,
        a.gender,
        a.apply_population
        FROM v2_goods AS a
        WHERE organ_id = #{chainId}
        AND a.status &lt; #{status}
        <if test="type != null">
            AND type = #{type}
        </if>
        <if test="subType != null">
            AND sub_type = #{subType}
        </if>
        <if test="cmSpec != null and cmSpec != ''">
            AND c_m_spec = #{cmSpec}
        </if>
        <if test="extendSpec != null and extendSpec != ''">
            AND extend_spec = #{extendSpec}
        </if>
    </select>
    <select id="countAllByOrganIdInAndTypeAndSubTypeAndBizRelevantIdInAndStatusLessThan" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM v2_goods AS a
        WHERE type = #{type}
        AND sub_type = #{subType}
        <if test="extendSpec != null and extendSpec != ''">
            AND extend_spec = #{extendSpec}
        </if>
        <if test="combineType != null">
            AND combine_type = #{combineType}
        </if>
        <if test="keyword != null and keyword !=''">
            AND (a.name LIKE CONCAT('%', #{keyword}, '%')
            OR a.py LIKE CONCAT('%', #{keyword}, '%')
            OR a.en_name LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        AND organ_id IN
        <foreach collection="chainIds" item="chainId" open="(" separator="," close=")">#{chainId}</foreach>
        <if test="bizRelevantIds != null and bizRelevantIds.size() > 0">
            AND a.biz_relevant_id IN
            <foreach collection="bizRelevantIds" item="bizRelevantId" open="(" separator="," close=")">
                #{bizRelevantId}
            </foreach>
        </if>
        AND a.status &lt; #{status}
    </select>

    <update id="updateGoodsExtendStockWarn">
        update v2_goods_extend
        set turnover_days = #{turnoverDays}, days_of_day_avg_sell = #{daysOfDayAvgSell}
        where chain_id = #{chainId} and organ_id = #{clinicId} and stock_warn_customize = 0 and goods_id in
        <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="updateGoodsSpuExtendStockWarn">
        update v2_goods_spu_extend
        set turnover_days = #{turnoverDays}, days_of_day_avg_sell = #{daysOfDayAvgSell}
        where chain_id = #{chainId} and clinic_id = #{clinicId} and stock_warn_customize = 0 and spu_goods_id in
        <foreach collection="spuGoodsIdList" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="countClinicPriceUpGoodsCount" resultType="cn.abcyun.cis.goods.dto.GoodsFeeTypeIdCount">
        select clinic_id as clinicId, count(distinct goods_id) goodsCount
        from v2_goods_stat
        where chain_id = #{chainId}
          and price_type = 3
        group by clinic_id
    </select>
    <select id="countGoodsFeeTypeIdGoodsCount" resultType="cn.abcyun.cis.goods.dto.GoodsFeeTypeIdCount">
        select fee_type_id feeTypeId, count(id) goodsCount
        from v2_goods
        where organ_id = #{chainId} and status = 1
        and fee_type_id in
        <foreach collection="feeTypeIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by fee_type_id
    </select>

    <select id="findNonStockGoodsIdList" resultType="java.lang.String">
        select id
        from v2_goods
        where organ_id = #{chainId}
          and type not in (1, 2, 7, 24)
          and status != 99
          and inner_flag != 1
        order by created_date
            limit #{offset}, #{limit}
    </select>

    <select id="findGoodsIdListByTypeAndFeeTypeIdList" resultType="java.lang.String">
        select distinct id
        from v2_goods
        where organ_id = #{chainId} and type = #{type} and status = 1 and disable = 0 and fee_type_id in
        <foreach collection="feeTypeIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findHospitalNotNeedExecutiveGoodsIdList" resultType="java.lang.String">
        select distinct id
        from v2_goods
        where organ_id = #{chainId}
          and type in (21, 4)
          and need_executive &amp; 0x2 = 0
          and status = 1
    </select>

    <select id="queryGoodsByName" resultType="cn.abcyun.cis.goods.model.Goods">
        select id id,
        name name,
        medicine_cadn medicineCadn,
        type type,
        sub_type subType,
        medicine_nmpn medicineNmpn,
        medicine_dosage_num medicineDosageNum,
        medicine_dosage_unit medicineDosageUnit,
        medicine_dosage_form medicineDosageForm,
        piece_num pieceNum,
        piece_unit pieceUnit,
        package_unit packageUnit,
        c_m_spec CMSpec,
        extend_spec extendSpec,
        material_spec materialSpec,
        spec_type specType,
        component_content_num componentContentNum,
        component_content_unit componentContentUnit
        from v2_goods where organ_id = #{organId} and status = 1 and type in (1) and
        <foreach collection="nameList" item="name" open="(" separator="or" close=")">
            (name like concat('%', #{name}, '%') or medicine_cadn like concat('%', #{name}, '%'))
        </foreach>
    </select>

    <update id="updateGoodsGspModifyStatus">
        update v2_goods
        set gsp_modify_status  = #{gspModifyStatus},
            gsp_modify_inst_id = #{gspModifyInstId}
        where id = #{goodsId}
    </update>
    <update id="enableDisableGoods">
        update v2_goods
        set disable            = 0,
            inorder_config=0,
            sell_config        =0,
            last_modified_date = now()
        where organ_id = #{chainId}
          and (disable != 0 or inorder_config != 0 or sell_config != 0)
    </update>
    <update id="enableDisableGoodsExtend">
        update v2_goods_extend
        set disable        = 0,
            inorder_config = 0,
            sell_config    = 0
        where chain_id = #{chainId}
          and organ_id = #{chainId}
          and (disable != 0 or inorder_config != 0 or sell_config != 0)
    </update>
    <update id="updateComposeOptClinicId">
        update v2_goods_compose_opt
        set clinic_id             = null,
            last_modified_user_id = 'sys'
        where chain_id = #{chainId}
          and clinic_id is not null
          and clinic_id != '' and compose_type = 10 and is_deleted = 0
    </update>

    <select id="findGoodsListNotCheck" resultType="cn.abcyun.cis.goods.dto.StockCheckGoodsDto">
        SELECT d.goods_id as goodsId,
        d.batch_id as batchId,
        d.batch_no as batchNo,
        d.expiry_date as expiryDate,
        d.package_cost_price as packageCostPrice,
        d.created_date as inDate,
        <if test="batchExport == 0">
            IFNULL(SUM(d.package_count), 0) as packageCount,
            IFNULL(SUM(d.piece_count), 0) as pieceCount
        </if>
        <if test="batchExport == 1">
            sum(ifnull(d.package_count, 0)) as packageCount,
            sum(ifnull(d.piece_count, 0)) as pieceCount
        </if>
        FROM v2_goods_stock as d
        WHERE d.chain_id = #{chainId} and d.organ_id = #{clinicId}
        and d.goods_id in
        <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and (d.package_count &gt; 0 or d.piece_count &gt; 0)
        <if test="batchExport == 0">
            group by d.goods_id
        </if>
        <if test="batchExport == 1">
            group by d.goods_id, d.batch_id
        </if>
        <if test="offset != null and offset gt -1 and limit != null and limit gt -1">
            LIMIT #{offset}, #{limit}
        </if>
    </select>
    <update id="updateRemoveGoodsTag">
        update v2_goods_tag
        set is_deleted = 1,
        last_modified = now(),
        last_modified_by = #{employeeId}
        where chain_id = #{chainId}
        and tag_id in
        <foreach collection="goodsTagIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <select id="countGoodsTagIdGoodsCount" resultType="cn.abcyun.cis.goods.dto.GoodsFeeTypeIdCount">
        select tag_id as tagId, count(distinct goods_id) goodsCount, count(distinct spu_goods_id) spuGoodsCount
        from v2_goods_tag
        where chain_id = #{chainId}
          and is_deleted = 0
        group by tag_id
    </select>

    <!-- 查询待生效的价格修改项 -->
    <select id="selectPendingPriceItems" resultType="cn.abcyun.cis.goods.model.GoodsModifyPriceOrderItem">
        SELECT i.goods_id as goodsId, i.clinic_id as clinicId,i.item_id as itemId,o.extend_infos as extendInfosStr
        FROM v2_goods_modify_price_order_item AS i
        INNER JOIN v2_goods_modify_price_order AS o
        ON i.order_id = o.id
        AND i.chain_id = o.chain_id
        WHERE i.status = 0
        AND i.is_deleted =0 and o.is_deleted = 0
        AND o.status IN (0,1, 2)
        AND i.chain_id = #{chainId}
        <if test="goodsIdList != null and goodsIdList.size() > 0">
            AND i.goods_id IN
            <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 更新商品统计表的待生效价格项ID -->
    <update id="updateGoodsStatPendingPriceItems">
        UPDATE v2_goods_stat AS a
        SET a.waiting_effect_update_price_item_id =
        CASE
        WHEN EXISTS (
        SELECT 1 FROM v2_goods_modify_price_order_item AS i
        INNER JOIN v2_goods_modify_price_order AS o
        ON i.order_id = o.id
        AND i.chain_id = o.chain_id
        WHERE i.status = 0
        AND o.status IN (1, 2)
        AND i.chain_id = #{chainId}
        AND i.goods_id = a.goods_id
        ) THEN (
        SELECT i.id FROM v2_goods_modify_price_order_item AS i
        INNER JOIN v2_goods_modify_price_order AS o
        ON i.order_id = o.id
        AND i.chain_id = o.chain_id
        WHERE i.status = 0
        AND o.status IN (1, 2)
        AND i.chain_id = #{chainId}
        AND i.goods_id = a.goods_id
        LIMIT 1
        )
        ELSE NULL
        END
        WHERE a.chain_id = #{chainId}
        <if test="goodsIdList != null and goodsIdList.size() > 0">
            AND a.goods_id IN
            <foreach collection="goodsIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="clinicIdList != null and clinicIdList.size() > 0">
            AND a.clinic_id IN
            <foreach collection="clinicIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="getLimitPriceUsedGoodsId" resultType="java.lang.String">
        select distinct a.goods_id
        from v2_goods_extend a
                 join v2_goods b on a.chain_id = b.organ_id and a.goods_id = b.id
        where a.chain_id = #{chainId}
          and a.organ_id = #{clinicId}
          and a.shebao_code_national_code = #{nationalCode}
          and b.type = 19
          and b.status = 1
    </select>

    <select id="queryCombineNotMatchGoodsIdList" resultType="java.lang.String">
        select distinct goods_id
        from v2_goods_medical_stat
        where chain_id = #{chainId}
          and (goods_type in (3, 4))
          and combine_type = 1
          and fee_compose_type = 0
    </select>
    <select id="queryCombineNotMatchGoodsIdListV1" resultType="java.lang.String">
        select id
        from v2_goods
        where organ_id = #{chainId}
          and type in (3, 4)
          and status = 1
    </select>

    <select id="querySingleNotMatchGoodsIdList" resultType="java.lang.String">
        select distinct goods_id
        from v2_goods_medical_stat
        where chain_id = #{chainId}
          and ((goods_type = 3 and combine_type = 0) or (goods_type in (4, 19)))
          and inner_flag = 0
          and fee_compose_type = 0
    </select>

    <select id="queryLimitPriceProductGoodsId" resultType="java.lang.String">
        select b.goods_id from v2_goods a
        join v2_goods_extend b on a.organ_id = b.chain_id and a.id = b.goods_id
        where a.organ_id = #{chainId} and a.status = 1 and b.organ_id = #{clinicId}
        and b.shebao_limit_price_rule_type = 1
        <choose>
            <when test="goodsType == 0">
                and a.type in (1, 2)
            </when>
            <otherwise>
                and a.type = 19
                <if test="isHospital == 0">
                    and a.inner_flag = 1
                </if>
            </otherwise>
        </choose>
        order by a.created_date
        limit #{offset}, #{limit}
    </select>

    <select id="countLimitPriceProductGoodsId" resultType="int">
        select count(1) from v2_goods a
        join v2_goods_extend b on a.organ_id = b.chain_id and a.id = b.goods_id
        where a.organ_id = #{chainId} and a.status = 1 and b.organ_id = #{clinicId}
        and b.shebao_limit_price_rule_type = 1
        <choose>
            <when test="goodsType == 0">
                and a.type in (1, 2)
            </when>
            <otherwise>
                and a.type = 19
                <if test="isHospital == 0">
                    and a.inner_flag = 1
                </if>
            </otherwise>
        </choose>
    </select>

    <update id="updateGoodsStatLastGoodsStockInOrderSupplier">
        update v2_goods_stat set last_stock_in_order_supplier = #{supplierName}
        where chain_id = #{chainId} and last_stock_in_id in
        <foreach collection="stockInIdList" item="stockIdId" open="(" separator="," close=")">
            #{stockIdId}
        </foreach>
    </update>

    <select id="queryGoodsStatInfoByShebaoCode" resultType="cn.abcyun.cis.goods.model.GoodsStat">
        select shebao_code_national_code shebaoCodeNationalCode, goods_id goodsId, pharmacy_no pharmacyNo
        from v2_goods_stat
        where chain_id = #{chainId} and clinic_id = #{clinicId} and pharmacy_no = #{pharmacyNo} and pharmacy_type = 0
        and
        <foreach collection="shebaoCodeList" item="shebaoCode" open="(" separator="or" close=")">
            shebao_code_national_code like concat(#{shebaoCode}, '%')
        </foreach>
    </select>

    <update id="updateChainToSingleDisableStatus">
        update v2_goods g
            join v2_goods_extend e
        on g.organ_id = e.chain_id and g.id = e.goods_id and e.organ_id = #{clinicId}
            set g.disable = e.disable, g.inorder_config = e.inorder_config, g.sell_config = e.sell_config, e.disable = 0, e.inorder_config = 0, e.sell_config = 0
        where g.organ_id = #{chainId}
          and (g.disable != e.disable
           or g.sell_config != e.sell_config
           or g.inorder_config != e.inorder_config)
          and (e.disable != 0
           or e.sell_config != 0
           or e.inorder_config != 0)
    </update>

    <select id="queryParentGoodsIdOfFeeComposeType" resultType="java.lang.String">
        select distinct parent_goods_id from v2_goods_compose_opt
        where chain_id = #{chainId}
        <choose>
            <when test="clinicId != null and clinicId != ''">
                and clinic_id = #{clinicId}
            </when>
            <otherwise>
                and (clinic_id is null or clinic_id = '')
            </otherwise>
        </choose>
        and is_deleted = 0 and compose_type = 10
    </select>

    <select id="queryGoodsIdOfFeeCompose" resultType="java.lang.String">
        select id
        from v2_goods
        where organ_id = #{chainId}
          and status = 1
          and type in (3, 4, 19)
          and inner_flag = 0
          and fee_compose_type = 0
    </select>

    <update id="deleteGoodsFeeComposeOpt">
        update v2_goods_compose_opt set is_deleted = 1, last_modified_date = now(), last_modified_user_id =
        #{operatorId}
        where chain_id = #{chainId} and is_deleted = 0 and compose_type = 10
        <choose>
            <when test="clinicId != null and clinicId != ''">
                and clinic_id = #{clinicId}
            </when>
            <otherwise>
                and (clinic_id is null or clinic_id = '')
            </otherwise>
        </choose>
        and parent_goods_id in
        <foreach collection="goodsIdList" item="goodsId" open="(" separator="," close=")">
            #{goodsId}
        </foreach>
    </update>
    <update id="updateGoodsFeeComposeOpt">
        update v2_goods_compose_opt set clinic_id = null, last_modified_date = now(), last_modified_user_id =
        #{operatorId}
        where chain_id = #{chainId} and is_deleted = 0 and compose_type = 10
        <choose>
            <when test="clinicId != null and clinicId != ''">
                and clinic_id = #{clinicId}
            </when>
            <otherwise>
                and (clinic_id is null or clinic_id = '')
            </otherwise>
        </choose>
        and parent_goods_id in
        <foreach collection="goodsIdList" item="goodsId" open="(" separator="," close=")">
            #{goodsId}
        </foreach>
    </update>

    <update id="updateGoodsFeeComposeType">
        update v2_goods set fee_compose_type = 20, compose_flag = compose_flag|0x40
        where organ_id = #{chainId} and fee_compose_type = 0 and status = 1
        and id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateGoodsStockLockingPieceNum">
        UPDATE v2_goods_stock_locking
        SET locking_piece_num           = #{newPieceNum},
            lock_left_total_piece_count = IF(lock_left_total_piece_count = locking_total_piece_count,
                                             locking_piece_count + locking_package_count * #{newPieceNum}, 0),
            locking_total_piece_count   = (locking_piece_count + locking_package_count * #{newPieceNum})
        WHERE goods_id = #{goodsId}
          AND status IN (0, 10)
    </update>
</mapper>