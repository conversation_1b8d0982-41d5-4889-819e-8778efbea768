package cn.abcyun.cis.goods.service;


import cn.abcyun.cis.goods.exception.CisGoodsServiceException;
import cn.abcyun.cis.goods.vo.frontend.ClientGoodsSearchSuggestReq;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.transaction.Transactional;

import static cn.abcyun.cis.goods.service.data.GoodsStockTestData.testChainId;
import static cn.abcyun.cis.goods.service.data.GoodsStockTestData.testClinicId;



@RunWith(SpringRunner.class)    //Spring Boot 单测的注解
@SpringBootTest                 //Spring Boot 单测的注解
@ActiveProfiles("local")        //配置文件用本地 --- 这里需要执行junyong的sh脚本映射
@Configuration
@Transactional //事务
@Rollback(true) //单测跑完，需要把全部数据回滚掉
public class GoodsRecommendServiceTest {
    @Autowired
    private GoodsService goodsService;



    @Before
    public void setUp() throws Exception {

    }


    /**
     * 测试推荐服务主流程
     */
//    @Test
//    public void testRecommend() throws CisGoodsServiceException, UnsupportedEncodingException {
//        GoodsRecommendReq req = new GoodsRecommendReq();
//        req.setClinicId("c0c9b546af44429482bae46ccc47a362");
//        req.setDiagnosis("急性支气管炎");
//        req.setDoctorId("d4e0ddd88273437aa6c24bdabf8ced6c");
//        GoodsRecommendReq.JsonGoodsType jsonType = new GoodsRecommendReq.JsonGoodsType();
//        jsonType.setType(1);
//        jsonType.setSubType(new ArrayList<>());
//        jsonType.getSubType().add(2);
//        req.setJsonType(new ArrayList<>());
//        req.getJsonType().add(jsonType);
//        req.setChainId(testChainId);
//        GoodsRecommendRsp rsp = goodsService.recommendGoods(req,testChainId,"c0c9b546af44429482bae46ccc47a362",2);
//        MatcherAssert.assertThat( rsp ,notNullValue());
//    }

    @Test
    public void testSuggest() throws CisGoodsServiceException {
        ClientGoodsSearchSuggestReq req = new ClientGoodsSearchSuggestReq();
        req.setClinicId(testClinicId);
        req.setChainId(testChainId);
        req.setKeyWord("");
        req.setLimit(10);
        req.setOffset(0);
//
//        ClientGoodsSearchSuggestRsp rsp = goodsService.searchStockGoods(req);
//        MatcherAssert.assertThat( rsp ,notNullValue());
    }

}