alter table v2_goods_pharmacy_rule
    add     rule_process_info       json        null comment '加工方式 null 未配置,[]所有' ;

    #默认的重要的下达规则是全部
update v2_goods_pharmacy_rule
set rule_process_info =JSON_OBJECT('processInfos',JSON_ARRAY())
where inner_flag = 1
  and JSON_OVERLAPS(JSON_EXTRACT(goods_type, '$.typeIdList[*].typeId'),'[14,15]')




SELECT CONCAT('this.put("',CONCAT(type,'_'),'", "',name,'");') from abc_cis_charge.v2_charge_rule_process_usage_type where parent_id = 0;
SELECT CONCAT('this.put("',CONCAT(type,'_',sub_type),'", "',name,'");') from abc_cis_charge.v2_charge_rule_process_usage_type where parent_id != 0;
