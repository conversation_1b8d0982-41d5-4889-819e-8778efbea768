
ALTER TABLE v2_goods_mall_purchase_car
    ADD       purchase_plan_idx      int unsigned       NOT NULL DEFAULT 0 COMMENT '采购计划的索引目前为0 1 2 3 4 [方便控制一个诊所最多创建5个采购计划]';
;

ALTER TABLE v2_goods_mall_purchase_temporary_order
    ADD       purchase_plan_idx      int unsigned       NOT NULL DEFAULT 0 COMMENT '采购计划的索引目前为0 1 2 3 4 [方便控制一个诊所最多创建5个采购计划]';
;


create table v2_goods_mall_purchase_plan_list
(
    id                   bigint unsigned primary key comment '雪花算法生成,数据库ID',
    chain_id             varchar(32)                              not null comment '总店id',
    clinic_id            varchar(32)                              not null comment '子店id',
    name             varchar(32)                              not null comment '采购计划的名字',
    purchase_plan_idx              int unsigned                                      NOT NULL DEFAULT 0 comment '采购计划的索引目前为0 1 2 3 4 [方便控制一个诊所最多创建5个采购计划]',
    is_deleted       tinyint(1) unsigned default 0                 not null comment '是否删除状态（0：正常；1：被删除）',
    created              timestamp      default CURRENT_TIMESTAMP not null comment '生成时间',
    created_by           varchar(32)    default ''                not null comment '生成员工id',
    last_modified        timestamp      default CURRENT_TIMESTAMP not null comment '最后修改时间',
    last_modified_by     varchar(32)    default ''                not null comment '最后修改时间的员工id'
);
create index ix_v2gmpplist_chainid_clinicid
    on v2_goods_mall_purchase_plan_list (chain_id,clinic_id);
