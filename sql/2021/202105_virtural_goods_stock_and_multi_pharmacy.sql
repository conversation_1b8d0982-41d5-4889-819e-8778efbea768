#诊所药房信息(no虽然是基于门店的,在设计上整个连锁内递增,如果想要两个门店的两个虚拟药房在总店聚合在一起可以把两个clinicId里面的虚拟药房的no设置为一样)
#关系 药品1--->0-n个药房
#本地实体药房 type = 0 stock_cut_type 0
#杭州代煎待配虚拟药房 type = 1 stock_cut_type 0
#空中药房  type = 2 stock_cut_type 1
create table v2_goods_pharmacy
(
    id               BIGINT UNSIGNED PRIMARY KEY COMMENT '雪花算法生成的数据库主键',
    chain_id         varchar(32)  default ''                not null comment '总部id',
    clinic_id        varchar(32)  default ''                not null comment '门店id',
    no               int          default 0                 NOT NULL COMMENT '[虚拟库存多药房]药房no从0开始累加(不用id是基于兼容性的考虑，我们约定0号药房是第一个本地药房)',
    name             VARCHAR(200) DEFAULT ''                NOT NULL COMMENT '药房名字',
    type             int          DEFAULT 0                 NOT NULL COMMENT '药房类型 0 实体药房 1 空中药房 2 虚拟药房',
    stock_cut_type   int          DEFAULT 0                 NOT NULL COMMENT '库存扣减类型 0 库存够才扣(不能扣成负) 1库存不足可扣(可以扣成负) ',
    sort             int          default 0                 NOT NULL COMMENT '排序字段升序',
    status           int unsigned default 1                 NOT NULL COMMENT '药房是否启用（0：未启用；1：启用）',
    is_deleted       int unsigned default 0                 NOT NULL COMMENT '是否删除状态（0：正常；1：被删除）',
    created_by       varchar(32)                            not null comment '创建人',
    created          timestamp    default CURRENT_TIMESTAMP not null comment '创建时间',
    last_modified_by varchar(32)                            not null comment '最后修改人',
    last_modified    timestamp    default CURRENT_TIMESTAMP not null comment '最后修改时间'
)
    comment '诊所药房信息表';


ALTER TABLE v2_goods_pharmacy
    ADD goods_type             int null comment '[虚拟药房支持的药品范围]null表示不限制(同goods类型)',
    ADD goods_sub_type         int null    comment '[虚拟药房支持的药品范围]null表示不限制(同goods类型)',
    ADD goods_cmspec           varchar(10)  NULL COMMENT  '[虚拟药房支持的药品范围]null表示不限制(同goods类型)';


create index ix_chainid_clinicid_and_no_on_v2gpharmacy
    on v2_goods_pharmacy (chain_id, clinic_id, no);
ALTER TABLE v2_goods_supplier
    ADD pharmacy_type int default 0 not null comment '[供应商所属药房类型]每种药房类型有自己独立的供应商,药房类型 0 实体药房 1 空中药房 2 虚拟药房';
#入库单上需要记录这个入库单是入到哪个药房的哪个供应商上 0 0 是现在默认的实体库存
ALTER TABLE v2_goods_stock_in_order
    ADD pharmacy_no int default 0 not null comment '[虚拟库存多药房]药房no从0开始累加';


#库存条目上也要记录药房和供应商
ALTER TABLE v2_goods_stock
    ADD pharmacy_no             int default 0 not null comment '[虚拟库存多药房]所属药房的ID',
    ADD supplier_id             VARCHAR(32)   null comment '[虚拟库存多药房]供应商的Id表示无供应商冗余入库上的supperlierId有两个好处1.不用关联三个表查询才能得到供应商2,为虚拟库存不入库指定供应商称为可能',
    ADD pharmacy_type           int DEFAULT 0 NOT NULL COMMENT '[冗余一下]药房类型 0 实体药房 1 空中药房 2 虚拟药房',
    ADD pharmacy_stock_cut_type int DEFAULT 0 NOT NULL COMMENT '[冗余一下]库存扣减类型 0库存够才扣(不能扣成负) 1库存不足可扣(可以扣成负) ';




#
ALTER TABLE v2_goods_stat
    DROP INDEX ix_u;


#add 药房号
ALTER TABLE v2_goods_stat
    ADD pharmacy_no int default 0 not null comment '[虚拟库存多药房]所属药房的no 默认值0为第一个实体药房';




#goods nodejs上使用唯一键进行更新goodsStat并发更新和使用的sequ库等验证用主键更新会出现更新失败的情况。
#在未迁移到scGoods上来之前还是用唯一键进行更新,再加一个唯一键回来
ALTER TABLE v2_goods_stat
ADD CONSTRAINT ix_u UNIQUE(chain_id,clinic_id,goods_id,pharmacy_no );





########################刷数据


#把order表上的supplierId刷到stock表上,这里innerjoin stock-in表,调拨入库的innerjoin不上,不刷数据.
#把supplierId刷到stock表上:1.stock上展示供应商不用关联两张表过去到 inorder表上;2.入库后供应商不会不修改;3为后面虚拟药房不入库也可以关联供应商提供了可能
#update v2_goods_stock a inner join v2_goods_stock_in as b on a.in_id = b.id and a.chain_id =#chainId
#    inner join v2_goods_stock_in_order c on b.order_id = c.id and c.chain_id =#chainId
#set a.supplier_id = c.supplier_id
#where a.supplier_id is null;

### 把之前那迁移的中医馆stock的bat_id是null的值刷成 id
