create table v2_goods_stat_offline_single_to_chain
(
    id              int(11) unsigned auto_increment primary key,
    chain_id   varchar(32)  not null comment 'chainId' ,
    status           int       default 0                 not null comment '状态 0 新建 1 刷出了goodsStat',
    created                 timestamp           default CURRENT_TIMESTAMP not null comment '门诊单创建创建时间',
 constraint ix_v2gsostochain_chain_id unique (chain_id)
)comment '同步单店升级连锁的任务表，不会太多，先不加索引，jenkins任务写入 离线任务处理后把status设置为1';